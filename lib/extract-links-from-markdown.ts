import _ from "lodash";
import { RawImage } from "../models/job/job";

export function extractLinksFromMarkdownUsingMarked(
  imageLinksAirtable?: string,
  description?: string,
): RawImage[] {
  if (!imageLinksAirtable) return [];

  const imageStrings = imageLinksAirtable.split(",");

  const imageLinks = imageStrings.map((imageString) => {
    const imageLink = imageString.replace(")", "(").split("(")[1];
    return imageLink;
  });

  return _.uniq(imageLinks).map((imageLink) => ({
    url: imageLink,
    description,
  }));
}
