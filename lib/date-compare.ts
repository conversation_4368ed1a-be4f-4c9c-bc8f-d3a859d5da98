import { isBefore, subDays } from "date-fns";
import { DateOrTimestamp, getDate } from "./date-or-timestamp";

type DateComparer<Fallback = never> = (
  time: DateOrTimestamp,
) => boolean | Fallback;
type SafeDateComparer = (time: Date) => boolean;

function checkDate<T>(
  comparer: SafeDateComparer,
  fallback?: T,
): DateComparer<T> {
  return (time: DateOrTimestamp) => {
    const date = getDate(time);
    if (!date) {
      if (fallback !== undefined) return fallback;
      throw new Error("Invalid date");
    }
    return comparer(date);
  };
}

export function isOlderThanDays<T>(
  days: number,
  fallback?: T,
): DateComparer<T> {
  return checkDate(
    (date) => isBefore(date, subDays(new Date(), days)),
    fallback,
  );
}
