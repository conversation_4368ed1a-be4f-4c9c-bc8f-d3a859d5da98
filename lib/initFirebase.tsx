import { getApps, initializeApp } from "firebase/app";
import { connectAuthEmulator, getAuth } from "firebase/auth";
import { connectFirestoreEmulator, getFirestore } from "firebase/firestore";
import { connectFunctionsEmulator, getFunctions } from "firebase/functions";
import { connectStorageEmulator, getStorage } from "firebase/storage";
import { environment, isDev, isEmulator } from "./environment";

export const PROJECT_ID = isDev ? "done-dev-f0434" : "done-50549";

function getFirebaseConfig() {
  return {
    prod: {
      apiKey: "AIzaSyCrxQidn5-tQYDtWQQcSFU0gFuf_JzuEl0",
      authDomain: "done-50549.firebaseapp.com",
      databaseURL: "https://done-50549.firebaseio.com",
      projectId: "done-50549",
      storageBucket: "done-50549.appspot.com",
      messagingSenderId: "865533511519",
      appId: "1:865533511519:web:e63ff7d4160289cbe8d8ea",
    },
    dev: {
      apiKey: "AIzaSyBjtVUJtmu5ChdHnqeoq-n-tSR1xSDmUug",
      authDomain: "done-dev-f0434.firebaseapp.com",
      databaseURL: "https://done-dev-f0434.firebaseio.com",
      projectId: "done-dev-f0434",
      storageBucket: "done-dev-f0434.appspot.com",
      messagingSenderId: "704360355803",
      appId: "1:704360355803:web:46c20c372f0d2dc0ebb9fe",
    },
  }[environment];
}

export function initFirebase() {
  const config = getFirebaseConfig();

  if (!config)
    throw new Error(`No config set for environment: "${environment}"`);

  if (getApps().length) return;

  initializeApp(config);

  if (!isEmulator) return;

  console.log("Connecting to Firebase emulator suite...");

  connectAuthEmulator(getAuth(), "http://localhost:9099");
  connectFirestoreEmulator(getFirestore(), "localhost", 8080);
  connectFunctionsEmulator(getFunctions(), "localhost", 5001);
  connectStorageEmulator(getStorage(), "localhost", 9199);
}
