import { FixedPriceJobQuantity } from "../models/job/fixed_price_job";
import { getUserAddressFromJob } from "./user-address-from-job";
import { Job } from "../models/job/job";

export async function fixedPriceJobOfferSmsMessage(
  job: Job,
  fixedPriceJobs: FixedPriceJobQuantity[],
): Promise<string> {
  const messageHeader = "Ny fastprisförfrågan från Done!\n";

  const fixedPricesInfo = fixedPriceJobs
    .map((e) => fixedPriceJobSmsMessageLine(e))
    .reduce((total, item) => total.concat("\n\n", item));

  const messageFooter = `\nBeskrivning:${job?.description}\nÖnskad start: ${
    job?.preferredStartDate
  }\nOmråde: ${await getUserAddressFromJob(
    job,
  )}\n\nVänligen svara på uppdraget i appen.\ndoneservices://app/jobOffer?jobOfferId=${
    job.reference.id
  }`;

  return `${messageHeader}\n${fixedPricesInfo}\n${messageFooter}`;
}

function fixedPriceJobSmsMessageLine(
  fixedPriceJob: FixedPriceJobQuantity,
): string {
  const message: string = `${fixedPriceJob.count}x ${fixedPriceJob.job.title}${
    fixedPriceJob.products && fixedPriceJob.products.length > 0
      ? `, ${fixedPriceJob.products
          ?.map((e) => `${e.title}`)
          .reduce((total, item) => total.concat(", ", item))}`
      : ""
  }`;
  return message;
}
