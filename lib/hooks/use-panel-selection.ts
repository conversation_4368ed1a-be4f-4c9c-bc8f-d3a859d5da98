import { useRef, useState, useEffect } from "react";
import { ImperativePanelHandle } from "react-resizable-panels";

/**
 * Hook to manage detail panel selection state and behavior
 * by providing a ref to the details panel and a function to select an id.
 *
 * Collapses the details panel when the selected id is null.
 * Opens when the selected id is not null.
 * @returns Object containing selection state and handlers
 */
export function usePanelSelection<T>() {
  const [selected, setSelected] = useState<T | null>(null);
  const detailsPanelRef = useRef<ImperativePanelHandle>(null);

  useEffect(() => {
    // Collapse the details panel on initial render
    detailsPanelRef.current?.collapse();
  }, []);

  const handleSelect = (id: T) => {
    if (selected === id) {
      setSelected(null);
      detailsPanelRef.current?.collapse();
    } else {
      setSelected(id);
      detailsPanelRef.current?.expand();
    }
  };

  const handleClose = () => {
    setSelected(null);
    detailsPanelRef.current?.collapse();
  };

  return [
    selected,
    setSelected,
    detailsPanelRef,
    handleSelect,
    handleClose,
  ] as const;
}
