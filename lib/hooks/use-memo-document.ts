import { useEffect, useState } from "react";
import pMemoize from "p-memoize";
import { FirestoreModel } from "../../models/firestore_model";
import { DocumentReference, getDoc } from "firebase/firestore";

async function documentLoader(ref?: DocumentReference) {
  if (!ref) {
    return undefined;
  }
  const snapshot = await getDoc(ref);
  if (snapshot.exists()) {
    return { reference: snapshot.ref, ...snapshot.data() };
  } else {
    return undefined;
  }
}

const loader = pMemoize(documentLoader, { cacheKey: ([ref]) => ref?.path });

/**
 * Loads a document once and memoizes the result.
 * @param ref Reference to the document to load.
 * @returns The document, loading state and error.
 */
export function useMemoDocument<T extends FirestoreModel>(
  ref?: DocumentReference,
) {
  const [document, setDocument] = useState<T>();
  const [loading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error>();

  useEffect(() => {
    loader(ref)
      .then((data) => {
        setDocument(data as T);
        setIsLoading(false);
      })
      .catch((error) => {
        setError(error);
        setIsLoading(false);
        setDocument(undefined);
      });
  }, [ref]);

  return [document, loading, error] as const;
}
