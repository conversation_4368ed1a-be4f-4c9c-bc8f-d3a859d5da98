import { useEffect, useState } from "react";

/**
 * Executes a promise and returns the result.
 * @param callback Callback that returns a promise. It's recommeneded to wrap the callback in useCallback.
 * @returns The result of the promise or an error if the promise failed.
 */
export function usePromise<T>(callback: () => Promise<T>) {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<T>();
  const [error, setError] = useState<Error>();

  useEffect(() => {
    // Avoid potential infinite loop and only execute the callback if there is no error.
    if (error) return;

    callback()
      .then((result) => {
        setResult(result);
      })
      .catch((error) => {
        setError(error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [callback, error]);

  return [result, isLoading, error] as const;
}
