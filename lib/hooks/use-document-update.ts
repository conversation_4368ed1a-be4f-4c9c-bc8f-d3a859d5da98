import {
  CollectionReference,
  doc,
  DocumentData,
  getFirestore,
  updateDoc,
} from "firebase/firestore";
import { useCallback, useState } from "react";
import { UnknownObject } from "../unknown-object";
import { flattenInput } from "../flatten-input";
import { ExtendedGridColDef } from "../../components/common/table/columns";

/**
 * Hook that returns a callback that updates a document in a collection.
 * @param collectionRef Reference to the collection containing the document to update.
 * @param editableProps A list of properties that can be edited. If undefined, all properties are editable.
 * @param identityProp Name of the property that uniquely identifies the document.
 * @returns A callback that updates a document in a collection.
 */
export function useDocumentUpdate<T extends DocumentData>(
  collectionRef: CollectionReference<T>,
  editableProps?: string[],
  identityProp: string = "id",
) {
  const [isLoading, setIsLoading] = useState(false);
  const callback = useCallback<(item: UnknownObject, id?: string) => void>(
    async (item, id) => {
      const documentId = id || item[identityProp];
      if (!documentId || typeof documentId !== "string") {
        throw new Error(
          `Invalid identity prop ${identityProp} for item ${item}`,
        );
      }

      const flat: UnknownObject = flattenInput(item);

      // Filter out the props that are not editable or are undefined.
      const edited: DocumentData = Object.fromEntries(
        Object.entries(flat).filter(
          ([key, value]) =>
            (editableProps
              ? editableProps.includes(key)
              : key !== identityProp) && value !== undefined,
        ),
      );

      // Update the document.
      setIsLoading(true);
      const ref = doc(getFirestore(), collectionRef.path, documentId);
      await updateDoc(ref, edited);
      setIsLoading(false);
    },
    [identityProp, collectionRef.path, editableProps],
  );
  return [callback, isLoading] as const;
}

/**
 * Returns a list of editable props from a list of columns.
 * @param columns columns to get editable props from.
 * @returns A list of property names.
 */
export function getEditable(columns: ExtendedGridColDef[]): string[] {
  return columns.reduce<string[]>(
    (acc, col) => (col.editable ? [...acc, col.field] : acc),
    [],
  );
}
