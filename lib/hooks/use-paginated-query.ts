import { useImmerReducer } from "use-immer";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  limit,
  onSnapshot,
  query,
  QuerySnapshot,
  startAfter,
  Unsubscribe,
} from "firebase/firestore";
import { Query } from "firebase/firestore";
import { DocumentData } from "firebase/firestore";
import { emptyList } from "./list-reducer";
import { listReducer } from "./list-reducer";
import { v4 as uuid } from "uuid";
import { GetCursorFunction } from "./firestore-cursors";

/**
 * Listens to a firestore query and allows to fetch pages of data.
 *
 * @param initialQuery - Query to listen to.
 * @param getCursor - Function to get cursor from query snapshot.
 * @param pageSize - Number of documents per page.
 */
export function usePaginatedQuery<
  AppModel extends DocumentData = DocumentData,
  DBModel extends DocumentData = DocumentData,
>(
  initialQuery: Query<AppModel, DBModel>,
  getCursor: GetCursorFunction<AppModel, DBModel>,
  pageSize: number = 30,
) {
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);

  // Previous query used as starting point for next page
  const previousQueryRef = useRef(initialQuery);

  // List of listeners
  const listenersRef = useRef<Unsubscribe[]>([]);

  // List of snapshots
  const [snapshots, dispatchSnapshots] = useImmerReducer(
    listReducer<QuerySnapshot<AppModel, DBModel>>,
    emptyList(),
  );

  // Get snapshot for current page
  const currentSnapshot: QuerySnapshot<AppModel, DBModel> | undefined =
    snapshots[page]?.value;

  // Create callback to create listener
  const listen = useCallback(
    (newQuery: Query<AppModel, DBModel>) => {
      const key = uuid();
      const limitedQuery = query(newQuery, limit(pageSize));

      // Create listener that adds updates to snapshots list
      const listener = onSnapshot(limitedQuery, (snapshot) => {
        setLoading(false);
        dispatchSnapshots({ type: "update", key, value: snapshot });
      });

      // Add listener to listeners list
      listenersRef.current.push(listener);
      previousQueryRef.current = limitedQuery;
    },
    [dispatchSnapshots, listenersRef, pageSize],
  );

  // Create callback to fetch next page
  const goToPage = useCallback(
    (newPage: number) => {
      // If we haven't finalized the first page yet, don't do anything
      if (!currentSnapshot) return;

      // If we already have loaded this page, just update the page number
      if (newPage < snapshots.length) {
        setPage(newPage);
        return;
      }

      // Going to a new page means we need to load a new page of data
      setLoading(true);
      const cursor = getCursor(currentSnapshot);
      const newQuery = query(previousQueryRef.current, startAfter(cursor));
      listen(newQuery);
      setPage(newPage);
    },
    [snapshots, listen, previousQueryRef, getCursor],
  );

  // Initiate first listener on mount and clear all listeners and snapshots on unmount
  useEffect(() => {
    setLoading(true);
    listen(initialQuery);
    setPage(0);
    return () => {
      listenersRef.current.forEach((listener) => {
        listener();
      });
      listenersRef.current = [];
      dispatchSnapshots({ type: "clear" });
    };
  }, [listenersRef, listen, initialQuery, dispatchSnapshots]);

  return [[currentSnapshot, snapshots], { goToPage, page }, loading] as const;
}
