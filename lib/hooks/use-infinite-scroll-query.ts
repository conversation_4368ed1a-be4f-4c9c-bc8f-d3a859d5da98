import { useMemo } from "react";
import { QuerySnapshot } from "firebase/firestore";
import { Query } from "firebase/firestore";
import { DocumentData } from "firebase/firestore";
import { last } from "lodash";
import { GetCursorFunction } from "./firestore-cursors";
import { usePaginatedQuery } from "./use-paginated-query";

/**
 * Hook for infinite scroll pagination with Firestore.
 * Maintains an array of query snapshots and handles real-time updates.
 *
 * @param initialQuery - The base query to paginate
 * @param getCursor - Function to extract cursor from snapshot
 * @param pageSize - Number of documents per page
 */
export function useInfiniteScrollQuery<
  AppModel extends DocumentData = DocumentData,
  DBModel extends DocumentData = DocumentData,
>(
  initialQuery: Query<AppModel, DBModel>,
  getCursor: GetCursorFunction<AppModel, DBModel>,
  pageSize: number = 30,
): [QuerySnapshot<AppModel, DBModel>["docs"], boolean, () => void, boolean] {
  const [[_, snapshots], { goToPage, page }, loading] = usePaginatedQuery(
    initialQuery,
    getCursor,
    pageSize,
  );

  const loadMore = () => goToPage(page + 1);

  const { documents, lastSnapshot } = useMemo(() => {
    const documents = snapshots.flatMap((snapshot) => snapshot.value.docs);
    const lastSnapshot = last(snapshots)?.value;
    return { documents, lastSnapshot };
  }, [snapshots]);

  return [
    documents,
    loading,
    loadMore,
    (lastSnapshot?.docs.length ?? 0) === pageSize,
  ] as const;
}
