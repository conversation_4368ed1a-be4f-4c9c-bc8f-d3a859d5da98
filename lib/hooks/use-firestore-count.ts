import { getCountFromServer, Query } from "firebase/firestore";
import { useCallback } from "react";
import { usePromise } from "./use-promise";

/**
 * Counts the number of documents affected by a query.
 * @param query The query to count.
 * @returns The number of documents affected by the query or undefined if the count is still loading.
 */
export function useFirestoreCount(
  query: Query,
): [number | undefined, boolean, Error | undefined] {
  const countCallback = useCallback(() => getCountFromServer(query), [query]);
  const [countResult, loading, error] = usePromise(countCallback);
  return [countResult?.data().count, loading, error];
}
