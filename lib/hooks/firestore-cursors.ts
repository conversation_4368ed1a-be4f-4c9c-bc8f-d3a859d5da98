import { FieldPath, QuerySnapshot, DocumentData } from "firebase/firestore";
import { last } from "lodash";

export type GetCursorFunction<
  AppModel extends DocumentData = DocumentData,
  DBModel extends DocumentData = DocumentData,
> = (snapshot: QuerySnapshot<AppModel, DBModel>) => unknown;

/**
 * Create cursor function that extracts cursor from last document in snapshot.
 * @param key field path or key of the property to get the cursor from.
 * @returns cursor value
 */
export function lastKeyCursor<
  AppModel extends DocumentData,
  DBModel extends DocumentData,
>(
  key: (keyof AppModel & string) | FieldPath,
): GetCursorFunction<AppModel, DBModel> {
  return (snapshot) => last(snapshot.docs)?.get(key);
}
