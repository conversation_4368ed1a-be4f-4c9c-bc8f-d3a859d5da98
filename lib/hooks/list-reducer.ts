import { assertNever } from "../assert-never";

type ReducerAction<K, V> =
  | AddAction<K, V>
  | UpdateAction<K, V>
  | DeleteAction<K>
  | ClearAction;

interface ClearAction {
  type: "clear";
}

interface UpdateAction<K, V> {
  type: "update";
  key: K;
  value: V;
}

interface DeleteAction<K> {
  type: "delete";
  key: K;
}

interface AddAction<K, V> {
  type: "add";
  key: K;
  value: V;
}

interface Entry<T> {
  key: string;
  value: T;
}

/**
 * Create empty list of entries.
 * @returns - Empty list of entries.
 */
export function emptyList<T>(): Entry<T>[] {
  return [];
}

/**
 * Reducer for a list of entries.
 * Intended to be used with useImmerReducer hook.
 *
 * @param draft - Current list of entries.
 * @param action - Action to perform.
 * @returns Updated list of entries or void.
 *
 * @example
 * const [list, dispatch] = useImmerReducer(listReducer<T>, emptyList());
 */
export function listReducer<T>(
  draft: Entry<T>[],
  action: ReducerAction<string, T>,
): Entry<T>[] | void {
  const type = action.type;
  switch (type) {
    case "add": {
      if (draft.find((entry) => entry.key === action.key)) {
        throw new Error(`Entry with key ${action.key} already exists`);
      }
      return void draft.push({ key: action.key, value: action.value });
    }
    case "update": {
      const index = draft.findIndex((entry) => entry.key === action.key);
      if (index === -1) {
        return void draft.push({ key: action.key, value: action.value });
      }
      return void (draft[index].value = action.value);
    }
    case "delete": {
      const index = draft.findIndex((entry) => entry.key === action.key);
      return void draft.splice(index, 1);
    }
    case "clear": {
      return [];
    }
    default:
      assertNever(type);
  }
}
