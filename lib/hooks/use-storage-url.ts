import { useState, useEffect } from "react";
import { getMemoFileUrl } from "../get-file-url";

/**
 * Returns the url for a file in storage.
 * @param path Path to the file.
 * @param bucket Bucket to use.
 * @returns The url for the file.
 */
export function useStorageURL(path: string | undefined, bucket?: string) {
  const [url, setUrl] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error>();

  useEffect(() => {
    if (!path) {
      setUrl(undefined);
      return;
    }
    setLoading(true);
    getMemoFileUrl(path, bucket)
      .then(setUrl)
      .catch(setError)
      .finally(() => setLoading(false));
  }, [path, bucket]);

  return [url, loading, error] as const;
}
