import { useCallback } from "react";

type ScrollDirection = "down" | "up";

interface UseInfiniteScrollOptions {
  /** Whether there are more pages to load */
  hasMorePages: boolean;
  /** Whether currently loading */
  loading: boolean;
  /** Callback to load more content */
  onLoadMore: () => void;
  /**
   * Threshold in pixels for triggering load more.
   * For "down": distance from bottom in pixels
   * For "up": distance from top in pixels
   * @default 200
   */
  threshold?: number;
  /**
   * Scroll direction for infinite loading
   * "down" = load when scrolling down (approaching bottom)
   * "up" = load when scrolling up (approaching top)
   * @default "down"
   */
  direction?: ScrollDirection;
}

/**
 * Hook for infinite scroll functionality in either direction.
 * Returns a scroll event handler that triggers loading more content
 * when the user scrolls near the specified edge.
 *
 * @param options Configuration for infinite scroll behavior
 * @returns Scroll event handler to attach to scrollable element
 *
 * @example
 * ```tsx
 * // Standard infinite scroll (load when approaching bottom)
 * const handleScroll = useInfiniteScroll({
 *   hasMorePages,
 *   loading,
 *   onLoadMore: () => goToPage(page + 1),
 *   threshold: 300, // Load when within 300px of bottom
 *   direction: "down"
 * });
 *
 * // Inverted infinite scroll (load when approaching top)
 * const handleScroll = useInfiniteScroll({
 *   hasMorePages,
 *   loading,
 *   onLoadMore: () => loadOlderDocuments(),
 *   threshold: 100, // Load when within 100px of top
 *   direction: "up"
 * });
 *
 * return <Content onScroll={handleScroll}>...</Content>;
 * ```
 */
export function useInfiniteScroll({
  hasMorePages,
  loading,
  onLoadMore,
  threshold = 1000,
  direction = "down",
}: UseInfiniteScrollOptions) {
  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;

      let withinLoadingThreshold = false;

      if (direction === "down") {
        // Load when approaching bottom: check distance from bottom
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
        withinLoadingThreshold = distanceFromBottom <= threshold;
      } else {
        // Load when approaching top: check distance from top
        withinLoadingThreshold = scrollTop <= threshold;
      }

      if (withinLoadingThreshold && hasMorePages && !loading) {
        onLoadMore();
      }
    },
    [hasMorePages, loading, onLoadMore, threshold, direction],
  );

  return handleScroll;
}
