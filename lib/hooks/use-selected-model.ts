import { isEqual } from "lodash";
import { useSearchParams } from "next/navigation";
import { useState, useCallback, useEffect } from "react";
import { FirestoreModel } from "../../models/firestore_model";

/**
 * Select a model from a list.
 * Automatically selects a matching model if `id` search param is set or the first item in the list if no item is selected.
 * @param models The list of models to select from.
 * @param searchParam The search param to use for the id. Defaults to `id`.
 * @returns
 */
export function useSelectedModel<T extends FirestoreModel>(
  models?: T[],
  searchParam?: string,
) {
  const searchParams = useSearchParams();

  // Selected id, used for matching.
  const [selectedId, setSelectedId] = useState<string | undefined>(
    searchParams.get(searchParam ?? "id") ?? undefined,
  );

  // Selected model used by the consumer of this hook.
  const [selectedModel, setSelectedModel] = useState<T>();

  // Function to set both selected id and model.
  const setSelected = useCallback((model: T | undefined) => {
    setSelectedId(model?.reference.id);
    setSelectedModel(model);
  }, []);

  useEffect(() => {
    let model: T | undefined;

    // If we already have a selected model, don't do anything.
    if (selectedModel) {
      return;
    }

    // If we have an id, let's try to find it.
    if (selectedId) {
      model = models?.find((model) => model.reference.id === selectedId);
    }

    // If we still don't have a model, pick the first one.
    if (!model) {
      model = models?.[0];
    }

    if (model && !isEqual(model, selectedModel)) {
      setSelected(model);
    }
  }, [models, selectedId, selectedModel, setSelected]);

  return [selectedModel, setSelected] as const;
}
