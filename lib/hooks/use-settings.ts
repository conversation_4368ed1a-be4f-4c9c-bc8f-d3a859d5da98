import { useDocumentData } from "react-firebase-hooks/firestore";
import { useAuthUser } from "../../components/auth/AuthProvider";
import {
  partnerSettingsRef,
  userSettingsRef,
} from "../../models/settings/repository";
import {
  DocumentReference,
  getDoc,
  serverTimestamp,
  setDoc,
  updateDoc,
} from "firebase/firestore";
import removeUndefined from "../remove-undefined";
import { SettingsInput } from "../../models/settings/settings";
import { DonePartnerId } from "../../models/other/partner";

export function useSettings() {
  const { userDoc, partner, isSuperAdmin } = useAuthUser();

  const userRef = userSettingsRef(userDoc?.reference.id);
  const partnerRef =
    isSuperAdmin && !partner // If super admin and not emulating a partner
      ? partnerSettingsRef(DonePartnerId)
      : partnerSettingsRef(partner);

  const [userSettings] = useDocumentData(userRef);
  const [partnerSettings] = useDocumentData(partnerRef);

  const userSettingsUpdater = buildUpdater(userRef);
  const partnerSettingsUpdater = buildUpdater(partnerRef);

  return [
    [userSettings, partnerSettings] as const,
    [userSettingsUpdater, partnerSettingsUpdater] as const,
  ] as const;
}

function buildUpdater(
  ref: DocumentReference | undefined,
): (input: SettingsInput) => Promise<void> {
  if (!ref) return async (_) => {};
  return async (input) => {
    // Here we create settings document if it doesn't exist. Must be a separate step as set dont support keypaths.
    // Settings aren't usually updated frequently so this two-step process is fine.
    if (!(await getDoc(ref)).exists()) {
      await setDoc(ref, { createTime: serverTimestamp() });
    }
    return updateDoc(ref, removeUndefined(input));
  };
}
