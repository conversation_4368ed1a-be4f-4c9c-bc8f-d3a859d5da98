import {
  Company,
  CompanyLocation,
  CompanyWithDistance,
} from "../models/company/company";
import { Job } from "../models/job/job";
import { JobOffer } from "../models/job/offer/job-offer";
import { DoneLocation } from "../models/user/user";
import { haversineDistance } from "./haversine-distance";

export function companiesSortedByDistanceToWork(
  companies: Company[],
  jobOffer: JobOffer,
  job: Job,
) {
  const suggestedCompanies: CompanyWithDistance[] = companies
    .filter((company) =>
      Boolean(jobOffer.suggestedCompanies?.[company.reference.id]),
    )
    .map((company) => ({
      ...company,
      distanceFromWork: jobOffer.suggestedCompanies?.[company.reference.id]
        ?.distanceInMeters
        ? jobOffer.suggestedCompanies?.[company.reference.id]
            ?.distanceInMeters / 1000
        : findShortestDistance(
            job.cache?.customerLocation,
            company.homeLocations,
          ),
      autoOffer: jobOffer.queuedOffers?.[company.reference.id],
    }))
    .sort(
      (a, b) =>
        (a.distanceFromWork ?? Number.MAX_VALUE) -
        (b.distanceFromWork ?? Number.MAX_VALUE),
    );

  const notSuggestedCompanies: CompanyWithDistance[] = companies
    .filter(
      (company) =>
        !Boolean(jobOffer.suggestedCompanies?.[company.reference.id]),
    )
    .map((company) => ({
      ...company,
      distanceFromWork: jobOffer.suggestedCompanies?.[company.reference.id]
        ?.distanceInMeters
        ? jobOffer.suggestedCompanies?.[company.reference.id]
            ?.distanceInMeters / 1000
        : findShortestDistance(
            job.cache?.customerLocation,
            company.homeLocations,
          ),
      autoOffer: jobOffer.queuedOffers?.[company.reference.id],
    }))
    .sort(
      (a, b) =>
        (a.distanceFromWork ?? Number.MAX_VALUE) -
        (b.distanceFromWork ?? Number.MAX_VALUE),
    );

  return [...suggestedCompanies, ...notSuggestedCompanies];
}

function findShortestDistance(
  jobLocation?: DoneLocation,
  homeLocations?: CompanyLocation[],
): number | undefined {
  if (
    !jobLocation?.coordinates ||
    !homeLocations ||
    homeLocations.length === 0
  ) {
    return undefined;
  }

  const distances = homeLocations.map((homeLocation) => {
    return haversineDistance(
      jobLocation.coordinates!,
      homeLocation.coordinates!,
    );
  });

  const shortestDistance = Math.min(...distances);

  return shortestDistance;
}
