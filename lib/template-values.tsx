import { getProperty } from "dot-prop";
import formatTimeRelative from "./format-time-relative";
import { fetchCompanyAdminFromCompanyReference } from "../models/company/company";
import { Job } from "../models/job/job";
import { User } from "../models/user/user";

export async function getTemplateValues(
  fields: any,
  job?: Job,
): Promise<Map<string, string>> {
  if (job) {
    return new Map(
      await Promise.all(
        fields.map(async (templateField: any) =>
          getTemplatesValuesFromJob(
            templateField,
            job,
            job.company &&
              (await fetchCompanyAdminFromCompanyReference(job.company)),
          ),
        ),
      ),
    );
  }

  return new Map();
}

function getTemplatesValuesFromJob(
  templateField: string,
  job: Job,
  companyAdmin?: User,
): string[] {
  switch (templateField) {
    case "customerName":
      var customerNameParts = job.cache?.customerName?.split(" ");

      return [
        templateField,
        customerNameParts && customerNameParts?.length > 0
          ? customerNameParts[0]
          : "",
      ];

    case "preferredCallDate":
      return [templateField, job.preferredCallTime ?? ""];

    case "companyName":
      return [
        templateField,
        getProperty(job, `cache.${templateField}`, "") ?? "",
      ];

    case "scheduledCallTime":
      return [
        templateField,
        job.scheduledCallTime
          ? (formatTimeRelative(job.scheduledCallTime?.toDate()) ?? "")
          : "",
      ];

    case "services":
      return [
        templateField,
        (
          job.services?.map((service) => service?.swedishLabel ?? "") ?? []
        ).join(", "),
      ];
    case "craftsmanName":
      return [templateField, companyAdmin?.firstName ?? "hantverkare"];
    default:
      return [templateField, getProperty(job, templateField, "") ?? ""];
  }
}
