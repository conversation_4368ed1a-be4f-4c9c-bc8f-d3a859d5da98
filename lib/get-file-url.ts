import { getApp } from "firebase/app";
import {
  getDownloadURL,
  getStorage,
  ref,
  StorageReference,
} from "firebase/storage";
import pMemoize from "p-memoize";

const getStorageRef = (fileRef: string, bucket?: string): StorageReference => {
  const storage = getStorage(getApp(), bucket);
  return ref(storage, fileRef);
};

export const getFileUrl = async (
  fileRef: string,
  bucket?: string,
): Promise<string> => getDownloadURL(getStorageRef(fileRef, bucket));

export const getMemoFileUrl = pMemoize(getFileUrl, {
  cacheKey: ([file, bucket]) => `${file}-${bucket}`,
});
