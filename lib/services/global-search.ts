import { DocumentReference } from "firebase/firestore";
import { maxLengthWithEllipsis } from "../max-length-with-ellipses";
import { doc, getFirestore } from "@firebase/firestore";
import { DonePartnerId } from "../../models/other/partner";
import { meiliSearch } from "./meili-search";

export type SearchIndexes = "jobs" | "companies" | "users";

export interface SearchData {
  id: string;
  title: string;
  href: string;
  type: string;
  payload: any;
  reference: DocumentReference;
}

export async function globalSearch(
  query: string,
  indexes: SearchIndexes[],
  partner?: string,
  includeCommands = false,
): Promise<SearchData[]> {
  let jobsResponse: any = [];
  let companiesResponse: any = [];
  let usersResponse: any = [];

  const db = getFirestore();
  const filterPartner = partner ?? DonePartnerId;

  // Search requests
  if (indexes.includes("jobs"))
    jobsResponse = await meiliSearch()
      .index("jobs")
      .search(query, {
        filter: `merchant = ${filterPartner} OR network = ${filterPartner}`,
      });

  if (indexes.includes("companies"))
    companiesResponse = await meiliSearch()
      .index("companies")
      .search(query, { filter: `memberOf = ${filterPartner}` });

  if (indexes.includes("users"))
    usersResponse = await meiliSearch()
      .index("users")
      .search(query, {
        filter: `customerOf = ${filterPartner} OR memberOf = ${filterPartner}`,
        sort: ["fullName:asc"],
      });

  const commands = includeCommands
    ? SHORT_CUTS.filter((e) => e.keyWord.startsWith(query.toLowerCase()))
    : [];

  // Parse search queries
  const jobs =
    jobsResponse?.hits?.map((hit: any) => {
      return {
        id: hit.id,
        payload: hit,
        type: "jobs",
        href: `/jobs/job?id=${hit.id}`,
        title: maxLengthWithEllipsis(130, hit.description),
        reference: doc(db, "jobs", hit.id),
      };
    }) ?? [];

  const companies =
    companiesResponse?.hits?.map((hit: any) => {
      return {
        id: hit.id,
        payload: hit,
        type: "companies",
        href: `/companies/company?id=${hit.id}`,
        title: hit.name,
        reference: doc(db, "companies", hit.id),
      };
    }) ?? [];

  const users =
    usersResponse?.hits?.map((hit: any) => {
      return {
        id: hit.id,
        payload: hit,
        type: "users",
        href: `/users/user?id=${hit.id}`,
        title: hit.fullName,
        reference: doc(db, "users", hit.id),
      };
    }) ?? [];

  return [...commands, ...companies, ...users, ...jobs];
}

const SHORT_CUTS = [
  {
    href: `/companies`,
    title: "Companies",
    keyWord: "/companies",
    type: "commands",
  },
  {
    href: `/csm`,
    title: "CSM",
    keyWord: "/csm",
    type: "commands",
  },
  {
    href: `/`,
    title: "Home",
    keyWord: "/home",
    type: "commands",
  },
  {
    href: `/invoices`,
    title: "Invoices",
    keyWord: "/invoices",
    type: "commands",
  },
  {
    href: `/jobs`,
    title: "Jobs",
    keyWord: "/jobs",
    type: "commands",
  },
  {
    href: `/reviews`,
    title: "Reviews",
    keyWord: "/reviews",
    type: "commands",
  },
  {
    href: `/settings`,
    title: "Settings",
    keyWord: "/settings",
    type: "commands",
  },
  {
    href: `/quotes`,
    title: "Quotes",
    keyWord: "/quotes",
    type: "commands",
  },
];
