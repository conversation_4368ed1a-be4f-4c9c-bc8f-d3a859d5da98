import { MeiliSearch } from "meilisearch";
import { environment } from "../environment";

// TODO: Store these keys elsewhere
const configs = {
  prod: {
    host: "https://v2.search.internal.doneservices.se",
    apiKey: "7bc14d07309d2f8a7bb59a34e686d6463b9005cceaafea623f6bf399bd2147ac",
  },
  dev: {
    host: "https://dev.search.internal.doneservices.se",
    apiKey:
      "YmaDEOVnc3ea410049dd7990063bc79c5095aa6cc0716fc490e704753880ee170e3c96d3",
  },
};

let _searchEngine: MeiliSearch | undefined;

export function meiliSearch(): MeiliSearch {
  if (!_searchEngine) {
    const config = configs[environment];

    if (!config)
      throw new Error(`No config set for environment: "${environment}"`);

    _searchEngine = new MeiliSearch(config);
  }

  return _searchEngine;
}
