const DONE_ENV = process.env.DONE_ENV ?? "dev";

/**
 * @returns {boolean} true if the environment is dev.
 */
export const isDev = ["dev", "emulator"].includes(DONE_ENV);

/**
 * @returns {boolean} true if the environment is prod.
 */
export const isProd = !isDev;

/**
 * @returns {boolean} true if the environment is emulator.
 * @note If true, isDev will also true.
 */
export const isEmulator = DONE_ENV === "emulator";

/**
 * @returns {string} the current environment. Can be 'dev' or 'prod'.
 */
export const environment = isDev ? "dev" : "prod";
