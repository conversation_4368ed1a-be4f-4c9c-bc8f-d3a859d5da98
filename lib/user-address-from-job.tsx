import { getDoc } from "firebase/firestore";
import { Job } from "../models/job/job";

export async function getUserAddressFromJob(job: Job) {
  if (job.cache?.customerLocation?.subLocality) {
    return job.cache.customerLocation.subLocality;
  }

  if (job.cache?.customerLocation?.postalTown) {
    return job.cache.customerLocation.postalTown;
  }

  if (!job.customer) return "-";

  const user = await getDoc(job.customer);
  const userData = user.data();

  if (!userData) throw "No user find in job while getting user address";

  return userData.address ? userData.address.zip : "-";
}
