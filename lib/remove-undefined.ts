/**
 * Removes properties with `undefined` as value.
 * @param object Object to check.
 * @returns A new object with same type as Object but with any undefined properties removed.
 */
export default function removeUndefined<T extends object>(object: T): T {
  return Object.entries(object).reduce(
    (obj, [key, value]) =>
      value === undefined ? obj : { ...obj, [key]: value },
    {},
  ) as T;
}
