import <PERSON> from "papaparse";
import { formatTime } from "./format-time-relative";

export async function downloadCsvFile(rows: any, filePrefix: string) {
  const csv = Papa.unparse(rows, {
    delimiter: "\t",
    quoteChar: '"',
    quotes: true,
    newline: "\r\n",
  });

  var pom = document.createElement("a");
  var csvContent = csv;
  var blob = new Blob([csvContent], {
    type: "text/csv;charset=utf-8;",
  });
  var url = URL.createObjectURL(blob);
  pom.href = url;
  pom.setAttribute("download", `${filePrefix}_${formatTime(new Date())}.csv`);
  pom.click();
}
