import { useEffect, useCallback } from "react";

/** Hook for listening to key presses globally. */
export function useKey(key: string, handler: (event: KeyboardEvent) => void) {
  const handleKeyPress = useCallback(
    (event: KeyboardEvent) => {
      if (key.toLowerCase() === event.key.toLowerCase()) {
        event.preventDefault();
        handler(event);
      }
    },
    [key, handler],
  );

  useEffect(() => {
    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [handleKeyPress]);
}
