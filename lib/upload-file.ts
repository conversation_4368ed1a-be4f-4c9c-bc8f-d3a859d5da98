import { getApp } from "firebase/app";
import { getStorage, ref, uploadBytes } from "firebase/storage";

export function getFileRef(path: string, bucket?: string) {
  const storageRef = getStorage(getApp(), bucket);
  return ref(storageRef, path);
}

export async function uploadFile(path: string, file: File, bucket?: string) {
  const fileRef = getFileRef(path, bucket);
  return uploadBytes(fileRef, file);
}
