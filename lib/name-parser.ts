export function parseFullName(fullName: string): UserName {
  if (!fullName || fullName.length == 0) {
    return { firstName: "", lastName: "" };
  }

  const nameArray = fullName.split(" ");

  return {
    firstName: (nameArray[0] || "").trim(),
    lastName: (nameArray.length === 1
      ? ""
      : nameArray[nameArray.length - 1] || ""
    ).trim(),
  };
}

interface UserName {
  firstName: string;
  lastName: string;
}
