import { getProperty } from "dot-prop";
import {
  DocumentReference,
  FieldValue,
  Timestamp,
  GeoPoint,
} from "firebase/firestore";

/**
 * Function that flattens an object to a single level using dot notation.
 * @param object Object to flatten.
 * @example
 * const obj = {
 *  a: {
 *   b: {
 *    c: 1,
 *    d: 2,
 *   },
 *  },
 *  e: 3,
 * }
 * flattenInput(obj) // { 'a.b.c': 1, 'a.b.d': 2, e: 3 }
 */
export function flattenInput<T extends Record<string, unknown>>(objec: T) {
  const keys = objectKeyIterator(objec).flat();
  return Object.fromEntries(
    keys.map((key) => [key, getProperty(objec, key, objec[key])]),
  );
}

function objectKeyIterator(object: object, path: string[] = []): string[] {
  // See if we hit the end of the object and return the path.
  if (
    typeof object !== "object" ||
    object === null || // Null is an object but let's not try to iterate over it.
    object instanceof DocumentReference || // Don't iterate over references.
    object instanceof FieldValue || // Don't iterate over field values.
    object instanceof Timestamp || // Don't iterate over timestamps.
    object instanceof GeoPoint // Don't iterate over geopoints.
  ) {
    return [path.join(".")];
  }

  // Iterate recursively over all entries in the object.
  return Object.entries(object).flatMap(([key, value]) =>
    objectKeyIterator(value, [...path, key]),
  );
}
