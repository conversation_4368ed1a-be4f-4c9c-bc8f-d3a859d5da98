import { GeoPoint } from "firebase/firestore";

/**
 * Calculates the haversine distance between point A, and B.
 * @param coords1 point A
 * @param coords2 point B
 */
export function haversineDistance(coords1: GeoPoint, coords2: GeoPoint) {
  function toRad(x: number) {
    return (x * Math.PI) / 180;
  }

  const dLat = toRad(coords2.latitude - coords1.latitude);
  const dLon = toRad(coords2.longitude - coords1.longitude);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(coords1.latitude)) *
      Math.cos(toRad(coords2.latitude)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

  const diameterOfWorld = 12742; // km

  return Math.floor(
    diameterOfWorld * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)),
  );
}
