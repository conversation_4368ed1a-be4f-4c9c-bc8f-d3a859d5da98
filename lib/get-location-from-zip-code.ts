export function getLocationFromZipCode(zipCode: string): string | null {
  try {
    zipCode = zipCode.replace(/\s/g, "");

    if (zipCode.length !== 5) return null;

    const firstDigit = zipCode.substring(0, 1);
    const secondDigit = zipCode.substring(1, 2);

    // Add available cities here.
    if (zipCodeInRange(zipCode, POSTAL_CODES_WITHIN_SODERTAlJE))
      return "Södertälje";
    if (zipCodeInRange(zipCode, POSTAL_CODES_WITHIN_60KM_FROM_UMEA))
      return "Umeå";
    if (firstDigit === "1") return "Stockholm";
    if (firstDigit === "2" && secondDigit === "0") return "Malmö";
    if (firstDigit === "2" && secondDigit === "1") return "Malmö";
    if (firstDigit === "3" && secondDigit === "5") return "Växjö";
    if (firstDigit === "4" && [0, 1, 2, 3].includes(parseInt(secondDigit)))
      return "Göteborg";
    if (firstDigit == "7" && secondDigit == "0") return "Örebro";
    if (firstDigit == "7" && secondDigit == "5") return "Uppsala";
    if (firstDigit == "9" && secondDigit == "0") return "Umeå";

    return null;
  } catch (e) {
    return null;
  }
}

export function zipCodeInRange(zipCode: string, ranges: number[][]): boolean {
  const zipCodeAsNumber = parseInt(zipCode);

  if (isNaN(zipCodeAsNumber)) return false;

  for (const e of ranges) {
    if (e[0] <= zipCodeAsNumber && e[1] >= zipCodeAsNumber) {
      return true;
    }
  }

  return false;
}

const POSTAL_CODES_WITHIN_SODERTAlJE = [
  [15023, 15394],
  [15300, 15395],
  [14556, 14565],
  [14653, 14785],
  [14500, 14590],
];

const POSTAL_CODES_WITHIN_60KM_FROM_UMEA = [
  [91341, 91380],
  [91301, 91335],
  [91801, 91821],
  [91101, 91194],
  [92261, 92266],
  [92201, 92232],
  [92281, 92293],
  [91601, 91693],
  [92276, 92278],
  [91401, 91491],
  [91501, 91596],
];
