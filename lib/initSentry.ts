import {
  BrowserOptions,
  browserTracingIntegration,
  feedbackIntegration,
  init,
} from "@sentry/react";
import { environment, isEmulator } from "./environment";

export function initSentry() {
  if (isEmulator) return;

  const defaultConfig: BrowserOptions = {
    dsn: "https://<EMAIL>/6501269",
    integrations: [
      browserTracingIntegration(),
      feedbackIntegration({
        autoInject: false,
        colorScheme: "system",
        showName: false,
        showEmail: false,
        formTitle: "Send feedback",
        submitButtonLabel: "Send",
        messagePlaceholder: "What feedback would you like to share?",
      }),
    ],
    tracesSampleRate: 1.0,
  };

  const config = {
    prod: {
      ...defaultConfig,
      environment: "production",
    },
    dev: {
      ...defaultConfig,
      environment: "development",
    },
  }[environment];

  if (!config)
    throw new Error(`No config set for environment: "${environment}"`);

  init(config);
}
