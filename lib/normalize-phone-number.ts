export function normalizePhoneNumber(phoneNumber: string): string {
  // Remove non-numeric characters
  const normalized = phoneNumber.replace(/\D/g, "");

  // Check if the number starts with '46' and optionally a '0' after it
  if (normalized.startsWith("460")) {
    return `+46${normalized.substring(3)}`;
  } else if (normalized.startsWith("46")) {
    return `+46${normalized.substring(2)}`;
  }

  // Check if the number starts with '07' or '7' and prepend +46 if so
  if (normalized.startsWith("07") || normalized.startsWith("7")) {
    return `+46${
      normalized.startsWith("07") ? normalized.substring(1) : normalized
    }`;
  }

  // Return the normalized number as is for other cases
  return normalized;
}
