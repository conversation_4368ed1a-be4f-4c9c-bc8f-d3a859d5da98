import { parseISO } from "date-fns";
import { Timestamp } from "firebase/firestore";

export type DateOrTimestamp = Date | Timestamp | null | string;

/**
 * Get a Date from a date-like object.
 */
export function getDate(time?: DateOrTimestamp): Date | null {
  if (!time) return null;

  if (time instanceof Timestamp) {
    return time.toDate();
  } else if (typeof time === "string") {
    return parseISO(time);
  } else {
    return time;
  }
}
