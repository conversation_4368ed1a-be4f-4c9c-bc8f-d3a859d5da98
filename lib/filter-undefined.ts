/**
 * Check if a value is defined or not.
 * @param value The value to check.
 * @returns Returns `false` if `value` is `null` or `undefined`, othwerwise the function returns `true`.
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  if (value === null || value === undefined) return false;
  return true;
}

/**
 * Check if a value is defined or not.
 * @param value The value to check.
 * @param keys The key(s) to check if they are defined.
 * @returns Returns `false` if `value` or any provided keys are `null` or `undefined`, otherwise `true`.
 */
export function isKeyDefined<T, K extends keyof T>(
  value: T | null | undefined,
  ...keys: K[]
): value is T & Required<Pick<T, K>> {
  if (value == null) return false;
  return keys.every((key) => value[key] !== undefined);
}
