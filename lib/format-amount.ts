export function formatAmount(
  value?: number,
  fraction: 0 | 2 = 0,
): string | undefined {
  if (value === undefined) return;
  return new Intl.NumberFormat("se-SE", {
    style: "currency",
    currency: "SEK",
    minimumFractionDigits: fraction,
    maximumFractionDigits: fraction,
  }).format(value);
}

export function formatAmountForExport(value?: number): string {
  if (!value) return "";

  return value.toString().replaceAll(".", ",");
}

export default formatAmount;
