import { GeoPoint } from "firebase/firestore";

export function geoCodeToLocation(geoCode: google.maps.GeocoderResult): {
  postalCode?: string;
  postalTown?: string;
  streetAddress: string;
  addressLine: string;
  coordinates: GeoPoint;
} {
  const postalCode = geoCode.address_components.find((e) =>
    e.types.includes("postal_code"),
  )?.long_name;

  const postalTown = geoCode.address_components.find((e) =>
    e.types.includes("postal_town"),
  )?.long_name;

  const streetNumber = geoCode.address_components.find((e) =>
    e.types.includes("street_number"),
  )?.long_name;

  const route = geoCode.address_components.find((e) =>
    e.types.includes("route"),
  )?.long_name;

  const streetAddress = [route, streetNumber].join(" ");

  const formattedPostalCode = postalCode?.trim().replace(" ", "");

  const addressLine = geoCode!.formatted_address;

  return {
    postalCode: formattedPostalCode,
    postalTown,
    streetAddress,
    addressLine,
    coordinates: new GeoPoint(
      geoCode.geometry.location.lat(),
      geoCode.geometry.location.lng(),
    ),
  };
}
