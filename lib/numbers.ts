/**
 * Checks if a value is a number.
 * @param value The value to check.
 * @returns Returns `true` if `value` is a number, else `false`.
 * @note This will also give hints to the compiler that the value is a number.
 */
export function isNumber(value: unknown): value is number {
  return typeof value === "number" && !isNaN(value);
}

/**
 * Returns the value as a number or undefined if the value is not a number.
 * @param value The value to convert.
 * @returns Returns the value as a number or undefined if the value is not a number.
 * @note This will also give hints to the compiler that the value is a number.
 */
export function asNumber(value: unknown): number | undefined {
  const result = Number(value);
  return isNumber(result) ? result : undefined;
}
