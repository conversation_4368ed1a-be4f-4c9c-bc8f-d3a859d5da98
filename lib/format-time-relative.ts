import {
  format,
  formatDistanceToNow,
  formatRelative,
  intervalToDuration,
} from "date-fns";
import { assertNever } from "./assert-never";
import { enGB } from "date-fns/locale";
import { DateOrTimestamp, getDate } from "./date-or-timestamp";

export default function formatTimeRelative(
  time?: DateOrTimestamp,
): string | null {
  const date = getDate(time);
  if (!date) return null;

  return formatDistanceToNow(date, { addSuffix: true });
}

export function formatTinyDistance(
  time?: DateOrTimestamp | undefined,
  to: DateOrTimestamp = new Date(),
): string | null {
  const end = getDate(to);
  const start = getDate(time);
  if (!start || !end) return null;

  const duration = intervalToDuration({
    start,
    end,
  });

  if (duration.years) {
    return `${duration.years}y`;
  }

  if (duration.months) {
    return `${duration.months}mo`;
  }

  if (duration.days) {
    return `${duration.days}d`;
  }

  if (duration.hours) {
    return `${duration.hours}h`;
  }

  if (duration.minutes) {
    return `${duration.minutes}m`;
  }

  if (duration.seconds) {
    return `${duration.seconds}s`;
  }

  return "now";
}

export function formatTime(time?: DateOrTimestamp): string | null {
  const date = getDate(time);
  if (!date) return null;

  return format(date, "yyyy-MM-dd HH:mm");
}

export function formatDate(time?: DateOrTimestamp): string | null {
  const date = getDate(time);
  if (!date) return null;

  return format(date, "yyyy-MM-dd");
}

export function formatTitle(
  time: DateOrTimestamp | undefined,
  mode: "month" | "week" | "day",
): string | null {
  const date = getDate(time);
  if (!date) return null;

  switch (mode) {
    case "month":
      return format(date, "MMMM yyyy");
    case "week":
      return format(date, "'Week' w MMMM yyyy");
    case "day":
      return format(date, "EEEE, MMMM do yyyy");
    default:
      assertNever(mode);
  }
}

export function formatTitleRelative(time?: DateOrTimestamp): string | null {
  const date = getDate(time);
  if (!date) return null;
  return formatRelative(date, new Date(), { locale: enGB });
}
