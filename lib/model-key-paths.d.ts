import { DocumentReference, Timestamp, GeoPoint } from "firebase/firestore";
import { Paths, EmptyObject } from "type-fest";

/**
 * Simplifies a Firestore document to avoid circular references in internal types and eases traversal.
 */
type SimplifiedData<T> = T extends DocumentReference
  ? EmptyObject
  : T extends Timestamp
    ? EmptyObject
    : T extends GeoPoint
      ? EmptyObject
      : T extends object
        ? T extends Array<infer ItemType>
          ? Array<SimplifiedData<ItemType>>
          : {
              [Property in keyof T]: SimplifiedData<T[Property]>;
            }
        : T;

/**
 * Gets the paths of a model.
 */
export type ModelPaths<T> = Paths<SimplifiedData<T>>;
