html,
body {
  padding: 0;
  margin: 0;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    Oxygen,
    Ubuntu,
    Can<PERSON>ell,
    Fira Sans,
    Droid Sans,
    Helvetica Neue,
    sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

#sentry-feedback {
  --inset: auto auto 0 0;
}

.rc-mentions {
  display: inline-block;
  position: relative;
  white-space: pre-wrap;
  font-size: 12px;
  border-radius: 3px;
  overflow: hidden;
  width: 100%;
  border-radius: 10px;
}

/* ================= Input Area ================= */
.rc-mentions > textarea,
.rc-mentions-measure {
  font-size: 12px;
  font-size-adjust: inherit;
  font-style: inherit;
  font-variant: inherit;
  font-stretch: inherit;
  font-weight: inherit;
  font-family: inherit;
  padding: 0;
  margin: 0;
  line-height: inherit;
  vertical-align: top;
  overflow: inherit;
  word-break: inherit;
  white-space: inherit;
  word-wrap: break-word;
  overflow-x: initial;
  overflow-y: auto;
  text-align: inherit;
  letter-spacing: inherit;
  tab-size: inherit;
  direction: inherit;
}

.rc-mentions textarea {
  padding: 12px;
  border-radius: 10px;
  resize: none;
  color: white;
  width: 100%;
  background: transparent;
  border: none !important;
  background-color: transparent !important;
  outline: none !important;
}

.rc-mentions-measure {
  position: absolute;
  left: 0;
  right: 0;
  top: 25px;
  bottom: 0;
  pointer-events: none;
  color: transparent;
  z-index: -1;
}

footer .rc-mentions-measure {
  top: 0;
}

/* ================== Dropdown ================== */
.rc-mentions-dropdown {
  position: absolute;
  border: 1px solid #999;
  border-radius: 3px;
  background: #fff;
  z-index: 100;
}

.rc-mentions-dropdown-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.rc-mentions-dropdown-menu-item {
  cursor: pointer;
  padding: 4px 8px;
}

.rc-mentions-dropdown-menu-item-active {
  background: #e6f7ff;
}

.rc-mentions-dropdown-menu-item-disabled {
  opacity: 0.5;
}
