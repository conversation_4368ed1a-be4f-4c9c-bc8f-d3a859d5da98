name: CD

on:
  push:
    branches: [main]
  release:
    types: [published]

jobs:
  update-release-draft:
    name: Update Release Draft
    runs-on: ubuntu-latest
    steps:
      - uses: release-drafter/release-drafter@v6
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  ci:
    name: <PERSON><PERSON>
    uses: ./.github/workflows/ci.yml

  deploy-dev:
    name: Deploy to Dev
    if: ${{ github.event_name == 'push' }}
    needs: [ci]
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "yarn"

      - name: Next cache
        uses: actions/cache@v4
        with:
          path: ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Set version
        run: npm version 0.0.0-dev.${{ github.sha }} --git-tag-version=false

      - name: Export
        run: yarn export:dev

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: "${{ secrets.GITHUB_TOKEN }}"
          firebaseServiceAccount: "${{ secrets.FIREBASE_SERVICE_ACCOUNT_DONE_DEV_F0434 }}"
          projectId: done-dev-f0434
          channelId: live
          target: dev

  deploy-prod:
    name: Deploy to Production
    if: ${{ github.event_name == 'release' }}
    needs: [ci]
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Get the version
        id: get_version
        run: echo ::set-output name=VERSION::${GITHUB_REF#refs/tags/v}

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "yarn"

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Set version
        run: npm version ${{ steps.get_version.outputs.VERSION }} --git-tag-version=false

      - name: Export
        run: yarn export:prod

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: "${{ secrets.GITHUB_TOKEN }}"
          firebaseServiceAccount: "${{ secrets.FIREBASE_SERVICE_ACCOUNT_DONE_50549 }}"
          projectId: done-50549
          channelId: live
          target: prod

      - name: Create Sentry release
        uses: getsentry/action-release@v3
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
        with:
          environment: production
          sourcemaps: "./out"
