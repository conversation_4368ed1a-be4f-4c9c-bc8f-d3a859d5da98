name: CI

on:
  pull_request:
  workflow_call:

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "yarn"

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Lint
        run: yarn lint

  tests:
    name: Tests
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "yarn"

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Run tests
        run: yarn test

  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "yarn"

      - name: Next cache
        uses: actions/cache@v4
        with:
          path: ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Build
        run: yarn build

  preview:
    name: Preview
    if: github.event_name == 'pull_request' && github.actor != 'dependabot[bot]'
    needs: [build]
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "yarn"

      - name: Next cache
        uses: actions/cache@v4
        with:
          path: ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Set version
        run: npm version 0.0.0-pr.${{ github.event.pull_request.number }} --git-tag-version=false

      - name: Export
        run: yarn export:dev

      - uses: FirebaseExtended/action-hosting-deploy@v0
        id: preview
        with:
          repoToken: "${{ secrets.GITHUB_TOKEN }}"
          firebaseServiceAccount: "${{ secrets.FIREBASE_SERVICE_ACCOUNT_DONE_DEV_F0434 }}"
          projectId: done-dev-f0434
          target: dev
