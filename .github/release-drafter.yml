name-template: "$RESOLVED_VERSION"
tag-template: "v$RESOLVED_VERSION"
exclude-labels:
  - "🛠️ tooling"
version-resolver:
  major:
    labels:
      - "major"
      - "💥 breaking"
  minor:
    labels:
      - "minor"
      - "🎉 feature"
  patch:
    labels:
      - "patch"
      - "dependencies"
      - "🐛 fix"
  default: patch
categories:
  - title: "💥 Breaking changes"
    labels:
      - "major"
      - "💥 breaking"
  - title: "🛡 Security"
    labels:
      - "security"
  - title: "🚀 Features"
    labels:
      - "minor"
      - "🎉 feature"
  - title: "🐛 Fixes"
    labels:
      - "patch"
      - "🐛 fix"
  - title: "📦 Dependencies"
    labels:
      - "dependencies"
  - title: "🛠️ Tooling"
    labels:
      - "🛠️ tooling"
template: |
  $CHANGES
