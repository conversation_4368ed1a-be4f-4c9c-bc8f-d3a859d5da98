import { PaletteMode, createTheme } from "@mui/material";
import {
  doneColorLight,
  doneColor,
  backgroundDefaultDark,
  backgroundDefaultLight,
} from "./colors";
import { blue, grey, orange, yellow, amber } from "@mui/material/colors";

declare module "@mui/material/styles" {
  interface Palette {
    externalMessage?: Palette["primary"];
    ownMessage?: Palette["primary"];
    internalMessage?: Palette["primary"];
    systemMessage?: Palette["primary"];
  }

  interface PaletteOptions {
    externalMessage?: Palette["primary"];
    ownMessage?: Palette["primary"];
    internalMessage?: Palette["primary"];
    systemMessage?: Palette["primary"];
  }
}

export function createDoneTheme(mode: PaletteMode) {
  let theme = createTheme({
    palette: {
      mode,
      primary: {
        main: mode === "dark" ? doneColorLight : doneColor,
        light: doneColor,
        dark: doneColorLight,
      },
      background: {
        default:
          mode === "dark" ? backgroundDefaultDark : backgroundDefaultLight,
        paper: mode === "dark" ? "#000000" : "#ffffff",
      },
    },
    components: {
      MuiAlert: {
        defaultProps: {
          variant: mode === "dark" ? "outlined" : "standard",
        },
      },
      MuiTextField: {
        defaultProps: {
          size: "small",
        },
      },
    },
    typography: {
      fontFamily: [
        "-apple-system",
        "BlinkMacSystemFont",
        '"Segoe UI"',
        "Roboto",
        '"Helvetica Neue"',
        "Arial",
        "sans-serif",
        '"Apple Color Emoji"',
        '"Segoe UI Emoji"',
        '"Segoe UI Symbol"',
      ].join(","),
      button: {
        textTransform: "none",
      },
    },
  });

  return createTheme(theme, {
    palette: {
      star: theme.palette.augmentColor({
        color: {
          main: mode === "dark" ? amber[600] : yellow[800],
        },
        name: "star",
      }),
      externalMessage: theme.palette.augmentColor({
        color: {
          main: mode === "dark" ? grey[800] : grey[200],
        },
        name: "externalMessage",
      }),
      ownMessage: theme.palette.augmentColor({
        color: {
          main: mode === "dark" ? "#051A3B" : blue[100],
        },
        name: "ownMessage",
      }),
      internalMessage: theme.palette.augmentColor({
        color: {
          main: mode === "dark" ? "#504533" : orange[50],
        },
        name: "internalMessage",
      }),
      systemMessage: {
        divider: mode === "dark" ? "#CCC1EB" : "#B4A3E1",
        name: "systemMessage",
      },
    },
  });
}
