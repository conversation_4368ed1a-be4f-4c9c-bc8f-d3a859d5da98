import { doc, getFirestore } from "firebase/firestore";
import { JobsTableType } from "../components/job/table/JobsTable";
import { CompanyStatus, CompanyUserRole } from "../models/company/company";
import { DeductionType, JobStatus } from "../models/job/job";
import { RawQuoteLineItemType, LineItemUnitType } from "../models/quote/quote";
import { DetailedDeductionType } from "../models/invoice/invoice-line-item";
import { environment } from "../lib/environment";
import { PartnerSegment } from "../models/other/partner";
import { OrderTypes, OrderProductCategories } from "../models/job/order";

export interface LabeledValue<T> {
  value: T;
  label: string;
  swedishLabel?: string;
}

export const JOBS_TABLE_TYPES: LabeledValue<JobsTableType | undefined>[] = [
  { label: "All", value: undefined },
  { label: "Marketplace", value: "marketPlace" },
  { label: "Partner", value: "partner" },
  { label: "Fixed price", value: "fixedPrice" },
  { label: "Subcontractor", value: "subcontractor" },
];

export const JOB_STATUSES: LabeledValue<JobStatus>[] = [
  { label: "Pending", value: "pending" },
  { label: "Inbox", value: "inbox" },
  { label: "Open", value: "open" },
  { label: "Closed", value: "closed" },
  { label: "Trashed", value: "trashed" },
];

export const PARTNER_JOB_STATUS: LabeledValue<JobStatus | undefined>[] = [
  { label: "All", value: undefined },
  { label: "Not matched", value: "inbox" },
  { label: "Matched", value: "open" },
  { label: "Closed", value: "closed" },
];

export const COMPANY_STATUSES: LabeledValue<CompanyStatus>[] = [
  { label: "Active", value: "active" },
  { label: "New", value: "new" },
  { label: "Inactive", value: "inactive" },
  { label: "Offboarded", value: "offboarded" },
];

export const COMPANY_INSURED_BY = [
  { label: "Dina", value: "dina" },
  { label: "Gjensidige", value: "gjensidige" },
  { label: "if", value: "if" },
  { label: "Länsförsäkringar", value: "länsförsäkringar" },
  { label: "Moderna", value: "moderna" },
  { label: "Swedbank", value: "swedbank" },
  { label: "Folksam", value: "folksam" },
  { label: "Trygg-hansa", value: "trygg-hansa" },
  { label: "Berkley Nordic", value: "berkley-nordic" },
  { label: "Svedea", value: "svedea" },
  { label: "Gallagher", value: "gallagher" },
];

export const INVOICE_STATUSES = [
  { filter: "last30days", label: "Last 30 days" },
  { filter: "sent", label: "Sent" },
  { filter: "paid", label: "Paid" },
  { filter: "credited", label: "Credited" },
  { filter: "creditLoss", label: "Credit loss" },
  { filter: "refuted", label: "Refuted" },
  { filter: "", label: "All" },
];

export const COMPANY_SOCIAL_URLS = [
  { field: "facebookUrl", label: "Facebook url" },
  { field: "instagramUrl", label: "Instagram url" },
  { field: "websiteUrl", label: "Website url" },
];

export const INSTITUTION_REQUEST_STATUSES = [
  { days: 1, label: "Today" },
  { days: 7, label: "Last 7 days" },
  { days: 30, label: "Last 30 days" },
  { days: -1, label: "All" },
];

export const CERTIFICATES = [
  { dataKey: "sakerVatten", label: "Certifierat av Säker Vatten" },
  { dataKey: "bkrVatrum", label: "Våtrumsbehörighet (Byggkeramikrådet)" },
  { dataKey: "elA", label: "EL A" },
  { dataKey: "elAL", label: "EL AL" },
  { dataKey: "elB", label: "EL B" },
];

export const COMPANY_SERVICES: LabeledValue<ServiceTypes>[] = [
  { value: "vvs", label: "Plumbing", swedishLabel: "VVS" },
  { value: "painting", label: "Painting", swedishLabel: "Målning" },
  { value: "carpenting", label: "Carpenting", swedishLabel: "Snickare" },
  { value: "electrician", label: "Electricity", swedishLabel: "Elektriker" },
  {
    value: "coolantTechnician",
    label: "Coolant Technician",
    swedishLabel: "Kyltekniker",
  },
  { value: "flooring", label: "Flooring", swedishLabel: "Golv" }, // deprecated
  {
    value: "kitchenAndBathroom",
    label: "Kök & badrum",
    swedishLabel: "Kök & Badrum",
  },
  {
    value: "gardening",
    label: "Trees & Gardening",
    swedishLabel: "Träd & trädgård ",
  },
  {
    value: "smallHomeProjects",
    label: "Small home projects",
    swedishLabel: "Småfix",
  },
  { value: "other", label: "Other", swedishLabel: "Övrigt" },
];

export const titleForService = (type: ServiceTypes) =>
  COMPANY_SERVICES.find((service) => service.value === type)?.label;

export type ServiceTypes =
  | "vvs"
  | "painting"
  | "carpenting"
  | "electrician"
  | "coolantTechnician"
  | "flooring"
  | "kitchenAndBathroom"
  | "gardening"
  | "smallHomeProjects"
  | "other";

export const QUOTE_STATUSES = [
  { value: "pending", label: "Pending" },
  { value: "accepted", label: "Accepted" },
  { value: "declined", label: "Declined" },
  { value: "cancelled", label: "Cancelled" },
];

export const COMPANY_ROLES: LabeledValue<CompanyUserRole>[] = [
  { value: "admin", label: "Admin" },
  { value: "user", label: "User" },
  { value: "owner", label: "Owner" },
];

// TODO: Remove this once job tags merged with all other tags
export const JOB_TAGS: LabeledValue<string>[] = [
  ...OrderTypes,
  ...OrderProductCategories,
];

export const PARTNER_TYPE_OPTION: LabeledValue<PartnerSegment>[] = [
  { value: "marketplace", label: "Marketplace" },
  { value: "ev-chargers", label: "Ev" },
  { value: "heat-pumps", label: "Heat Pumps" },
];

export const DEDUCTION_TYPES: LabeledValue<DeductionType>[] = [
  { value: "rot", label: "ROT" },
  { value: "rut", label: "RUT" },
  { value: "greenTechnology", label: "Green Technology" },
];

export const DETAILED_DEDUCTION_TYPES: LabeledValue<DetailedDeductionType>[] = [
  { value: "rotConstruction", label: "Construction" },
  { value: "rotElectricity", label: "Electricity" },
  { value: "rotGlassMetalWork", label: "Glass & Metal Work" },
  { value: "rotGroundDrainageWork", label: "Ground & Drainage Work" },
  { value: "rotMasonry", label: "Masonry" },
  { value: "rotPaintingWallpapering", label: "Painting & Wallpapering" },
  { value: "rotHvac", label: "HVAC" },
  { value: "rutFurnishing", label: "Furnishing" },
  { value: "rutMoving", label: "Moving" },
  { value: "rutCleaning", label: "Cleaning" },
  { value: "rutGardening", label: "Gardening" },
  { value: "rutWhiteGoods", label: "White Goods" },
  { value: "rutITServices", label: "IT Services" },
  { value: "greenTechnologySolarPower", label: "Solar Power" },
  { value: "greenTechnologyEnergyStorage", label: "Energy Storage" },
  { value: "greenTechnologyCarCharger", label: "Car Charger" },
];

export const LINE_ITEM_TYPES: LabeledValue<RawQuoteLineItemType>[] = [
  { value: "labor", label: "Labor" },
  { value: "material", label: "Material" },
  { value: "travel", label: "Travel" },
  { value: "other", label: "Other" },
];

export const LINE_ITEM_UNIT_TYPE: LabeledValue<LineItemUnitType>[] = [
  { value: "days", label: "Days" },
  { value: "hours", label: "Hours" },
  { value: "meters", label: "Meters" },
  { value: "pcs", label: "Pieces" },
  { value: "sqm", label: "Square Meters" },
  { value: "kilometers", label: "Kilometers" },
  { value: "miles", label: "Miles" },
];

export const ACCEPTED_IMAGE_FILE_TYPES = ["jpg", "jpeg", "png"];

export const DONE_BACKOFFICE_USER_REFERENCE = () =>
  doc(getFirestore(), "users", "gthflMdLjCZlIuRkp6ayoKEUVcg1");

// PROD Only
export const TEST_USER_IDS = [
  "yHaClXlQZ2EKLgwTedWx",
  "ItdrbjLNrsiR1pSZcEfa",
  "jYGmhHw208QH20NYgq5X6QaRrsw2",
  "6m67wKqq3ZUxxWN5Msb8",
  "MWgk68sU4XnewPtF8OsX",
  "Jf1fxC1JeAfm2Cy8zpG8",
  "XeBjtaNDW5GBHsWxOTCo",
  "g451FpD4gWdRkojxwRqM",
  "Lfs7TD5aTQjpmnXJsfM0",
  "eQsNh3EBT1gdcPILNTMgwZAXrZi2",
  "41JzLSeSyAgprqNI2THhbOmiicn2",
  "IzwiiFcFuxGqhEfA43DJ",
  "fRwEOyEin5ahNOJQNFPA",
  "dvv7qNfvlQcGFyfaPPK9",
  "UcUuqWSyMEXcbCPO1HkC",
  "1C4ZGnnJIVfZlXzFHg52",
  "lt8lmVHcb87TCIZLRHSq",
  "gthflMdLjCZlIuRkp6ayoKEUVcg1",
  "FSFv4iD7gSTBS2hMLd8WeqJDtkS2",
  "bTwHe8uXUHDw51fQg6rM",
  "jYGmhHw208QH20NYgq5X6QaRrsw2",
  "EBhtuDYwgLkpRWb4znl7",
  "mulggME5QVN2CRO0UPT7vR4GNv73",
  "mskgbjbhgYeA5QVncHRzYIrmS9x1",
  "ZeHiNFuqgOdwQ58nZ5Dl",
  "JMfCcyubjIqKgYHL8PsW",
  "qCnHlhMTmIuTXhPt7DeW",
  "fiV9SflOcSMSGtWuDU2xi7rViYx2", // k-rauta test account
];

export const FIREBASE_URL = {
  prod: "https://console.firebase.google.com/u/0/project/done-50549",
  dev: "https://console.firebase.google.com/u/0/project/done-dev-f0434",
}[environment];

export const TEMP_BUCKET = {
  prod: "gs://done-temp",
  dev: "gs://done-dev-temp",
}[environment];

export const GOOGLE_MAP_API_KEY = "AIzaSyB1PNq7nmA0J8cupYmstael3q5CSGHBkGM";

export const AIRTABLE_TOKEN =
  "**********************************************************************************";

export const SEARCH_OPTIONS = ["all", "jobs", "companies", "users"];

export const VAPID_KEY = {
  dev: "BEkDdMhaGzQej1wrHNwiZHzTduFvwWuG_jIhKF-dqAurBA8W8DLRiy2ff4hewiUQh6uUql-ArP5RJskLlC0l1OA",
  prod: "BKxjN_qpo8qNkfOt4iMaZummz7yjguDnyRXeDg1YZDiXPVKMPWKgr4DhBHtCQ8r-gZ4wLZTzdBQNuuOQ42ktcqg",
}[environment];

export const COMPANY_ROLE_ORDERS: CompanyUserRole[] = [
  "owner",
  "admin",
  "user",
];

export const BILLECTA_URL = {
  prod: "https://app.billecta.com",
  dev: "https://apptest.billecta.com",
}[environment];

export const INTERCOM_APP_ID = {
  prod: "gn8o0bfp",
  dev: "lfwvpegs",
}[environment];

export const CENTRAL_SWEDEN_COORDINATES = {
  lat: 60.032707,
  lng: 15.381737,
};
