const withImages = require("next-images");
const { version } = require("./package.json");

module.exports = withImages({
  output: "export",
  productionBrowserSourceMaps: true,
  webpack(config) {
    config.plugins.push({
      apply(compiler) {
        compiler.hooks.afterEmit.tap("CreateVersionManifest", () => {
          const fs = require("fs");
          fs.mkdirSync("./public/_meta", { recursive: true });
          fs.writeFileSync(
            "./public/_meta/version.json",
            JSON.stringify({ version }),
          );
        });
      },
    });

    return config;
  },
  env: {
    DONE_ENV: process.env.DONE_ENV,
  },
  trailingSlash: true,
  publicRuntimeConfig: {
    version,
  },
  images: {
    domains: ["firebasestorage.googleapis.com"],
    unoptimized: true,
  },
});
