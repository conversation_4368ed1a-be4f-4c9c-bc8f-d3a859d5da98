{"name": "done-backoffice", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "start": "next start", "lint": "next lint && prettier --check .", "format": "prettier --write .", "export": "next export", "export:emulator": "DONE_ENV=emulator next build && node ./post-build.js", "export:dev": "DONE_ENV=dev next build && node ./post-build.js", "export:prod": "DONE_ENV=prod next build && node ./post-build.js", "serve:emulator": "DONE_ENV=emulator next dev", "serve:dev": "DONE_ENV=dev next dev", "serve:prod": "DONE_ENV=prod next dev", "static-serve": "serve out", "deploy:dev": "ts-node ./scripts/check.ts --target dev && yarn export:dev && firebase deploy --only hosting:dev --project done-dev-f0434", "deploy:prod": "ts-node ./scripts/check.ts --target prod && yarn export:prod && firebase deploy --only hosting:prod --project done-50549", "test": "jest --passWithNoTests"}, "dependencies": {"@date-io/date-fns": "^3.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/js-api-loader": "^1.16.8", "@googlemaps/markerclusterer": "^2.5.3", "@googlemaps/react-wrapper": "^1.2.0", "@mui/icons-material": "^6.4.8", "@mui/lab": "6.0.0-beta.31", "@mui/material": "^6.4.8", "@mui/styles": "^6.4.8", "@mui/system": "^6.4.8", "@mui/x-data-grid": "^7.28.1", "@mui/x-date-pickers": "^7.28.0", "@rjsf/core": "^5.24.11", "@rjsf/mui": "^5.24.11", "@rjsf/utils": "^5.24.11", "@rjsf/validator-ajv6": "^5.24.11", "@sentry/browser": "^9.27.0", "@sentry/react": "^9.27.0", "@sentry/tracing": "^7.120.3", "@toast-ui/react-calendar": "^2.1.3", "@vis.gl/react-google-maps": "^1.5.2", "airtable": "^0.12.2", "ajv": "^8.17.1", "autosuggest-highlight": "^3.3.4", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "date-fns": "^4.1.0", "dot-prop": "^9.0.0", "emojis-list": "^3.0.0", "file-saver": "^2.0.5", "final-form": "^4.20.10", "final-form-arrays": "^3.1.0", "firebase": "^11.9.0", "flat": "^6.0.1", "handlebars": "^4.7.8", "immer": "^10.1.1", "libphonenumber-js": "^1.11.19", "lodash": "^4.17.21", "markdown-to-jsx": "^7.7.3", "marked": "^15.0.12", "material-ui-popup-state": "^5.3.3", "mdi-material-ui": "^7.9.3", "meilisearch": "^0.51.0", "mui-markdown": "^1.2.5", "mui-rff": "^8.0.1", "next": "^15.2.4", "next-images": "^1.8.5", "notistack": "^3.0.2", "organisationsnummer": "^1.2.0", "p-memoize": "^7.1.1", "papaparse": "^5.5.3", "rc-mentions": "^2.19.1", "react": "^19.1.0", "react-code-input": "^3.10.1", "react-dom": "^19.1.0", "react-file-drop": "^3.1.6", "react-final-form": "^6.5.9", "react-final-form-arrays": "^3.1.4", "react-firebase-hooks": "^5.1.1", "react-google-places-autocomplete": "^4.1.0", "react-markdown": "^10.1.0", "react-mentions": "^4.4.10", "react-phone-number-input": "^3.4.11", "react-resizable-panels": "^2.1.7", "react-responsive": "^10.0.0", "react-select": "^5.10.0", "react-use-intercom": "^5.4.3", "uniqolor": "^1.1.1", "use-debounce": "^10.0.4", "use-immer": "^0.11.0", "uuid": "^11.0.5"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/autosuggest-highlight": "^3.2.3", "@types/d3-scale": "^4.0.9", "@types/d3-scale-chromatic": "^3.1.0", "@types/file-saver": "^2.0.7", "@types/flat": "^5.0.5", "@types/google.maps": "^3.58.1", "@types/lodash.add": "^3.7.9", "@types/papaparse": "^5.3.16", "@types/react": "19.1.6", "@types/react-mentions": "^4.4.1", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.0.1", "firebase-tools": "^14.6.0", "inquirer": "^12.5.2", "jest": "^29.7.0", "prettier": "^3.5.1", "serve": "^14.2.4", "ts-jest": "^29.3.0", "ts-node": "^10.9.2", "type-fest": "^4.38.0", "typescript": "^5"}, "packageManager": "yarn@3.8.4"}