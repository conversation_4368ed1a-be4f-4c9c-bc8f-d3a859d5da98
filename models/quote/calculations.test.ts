import { describe, expect, it } from "@jest/globals";
import { calculatedValuesForLines, toPayAmount } from "./calculations";

/**
 * All tests below must use **real** examples from Billecta to validate results if possible.
 */
describe("Calculated values for lines", () => {
  it("works with no lines", () => {
    expect(calculatedValuesForLines([])).toMatchInlineSnapshot(`
      {
        "centsRounding": 0,
        "fullTaxDeduction": 0,
        "greenTechDeduction": 0,
        "laborCost": 0,
        "materialCost": 0,
        "otherCost": 0,
        "rotDeduction": 0,
        "rutDeduction": 0,
        "taxDeduction": 0,
        "totalGross": 0,
        "totalNet": 0,
        "totalNetAfterDeductions": 0,
        "travelCost": 0,
        "vat": 0,
      }
    `);
  });

  it("works with ROT lines containing rounding", () => {
    expect(
      calculatedValuesForLines([
        {
          deductionType: "rot",
          description: "Work",
          type: "labor",
          unitCount: 2,
          unitPrice: 789,
          unitType: "hours",
        },
        {
          deductionType: null,
          description: "Some material",
          type: "material",
          unitCount: 1,
          unitPrice: 785,
          unitType: "pcs",
        },
        {
          deductionType: null,
          description: "Startavgift",
          type: "travel",
          unitCount: 1,
          unitPrice: 236,
          unitType: "pcs",
        },
      ]),
    ).toMatchInlineSnapshot(`
      {
        "centsRounding": 25,
        "fullTaxDeduction": 98600,
        "greenTechDeduction": 0,
        "laborCost": 157800,
        "materialCost": 78500,
        "otherCost": 0,
        "rotDeduction": 98600,
        "rutDeduction": 0,
        "taxDeduction": 98600,
        "totalGross": 259900,
        "totalNet": 324900,
        "totalNetAfterDeductions": 226300,
        "travelCost": 23600,
        "vat": 64975,
      }
    `);
  });

  it("works with RUT lines containing rounding", () => {
    expect(
      calculatedValuesForLines([
        {
          deductionType: "rut",
          description: "Work",
          type: "labor",
          unitCount: 2,
          unitPrice: 789,
          unitType: "hours",
        },
        {
          deductionType: null,
          description: "Some material",
          type: "material",
          unitCount: 1,
          unitPrice: 785,
          unitType: "pcs",
        },
        {
          deductionType: null,
          description: "Startavgift",
          type: "travel",
          unitCount: 1,
          unitPrice: 236,
          unitType: "pcs",
        },
      ]),
    ).toMatchInlineSnapshot(`
      {
        "centsRounding": 25,
        "fullTaxDeduction": 98600,
        "greenTechDeduction": 0,
        "laborCost": 157800,
        "materialCost": 78500,
        "otherCost": 0,
        "rotDeduction": 0,
        "rutDeduction": 98600,
        "taxDeduction": 0,
        "totalGross": 259900,
        "totalNet": 324900,
        "totalNetAfterDeductions": 226300,
        "travelCost": 23600,
        "vat": 64975,
      }
    `);
  });

  it("works with green tech lines containing both labor and material", () => {
    expect(
      calculatedValuesForLines([
        {
          deductionType: "greenTechnology",
          description: "Work",
          type: "labor",
          unitPrice: 200,
          unitCount: 1,
          unitType: "hours",
        },
        {
          deductionType: "greenTechnology",
          description: "Material",
          type: "material",
          unitPrice: 300,
          unitCount: 1,
          unitType: "pcs",
        },
      ]),
    ).toMatchInlineSnapshot(`
      {
        "centsRounding": 0,
        "fullTaxDeduction": 31200,
        "greenTechDeduction": 31200,
        "laborCost": 20000,
        "materialCost": 30000,
        "otherCost": 0,
        "rotDeduction": 0,
        "rutDeduction": 0,
        "taxDeduction": 0,
        "totalGross": 50000,
        "totalNet": 62500,
        "totalNetAfterDeductions": 31300,
        "travelCost": 0,
        "vat": 12500,
      }
    `);
  });

  it("adds 25% vat by default", () => {
    expect(toPayAmount(100)).toBe(125);
  });

  it("supports changing vat", () => {
    expect(toPayAmount(100, { vat: 0.5 })).toBe(150);
  });

  it("can calculate to pay amount for a ROT deduction", () => {
    expect(toPayAmount(100, { deduction: "rot" })).toBe(63);
  });

  it("can calculate to pay amount for a RUT deduction", () => {
    expect(toPayAmount(100, { deduction: "rut" })).toBe(63);
  });

  it("can calculate to pay amount for a green tech deduction", () => {
    expect(toPayAmount(100, { deduction: "greenTechnology" })).toBe(63);
  });

  it("can calculate to pay amount for a green tech standard deduction", () => {
    expect(toPayAmount(100, { deduction: "greenTechnologyStandard" })).toBe(65);
  });
});
