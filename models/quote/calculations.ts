import {
  ExtendedDeductionType,
  deductionRate,
  isDeductionApplicable,
} from "../finance/deductions";
import { defaultVat } from "../finance/vat";
import { CalculatedValues } from "../invoice/calculated-values";
import { RawQuoteLineItem, RawQuoteLineItemType } from "./raw-quote-line-item";
import { toCents } from "../finance/cent-value";
import { DeductionType } from "./raw-quote";

interface CalculationOptions {
  /**
   * Whether to include the VAT in the calculation.
   * Default is true.
   */
  includeVat?: boolean;
  /**
   * Whether to include the deduction in the calculation.
   * Default is true.
   */
  includeDeduction?: boolean;
}

export function calculatedValuesForLines(
  lines: RawQuoteLineItem[],
  options: CalculationOptions = {},
): CalculatedValues {
  const { includeVat = true, includeDeduction = true } = options;

  const totalGross = reduceLines(lines, sumForItem);
  const vat = includeVat ? reduceLines(lines, vatForItem) : 0;

  const laborCost = reduceLines(lines, sumForItem, typeFilter("labor"));
  const materialCost = reduceLines(lines, sumForItem, typeFilter("material"));
  const travelCost = reduceLines(lines, sumForItem, typeFilter("travel"));
  const otherCost = reduceLines(lines, sumForItem, typeFilter("other"));

  const deductor = deductionForItem({ includeVat, includeDeduction });

  const rotDeduction = Math.floor(
    reduceLines(lines, deductor, deductionFilter("rot")),
  );
  const rutDeduction = Math.floor(
    reduceLines(lines, deductor, deductionFilter("rut")),
  );
  const greenTechDeduction = Math.floor(
    reduceLines(lines, deductor, deductionFilter("greenTechnology")),
  );

  const fullTaxDeduction = rotDeduction + rutDeduction + greenTechDeduction;

  const initialNet = totalGross + vat;
  const totalNet = Math.round(initialNet);
  const centsRounding = totalNet - initialNet;

  return {
    vat: toCents(vat),
    totalNet: toCents(totalNet),
    laborCost: toCents(laborCost),
    otherCost: toCents(otherCost),
    totalGross: toCents(totalGross),
    travelCost: toCents(travelCost),
    materialCost: toCents(materialCost),
    totalNetAfterDeductions: toCents(
      totalGross + vat + centsRounding - fullTaxDeduction,
    ),
    rotDeduction: toCents(rotDeduction),
    rutDeduction: toCents(rutDeduction),
    greenTechDeduction: toCents(greenTechDeduction),
    taxDeduction: toCents(rotDeduction), // Old deprecated ROT field.
    fullTaxDeduction: toCents(fullTaxDeduction),
    centsRounding: toCents(centsRounding),
  };
}

function deductionFilter(
  deduction: DeductionType,
): (item: RawQuoteLineItem) => boolean {
  return ({ type, deductionType }) =>
    deductionType === deduction && isDeductionApplicable(deductionType, type);
}

function typeFilter(
  lineType: RawQuoteLineItemType,
): (item: RawQuoteLineItem) => boolean {
  return ({ type }) => type === lineType;
}

function reduceLines(
  lines: RawQuoteLineItem[],
  mapper: (item: RawQuoteLineItem) => number,
  filter?: (item: RawQuoteLineItem) => boolean,
): number {
  return (filter ? lines.filter(filter) : lines).reduce(sumReducer(mapper), 0);
}

function sumReducer(mapper: (item: RawQuoteLineItem) => number) {
  return (total: number, item: RawQuoteLineItem) => total + mapper(item);
}

function sumForItem(item: RawQuoteLineItem) {
  const { unitPrice = 0, unitCount = 1 } = item;
  return unitPrice * unitCount;
}

function vatForItem(item: RawQuoteLineItem) {
  const sum = sumForItem(item);
  return sum * defaultVat;
}

function deductionForItem(
  options: CalculationOptions,
): (item: RawQuoteLineItem) => number {
  return (item) => {
    if (!options.includeDeduction) {
      return 0;
    }

    const vatRate = 1 + (options.includeVat ? defaultVat : 0);
    const sum = sumForItem(item);
    return Math.floor(
      sum *
        vatRate *
        (item.deductionType ? 1 - deductionRate[item.deductionType] : 0),
    );
  };
}

/**
 * Returns the amount that the customer has to pay for the given amount.
 * @param amount Amount without VAT and before deduction.
 * @param options.vat VAT rate to use. Default is 25%.
 * @param options.deduction Deduction type to use. Default is no deduction.
 * @returns
 */
export function toPayAmount(
  amount: number,
  options: { vat?: number; deduction?: ExtendedDeductionType | null } = {},
) {
  const { vat = defaultVat, deduction } = options;
  const vatRate = 1 + vat;

  return Math.ceil(
    amount * vatRate * (deduction ? deductionRate[deduction] : 1),
  );
}
