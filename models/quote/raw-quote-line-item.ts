import { DocumentReference } from "firebase/firestore";
import { DeductionType } from "./raw-quote";
import { Article, EmbeddableArticle } from "../article-groups/article";

export interface RawQuoteLineItem {
  uid?: string;
  unitPrice: number;
  unitCount: number;
  description: string;
  deductionType: DeductionType | null;
  type: RawQuoteLineItemType;
  unitType: LineItemUnitType;
  article?: LineItemArticle;
  prepaid?: boolean;
}

/**
 * Article when embedded in a line item.
 * @note Should reflect the model used in the app.
 */
export interface LineItemArticle extends EmbeddableArticle {
  /**
   * Reference to the article in the database.
   * @note Not available on custom articles.
   */
  reference?: DocumentReference<Article>;
  type: "standard" | "custom";
}

export type RawQuoteLineItemType = "labor" | "material" | "travel" | "other";
export type LineItemUnitType =
  | "pcs"
  | "hours"
  | "days"
  | "meters"
  | "sqm"
  | "kilometers"
  | "miles";
