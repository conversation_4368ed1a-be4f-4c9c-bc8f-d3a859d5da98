import {
  collection,
  DocumentReference,
  getFirestore,
  query,
  Timestamp,
  where,
} from "firebase/firestore";
import { ArticleGroup } from "../article-groups/article-group";
import { FirestoreModel } from "../firestore_model";
import { CalculatedValues } from "../invoice/calculated-values";
import { JobCache } from "../job/job_cache";
import { Partner } from "../other/partner";
import { CalculationOptions } from "./calculation-options";
import { QuoteStatus } from "./quote";
import { RawQuoteLineItem } from "./raw-quote-line-item";
import { createConverter } from "../firestore-converter";

export const RAW_QUOTE_GENERATOR_VERSION = "5";

export type DeductionType = "rot" | "rut" | "greenTechnology";

export interface RawQuote extends FirestoreModel {
  createTime: Timestamp;
  ownerId: string;
  jobId: string;
  jobRef: DocumentReference;
  version: string;
  companyId: string;
  deductionTypes?: string[];
  companyRef: DocumentReference;
  createdBy?: DocumentReference;
  fileRef?: string;
  customerId: string;
  customerRef: DocumentReference;
  jobCache: JobCache;
  pricesIncludeVat?: boolean;
  status: "draft" | "sent";
  quoteStatus?: QuoteStatus;
  workDescription: string;
  otherInformation: string;
  contact: string;
  lineItems: Array<RawQuoteLineItem>;
  calculatedValues?: CalculatedValues;
  calculationOptions?: CalculationOptions;

  /**
   * If set, only articles from this group will be allowed to be added on the quote.
   */
  lockedArticleGroup?: DocumentReference<ArticleGroup>;

  /**
   * If set, the quote will be sent on behalf of this partner.
   */
  partnerRef?: DocumentReference<Partner>;

  /**
   * Original quote that this quote will replace if accepted.
   */
  originalQuote?: DocumentReference<RawQuote>;
}

export const rawQuoteConverter = createConverter<RawQuote>();

export const jobDraftQuotes = (jobRef: DocumentReference) =>
  query(
    collection(getFirestore(), "rawQuotes"),
    where("jobRef", "==", jobRef),
    where("status", "==", "draft"),
  ).withConverter(rawQuoteConverter);
