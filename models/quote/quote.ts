import { collection, DocumentReference, Timestamp } from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { createConverter } from "../firestore-converter";
import { CalculatedValues } from "../invoice/calculated-values";

export interface Quote extends QuoteInput, FirestoreModel {
  createTime: Timestamp;
  acceptedReason?: string;
  acceptedBy?: DocumentReference;
}

export interface QuoteInput {
  createTime: Timestamp;
  amountAfterTaxDeductions: number;
  amountIncVat: number;
  timeStamp: Timestamp;
  cache: QuoteCache;
  company: DocumentReference;
  customer: DocumentReference;
  deductionTypes: Array<"rot" | "rut">;
  fileRef?: string;
  filename?: string;
  rawQuote?: DocumentReference;
  remindersSent?: Map<string, any>;
  declineReason?: string;
  declineReasonOther?: string;
  status: "pending" | "accepted" | "declined";
  sender?: DocumentReference;
  type: "offer" | "quote";
}

export interface QuoteCache {
  companyName?: string;
  customerName?: string;
  senderName?: string;
}

export enum QuoteReminderType {
  afterThreeDays = "afterThreeDays",
  afterSevenDays = "afterSevenDays",
  afterThirteenDays = "afterThirteenDays",
  offerExpired = "offerExpired",
}

export type RawQuoteLineItemType = "labor" | "material" | "travel" | "other";
export type LineItemUnitType =
  | "pcs"
  | "hours"
  | "days"
  | "meters"
  | "sqm"
  | "kilometers"
  | "miles";

export function hasAlreadySentReminder(
  quote: Quote,
  quoteReminderType: QuoteReminderType,
): boolean {
  if (quote.remindersSent == null) return false;

  if (quoteReminderType in quote.remindersSent) return true;

  return false;
}

export type QuoteStatus = "pending" | "accepted" | "declined" | "cancelled";

export const quotesConverter = createConverter<Quote>();

export const jobQuotes = (jobRef: DocumentReference) =>
  collection(jobRef, "quotes").withConverter(quotesConverter);

export interface QuoteCardData {
  reference: DocumentReference;
  jobRef: DocumentReference;
  fileRef: string;
  status: QuoteStatus | "draft" | "sent";
  amountAfterTaxDeductions?: number;
  acceptedReason?: string;
  acceptedBy?: DocumentReference;
  calculatedValues?: CalculatedValues;
  partnerRef?: DocumentReference;
}
