import { DocumentReference, Timestamp } from "firebase/firestore";
import { Company } from "../company/company";
import { Job } from "../job/job";
import { Partner } from "../other/partner";
import { FirestoreModel } from "../firestore_model";
import { User } from "../user/user";

/**
 * An issue is something that needs attention from one or more parties.
 */
export interface Issue extends FirestoreModel {
  createTime: Timestamp;

  /**
   * The subject of the issue. Must have a subcollection with internal comments.
   * A subject have a maximum of one issue in total.
   */
  subject: DocumentReference<Job | Company>;

  /**
   * Participants of the issue.
   */
  participants: {
    done: Omit<ParticipantDetails<unknown>, "reference">;
    partner?: ParticipantDetails<Partner>;
    craftsman?: ParticipantDetails<Company>;
    user?: ParticipantDetails<User>;
  };

  /**
   * List of users that have viewed the issue.
   */
  viewedBy: DocumentReference<User>[];

  /**
   * Users mentioned within the issue.
   */
  mentions: {
    unread: DocumentReference<User>[];
    all: DocumentReference<User>[];
  };
}

export type IssueMentionStatus = keyof Issue["mentions"];
export type IssueParticipant = keyof Issue["participants"];
export type IssueAssignee = DocumentReference | null;

export interface ParticipantDetails<T = Partner | Company> {
  lastUpdateTime: Timestamp;
  reference: DocumentReference<T>;
  status: IssueStatus;
  assignee: IssueAssignee;
  cache?: IssueCache;
  snoozedUntil?: Timestamp;
}

export interface IssueCache {
  jobReferenceNumber?: string;
  jobExternalReference?: string;
  customerName?: string;
  partnerName?: string;
  companyName?: string;
  lastMessage?: string;
  lastAuthor?: DocumentReference<User>;
}

export type IssueStatus = "open" | "snoozed" | "closed";
