import {
  collection,
  DocumentReference,
  FieldValue,
  Timestamp,
} from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { createConverter } from "../firestore-converter";

const converter = createConverter<InternalComment>();
export const internalCommentsCollection = (subject: DocumentReference) =>
  collection(subject, "internalComments").withConverter(converter);

export interface InternalComment
  extends FirestoreModel,
    InternalCommentInput,
    Partial<DeletedInternalComment> {
  createTime: Timestamp;
}

export interface InternalCommentInput {
  createTime: FieldValue;
  createdBy: DocumentReference;
  createdByType: InternalCommentParticipant;
  note: string;
  visibleFor?: { [from in InternalCommentParticipant]?: boolean };
  mentioned?: DocumentReference[];
  files?: InternalCommentFile[];
}

interface DeletedInternalComment {
  deletedTime: Timestamp;
  isDeleted: true;
  deletedBy: DocumentReference;
}

export interface InternalCommentFile {
  name: string;
  path: string;
}

export type InternalCommentParticipant =
  | "done"
  | "partner"
  | "craftsman"
  | "user";
