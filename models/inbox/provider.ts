import {
  DocumentReference,
  QueryFieldFilterConstraint,
  QueryOrderByConstraint,
  Timestamp,
  collection,
  deleteField,
  getFirestore,
  limit,
  orderBy,
  query,
  serverTimestamp,
  updateDoc,
  where,
} from "firebase/firestore";
import { isDefined } from "../../lib/filter-undefined";
import { IssueParticipant, IssueStatus, IssueAssignee, Issue } from "./issue";
import { createConverter } from "../firestore-converter";

const converter = createConverter<Issue>();
export const issuesCollection = () =>
  collection(getFirestore(), "issues").withConverter(converter);

export interface InboxQueryOptions {
  participant: IssueParticipant;
  status?: IssueStatus;
  assignee?: IssueAssignee;
  participantReference?: DocumentReference;
}

const inboxBaseQuery = (
  { participant, status, assignee, participantReference }: InboxQueryOptions,
  ...extra: (QueryFieldFilterConstraint | QueryOrderByConstraint)[]
) => {
  return query(
    issuesCollection(),
    ...[
      participantReference !== undefined
        ? where(
            `participants.${participant}.reference`,
            "==",
            participantReference,
          )
        : undefined,
      status !== undefined
        ? where(`participants.${participant}.status`, "==", status)
        : undefined,
      assignee !== undefined
        ? where(`participants.${participant}.assignee`, "==", assignee)
        : undefined,
      ...extra,
      orderBy(`participants.${participant}.lastUpdateTime`, "desc"),
    ].filter(isDefined),
  );
};

export interface InboxMentionsQueryOptions
  extends Omit<InboxQueryOptions, "assignee" | "status"> {
  mentioned: DocumentReference;
  status: "unread" | "all";
}

export const inboxMentionsQuery = ({
  mentioned,
  status,
  ...options
}: InboxMentionsQueryOptions) =>
  inboxBaseQuery(
    options,
    where(`mentions.${status}`, "array-contains", mentioned),
  );

export const inboxQuery = (options: InboxQueryOptions) =>
  inboxBaseQuery(options);

export const inboxViewedByQuery = (
  options: InboxQueryOptions & { viewer: DocumentReference },
) =>
  inboxBaseQuery(options, where("viewedBy", "array-contains", options.viewer));

export const snoozeIssue = (
  issue: DocumentReference,
  participant: IssueParticipant,
  snoozeUntilDate: Date,
) =>
  updateDoc(issue, {
    [`participants.${participant}.status`]: "snoozed",
    [`participants.${participant}.snoozedUntil`]:
      Timestamp.fromDate(snoozeUntilDate),
  });

export const unSnoozeIssue = (
  issue: DocumentReference,
  participant: IssueParticipant,
) =>
  updateDoc(issue, {
    [`participants.${participant}.status`]: "open",
    [`participants.${participant}.snoozedUntil`]: deleteField(),
  });

export const issueForSubjectQuery = (
  subject: DocumentReference,
  partner?: DocumentReference,
) =>
  partner
    ? query(
        issuesCollection(),
        where("subject", "==", subject),
        where("participants.partner.reference", "==", partner),
        limit(1),
      )
    : query(issuesCollection(), where("subject", "==", subject), limit(1));

export const deleteComment = (
  comment: DocumentReference,
  deleter: DocumentReference,
) =>
  updateDoc(comment, {
    deletedTime: serverTimestamp(),
    isDeleted: true,
    deletedBy: deleter,
  });
