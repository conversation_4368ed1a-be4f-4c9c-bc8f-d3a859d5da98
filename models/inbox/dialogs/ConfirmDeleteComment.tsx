import ConfirmDialog, {
  ConfirmActions,
} from "../../../components/dialogs/ConfirmDialog";
import { DialogProps } from "../../../components/dialogs/default-dialog-props";
import { useAuthUser } from "../../../components/auth/AuthProvider";
import { InternalComment } from "../internal-comment";
import { deleteComment } from "../provider";

interface ConfirmDeleteCommentDialogProps extends DialogProps {
  comment?: InternalComment;
}

export default function ConfirmDeleteCommentDialog({
  comment,
  close,
  isOpen,
}: ConfirmDeleteCommentDialogProps) {
  const authUser = useAuthUser();
  return (
    <ConfirmDialog
      title={"Delete message"}
      description={"Are you sure you want to delete this message?"}
      onClose={close}
      isOpen={isOpen}
      actions={[
        ConfirmActions.cancel,
        {
          title: "Delete",
          onClick: async () => {
            if (!comment || !authUser.userDoc) return;
            await deleteComment(comment.reference, authUser.userDoc.reference);
          },
        },
      ]}
    />
  );
}
