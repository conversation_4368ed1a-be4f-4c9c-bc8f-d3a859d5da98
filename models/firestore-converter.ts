import { FirestoreDataConverter } from "firebase/firestore";
import { FirestoreModel } from "./firestore_model";

/**
 * Creates a Firestore data converter that removes the reference from the model
 * and provides type safety for the model.
 *
 * @example
 * ```ts
 * const jobConverter = createConverter<Job>()
 *
 * export const jobsCollection = () =>
 *   collection(getFirestore(), "jobs").withConverter(
 *     jobConverter
 *   );
 *
 * export const jobsQuery = () => query(jobsCollection());
 * ```
 */
export const createConverter = <
  T extends FirestoreModel,
>(): FirestoreDataConverter<T, Omit<T, "reference">> => ({
  toFirestore: (model) => {
    const { reference, ...rest } = model;
    return rest as Omit<T, "reference">;
  },
  fromFirestore: (document) =>
    ({
      ...document.data(),
      reference: document.ref,
    }) as T,
});
