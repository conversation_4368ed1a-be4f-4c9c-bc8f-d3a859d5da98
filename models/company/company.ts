import { DoneLocation } from "./../user/user";
import { Query, Timestamp, getDocs } from "@firebase/firestore";
import { DoneAddress } from "../other/done_address";
import { FirestoreModel } from "../firestore_model";
import { useDocument } from "react-firebase-hooks/firestore";
import { User } from "../user/user";
import {
  DocumentReference,
  DocumentSnapshot,
  getDoc,
  doc,
  collection,
  getFirestore,
  FieldValue,
  query,
  where,
} from "firebase/firestore";
import { UserRoleMap } from "../user/user-role";
import { QueuedOffer } from "../job/offer/job-offer";
import { createConverter } from "../firestore-converter";

export type InsuranceInstitution =
  | "dina"
  | "folksam"
  | "gjensidige"
  | "if"
  | "länsförsäkringar"
  | "moderna"
  | "swedbank"
  | "trygg-hansa"
  | "svedea";

export interface CompanyLocation extends DoneLocation {
  uuid: string;
  type: "premise";
}

export interface CompanyWithDistance extends Company {
  distanceFromWork?: number; // km
  autoOffer?: QueuedOffer;
}

export interface Company extends CompanyInput, FirestoreModel {
  createTime: Timestamp;
}

export interface CompanyInput {
  address?: DoneAddress;
  createTime: FieldValue;
  admin?: DocumentReference;
  legalName?: string;
  services?: string[];
  homeLocations?: CompanyLocation[];
  tags?: any;
  terms?: any;
  logoRef?: string;
  status?: CompanyStatus;
  name: string;
  users?: DocumentReference[];
  usersV2: UserRoleMap;
  insuredBy?: InsuranceInstitution | string;
  activeFixedPriceJobs?: string[];
  serviceCity?: string[];
  events?: CompanyEvents;
  // Add new available social url fields to `COMPANY_SOCIAL_URLS` also
  socialUrls?: {
    facebookUrl?: string;
    instagramUrl?: string;
    websiteUrl?: string;
  };
  billingSettings?: {
    paymentInfo?: {
      bankgiro?: string;
      bankAccount?: string;
    };
  };
  onboardingStatus?: "ongoing" | "completed";
  orgNo: string;
  statistics: CompanyStatistics;
  checkStatus: "failed" | "passed";
  checks?: {
    insuranceDocument: boolean;
    ueAgreement: boolean;
    bankGiro: boolean;
    companyDescription: boolean;
    sakerVattenCertificate: boolean;
    elAlCertificate: boolean;
    adminUser: boolean;
    homeAddress: boolean;
  };

  /** Id of each network that this company is a member of */
  memberOf: string[];
}

export interface CompanyStatistics {
  lastOfferReplyTime?: Timestamp;
  numberOfCompletedJobs?: number;
  ratingSum?: number;
  numberOfReviews?: number;
  rating?: number;
}

export type CompanyStatus = "active" | "inactive" | "offboarded" | "new";

export type CompanyUserRole = "admin" | "owner" | "user";

export interface CompanyRow {
  name: string;
  createTime?: Date;
  services?: string;
  serviceCity?: string;
  orgNo: string;
  tags?: string;
  status?: "active" | "inactive" | "offboarded" | "new";
  lastOfferReplyTime?: Date;
  company: Company;
  numberOfCompletedJobs: number;
  unresponsive: "responsive" | "unresponsive" | "aboutToLose";
  reference: DocumentReference;
  id: string;
  href: string;
}

export type CompanyEvents = {
  firstOfferReceived?: Timestamp;
  firstOfferAccepted?: Timestamp;
  firstMatch?: Timestamp;
  firstCallTimeScheduled?: Timestamp;
  firstCustomerCallMade?: Timestamp;
  firstQuoteSent?: Timestamp;
  firstQuoteDeclined?: Timestamp;
  firstQuoteAccepted?: Timestamp;
  firstWorkTimeScheduled?: Timestamp;
  firstJobDone?: Timestamp;
  firstReviewFromCustomer?: Timestamp;
};

export function companyAddressForPdfReceipt(
  address: DoneAddress | undefined,
): string {
  if (address == null) return "-";

  return (
    address?.line1 +
    (address.line1 != null ? ", " : "") +
    address?.zip +
    " " +
    address?.city
  );
}

const converter = createConverter<Company>();
export const companyCollection = () =>
  collection(getFirestore(), "companies").withConverter(converter);

export const companyRef = (companyId: string) =>
  doc(companyCollection(), companyId);

export const useCompanyDocument = (
  companyId: string,
): { company?: Company; loading: Boolean } => {
  const [companySnapshot, loading] = useDocument(
    doc(companyCollection(), companyId),
  );

  return {
    company:
      companySnapshot?.data() &&
      ({
        ...companySnapshot.data(),
        reference: companySnapshot.ref,
      } as Company),
    loading,
  };
};

export function companyOwner(company: Company): DocumentReference | undefined {
  return Object.values(company.usersV2).find((entry) => entry.role == "owner")
    ?.reference;
}
export function companyReference(id: string): DocumentReference {
  return doc(companyCollection(), id);
}

export async function fetchCompany(id: string): Promise<Company> {
  const reference = doc(companyCollection(), id);
  const quoteSnapshot = await getDoc(reference);
  if (!quoteSnapshot.exists)
    throw Error(`Company with id ${id} does not exist`);
  return { reference, ...quoteSnapshot.data() } as Company;
}

export async function fetchCompanies(query?: Query): Promise<Company[]> {
  const companyData = await getDocs(query || companyCollection());

  return companyData.docs.map((companyDoc) => {
    return { ...companyDoc.data(), reference: companyDoc.ref } as Company;
  });
}

export async function fetchCompaniesById(ids: string[]): Promise<Company[]> {
  return Promise.all(ids.map(fetchCompany));
}

export const companiesInNetworkQuery = (networkId: string) =>
  query(
    companyCollection(),
    where("status", "in", ["active", "new"]),
    where("memberOf", "array-contains", networkId),
  );

export async function fetchCompanyAdmin(
  company: Company,
): Promise<User | undefined> {
  if (!company) {
    console.error(
      `fetchCompaniesAdminContact: Company data not found from company ref! - company ${company}`,
    );

    return;
  }

  if (!company.admin) {
    console.error(
      `fetchCompaniesAdminContact: Admin reference not found in company! - companyId ${company.reference.id}`,
    );
    return;
  }

  const companyAdminRef = company.admin;
  const companyAdminSnapshot: DocumentSnapshot = await getDoc(companyAdminRef);
  const companyAdminData = companyAdminSnapshot.data();

  if (!companyAdminData) {
    throw new Error(
      `fetchCompaniesAdmin: Admin not found in Company with reference! - companyId ${company.reference.id}`,
    );
  }

  if (!company.reference) {
    throw new Error(
      `fetchCompaniesAdminContact: Company doesn't have a document reference!`,
    );
  }

  return {
    ...companyAdminData,
    reference: companyAdminRef,
  };
}

export async function fetchCompanyAdminFromCompanyReference(
  reference: DocumentReference,
): Promise<User | undefined> {
  const company: Company = await fetchCompany(reference.id);

  if (!company) return;

  return await fetchCompanyAdmin(company);
}
