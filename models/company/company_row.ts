import { DocumentReference, QuerySnapshot } from "firebase/firestore";
import { TableRow } from "../../components/common/table/PaginatedDataGrid";
import { Company, CompanyStatus } from "./company";

export interface CompanyRow extends TableRow {
  externalReference?: string;
  name: string;
  createTime: Date;
  services: string[];
  serviceCity: string;
  orgNo: string;
  tags: string[];
  status?: CompanyStatus;
  lastOfferReplyTime?: Date;
  company: Company;
  numberOfCompletedJobs?: number;
  id: string;
  reference: DocumentReference;
}

export function generateCompanyRow(value?: QuerySnapshot): CompanyRow[] {
  return (
    value?.docs.map((document) => {
      const company = {
        ...document.data(),
        reference: document.ref,
      } as Company;
      return {
        company: company,
        tags: company.tags as string[],
        name: company.name,
        services: company.services || [],
        serviceCity: (company.serviceCity || []).join(", "),
        orgNo: company.orgNo,
        status: company.status,
        lastOfferReplyTime: company.statistics?.lastOfferReplyTime?.toDate(),
        numberOfCompletedJobs: company.statistics?.numberOfCompletedJobs ?? 0,
        reference: company.reference,
        paginationCursor: company.createTime!,
        id: company.reference.id,
        createTime: company.createTime?.toDate(),
      };
    }) ?? []
  );
}
