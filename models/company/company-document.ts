import { FieldValue } from "firebase/firestore";
import { LabeledValue } from "../../config/constants";

export interface CompanyDocument {
  createTime: FieldValue;
  name: string;
  active: boolean;
  fileRef: string;
  expiresAt?: FieldValue;
}

export type CompanyDocumentType =
  | "insurance"
  | "certificate"
  | "agreement"
  | "other";

export const COMPANY_DOCUMENT_TYPES: LabeledValue<CompanyDocumentType>[] = [
  { value: "agreement", label: "Agreement" },
  { value: "certificate", label: "Certificate" },
  { value: "insurance", label: "Insurance" },
  { value: "other", label: "Other" },
];
