import { DocumentReference, Timestamp } from "@firebase/firestore";
import { FieldValue } from "firebase/firestore";
import { CalculatedValues } from "./calculated-values";
import { Company } from "../company/company";
import { FirestoreModel } from "../firestore_model";
import { User } from "../user/user";
import { SensitiveUserData } from "../user/sensitive-user-data";
import { InvoiceLineItem } from "./invoice-line-item";
import { DeductionType } from "../quote/raw-quote";

/** Represents an Invoice document from Firestore */
export interface Invoice extends InvoiceBase, FirestoreModel {
  createTime: Timestamp;
}

/** Defines the required parameters to create a new invoice. */
export interface InvoiceInput extends InvoiceBase {
  createTime: FieldValue;
}

export type InvoiceStatus =
  | "draft"
  | "sent"
  | "paid"
  | "refuted"
  | "credited"
  | "creditLoss";
export type InvoiceAdminStatus =
  | "pending"
  | "ready"
  | "reported"
  | "partial"
  | "denied"
  | "closed";

export interface InvoiceBase {
  type?: "basis" | "generated" | "pdf";
  calculatedValues?: CalculatedValues | undefined;
  job: DocumentReference;
  quote: DocumentReference;
  /** Actual full copy of User document + reference */
  customer: User;
  /** A copy of the sensitive user info document on the customer */
  company: Company;
  billectaId?: string;
  number?: string;
  pdfFilePath?: string;
  dueDate?: Timestamp;
  createTime: FieldValue;
  contact: string;
  workDescription: string;
  otherInformation: string;
  status: InvoiceStatus;
  adminStatus?: InvoiceAdminStatus;
  basisStatus?: "draft" | "sent";
  events: InvoiceEvents;
  payments: []; // Placeholder
  reminders: []; // Placeholder
  customerSensitiveData?: SensitiveUserData;
  lineItems: InvoiceLineItem[];
  deductionTypes: DeductionType[];
  /**
   * User that submitted the basis.
   */
  basisSentBy?: DocumentReference;

  lockedArticleGroup?: DocumentReference;

  partner: DocumentReference;
}

/** A collection of timely events triggered by both users and systems.
 * Once an event has been set, it cannot be unset.
 */
export interface InvoiceEvents {
  /** Set when invoice is moved from `draft` to `sent`status. */
  sent?: Timestamp;

  /** Set when the first reminder is sent */
  reminder1?: Timestamp;

  /** Set when the second reminder is sent */
  reminder2?: Timestamp;

  /** Set when the final reminder is sent. In the final reminder, the customer is informed about debt collection as next step. */
  finalReminder?: Timestamp;

  /** Set when a partial payment is done by the customer but there is still an amount left to pay. */
  paidInPart?: Timestamp;

  /** Set when the invoice is fully paid */
  fullyPaid?: Timestamp;

  /** Set when the company marks the invoice as a loss (customer won't pay and no debt collection
   * will be made). Invoice is then also moved from `sent` to `creditLoss`status. */
  markedAsCreditLoss?: Timestamp;

  /** Set when the customer has viewed the invoice in the Done app */
  viewedByCustomer?: Timestamp;

  /** Set when the customer has viewed the invoice in the Done app */
  viewedAsPDFByCustomer?: Timestamp;

  /** Set when the customer refutes the invoice through customer service */
  refuted?: Timestamp;

  /** Set when the company has credited the invoice partly or fully */
  credited?: Timestamp;

  /**
   * Date when the basis was submitted.
   */
  basisSent?: Timestamp;
}

export const invoiceContainsNonArticleLines = (invoice: Invoice) =>
  invoice.lineItems.some((lineItem) => !lineItem.article);
