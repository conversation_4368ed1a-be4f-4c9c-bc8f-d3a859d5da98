import { RawQuoteLineItem } from "../quote/raw-quote-line-item";

export interface InvoiceLineItem extends RawQuoteLineItem {
  workedHours: number | null;
  detailedDeductionType: DetailedDeductionType | null;
}

export type DetailedDeductionType =
  | "rotConstruction"
  | "rotElectricity"
  | "rotGlassMetalWork"
  | "rotGroundDrainageWork"
  | "rotMasonry"
  | "rotPaintingWallpapering"
  | "rotHvac"
  | "rutFurnishing"
  | "rutMoving"
  | "rutCleaning"
  | "rutGardening"
  | "rutWhiteGoods"
  | "rutITServices"
  | "greenTechnologySolarPower"
  | "greenTechnologyEnergyStorage"
  | "greenTechnologyCarCharger";
