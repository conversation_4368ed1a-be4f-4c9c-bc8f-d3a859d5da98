import {
  FieldValue,
  DocumentReference,
  Timestamp,
  getDocs,
  getFirestore,
  collection,
  query,
  where,
} from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";

export interface ParsedInvoiceLine extends FirestoreModel {
  createTime: FieldValue;
  job: DocumentReference | null;
  type: "customerInvoice" | "selfInvoice";
  partner: DocumentReference | null;
  jobDoneReference: string | null;
  jobExternalReference: string | null;
  company: DocumentReference | null;
  companyName: string | null;
  customer: DocumentReference | null;
  customerName: string | null;
  invoiceBillectaId: string;
  invoiceNumber: string;
  invoiceDate: Timestamp;
  invoiceOCR: string | null;
  parsedInfo: {
    groupLine: string | null;
    customerName: string | null;
    reference: string | null;
    partnershipName: string | null;
  };
  line: string | null;
  perAmount: number | null;
  count: number | null;
  totalAmount: number | null;
}

export async function fetchParsedInvoiceLinesForJob(
  reference: DocumentReference,
): Promise<ParsedInvoiceLine[]> {
  const _collection = collection(
    getFirestore(),
    "admin/billecta/parsedInvoiceLines/",
  );

  const invoiceLineQuery = query(
    _collection,
    where("job", "==", reference),
    where("type", "==", "selfInvoice"),
  );

  const invoiceLineSnapshot = await getDocs(invoiceLineQuery);

  const invoiceLines =
    invoiceLineSnapshot.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as ParsedInvoiceLine,
    ) ?? [];

  return invoiceLines;
}
