// All monetary values are in cents.
export interface CalculatedValues {
  vat: number;
  totalNet: number;
  laborCost: number;
  otherCost: number;
  totalGross: number;
  travelCost: number;
  /**
   * @deprecated This was used when ROT was the only tax deduction. Now you must use [fullTaxDeduction] instead
   */
  taxDeduction?: number;
  materialCost: number;
  totalNetAfterDeductions: number;
  rotDeduction: number;
  rutDeduction: number;
  greenTechDeduction: number;
  fullTaxDeduction: number;
  centAmountToPay?: number;
  centsRounding?: number;
}
/// turns values defined in cents into strings that are used for display
export function formattedValue(
  valueInCents: number,
  maxFractionDigits: number,
): string {
  const valueWithDecimals = valueInCents / 100.0;

  return Intl.NumberFormat("sv-SE", {
    style: "currency",
    currency: "SEK",
    minimumFractionDigits: 0,
    maximumFractionDigits: maxFractionDigits,
  }).format(valueWithDecimals);
}
