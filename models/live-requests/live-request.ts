import { Timestamp } from "firebase/firestore";

/**
 * Represents a document that the client expects to be continously updated until it is completed.
 * Should not be used directly, but extended by more specific interfaces.
 */
export interface LiveRequest {
  createTime: Timestamp;
  status: string; // Will be more specific in extending interfaces.

  /**
   * Action that the requesting client needs to perform.
   */
  clientActions?: LiveRequestClientActions;

  /**
   * Errors that occured during the request.
   * Only used internally.
   */
  errors?: LiveRequestError[];
}

export interface LiveRequestError {
  /**
   * Message describing the error.
   */
  message: string;

  /**
   * If true, the error is fatal and the request failed to complete.
   */
  fatal: boolean;
}

/**
 * Actions that the requesting client needs to perform.
 */
export interface LiveRequestClientActions {
  /**
   * If set, the client should open the provided URL.
   */
  openUrl?: string;

  /**
   * If set, display messages in the client.
   */
  displayMessages?: LiveRequestClientMessage[];
}

/**
 * Type of client message.
 */
export type LiveRequestClientMessageType =
  | "info"
  | "question"
  | "warning"
  | "error";

/**
 * Message to display in the client.
 */
export interface LiveRequestClientMessage {
  /**
   * Type of message.
   */
  type: LiveRequestClientMessageType;

  /**
   * Message to display.
   */
  message: string;
}
