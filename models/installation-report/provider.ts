import {
  doc,
  DocumentReference,
  getDoc,
  getFirestore,
  query,
  updateDoc,
  where,
} from "firebase/firestore";
import { collection } from "firebase/firestore";
import { createConverter } from "../firestore-converter";
import {
  CompletedPartnerInstallation,
  InstallationBillingStatus,
} from "./installation-report";
import { InvoiceLineItem } from "../invoice/invoice-line-item";

const converter = createConverter<CompletedPartnerInstallation>();

export const installationReportsCollection = () =>
  collection(getFirestore(), "completedPartnerInstallations").withConverter(
    converter,
  );

export const fetchReport = async (
  idOrRef: string | DocumentReference<CompletedPartnerInstallation>,
) => {
  const ref =
    typeof idOrRef === "string"
      ? doc(installationReportsCollection(), idOrRef)
      : idOrRef;
  const document = await getDoc(ref);
  return {
    ...document.data(),
    reference: ref,
  } as CompletedPartnerInstallation;
};

export const installationReportsForJobQuery = (jobRef: DocumentReference) =>
  query(installationReportsCollection(), where("job", "==", jobRef));

export const installationReportsForPartnerQuery = (jobRef: DocumentReference) =>
  query(installationReportsCollection(), where("job", "==", jobRef));

export const updateLines = async (id: string, lines: InvoiceLineItem[]) =>
  updateDoc(doc(installationReportsCollection(), id), { lines });

export const updateStatus = async (
  id: string,
  status: InstallationBillingStatus,
) => updateDoc(doc(installationReportsCollection(), id), { status });

export const installationReportsForPartnerAndStatusQuery = (
  partnerRef: DocumentReference,
  status: InstallationBillingStatus,
) =>
  query(
    installationReportsCollection(),
    where("managedBy", "==", partnerRef),
    where("status", "==", status),
  );

export const openInstallationReportsForPartner = (
  partnerRef: DocumentReference,
) =>
  query(
    installationReportsCollection(),
    where("managedBy", "==", partnerRef),
    where("status", "in", ["paused", "pending", "queued"]),
  );
