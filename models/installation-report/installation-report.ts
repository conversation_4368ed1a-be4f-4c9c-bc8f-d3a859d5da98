import { FieldValue } from "firebase/firestore";

import { Timestamp } from "firebase/firestore";

import { DocumentReference } from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { RawQuoteLineItem } from "../quote/raw-quote-line-item";

export interface CompletedPartnerInstallation
  extends CompletedPartnerInstallationInput,
    FirestoreModel {
  createTime: Timestamp;
  partnership: string;

  /** The time the installation was reported. Usually same as createTime */
  reportedTime: Timestamp;
  /** The company user which reported this installation. */
  reportedBy: DocumentReference;

  job: DocumentReference;
  jobReferenceNumber: string;

  customer: DocumentReference;
  customerName: string;

  company: DocumentReference;
  companyName: string;

  /** Set to indicate that the installation should not be invoiced for at this point. */
  invoicingPaused?: boolean;

  /** Set when a self-invoice has been created for paying the craftsman for this installation. */
  selfInvoiceCreateTime?: Timestamp;

  /** Set when the partner has been invoiced for this installation. */
  partnerInvoiceCreateTime?: Timestamp;
}

/**
 * The status of an installation in the billing process.
 *
 * - trashed: The installation has been trashed and should not be billed.
 * - paused: The installation is paused and will not be billed.
 * - pending: The installation is pending and waiting to be billed.
 * - queued: The installation is queued and will be billed when comitted.
 * - processing: The installation is comitted and is being billed.
 * - billed: The installation has been fully billed.
 */
export type InstallationBillingStatus =
  | "trashed"
  | "paused"
  | "pending"
  | "queued"
  | "processing"
  | "billed";

export interface CompletedPartnerInstallationInput {
  createTime: FieldValue;
  partnership: string;
  partnerReferenceNumber: string;

  /** The time the installation was reported. Usually same as createTime */
  reportedTime: FieldValue;
  /** The company user which reported this installation. */
  reportedBy: DocumentReference;

  job: DocumentReference;
  jobReferenceNumber: string;

  customer: DocumentReference;
  customerName: string;

  company: DocumentReference;
  companyName: string;

  type?: "article-based";
  lines?: RawQuoteLineItem[];

  /** The partner which is responsible for billing for this installation. */
  managedBy: DocumentReference;

  /**
   * Hours worked as reported by the installer.
   */
  hoursWorked?: number;

  /** Set when a self-invoice has been created for paying the craftsman for this installation. */
  selfInvoiceCreateTime?: FieldValue;

  /**
   * The invoice number (alphanumeric) referring to an invoice in an external billing system used to pay out this company for this installation.
   * Initially null, before installation has been invoiced to partner, to enable filtering of installations which have not been invoiced.
   */
  selfInvoiceNumber: string | null;

  /**
   * Set when a draft invoice have been created in Billecta.
   */
  selfInvoiceBillectaId?: string;

  /** Set when the partner has been invoiced for this installation. */
  partnerInvoiceCreateTime?: FieldValue;

  /**
   * The invoice number (alphanumeric) referring to an invoice in an external billing system used to invoice the partner for this installation.
   * Initially null, before installation has been invoiced to partner, to enable filtering of installations which have not been invoiced.
   */
  partnerInvoiceNumber: string | null;

  /**
   * Set when a draft invoice have been created in Billecta.
   */
  partnerInvoiceBillectaId?: string;

  /** Used internally by Done to signify notes */
  doneNotes?: string;

  /** Set in Retool when the parameters of an installation has been edited by Done. */
  editedByDoneTime?: FieldValue;

  /**
   * Set to indicate that the installation should not be invoiced for at this point.
   *
   * @deprecated Use `status` instead.
   */
  invoicingPaused?: boolean;

  /** The status of an installation in the billing process */
  status: InstallationBillingStatus;
}
