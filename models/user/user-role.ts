import { Timestamp, DocumentReference, FieldValue } from "firebase/firestore";

export type UserRoleMap = { [userId: string]: UserRoleContainer };

export interface UserRoleContainer extends UserRoleContainerInput {
  lastUpdatedAt: Timestamp;
}

export interface UserRoleContainerInput {
  reference: DocumentReference;
  role: UserRole;
  lastUpdatedAt: FieldValue;
  fullName?: string;
}

export type UserRole = "admin" | "owner" | "user";
