import { DocumentReference, FieldValue, Timestamp } from "firebase/firestore";
import { DoneAddress } from "../other/done_address";
import { FirestoreModel } from "../firestore_model";

/**
 * Represents a document in the `_sensitive` sub-collection for a user.
 * Used to store information that only craftsmen who
 * have an established business relation (accepted quote)
 * with the client may access.
 */
export interface SensitiveUserData
  extends FirestoreModel,
    SensitiveUserDataBase {
  createTime: Timestamp;
}

export interface SensitiveUserDataInput extends SensitiveUserDataBase {
  createTime: FieldValue;
}

interface SensitiveUserDataBase {
  address?: DoneAddress;
  rotInfo?: UserRotInfo;
  trustedCompanies?: Array<DocumentReference>;
}

// FIXME : remove explicit null values when source is fixed
export interface UserRotInfo {
  personIdNumber?: string;
  propertyDesignation?: string | null;
  propertyType?: "apartmentOwner" | "house" | "rentedApartment";
  housingCooperative?: string | null;
  apartmentNumber?: string | null;
}
