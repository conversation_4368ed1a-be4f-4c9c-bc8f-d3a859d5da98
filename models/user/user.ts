import {
  collection,
  doc,
  DocumentReference,
  GeoPoint,
  getDoc,
  getFirestore,
  Timestamp,
} from "firebase/firestore";

import { User as FirebaseUser } from "firebase/auth";
import { Partner } from "../other/partner";
import { createConverter } from "../firestore-converter";
import { DONE_BACKOFFICE_USER_REFERENCE } from "../../config/constants";
import { memoize } from "lodash";

export interface User {
  reference: DocumentReference;
  address?: DoneAddress;
  company?: DocumentReference;
  createTime?: Timestamp;
  firstName?: string;
  email?: string;
  profileImage?: string;
  propertyDesignation?: string;
  typeOfHousing?: string;
  housingCooperative?: string;
  apartmentNumber?: string;
  lastName?: string;
  tokenIsDirty?: boolean;
  location?: DoneLocation;
  isSuperUser?: boolean;
  phoneNumber?: string;
  discountCodes?: Array<string>;
  hasEnteredROTInformation?: boolean;

  /** Id of each merchant that the customer belongs to */
  customerOf?: string[];

  /** Id of each network that the installer belongs to */
  memberOf?: string[];

  /** @deprecated Use `customerOf` instead */
  partner?: DocumentReference;
}

export function getFullUserName(user: User) {
  return `${user.firstName} ${user.lastName}`;
}

const converter = createConverter<User>();

export const userCollection = () =>
  collection(getFirestore(), "users").withConverter(converter);

export const userRef = (userId: string | undefined) =>
  userId ? doc(userCollection(), userId) : undefined;

export async function fetchUser(
  reference?: DocumentReference,
): Promise<User | null> {
  if (!reference) return null;

  const userSnapshot = await getDoc(reference);
  const userData = userSnapshot.data();

  if (userData == null) return null;

  return { reference, ...userData };
}

export const isSystemUser = memoize(
  (reference: DocumentReference) =>
    reference.path === DONE_BACKOFFICE_USER_REFERENCE().path,
);

export interface DoneAddress {
  zip?: string;
  streetAddress?: string;
  floor?: string;
  doorCode?: string;
  postalCode?: string;
  city?: string;
  line1?: string;
  country?: string;
  line2?: string;
}

export interface DoneLocation {
  addressLine?: string;
  coordinates?: GeoPoint;
  postalTown?: string;
  subLocality?: string;
}

export interface UserInput {
  firstName?: string;
  lastName?: string;
  phoneNumber: string;
  email: string;
  address: DoneAddress;
  company?: DocumentReference;
}

export interface AuthUser {
  firebaseUser?: FirebaseUser | null;
  userDoc?: User;
  partnerDoc?: Partner;
  partner?: string;
  isSuperAdmin?: boolean;
  isEmulatingPartner?: boolean;
}
