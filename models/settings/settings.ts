import {
  arrayRemove,
  arrayUnion,
  DocumentData,
  DocumentReference,
  Timestamp,
} from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { FilterConfig } from "../../components/filtering/lib/filter";
import { PartialDeep } from "type-fest";

/**
 * Represents a saved filter configuration
 */
export interface SavedFilter {
  createdBy: DocumentReference;
  config: FilterConfig;
}

interface FilterCollections {
  jobs?: SavedFilter[];
}

/**
 * Backoffice-specific settings
 */
interface BackofficeSettings {
  /** Saved filter configurations */
  savedFilters?: FilterCollections;
}

/**
 * Comprehensive settings document for a user
 */
export interface Settings extends FirestoreModel {
  createTime: Timestamp;

  /** Settings specific to the backoffice client */
  backoffice?: BackofficeSettings;
}

export type SettingsInput =
  | DocumentData
  | PartialDeep<Omit<Settings, "createTime" | "reference">>;

export function addFilterInput(
  conf: keyof FilterCollections,
  filter: SavedFilter,
) {
  return { [`backoffice.savedFilters.${conf}`]: arrayUnion(filter) };
}

export function removeFilterInput(
  conf: keyof FilterCollections,
  filter: SavedFilter,
) {
  return { [`backoffice.savedFilters.${conf}`]: arrayRemove(filter) };
}
