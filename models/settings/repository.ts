import { doc, getFirestore } from "firebase/firestore";
import { createConverter } from "../firestore-converter";
import { Settings } from "./settings";

const converter = createConverter<Settings>();

export const userSettingsRef = (userId: string | undefined) =>
  userId
    ? doc(getFirestore(), "users", userId, "settings", userId).withConverter(
        converter,
      )
    : undefined;

export const partnerSettingsRef = (partnerId: string | undefined) =>
  partnerId
    ? doc(
        getFirestore(),
        "partners",
        partnerId,
        "settings",
        partnerId,
      ).withConverter(converter)
    : undefined;
