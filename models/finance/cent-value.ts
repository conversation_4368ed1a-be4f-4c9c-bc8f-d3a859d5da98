/**
 * Value in cents eg. a CentValue of 1000 equals 10.00
 */
export type CentValue = number;

/**
 * Convert a number to a `CentValue`
 * @param numberValue Number to convert.
 * @returns Returns `CentValue`.
 */
export const toCents = (numberValue: number): CentValue => numberValue * 100;

/**
 * Convert a `CentValue` to a regular number.
 * @param centsValue `CentValue` to convert.
 * @returns Returns a number.
 */
export const toNumber = (centsValue: CentValue): number => centsValue / 100;
