import { DeductionType } from "../quote/raw-quote";
import { RawQuoteLineItemType } from "../quote/raw-quote-line-item";

export type ExtendedDeductionType = DeductionType | "greenTechnologyStandard";

export const deductionRate: Record<ExtendedDeductionType, number> = {
  rot: 0.5,
  rut: 0.5,
  greenTechnology: 0.5,
  greenTechnologyStandard: 0.515,
};

export function isDeductionApplicable(
  deductionType: ExtendedDeductionType,
  lineType: RawQuoteLineItemType,
) {
  switch (deductionType) {
    case "greenTechnology":
    case "greenTechnologyStandard":
      return lineType === "labor" || lineType === "material";
    default:
      return lineType === "labor";
  }
}
