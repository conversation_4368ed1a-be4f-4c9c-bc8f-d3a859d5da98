import { DocumentReference, Timestamp } from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";

export const InstitutionRequestStatuses = [
  "pending",
  "starting",
  "running",
  "waitingForBankId",
  "failed",
  "completed",
] as const;

type InstitutionRequestStatusTuple = typeof InstitutionRequestStatuses;

/**
 * Status of the institution request.
 *
 * - `pending` The request is scheduled but have not yet started.
 * - `starting` The request have been recieved and is preparing to be processed.
 * - `running` The request is valid and is currently being processed.
 * - `waitingForBankId` The request is waiting for BankID to complete. The user needs to sign a request in the BankID app.
 * - `failed` The request failed.
 * - `completed` The request has been completed successfully.
 */
export type InstitutionRequestStatus = InstitutionRequestStatusTuple[number];

export interface InstitutionRequest extends FirestoreModel {
  createTime?: Timestamp;

  /**
   * Name of the function to execute.
   */
  type: string;

  /**
   * Reference to the user that initiated the request.
   */
  requester: DocumentReference;

  /**
   * Current status of the request.
   */
  status: InstitutionRequestStatus;

  /**
   * Any errors that have occurred during the request.
   */
  errors?: InstitutionRequestError[];

  /**
   * Sensitive data used by the request function.
   * This data is emmediately deleted when the request starts.
   */
  sensitive: InstitutionRequestInput;

  /**
   * Input to the request.
   */
  input: InstitutionRequestInput;
}

export interface InstitutionRequestInput {
  [key: string]: unknown;
}

export interface InstitutionRequestError {
  message: string;
  fatal: boolean;
  screenshotUrl?: string;
  snapshotUrl?: string;
}
