import { FirestoreModel } from "../firestore_model";
import {
  DocumentReference,
  Timestamp,
  collection,
  doc,
  getDoc,
  getFirestore,
} from "firebase/firestore";
import { UserRoleMap } from "../user/user-role";
import { Job } from "../job/job";
import { Invoice } from "../invoice/invoice";
import { QuoteStatus } from "../quote/quote";
import { Company } from "../company/company";
import { createConverter } from "../firestore-converter";
import { AlertMessage } from "../../components/common/MessageBanner";
import { ServiceTypes } from "../../config/constants";
import { RichMetadata } from "./rich-metadata";
import { memoize } from "lodash";

export type PartnerSegment = "ev-chargers" | "marketplace" | "heat-pumps";

export interface Partner extends FirestoreModel {
  createTime: Timestamp;

  /** @deprecated Use `displayName` or `legalName` instead when possible */
  name: string;
  displayName?: string;
  legalName?: string;

  users: UserRoleMap;

  /**
   * Partner's logo displayed in quotes and invoices.
   */
  logo?: string;

  /**
   * Partner's logo displayed in attachments.
   */
  smallLogo?: string;

  /**
   * Booking terms displayed in quotes and invoices.
   * Supports markdown.
   */
  terms?: string;

  /**
   * Boolean indicating if API access is enabled.
   */
  apiEnabled?: boolean;

  /**
   * Boolean indicating if partner can register new orders.
   * @default true
   */
  orderCreationEnabled?: boolean;

  /**
   * Settings applied to jobs created by this partner.
   */
  jobSettings?: PartnerJobSettings;

  /**
   * Settings applied to the API.
   */
  apiSettings: PartnerAPISettings;

  /**
   * If set, overrides information on invoices.
   */
  billingSettings?: PartnerBillingSettings;

  /**
   * SendGrid confirmation email template id.
   */
  confirmationEmailTemplateId?: string;

  /**
   * Confirmation sms template should used with Mustache
   * in order to replace customer name and partner name
   */
  confirmationSms?: string;

  segment: PartnerSegment;

  welcomeChatMessage: string;

  /**
   * Users who receive emails for invoices.
   */
  financeAdministrators?: DocumentReference[];

  /**
   * Looker studio report url.
   */
  reportUrl?: string;

  /**
   * Map of specific messages to send to the customer.
   */
  messages?: PartnerMessages;

  /**
   * Message to display to the partner
   */
  backofficeMessage?: AlertMessage;

  /**
   * Partner tier.
   * Decides on which level of features the partner has access to.
   */
  tier: PartnerTier;

  /**
   * Networks the partner have access to.
   */
  networks: string[];

  /**
   * Features the partner has access to.
   */
  features: PartnerFeatures;
}

export interface PartnerMessages {
  /**
   * Partner specific job done message to send to the customer
   */
  jobDoneMessage?: string;
}

export interface PartnerJobSettings {
  /**
   * Extra information for craftsman.
   */
  noteAttachments?: RichMetadata[];

  /**
   * Extra attachments to add to the job, visible to both craftsman and customer.
   */
  jobAttachments?: RichMetadata[];

  /**
   * Should the job be invoiced through the partner?
   */
  invoicedThroughPartnership?: boolean;

  /**
   * If set, automatically creates a quote with the given status containing attached articles.
   */
  createQuoteWithStatus?: QuoteStatus | "draft";

  /**
   * If set, all attached articles are marked as prepaid.
   * @default true
   */
  allAttachedArticlesPrepaid?: boolean;

  /**
   * If set, the matched company must have the partner tag set for rules to be applicable.
   * @default false
   */
  requireMatchingCompanyTag?: boolean;

  /**
   * If set, automatically creates an invoice with the given status containing attached articles.
   */
  createInvoiceWithStatus?: Invoice["status"];

  /**
   * If set, only the given quote types are allowed.
   * @note Also affects which invoice types are allowed.
   */
  allowedQuoteTypes?: Job["allowedQuoteTypes"];

  /**
   * Form that needs to be submitted in order to complete the job.
   */
  installationReportForm?: DocumentReference;
}

export interface PartnerAPISettings {
  /**
   * Default services set.
   */
  defaultServices: ServiceTypes[];
}

export interface PartnerBillingSettings {
  legalName: string;
  orgNumber: string;
  billectaCustomerId: string;
  invoiceNetDays: number;

  /**
   * Id to document in Billecta creditors collection.
   */
  billectaCreditorId: string;
}

export interface PartnerFeatures {
  /**
   * If set, the partner can assign installers to jobs directly.
   * Only available for platform partners.
   */
  assignInstallers?: boolean;
}

export enum PartnerTier {
  None = 0,
  Standard,
  Platform,
}

/**
 * Function that checks if a partner is applicable to a company.
 * @param partner Partner to check.
 * @param company Company to check.
 * @returns True if the partner is applicable to the company.
 */
export function isPartnerApplicable(
  partner: Pick<Partner, "reference" | "jobSettings">,
  company: Pick<Company, "tags">,
): boolean {
  if (partner.jobSettings?.requireMatchingCompanyTag) {
    return company.tags?.includes(partner.reference.id) ?? false;
  }
  return true;
}

const converter = createConverter<Partner>();
export const partnersCollection = () =>
  collection(getFirestore(), "partners").withConverter(converter);

/**
 * Returns the document reference for a partner.
 */
export const partnerRef = (partnerId: string | undefined) =>
  partnerId ? doc(partnersCollection(), partnerId) : undefined;

/**
 * Fetches a partner from the database.
 * @param partnerId Id of the partner to fetch.
 * @returns The partner, or undefined if not found.
 */
export const fetchPartner = async (partnerId: string | undefined) => {
  const ref = partnerRef(partnerId);
  if (!ref) {
    return undefined;
  }
  const partner = await getDoc(ref);
  return partner.data();
};

export const DonePartnerId = "done";
export const DonePartnerReference = memoize(() => {
  const ref = partnerRef(DonePartnerId);
  if (!ref) {
    throw new Error("Done partner not found");
  }
  return ref;
});

/**
 * Returns true if the given id is not the `done` partner.
 * Used to filter out the `done` partner from arrays.
 */
export function notDone(id: string) {
  return id !== DonePartnerId;
}

export function isPlatformPartner(partner: Partner | undefined) {
  if (!partner) {
    return false;
  }
  return partner.tier >= PartnerTier.Platform;
}
