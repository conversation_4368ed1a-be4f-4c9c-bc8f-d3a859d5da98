import { Timestamp, DocumentReference, FieldValue } from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";

/**
 * The report of a completed partnership installation.
 * Created when a craftsman marks a partnership job as completed and fills out a form for how the installation was completed.
 * Used in partnerships where the partner invoices the customer themselves instead of through Done.
 * Examples: Evify, Elbilsvaruhuset.
 */
export interface CompletedPartnerInstallation
  extends FirestoreModel,
    CompletedPartnerInstallationInput {
  createTime: Timestamp;
  partnership: string;

  /** The time the installation was reported. Usually same as createTime */
  reportedTime: Timestamp;
  /** The company user which reported this installation. */
  reportedBy: DocumentReference;

  job: DocumentReference;
  jobReferenceNumber: string;

  customer: DocumentReference;
  customerName: string;

  company: DocumentReference;
  companyName: string;

  /** Set when a self-invoice has been created for paying the craftsman for this installation. */
  selfInvoiceCreateTime?: Timestamp;

  /** Set when the partner has been invoiced for this installation. */
  partnerInvoiceCreateTime?: Timestamp;
}

export interface CompletedPartnerInstallationInput {
  createTime: FieldValue;
  partnership: string;
  partnerReferenceNumber: string;

  installationParameters: any;

  /** The time the installation was reported. Usually same as createTime */
  reportedTime: FieldValue;
  /** The company user which reported this installation. */
  reportedBy: DocumentReference;

  job: DocumentReference;
  jobReferenceNumber: string;

  customer: DocumentReference;
  customerName: string;

  company: DocumentReference;
  companyName: string;

  /** Set when a self-invoice has been created for paying the craftsman for this installation. */
  selfInvoiceCreateTime?: FieldValue;

  /**
   * The invoice number (alphanumeric) referring to an invoice in an external billing system used to pay out this company for this installation.
   * Initially null, before installation has been invoiced to partner, to enable filtering of installations which have not been invoiced.
   */
  selfInvoiceNumber: string | null;

  /**
   * Set when a draft invoice have been created in Billecta.
   */
  selfInvoiceBillectaId?: string;

  /** Set when the partner has been invoiced for this installation. */
  partnerInvoiceCreateTime?: FieldValue;

  /**
   * The invoice number (alphanumeric) referring to an invoice in an external billing system used to invoice the partner for this installation.
   * Initially null, before installation has been invoiced to partner, to enable filtering of installations which have not been invoiced.
   */
  partnerInvoiceNumber: string | null;

  /** Used internally by Done to signify notes */
  doneNotes?: string;
}
