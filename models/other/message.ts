import { Timestamp, DocumentReference } from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { InvoiceBase } from "../invoice/invoice";
import { RichMetadata } from "./rich-metadata";

export interface ChatMessage extends FirestoreModel {
  createTime: Timestamp;
  sender: DocumentReference;
  type:
    | "call"
    | "text"
    | "offer"
    | "missed-call"
    | "declined-call"
    | "image"
    | "video"
    | "file"
    | "quote"
    | "invoice"
    | "cancelled-booked-call"
    | "cancelled-scheduled-work-time"
    | "info";

  body?: string;
  receivingUser?: DocumentReference;
  metadata?: RichMetadata[];
}

export interface QuoteChatMessage extends ChatMessage {
  status?: string;
  declineReason?: string;
  declineReasonOther?: string;
  amountAfterTaxDeductions?: number;
  amountIncVat?: number;
  fileRef?: string;
  filename?: string;
  deductionTypes?: Array<"rot" | "rut">;
}

export interface ImageChatMessage extends ChatMessage {
  hasThumbnail?: boolean;
  imageRef?: string;
  thumbRef?: string;
}

export interface VideoChatMessage extends ChatMessage {
  thumbRef?: string;
}

export interface FileChatMessage extends ChatMessage {
  hasThumbnail?: boolean;
  fileRef?: string;
  filename?: string;
}

export interface CallChatMessage extends ChatMessage {
  callingUser: DocumentReference;
  receivingUser: DocumentReference;
  callerFeedback?: DocumentReference;
  receiverFeedback?: DocumentReference;
  callerSkippedFeedback?: boolean;
  receiverSkippedFeedback?: boolean;
  endTime?: Timestamp | null;
  startTime?: Timestamp | null;
}

export interface InvoiceChatMessage extends ChatMessage {
  invoice?: InvoiceChatMessagePayload;
}

export interface InvoiceChatMessagePayload extends InvoiceBase, FirestoreModel {
  createTime: Timestamp;
}
