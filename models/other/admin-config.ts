import { Timestamp } from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { LabeledValue } from "../../config/constants";
import { PartnerSegment } from "./partner";

export interface AdminAppConfiguration extends FirestoreModel {
  createTime: Timestamp;
  serviceAvailability: { [service: string]: ServiceAvailability };
  serviceCities: string[];
  tags: { [id: string]: Tag };
  areas: string[];
  segments: string[] | PartnerSegment[];
  autoOfferConfiguration: {
    timeIntervalInSeconds: number;
    enabled: boolean;
    numberOfCompanies: number;
    maxRangeInKm: number;
    initialDelayInSeconds: number;
  };
}

export interface ServiceAvailability extends FirestoreModel {
  active: boolean;
  serviceCities: string[];
}

export interface Tag {
  name: string; //For display purposes
  intercomId?: string;
  show?: TagVisibilityType[];
  requiredForAutoOffer?: string[];
}

type TagVisibilityType = "jobs" | "companies";

export function tagToLabeledValue(
  tags?: {
    [id: string]: Tag;
  },
  type: TagVisibilityType[] = ["jobs", "companies"],
): LabeledValue<string>[] {
  if (!tags) return [];

  return Object.entries(tags)
    .filter(([_, value]) => type.some((e) => value.show?.includes(e)))
    .map(
      ([key, value]) =>
        ({
          value: key,
          label: value.name,
        }) as LabeledValue<string>,
    );
}
