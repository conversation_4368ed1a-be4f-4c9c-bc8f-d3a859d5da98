import {
  addDoc,
  collection,
  DocumentReference,
  query,
  Timestamp,
  orderBy,
  getFirestore,
  limit,
} from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { createConverter } from "../firestore-converter";
import removeUndefined from "../../lib/remove-undefined";
import { CompletedPartnerInstallation } from "../installation-report/installation-report";
export interface InvoicingBatch extends FirestoreModel {
  createTime: Timestamp;
  status: InvoicingBatchStatus;
  createdBy: DocumentReference;
  partner: DocumentReference;
  reports: DocumentReference<CompletedPartnerInstallation>[];

  /** If true, the batch will only create draft invoices. */
  draftOnly?: boolean;

  progress: {
    total: number;
    completed: number;
    failed: number;
  };
  errors?: InvoicingBatchError[];
}

export type InvoicingBatchStatus =
  | "pending"
  | "processing"
  | "completed"
  | "failed";

interface InvoicingBatchError {
  error: string;
  affectedReports: DocumentReference[];
}

const converter = createConverter<InvoicingBatch>();

const invoicingBatchesCollection = (partnerRef: DocumentReference) =>
  collection(getFirestore(), partnerRef.path, "invoicingBatches").withConverter(
    converter,
  );

export const createInvoicingBatch = async (
  partner: DocumentReference,
  createdBy: DocumentReference,
  reports: DocumentReference[],
  draftOnly?: boolean,
) =>
  await addDoc(
    invoicingBatchesCollection(partner),
    removeUndefined({
      createTime: Timestamp.now(),
      status: "pending",
      reports,
      partner,
      createdBy,
      draftOnly,
      progress: {
        total: reports.length,
        completed: 0,
        failed: 0,
      },
    }) as InvoicingBatch,
  );

export const latestInvoicingBatchQuery = (
  partner: DocumentReference,
  numberOfBatches = 1,
) =>
  query(
    invoicingBatchesCollection(partner),
    orderBy("createTime", "desc"),
    limit(numberOfBatches),
  );
