import { collection, getFirestore, query } from "firebase/firestore";
import { AssistantSession } from "./assistant-session";
import { createConverter } from "../firestore-converter";

const sessionsConverter = createConverter<AssistantSession>();

export const sessionsCollection = () =>
  collection(getFirestore(), "assistantSessions").withConverter(
    sessionsConverter,
  );

export const sessionsQuery = () => query(sessionsCollection());
