import { DocumentReference, Timestamp } from "firebase/firestore";
import { FirestoreModel } from "../firestore_model";
import { User } from "../user/user";
import {
  LiveRequest,
  LiveRequestClientMessage,
} from "../live-requests/live-request";
import { LabeledValue } from "../../config/constants";

type AssistantId = "ReportFormAssistant";

export const availableAssistants: readonly LabeledValue<AssistantId>[] = [
  { label: "ReportFormAssistant", value: "ReportFormAssistant" },
] as const;

/**
 * A session with an AI assistant.
 * Should be extended by a concrete assistant session.
 */
export interface AssistantSession extends LiveRequest, FirestoreModel {
  /**
   * Our identifier for the assistant.
   */
  assistant: AssistantId;

  /**
   * The current status of the assistant session.
   */
  status: "pending" | "running" | "cancelled" | "completed" | "failed";

  /**
   * The user that requested the assistant session.
   */
  requester: DocumentReference<User>;

  /**
   * Messages sent to and from the assistant.
   * Contains the entire conversation, including internally sent messages.
   */
  messages: AssistantSessionMessage[];

  /**
   * The subject of the assistant session.
   */
  subject?: DocumentReference;

  /**
   * The latest input to the assistant.
   * Can not be set if the assistant is running.
   */
  input?: string;

  /**
   * The latest output from the assistant.
   */
  output?: unknown;

  /**
   * The thread id of the assistant session.
   */
  threadId?: string;

  /**
   * The latest run id of the assistant session.
   */
  runId?: string;
}

/**
 * A message sent to and from the assistant.
 */
export interface AssistantSessionMessage {
  id: string;
  createTime: Timestamp;

  content: string;
  role: "user" | "assistant";

  clientMessages?: LiveRequestClientMessage[];
}
