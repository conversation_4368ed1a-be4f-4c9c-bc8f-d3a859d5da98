import { FirestoreModel } from "../firestore_model";
import { FixedPriceJobParameters, FixedPriceProduct } from "./job";
import { roundToTwo } from "../../lib/round-to-two";
import {
  FieldValue,
  getFirestore,
  getDoc,
  doc,
  collection,
} from "firebase/firestore";
import formatAmount from "../../lib/format-amount";

export type FixedPriceBreakDown = {
  material: string;
  startAndTravel: string;
  laborAfterDeduction: string;
  labor: string;
  laborDeduction: string;
  grossTotal: string;
  customerToPay: string;
};

export interface FixedPriceJob extends FirestoreModel {
  termsFileName?: string;
  createTime: FieldValue;
  description: string;
  checkListTitle: string;
  checkListSubTitle: string;
  availableLocations: Array<string>;
  emoji: string;
  autoMessage: string;
  order: number;
  formLink: string;
  rotDeductible: boolean;
  services: Array<string>;
  title: string;
  subtitle: string;
  checkListNote: string;
  checkList: Array<FixedPriceCheckListItem>;
  hasCheckList: boolean;
  imageUrl: string;
  collaborations?: string[];
  deductionType: "rot" | "rut" | "greenTechnology" | null;
  priceBreakdown: {
    materialBeforeDeduction?: number;
    laborBeforeDeduction?: number;
    startAndTravelCost?: number;
    totalDeduction?: number;
    totalCustomerPriceAfterDeduction?: number;
    craftsmanProceeds?: number;
  };
  // TODO: remove deprecated calculations
  /** @deprecated Use the priceBreakdown instead. */
  materialCosts: number;
  /** @deprecated Use the priceBreakdown instead. */
  startCost: number;
  /** @deprecated Use the priceBreakdown instead. */
  priceAfterDeductibles: number;
  /** @deprecated Use the priceBreakdown instead. */
  labor: number;
  /** @deprecated Use the priceBreakdown instead. */
  craftsmanProceeds?: number;
}

interface FixedPriceCheckListItem {
  title: string;
  content: string;
}

export async function fetchFixedPriceJobData(
  fixedPriceJobs: FixedPriceJobParameters[],
): Promise<FixedPriceJobQuantity[]> {
  return Promise.all(
    fixedPriceJobs.map(async (parameters) => {
      const snapshot = await getDoc(
        doc(collection(getFirestore(), "fixed-price-jobs"), parameters.id),
      );

      if (!snapshot.data) {
        throw `Cannot fetch fixed price job data for id ${parameters.id}`;
      }
      return {
        products: parameters.products,
        job: snapshot.data()! as FixedPriceJob,
        count: parameters.quantity,
      };
    }),
  );
}

export function customerToPayFixedPriceJobs(
  fixedPriceJobsPayload: FixedPriceJobQuantity[],
): string {
  const customerToPay = fixedPriceJobsPayload
    .map((e) => e.count * (e.job.priceAfterDeductibles + e.job.materialCosts))
    .reduce(
      (total, customerToPayForFixedPriceJob) =>
        total + customerToPayForFixedPriceJob,
    );

  return (
    formatAmount(
      roundToTwo(customerToPay + fixedPriceJobsPayload[0].job.startCost),
    ) || "n/a"
  );
}

export type FixedPriceJobQuantity = {
  job: FixedPriceJob;
  count: number;
  products?: Array<FixedPriceProduct>;
};
