import {
  and,
  collection,
  doc,
  getFirestore,
  or,
  orderBy,
  query,
  updateDoc,
  where,
} from "firebase/firestore";
import { isDefined } from "../../lib/filter-undefined";
import { createConverter } from "../firestore-converter";
import { Job } from "./job";
import { DonePartnerReference, partnerRef } from "../other/partner";

const converter = createConverter<Job>();

export const jobsCollection = () =>
  collection(getFirestore(), "jobs").withConverter(converter);

export const jobReference = (jobId: string) => doc(jobsCollection(), jobId);

export const jobNotBilledQuery = () =>
  query(
    jobsCollection(),
    or(
      where("events.craftsmanBilled", "==", null),
      where("events.partnerBilled", "==", null),
      where("events.invoiceSent", "==", null),
    ),
    orderBy("events.jobDone", "desc"),
  );

export const partnerJobNotBilledQuery = (partner: string) =>
  query(
    jobsCollection(),
    and(
      where("merchant", "==", partner<PERSON>ef(partner)),
      or(
        where("events.craftsmanBilled", "==", null),
        where("events.partnerBilled", "==", null),
        where("events.invoiceSent", "==", null),
      ),
    ),
    orderBy("events.jobDone", "desc"),
  );

export const openJobsQuery = (
  partner?: string,
  range?: { from: Date; to: Date },
) =>
  query(
    jobsCollection(),
    ...[
      where("status", "==", "open"),
      where("merchant", "==", partnerRef(partner) ?? DonePartnerReference()),
      range?.from && where("events.scheduledWorkStartTime", ">=", range.from),
      range?.to && where("events.scheduledWorkStartTime", "<=", range.to),
    ].filter(isDefined),
  );

export const setJobPrerequisitesFulfilled = async (
  jobId: string,
  prerequisitesFulfilled: boolean,
) => {
  const jobRef = jobReference(jobId);
  await updateDoc(jobRef, {
    requiresPrerequisitesFulfilled: prerequisitesFulfilled,
  });
};
