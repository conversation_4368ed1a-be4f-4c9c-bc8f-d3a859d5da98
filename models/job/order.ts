import { DocumentReference, FieldValue, Timestamp } from "firebase/firestore";
import { BackofficeUploadJobData } from "./job";
import { FirestoreModel } from "../firestore_model";
import { LabeledValue } from "../../config/constants";

export type OrderType = "warranty" | "installation";
export type OrderCategory = "ev-charger" | "battery" | "other";

export type OrderTypeTag = `type:${OrderType}`;
export type OrderCategoryTag = `category:${OrderCategory}`;

export const OrderTypes: LabeledValue<OrderTypeTag>[] = [
  { label: "Warranty", value: "type:warranty" },
  { label: "Installation", value: "type:installation" },
];

export const OrderProductCategories: LabeledValue<OrderCategoryTag>[] = [
  { label: "EV Charger", value: "category:ev-charger" },
  { label: "Battery", value: "category:battery" },
  { label: "Other", value: "category:other" },
];

export interface Order extends FirestoreModel {
  createTime: Timestamp;
  createdBy: DocumentReference;
  data: BackofficeUploadJobData;
  partner: DocumentReference;
  status: "pending" | "accepted" | "rejected";
  jobReference?: DocumentReference;
  tempImages?: string[];
  typeTag?: OrderTypeTag;
}
export interface OrderInput extends Omit<Order, "reference" | "createTime"> {
  createTime: FieldValue;
  status: "pending";
}
