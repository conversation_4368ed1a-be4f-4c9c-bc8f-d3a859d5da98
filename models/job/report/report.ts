import { DocumentReference } from "@firebase/firestore";
import { FirestoreModel } from "../../firestore_model";
import { createConverter } from "../../firestore-converter";
import { collection, where, query } from "firebase/firestore";
import { jobReference } from "../job-provider";
import { JsonObject } from "type-fest";
import { JSONSchemaType } from "ajv";

export interface JobReport extends FirestoreModel {
  status: "draft" | "submitted" | "accepted" | "rejected";

  createdBy: DocumentReference;
  company: DocumentReference;
  form: DocumentReference;

  type: "installation" | "precheck";

  /**
   * The submitted data.
   */
  data: { [key: string]: unknown };

  /**
   * Cache of the form schema used to validate the data.
   */
  cache?: {
    formSchema: JSONSchemaType<JsonObject>;
    uiSchema: JsonObject & { "ui:order"?: string[] };
  };
}

export interface FormConfiguration extends FirestoreModel {
  type: "installation" | "precheck";

  /**
   * A JSON schema describing a form.
   */
  formSchema: { [key: string]: unknown };

  /**
   * A schema describing how the form should be displayed.
   */
  uiSchema?: { [key: string]: unknown };
}

const converter = createConverter<JobReport>();

export const reportsCollection = (jobId: string) =>
  collection(jobReference(jobId), "reports").withConverter(converter);

export const acceptedReportsQuery = (jobId: string) =>
  query(reportsCollection(jobId), where("status", "==", "accepted"));
