import {
  collection,
  collectionGroup,
  getFirestore,
  query,
} from "firebase/firestore";
import { createConverter } from "../../firestore-converter";
import { FormConfiguration } from "./report";

const formsConverter = createConverter<FormConfiguration>();

// Any read or write to this collection is now type checked.
export const formsCollectionGroup = () =>
  collectionGroup(getFirestore(), "forms").withConverter(formsConverter);

export const formsCollection = (partnerId: string) =>
  collection(getFirestore(), "partners", partnerId, "forms").withConverter(
    formsConverter,
  );

// Typed query
export const formsQuery = (partnerId?: string) =>
  partnerId ? query(formsCollection(partnerId)) : query(formsCollectionGroup());
