import { DocumentReference } from "firebase/firestore";
import { Article, EmbeddableArticle } from "../article-groups/article";
import { RichMetadata } from "../other/rich-metadata";

export type JobArticle = JobStandardArticle | JobCustomArticle;

/**
 * An article that is ordered for a job.
 */
export interface JobStandardArticle extends RichMetadata {
  article: DocumentReference<Article>;
  quantity: number;
  title: string;
}

/**
 * A custom article with specific settings.
 */
export interface JobCustomArticle extends EmbeddableArticle, RichMetadata {
  quantity: number;
  title: string;
}

export function isCustomArticle(
  jobArticle: unknown,
): jobArticle is JobCustomArticle {
  return (jobArticle as JobCustomArticle)?.prices !== undefined;
}

export function isStandardArticle(
  jobArticle: unknown,
): jobArticle is JobStandardArticle {
  return (jobArticle as JobStandardArticle)?.article !== undefined;
}
