import { FirestoreModel } from "../firestore_model";
import { JobCache } from "./job_cache";

import {
  DocumentReference,
  getFirestore,
  Timestamp,
  collection,
  doc,
} from "firebase/firestore";
import { User, UserInput } from "../user/user";
import { Article } from "../article-groups/article";
import { RichMetadata } from "../other/rich-metadata";
import { Company } from "../company/company";
import { JobImage } from "./job-image";
import { JobArticle } from "./job-article";
import { AlertMessage } from "../../components/common/MessageBanner";
import { CalculationOptions } from "../quote/calculation-options";
import { createConverter } from "../firestore-converter";
import { CentValue } from "../finance/cent-value";

export interface Job extends FirestoreModel {
  slackMessageId?: string;
  createdBy?: DocumentReference;
  reference: DocumentReference;
  fixedPriceJobs?: FixedPriceJobParameters[];
  description?: string;
  cache?: JobCache;
  company?: DocumentReference<Company>;
  createTime?: Timestamp;
  customer?: DocumentReference<User>;
  events?: JobEvents;
  copiedTo?: DocumentReference[];
  copiedFrom?: DocumentReference;
  internalComment?: string;
  isDemo?: boolean;
  referenceNumber?: string;
  lastCommunicatedTime?: Timestamp;
  lastRead?: Array<Map<string, Timestamp>>;
  preferredCallTime?: any;
  preferredStartDate?: any;
  scheduledCallTime?: Timestamp;
  scheduledWorkStartTime?: Timestamp;
  lastInternalNote?: Timestamp;
  services?: any[];
  tags?: any[];
  operationTags?: any[];
  status?: JobStatus;
  utm_campaign?: string;
  // Image references comes from mobile app native booking flow
  // These strings contains paths on firestore under jobs/images
  images?: JobImage[];
  utm_content?: string;
  utm_source?: string;
  budget?: string;
  companySubscribers?: DocumentReference[];
  closeReason?: string;
  userCloseReasonInput?: string;
  closedBy?: DocumentReference;
  sendAutomaticRawQuoteDraft?: boolean;
  discountCode?: string;
  externalReference?: string;
  webReferrer?: string;

  /** When set, means that the customer will not receive an invoice for this job through the Done app. */
  invoicedThroughPartnership?: boolean;

  /** When set, means that there are prerequisites that need to be fulfilled before the job be performed. */
  requiresPrerequisitesFulfilled?: boolean;

  /** Partner-specific notice information. It provides relevant information or updates intended for the partner. */
  partnerNotice?: AlertMessage;

  /** Attachments on the job. */
  attachments?: Attachment[];

  /**
   * Article groups that can be choosen when creating quotes.
   */
  articleGroups?: JobArticleGroup[];

  /** Articles that are ordered for the job. */
  articles?: JobArticle[];

  /** Craftsman proceeds for the job. */
  craftsmanProceeds?: JobCraftsmanProceeds;

  /** @deprecated  Use `articles` instead. */
  products?: Product[];

  /**
   * If set, only the given quote types are allowed.
   * @note Also affects which invoice types are allowed.
   */
  allowedQuoteTypes?: JobQuoteType[];

  /** Last message from non automated messages */
  lastChatMessage?: string;

  /** Reference to document containing a schema for the installation report. */
  installationReportForm?: JobReportForm;

  /** Reference to document containing a schema for the customer prechecks report. */
  prechecksReportForm?: JobReportForm;

  /** Id of the merchant that the job originates from */
  merchant: DocumentReference;

  /** Id of the network that manages this job */
  network: DocumentReference;

  /**
   * Id of partner that is invoiced for this job.
   * @deprecated Refer to `merchant` or `network` instead.
   */
  partner?: string;
}

export interface JobCraftsmanProceeds {
  isEstimation: boolean;
  value?: CentValue;
}

export interface JobReportForm {
  /** Reference to a ReportForm document */
  reference: DocumentReference;
}

export interface JobArticleGroup {
  title: string;
  calculationOptions: CalculationOptions;
  reference: DocumentReference;
  partner?: DocumentReference;
}

export type JobQuoteType = "normal" | "articles" | "fixedPrice" | "pdf";

export type JobStatus = "pending" | "inbox" | "open" | "closed" | "trashed";
export type DeductionType = "rot" | "rut" | "greenTechnology";

/**
 * An attachment on a job.
 */
export interface Attachment extends RichMetadata {
  /**
   * @deprecated Article attachments are deprecated, use `articles` property on job instead.
   */
  article?: DocumentReference<Article>;

  /**
   * @deprecated Article attachments are deprecated, use `articles` property on job instead.
   */
  quantity?: number;
}

export interface FixedPriceJobParameters {
  id: string;
  quantity: number;
  products?: Array<FixedPriceProduct>;
}

export interface JobEvents {
  customerLoggedIn?: Timestamp;
  partnershipPrechecksReported: Timestamp | null;
  matchInitiated: Timestamp | null;
  companyMatched?: Timestamp;
  firstMessageSentByCraftsman: Timestamp | null;
  callSkipped?: Timestamp;
  callScheduled?: Timestamp;
  scheduledCallCancelled?: Timestamp;
  callMade?: Timestamp;
  offerSent?: Timestamp;
  offerAccepted?: Timestamp;
  offerDeclined?: Timestamp;
  offersChecked?: Timestamp;
  workTimeScheduled: Timestamp | null;
  scheduledWorkTimeCancelled?: Timestamp;
  partnershipInstallationReported: Timestamp | null;
  jobDone: Timestamp | null;
  invoiceBasisSent?: Timestamp;
  invoiceSent?: Timestamp;
  invoicePaid?: Timestamp;
  craftsmanBilled?: Timestamp;
  partnerBilled?: Timestamp;
  companyReviewed?: Timestamp;
  jobClosed?: Timestamp;
  jobCancelled?: Timestamp;
  companyClosedProject?: Timestamp;
  customerClosedProject?: Timestamp;
  prerequisitesPending?: Timestamp;
  prerequisitesFulfilled?: Timestamp;
}

type InitialEventTypes =
  | "jobDone"
  | "workTimeScheduled"
  | "firstMessageSentByCraftsman"
  | "matchInitiated"
  | "partnershipPrechecksReported";

export const initialJobEvents: Pick<JobEvents, InitialEventTypes> = {
  jobDone: null,
  workTimeScheduled: null,
  firstMessageSentByCraftsman: null,
  matchInitiated: null,
  partnershipPrechecksReported: null,
};

export interface FixedPriceProduct {
  units: number;
  articleId?: string;
  ean?: string;
  title?: string;
  imageURL?: string;
  webURL?: string;
}

export interface Product {
  title: string;
  description?: string;
  image?: string; // Url to an image.
  url?: string; // Url that will be opened when user opens the item.
  quantity?: number;
}

export interface BackofficeUploadJobData {
  customerData: UserInput;
  jobData: UploadJobData;
}

export interface RawImage {
  description?: string;
  url: string;
}

export interface UploadJobData {
  noteForCraftsman?: string;
  externalReference: string;
  description: string;
  articles?: JobArticle[];
  images?: RawImage[];
  // Pre-installation form provided by partner
  pdfLink?: {
    url: string;
    title: string;
    description: string;
    image: string;
    logo: string;
  };
}

export type JobTags =
  | "express"
  | "ampy"
  | "chargehome"
  | "fixedPrice"
  | "vvsochbad"
  | "k-rauta"
  | "elbilsvaruhuset"
  | "leadme"
  | "pickyliving"
  | "evify"
  | "laddboxbolaget"
  | "byoc"
  | "bygglovsexperten"
  | "vattenfall"
  | "zolartech"
  | "evfuel"
  | "eways"
  | "voltplan"
  | "ev-chargers"
  | "marketplace"
  | "laddstoppet"
  | "done"
  | "hantverkare.nu"
  | "GT 2023"
  | "GT 2024"
  | "evobox"
  | "heat-pumps"
  | "demo-partner"; // for tests

const converter = createConverter<Job>();

const jobsCollection = () =>
  collection(getFirestore(), "jobs").withConverter(converter);

export const jobReference = (id: string | undefined) =>
  id ? doc(jobsCollection(), id) : undefined;
