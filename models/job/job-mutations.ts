import removeUndefined from "../../lib/remove-undefined";
import { Company } from "../company/company";
import { partnerRef } from "../other/partner";
import { Job } from "./job";
import { deleteField, Timestamp, updateDoc } from "firebase/firestore";

/**
 * Removes company from job and resets all related fields.
 * @param job - The job to remove the company from.
 */
export const removeCompanyFromJob = async (job: Pick<Job, "reference">) =>
  updateDoc(job.reference, {
    company: null,
    "cache.companyName": null,
    "events.companyMatched": null,
    "events.workTimeScheduled": null,
    status: "inbox",
    sendAutomaticRawQuoteDraft: null,
    companySubscribers: deleteField(),
    scheduledWorkStartTime: deleteField(),
  });

/**
 * Assigns a company to a job and updates related fields.
 * @param job - The job to assign the company to.
 * @param company - The company to assign to the job.
 */
export const assignCompanyToJob = async (
  job: Pick<Job, "reference">,
  company: Company,
  scheduledWorkStartTime?: Date,
) =>
  await updateDoc(
    job.reference,
    removeUndefined({
      company: company.reference,
      status: "open",
      scheduledWorkStartTime: scheduledWorkStartTime
        ? Timestamp.fromDate(scheduledWorkStartTime)
        : undefined,
      "events.companyMatched": Timestamp.now(),
      "events.workTimeScheduled": scheduledWorkStartTime
        ? Timestamp.now()
        : undefined,
      "cache.companyName": company.name,
    }),
  );

/**
 * Sets the network for a job.
 * @param job - The job to set the network for.
 * @param network - The network to set for the job.
 */
export const setJobNetwork = async (
  job: Pick<Job, "reference">,
  network: string,
) => updateDoc(job.reference, { network: partnerRef(network) });
