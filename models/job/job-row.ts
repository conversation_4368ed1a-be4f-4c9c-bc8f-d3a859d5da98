import { QuerySnapshot } from "firebase/firestore";
import { differenceInDays } from "date-fns";
import { TableRow } from "../../components/common/table/PaginatedDataGrid";
import { Job } from "./job";
import { isDefined } from "../../lib/filter-undefined";

export interface JobRow extends TableRow {
  customerLocation?: string;
  referenceNumber: string;
  createTime?: Date;
  description: string;
  companyName: string;
  customerName: string;
  location: string;
  services: string;
  operationTags: string;
  discountCode: string;
  warning?: boolean;
  tags: string;
  status?: string;
  tag?: string;
  scheduledCallTime?: Date;
  lastCommunicatedTime?: Date;
  utm_source: string;
  utm_content: string;
  utm_campaign: string;
  data: Job;
  externalReference?: string;
  partner?: string;
  lastChatMessage?: string;
}

export function generateJobRow(value?: QuerySnapshot): JobRow[] {
  return (
    value?.docs.map((document) => {
      const job = { ...document.data(), reference: document.ref } as Job;
      return generateRowForJob(job);
    }) ?? []
  );
}

export function generateRowForJob(job: Job): JobRow {
  return {
    paginationCursor: job.createTime!,
    id: job.reference.id,
    createTime: job.createTime?.toDate(),
    customerLocation: job.cache?.customerRegion ?? "",
    externalReference: job.externalReference ?? "",
    referenceNumber: job.referenceNumber ?? "",
    description: job.description ?? "",
    companyName: job.company
      ? (job.cache?.companyName ?? "(cache missing)")
      : "",
    customerName: job.cache?.customerName ?? "(cache missing)",
    location: job.cache
      ? (job.cache?.customerLocation?.postalTown ??
        job.cache.customerLocation?.addressLine ??
        "")
      : "(cache missing)",
    services: (job.services || []).join(", "),
    operationTags: (job.operationTags || []).join(", "),
    discountCode: job.discountCode ?? "",
    tags: (job.tags || []).join(", "),
    scheduledCallTime: job.scheduledCallTime?.toDate(),
    lastCommunicatedTime: job.lastCommunicatedTime?.toDate(),
    scheduledWorkStartTime: job.scheduledWorkStartTime?.toDate(),
    utm_source: job.utm_source ?? "",
    utm_content: job.utm_content ?? "",
    status: job.status,
    utm_campaign: job.utm_campaign ?? "",
    warning: checkWarning(job),
    tag: job?.tags && job?.tags[0],
    data: job,
    partner: job?.partner,
    merchant: job?.merchant?.id ?? "",
    network: job?.network?.id ?? "",
    lastChatMessage: job?.lastChatMessage,
    offerStatus: latest([
      { status: "offerSent", date: job?.events?.offerSent?.toDate() },
      { status: "offerDeclined", date: job?.events?.offerDeclined?.toDate() },
      { status: "offerAccepted", date: job?.events?.offerAccepted?.toDate() },
    ]),
    offerSentAt: job?.events?.offerSent?.toDate(),
    offersCheckedAt: job?.events?.offersChecked?.toDate(),
    invoicedAt: job?.events?.partnerBilled?.toDate(),
    doneAt: job?.events?.jobDone?.toDate(),
  };
}

function latest(dates: { date: Date | undefined; [key: string]: unknown }[]) {
  if (!dates.length) return null;
  return dates
    .filter((item) => isDefined(item.date))
    .sort((a, b) => b.date!.getTime() - a.date!.getTime())[0];
}

function checkWarning(job: Job): boolean {
  if (!job.company) return false;
  if (!job.events?.companyMatched) return false;
  if (job.events?.offerSent) return false;

  const duration = differenceInDays(
    new Date(),
    job.events.companyMatched.toDate(),
  );

  return duration > 14;
}
