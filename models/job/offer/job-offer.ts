import { Timestamp, DocumentReference } from "firebase/firestore";
import { FirestoreModel } from "../../firestore_model";
import { DoneLocation, User } from "../../user/user";
import { FixedPriceJobParameters, JobTags } from "../job";
import { Company } from "../../company/company";

export interface JobOffer extends FirestoreModel {
  createTime: Timestamp;
  status: string;
  jobStatus?: string;
  description?: string;
  preferredCallTime?: string;
  budget?: string;
  referenceNumber: string;
  preferredStartDate?: string;
  location?: DoneLocation;
  services?: string[];
  orgNo?: string;
  tags?: JobTags[];
  images?: string[];
  fixedPriceJobs?: FixedPriceJobParameters[];
  offeredTo?: DocumentReference[];
  offeredAt: { [key: string]: Timestamp };
  lastSeenAt?: { [key: string]: Timestamp };
  replies: { [key: string]: JobOfferReply | null };
  cloudQueueIds: {
    noAnswerIn6Hours?: { qId: string; companyId: string }[];
  };
  suggestedCompanies?: SuggestedCompanies;

  // Queued offers that are automatically sent to companies.
  queuedOffers?: { [companyId: string]: QueuedOffer };

  /** Id of the merchant that the job originates from */
  merchant: DocumentReference;

  /** Id of the network that manages this job */
  network: DocumentReference;
}

export interface QueuedOffer {
  company: DocumentReference<Company>;
  status: "scheduled" | "sent" | "cancelled";
  taskId: string;
  scheduledTime: Timestamp;
  cancelledTime?: Timestamp;
  cancelledBy?: DocumentReference<User>;
}

/**
 * Suggested companies with distance information.
 */
export interface SuggestedCompanies {
  [companyId: string]: CompanySuggestion;
}

export interface CompanySuggestion {
  // Source of suggestion
  suggestedSource: "drivingDistance";
  // Time of suggestion
  createTime: Timestamp;
  // Drive distance in meters, from company location to customer location
  distanceInMeters: number;
  // Driving time in seconds, from company location to customer location
  drivingTimeInSeconds: number;
}

export type JobOfferAnswer = "accept" | "decline" | "conditionallyAccepted";

export type JobOfferReply = {
  answer: JobOfferAnswer;
  user: DocumentReference;
  createdAt: Timestamp;
  userName?: string;
  declineReason?: string;
  declineReasonOther?: string;
  acceptConditionReasonOther?: string;
  acceptCondition?: string;
};

export type JobOfferReminderCloudTaskPayload = {
  companyId: string;
  jobId: string;
};
