import { RichMetadata } from "../other/rich-metadata";

export interface JobNote {
  createTime: Date;
  /**
   * The text content of the note.
   */
  content: string;

  /**
   * Additional attachments on the note.
   */
  attachments?: RichMetadata[];

  /**
   * Installation information.
   */
  installationInformation?: InstallationInformation[];
}

export interface LabeledValue {
  label: string;
  value: string;
}

export interface InstallationInformation extends Omit<RichMetadata, "url"> {
  id: string;
  fields: LabeledValue[];
  actions: RichMetadata[];
}
