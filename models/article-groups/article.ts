import {
  doc,
  collection,
  Timestamp,
  orderBy,
  query,
  DocumentReference,
} from "firebase/firestore";
import { FirestoreInput } from "../firestore_model";
import { DeductionType } from "../job/job";
import { LineItemUnitType, RawQuoteLineItemType } from "../quote/quote";
import { articleGroupRef } from "./article-group";
import { Float, SetRequired } from "type-fest";
import { VAT } from "../finance/vat";
import { CentValue } from "../finance/cent-value";
import { createConverter } from "../firestore-converter";
import { DetailedDeductionType } from "../invoice/invoice-line-item";
import { RichMetadata } from "../other/rich-metadata";
import { InstallationInformation } from "../job/job-note";
import { pick } from "lodash";
import removeUndefined from "../../lib/remove-undefined";
import { assertNever } from "../../lib/assert-never";

export interface Article extends EmbeddableArticle {
  reference: DocumentReference<Article>;
  createTime: Timestamp;
  enabled: boolean;
  attachment: AttachmentConfig;
  order: number | null;
  fieldsConfig?: FieldsConfig;

  /**
   * Attachments that should be added to jobs containing this article.
   */
  jobAttachments?: ArticleJobAttachment[];
}

interface AttachmentBase {
  /**
   * The source of the attachment.
   */
  source: "root" | "self";

  /**
   * The ID of the attachment.
   */
  id: string;

  /**
   * The type of the attachment.
   */
  type: string;
}

interface NoteAttachment extends RichMetadata, AttachmentBase {
  type: "note";
}

interface FieldsAttachment extends InstallationInformation, AttachmentBase {
  type: "installation";
}

export type ArticleJobAttachment = NoteAttachment | FieldsAttachment;

/**
 * Sanitizes the attachment fields depending on the type.
 * @param attachment Attachment to sanitize.
 * @returns Sanitized attachment.
 */
export function sanitizeAttachment(
  attachment: ArticleJobAttachment,
): ArticleJobAttachment {
  if (!attachment.type || !attachment.id) {
    throw new Error("Attachment must have a type and an ID");
  }

  const type = attachment.type;

  switch (type) {
    case "note":
      return removeUndefined(
        pick(attachment, [
          "id",
          "type",
          "source",
          "title",
          "description",
          "url",
          "logo",
          "image",
        ]),
      );
    case "installation":
      return removeUndefined(
        pick(attachment, [
          "id",
          "type",
          "source",
          "title",
          "description",
          "logo",
          "image",
          "fields",
          "actions",
        ]),
      );
    default:
      assertNever(type);
  }
}

/**
 * The foundation of an article that is suitable for embedding in other entities.
 */
export interface EmbeddableArticle {
  title: string;
  description?: string;
  deduction?: DeductionType;
  detailedDeductionType?: DetailedDeductionType;
  unitType: LineItemUnitType;
  lineItemType: RawQuoteLineItemType;
  prices: AmountBreakdown;
}

/**
 * Converts a article to an embedable article.
 * @param article The article to convert
 * @returns The embeddable article.
 */
export function toEmbeddable(article: Article): EmbeddableArticle {
  return pick(article, [
    "title",
    "description",
    "deduction",
    "detailedDeductionType",
    "unitType",
    "lineItemType",
    "prices",
  ]);
}

export interface ArticleInput
  extends Omit<Article, "createTime" | "reference">,
    FirestoreInput {}

export interface AmountBreakdown {
  /**
   * Customer price **excluding** VAT and deductions.
   */
  customerPrice: AmountInfo<CentAmount>;

  /**
   * Price billed against partner.
   */
  partnerPrice?: PartnerBillingPriceInfo;

  /**
   * Proceeds for craftsman.
   */
  craftsmanProceeds: AmountInfo;

  /**
   * Proceeds for Done.
   */
  doneProceeds?: AmountInfo;
}

export type ArticlePriceType = keyof Omit<AmountBreakdown, "doneProceeds">;

export interface PartnerBillingPriceInfo extends AmountInfo {
  titlePrefix?: string;
}

export interface AmountInfo<A extends BaseAmount = Amount> {
  amount: A;
  billectaProductId?: string;
}

/**
 * An amount in either cents or a percentage.
 */
export type Amount = CentAmount | PercentageAmount;

interface BaseAmount {
  vat?: VAT;
}

export interface CentAmount extends BaseAmount {
  value?: CentValue;
}

export interface PercentageAmount extends BaseAmount {
  percentage?: Float<number> | undefined;
}

/**
 * Configuration for the fields in quote editor.
 */

export interface FieldsConfig {
  allowDeductionSelection?: boolean;
  allowDescriptionEditing?: boolean;
  allowUnitTypeEditing?: boolean;
  allowUnitPriceEditing?: boolean;
  allowUnitCountEditing?: boolean;
}

export interface AttachmentConfig {
  enabled: boolean;
  quantityToAttach?: number;
  matchRegExp?: RegExpConfig;
}

interface RegExpConfig {
  pattern: string;
  flags?: string;
}

/**
 * Checks if the given amount is a cent amount.
 * @param amount Amount to check.
 * @returns Returns true if the amount is a cent amount.
 */
export function isCentAmount(
  amount: AmountInfo<CentAmount | PercentageAmount> | undefined,
): amount is AmountInfo<SetRequired<CentAmount, "value">> {
  return (amount as AmountInfo<CentAmount>)?.amount?.value !== undefined;
}

/**
 * Checks if the given amount is a percentage amount.
 * @param amount Amount to check.
 * @returns Returns true if the amount is a percentage amount.
 */
export function isPercentageAmount(
  amount: AmountInfo<CentAmount | PercentageAmount> | undefined,
): amount is AmountInfo<SetRequired<PercentageAmount, "percentage">> {
  return (
    (amount as AmountInfo<PercentageAmount>)?.amount?.percentage !== undefined
  );
}

const converter = createConverter<Article>();

/**
 * Returns a reference to the article with the given id.
 * @param groupId Id of the group that the article belongs to.
 * @param id The id of the article.
 * @returns A reference to the article.
 */
export const articleRef = (groupId: string, id: string) =>
  doc(articlesCollectionRef(groupId), id);

export const articlesCollectionRef = (groupId: string) =>
  collection(articleGroupRef(groupId), "articles").withConverter(converter);

/**
 * Query that returns all articles for the given article group.
 * @param groupId The id of the article group.
 * @returns A query that returns all articles for the given article group.
 */
export const articlesQuery = (groupId: string) =>
  query(
    articlesCollectionRef(groupId),
    orderBy("order", "desc"),
    orderBy("title", "asc"),
  );
