import {
  collection,
  doc,
  DocumentReference,
  getFirestore,
  limit,
  orderBy,
  query,
  Timestamp,
  where,
} from "firebase/firestore";
import { ServiceTypes } from "../../config/constants";
import { FirestoreInput, FirestoreModel } from "../firestore_model";
import { Partner } from "../other/partner";
import { CalculationOptions } from "../quote/calculation-options";
import { createConverter } from "../firestore-converter";
import { Company } from "../company/company";

export interface ArticleGroup {
  /**
   * The time when the article group was created.
   */
  createTime: Timestamp;

  /**
   * Timestamp when the article group was or is scheduled to be activated.
   * Date may be in the future.
   */
  startTime?: Timestamp;

  /**
   * Timestamp when the article group was or is scheduled to be deactivated.
   */
  endTime?: Timestamp;

  /**
   * The name of the article group.
   */
  title: string;

  /**
   * The name of the article group used internally.
   */
  name?: string;

  /**
   * Type of article group.
   */
  type: ArticleGroupType;

  /**
   * Parent article group.
   * Only applicable for branched article groups.
   */
  parent?: DocumentReference<ArticleGroup>;

  /**
   * Root article group.
   * Only applicable for branched article groups.
   */
  root?: DocumentReference<ArticleGroup>;

  /**
   * Version of the article group.
   * Used when replacing an older version.
   */
  version: number;

  /**
   * Boolean indicating if the article group is enabled or not.
   */
  enabled: boolean;

  /**
   * Boolean indicating if the article group should be added as an attachment visible to the customer on the job.
   */
  addToAttachments: boolean;

  /**
   * Url to a logo to illustrate the article group.
   */
  logo?: string;

  /**
   * The partner that owns the article group.
   * A partner may only have one article group active at a given time.
   * @deprecated Use merchant or network instead to refer to the partner.
   */
  partner?: DocumentReference<Partner>;

  /**
   * The merchant that the article group targets.
   */
  merchant: DocumentReference<Partner> | null;

  /**
   * The network that the article group targets.
   */
  network: DocumentReference<Partner> | null;

  /**
   * Companies that this article group targets.
   * Set by the network.
   */
  companies: DocumentReference<Company>[] | null;

  /**
   * The older article group that this article group replaces.
   */
  replaces?: DocumentReference<ArticleGroup>;

  /**
   * The newer article group that replaces this article group.
   */
  replacedBy?: DocumentReference<ArticleGroup>;

  /**
   * Calculation options for the article group, transferred to any quote that's locked to this group.
   */
  calculationOptions: CalculationOptions;

  /**
   * The services that are available in this article group.
   */
  services: ServiceTypes[];
}

/**
 * The type of article group.
 * - root: Can define articles and some default settings.
 * - branch: Can override settings but not define new articles.
 * - standalone: Can define articles and settings but is not part of a branch.
 */
export type ArticleGroupType = "root" | "branch" | "standalone";

export interface ArticleGroupInput
  extends Partial<Omit<ArticleGroup, "createTime">>,
    FirestoreInput {}

const converter = createConverter<ArticleGroup & FirestoreModel>();

/**
 * Returns a reference to the article group with the given id.
 * @param id The id of the article group.
 * @returns A reference to the article group.
 */
export function articleGroupRef(id: string) {
  return doc(
    collection(getFirestore(), "articleGroups").withConverter(converter),
    id,
  );
}

/**
 * Query that returns the active article group for the given partner and time.
 * @param partnerId The id of the partner.
 * @param time The time to check for.
 * @returns A query that returns the active article group for the given partner and time.
 */
export function activeArticleGroupQuery(
  partnerId: string,
  time: Timestamp,
  services: ServiceTypes[],
) {
  return query(
    collection(getFirestore(), "articleGroups").withConverter(converter),
    where("enabled", "==", true),
    where("partner", "==", doc(getFirestore(), "partners", partnerId)),
    where("services", "array-contains-any", services),
    where("startTime", "<=", time),
    orderBy("startTime", "desc"),
    limit(1),
  );
}

export function canBeBranchedByPartner(
  group: Pick<ArticleGroup, "type" | "merchant" | "network" | "companies">,
  partner: Partner | undefined,
) {
  // If companies are set, no branching.
  if (group.companies) {
    return false;
  }

  // No partner, full branching.
  if (!partner) {
    return true;
  }

  // If the group is a root group, it can be branched by the merchant if set or by the network.
  return (
    group.merchant?.id === partner.reference.id ||
    group.network?.id === partner.reference.id
  );
}
