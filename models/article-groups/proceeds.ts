import { CentValue } from "../finance/cent-value";
import {
  AmountBreakdown,
  isPercentageAmount,
  isCentAmount,
  AmountInfo,
} from "./article";

/**
 * Returns whether the given breakdown have a price and a cost set.
 * @param prices The prices to check.
 * @returns Returns true if the prices have a price and a cost set.
 */
export function havePrices(prices: AmountBreakdown): boolean {
  if (!prices) {
    return false;
  }

  const { customerPrice, partnerPrice, craftsmanProceeds } = prices;

  const price: AmountInfo = partnerPrice ?? customerPrice;
  const cost: AmountInfo = craftsmanProceeds;

  if (!price || !cost) {
    return false;
  }

  if (isPercentageAmount(cost) || isCentAmount(cost)) {
    return true;
  }

  if (isPercentageAmount(price) || isCentAmount(price)) {
    return true;
  }

  return false;
}

/**
 * Returns the proceeds for the given prices as a percentage.
 * @param prices The prices to calculate the proceeds for.
 * @returns The proceeds as a percentage or NaN if the calculation is not possible.
 */
export function doneProceeds(prices: AmountBreakdown): number {
  const { craftsmanProceeds } = prices;

  const price: AmountInfo = priceFromBreakdown(prices);
  const cost: AmountInfo = craftsmanProceeds;

  // If cost is not set, proceeds are 100% of the price.
  if (!cost) {
    return 1;
  }

  if (isPercentageAmount(cost)) {
    const pricePercentage = isPercentageAmount(price)
      ? (price.amount.percentage ?? 1)
      : 1;
    return pricePercentage - (cost.amount.percentage ?? 0);
  }

  if (isCentAmount(cost)) {
    if (!isCentAmount(price)) {
      return NaN;
    }
    const priceValue = price.amount.value ?? 1;
    return 1 - (cost.amount.value ?? 0) / priceValue;
  }

  // If cost is set but empty, proceeds are 100% of the price.
  return 1;
}

/**
 * Returns the proceeds for the given prices as a `CentValue`.
 * @param prices The prices to calculate the proceeds for.
 * @returns The proceeds as a percentage or NaN if the calculation is not possible.
 */
export function doneProceedsAmount(prices: AmountBreakdown): CentValue {
  const price = priceFromBreakdown(prices);
  const proceeds = doneProceeds(prices);

  if (!isCentAmount(price) || isNaN(proceeds)) {
    return NaN;
  }

  return price.amount.value * proceeds;
}

/**
 * Returns either the partner price or the customer price from the given breakdown.
 * @param prices The breakdown to get the price from.
 * @returns Either the partner price or the customer price.
 */
export function priceFromBreakdown(prices: AmountBreakdown): AmountInfo {
  const { customerPrice, partnerPrice } = prices;

  if (isPercentageAmount(partnerPrice) || isCentAmount(partnerPrice)) {
    return partnerPrice;
  }

  return customerPrice;
}
