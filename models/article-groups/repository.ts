import {
  QueryConstraint,
  and,
  collection,
  doc,
  getFirestore,
  or,
  orderBy,
  query,
  where,
} from "firebase/firestore";
import { createConverter } from "../firestore-converter";
import { FirestoreModel } from "../firestore_model";
import { Article } from "./article";
import { ArticleGroup } from "./article-group";
import { partnerRef } from "../other/partner";

const groupsConverter = createConverter<ArticleGroup & FirestoreModel>();

export const articleGroupsCollection = () =>
  collection(getFirestore(), "articleGroups").withConverter(groupsConverter);

export const articleGroupsQuery = (...queryConstraints: QueryConstraint[]) =>
  query(articleGroupsCollection(), ...queryConstraints);

export const rootArticleGroupsQuery = (partnerId: string) =>
  query(
    articleGroupsCollection(),
    and(
      or(
        where("network", "==", partnerRef(partnerId)),
        where("merchant", "==", partnerRef(partnerId)),
      ),
      where("type", "==", "root"),
    ),
    orderBy("createTime", "desc"),
  );

const articlesConverter = createConverter<Article & FirestoreModel>();

export const articlesCollection = (articleGroupId: string) =>
  collection(
    getFirestore(),
    "articleGroups",
    articleGroupId,
    "articles",
  ).withConverter(articlesConverter);

export const articleDoc = (groupId: string, articleId: string) =>
  doc(
    getFirestore(),
    "articleGroups",
    groupId,
    "articles",
    articleId,
  ).withConverter(articlesConverter);
