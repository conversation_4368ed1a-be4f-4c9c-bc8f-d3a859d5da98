import { describe, expect, it } from "@jest/globals";
import { doneProceeds } from "./proceeds";
import { AmountBreakdown } from "./article";
import { Float } from "type-fest";

describe("doneProceeds", () => {
  it("returns 1 when both price and cost are missing", () => {
    const prices = {
      customerPrice: null,
      partnerPrice: null,
      craftsmanProceeds: null,
    } as unknown as AmountBreakdown;

    expect(doneProceeds(prices)).toBeCloseTo(1);
  });

  it("returns 1 when cost is missing", () => {
    const prices = {
      customerPrice: { amount: { amount: 100 } },
      partnerPrice: null,
      craftsmanProceeds: null,
    } as unknown as AmountBreakdown;

    expect(doneProceeds(prices)).toBeCloseTo(1);
  });

  it("returns 1 when cost is empty", () => {
    const prices = {
      customerPrice: { amount: { amount: 100 } },
      partnerPrice: { amount: {} },
      craftsmanProceeds: { amount: {} },
    } as AmountBreakdown;

    expect(doneProceeds(prices)).toBeCloseTo(1);
  });

  it("calculates correctly when both are percentages", () => {
    const prices = {
      customerPrice: { amount: {} },
      partnerPrice: { amount: { percentage: 0.8 as Float<number> } },
      craftsmanProceeds: { amount: { percentage: 0.7 as Float<number> } },
    };

    expect(doneProceeds(prices)).toBeCloseTo(0.1);
  });

  it("calculates correctly when using only craftsman proceeds", () => {
    const prices = {
      customerPrice: { amount: {} },
      partnerPrice: { amount: {} },
      craftsmanProceeds: { amount: { percentage: 0.4 as Float<number> } },
    };

    expect(doneProceeds(prices)).toBeCloseTo(0.6);
  });

  it("calculates the difference between price and cost when both are cent amounts", () => {
    const prices = {
      customerPrice: { amount: {} },
      partnerPrice: { amount: { value: 120 } },
      craftsmanProceeds: { amount: { value: 60 } },
    };

    expect(doneProceeds(prices)).toBeCloseTo(0.5);
  });

  it("cannot calculate proceeds if price is a precentage but cost is a value", () => {
    const prices = {
      customerPrice: { amount: {} },
      partnerPrice: { amount: { percentage: 0.8 as Float<number> } },
      craftsmanProceeds: { amount: { value: 60 } },
    };

    expect(doneProceeds(prices)).toBe(NaN);
  });

  it("calculates the difference between price and cost when both are cent amounts", () => {
    const prices = {
      customerPrice: { amount: { value: 60 } },
      partnerPrice: { amount: {} },
      craftsmanProceeds: { amount: { percentage: 0.7 as Float<number> } },
    };

    expect(doneProceeds(prices)).toBeCloseTo(0.3);
  });
});
