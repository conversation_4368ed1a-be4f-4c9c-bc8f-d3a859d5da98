import { getApp } from "firebase/app";
import { getFunctions, httpsCallable } from "firebase/functions";
import { ArticleGroup } from "./article-group";

interface ArticleGroupOverride
  extends Partial<
    Pick<
      ArticleGroup,
      "title" | "name" | "enabled" | "calculationOptions" | "version"
    >
  > {
  network?: string;
  merchant?: string;
  companies?: string[];
  startTime?: string;
}

interface DuplicateArticleGroupInput {
  sourceGroupId: string;
  overrides: ArticleGroupOverride;
}

export interface DuplicateArticleGroupResponse {
  newGroupId: string;
}

export async function duplicateArticleGroup(
  groupId: string,
  overrides: ArticleGroupOverride,
) {
  const payload = {
    sourceGroupId: groupId,
    overrides,
  };

  const result = await httpsCallable<
    DuplicateArticleGroupInput,
    DuplicateArticleGroupResponse
  >(
    getFunctions(getApp(), "europe-west1"),
    "duplicateArticleGroup",
  )(payload);

  return result.data.newGroupId;
}
