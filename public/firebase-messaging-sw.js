// Change this to dev for testing
const env = "prod";

importScripts(
  "https://www.gstatic.com/firebasejs/9.9.1/firebase-app-compat.js",
);
importScripts(
  "https://www.gstatic.com/firebasejs/9.9.1/firebase-messaging-compat.js",
);

const config = {
  prod: {
    apiKey: "AIzaSyCrxQidn5-tQYDtWQQcSFU0gFuf_JzuEl0",
    authDomain: "done-50549.firebaseapp.com",
    databaseURL: "https://done-50549.firebaseio.com",
    projectId: "done-50549",
    storageBucket: "done-50549.appspot.com",
    messagingSenderId: "865533511519",
    appId: "1:865533511519:web:e63ff7d4160289cbe8d8ea",
  },
  dev: {
    apiKey: "AIzaSyBjtVUJtmu5ChdHnqeoq-n-tSR1xSDmUug",
    authDomain: "done-dev-f0434.firebaseapp.com",
    databaseURL: "https://done-dev-f0434.firebaseio.com",
    projectId: "done-dev-f0434",
    storageBucket: "done-dev-f0434.appspot.com",
    messagingSenderId: "704360355803",
    appId: "1:704360355803:web:46c20c372f0d2dc0ebb9fe",
  },
}[env];

firebase.initializeApp(config);

const messaging = firebase.messaging();
messaging.onBackgroundMessage(({ data: { title, body } }) => {
  self.registration.showNotification(title, {
    body,
    icon: "./android-chrome-192x192.png",
  });
});
