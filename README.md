![done_github_back_office_logo](https://user-images.githubusercontent.com/19980760/83326006-1c6b0000-a271-11ea-9098-dfbd038a4d42.png)

## Setup:

For setup and installation of necessary libraries run `yarn` command from terminal in project directory.

## Running Project:

For running against dev and prod backend locally:

```
yarn dev
yarn prod
```

While trying to run project if you got error about ports you can run following command.

```
killall -9 node
```

or

```
yarn watch --port SOME_PORT
```

## Deployment

PROD:

```
yarn deploy:prod
```

DEV:

```
yarn deploy:dev
```

# Serving the Application Statically

To properly test routing and simulate the production environment, you should serve the application statically. This is because the application is deployed as a static site without server-side rendering (SSR).

## Steps to Serve Statically

1. Build and export the static files:
   Choose the appropriate environment (emulator, dev, or prod) and run one of the following commands:

   ```bash
   yarn export:emulator|dev|prod
   ```

   This will create a static export of the application in the `out` directory.

2. Serve the static files:
   After the export is complete, run:

   ```bash
   yarn static-serve
   ```

## Important Note

Running `yarn serve:<env>` (e.g., `yarn serve:dev`) sets up Next.js with server-side rendering, which does not accurately represent the production environment. To test routing properly and ensure your application behaves correctly when deployed, always use the static serving method described above.

This is especially important for testing the routing behavior of the application, as the routing behavior is different in a static environment compared to an SSR setup.
