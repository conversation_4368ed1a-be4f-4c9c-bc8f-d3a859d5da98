import { useMapsLibrary } from "@vis.gl/react-google-maps";
import { useCallback, useRef } from "react";

/**
 * Custom hook for Google Maps Geocoding API using the useMapsLibrary pattern.
 * Replaces the need for react-google-places-autocomplete's geocodeByPlaceId function.
 */
export function useGoogleGeocoding() {
  const geocoding = useMapsLibrary("geocoding");
  const geocoderRef = useRef<google.maps.Geocoder | null>(null);

  // Initialize geocoder when the library is available
  if (geocoding && !geocoderRef.current) {
    geocoderRef.current = new geocoding.Geocoder();
  }

  const geocodeByPlaceId = useCallback(
    async (placeId: string): Promise<google.maps.GeocoderResult[]> => {
      if (!geocoderRef.current) {
        throw new Error(
          "Geocoder not initialized. Make sure the component is wrapped in APIProvider.",
        );
      }

      return new Promise((resolve, reject) => {
        geocoderRef.current!.geocode({ placeId }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results) {
            resolve(results);
          } else {
            reject(new Error(`Geocoding failed: ${status}`));
          }
        });
      });
    },
    [geocoding],
  );

  const geocodeByAddress = useCallback(
    async (address: string): Promise<google.maps.GeocoderResult[]> => {
      if (!geocoderRef.current) {
        throw new Error(
          "Geocoder not initialized. Make sure the component is wrapped in APIProvider.",
        );
      }

      return new Promise((resolve, reject) => {
        geocoderRef.current!.geocode({ address }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results) {
            resolve(results);
          } else {
            reject(new Error(`Geocoding failed: ${status}`));
          }
        });
      });
    },
    [geocoding],
  );

  const geocodeByLatLng = useCallback(
    async (
      latLng: google.maps.LatLng | google.maps.LatLngLiteral,
    ): Promise<google.maps.GeocoderResult[]> => {
      if (!geocoderRef.current) {
        throw new Error(
          "Geocoder not initialized. Make sure the component is wrapped in APIProvider.",
        );
      }

      return new Promise((resolve, reject) => {
        geocoderRef.current!.geocode(
          { location: latLng },
          (results, status) => {
            if (status === google.maps.GeocoderStatus.OK && results) {
              resolve(results);
            } else {
              reject(new Error(`Reverse geocoding failed: ${status}`));
            }
          },
        );
      });
    },
    [geocoding],
  );

  return {
    geocodeByPlaceId,
    geocodeByAddress,
    geocodeByLatLng,
    isReady: !!geocoding,
  };
}
