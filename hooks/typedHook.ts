import { FirestoreModel } from "./../models/firestore_model";
import {
  collection as fireCollection,
  getFirestore,
  doc,
} from "firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";

export const useTypedDoc = <T extends FirestoreModel>(
  collection: string,
  docId: string,
): [T | undefined, boolean] => {
  const [response, loading] = useDocument(
    doc(fireCollection(getFirestore(), collection), docId),
  );

  if (!response) return [undefined, false];

  const document = {
    ...response.data(),
    reference: response.ref,
  } as T;

  return [document, loading];
};
