import {
  collection,
  getFirestore,
  doc,
  DocumentReference,
  orderBy,
  where,
  query,
} from "firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";
import { Job } from "../../models/job/job";
import { usePeriodicRender } from "../../lib/hooks/periodic-render";
import { useMemo } from "react";
import { useFirestoreCount } from "../../lib/hooks/use-firestore-count";

export const useJob = (
  jobId: string | undefined,
): { job?: Job; loading: Boolean } => {
  const [response, loading] = useDocument(
    jobId ? doc(collection(getFirestore(), "jobs"), jobId) : undefined,
  );

  return {
    job:
      response?.data() &&
      ({ ...response.data(), reference: response.ref } as Job),
    loading,
  };
};

export function useCustomerJobsCount(userReference: DocumentReference) {
  const tick = usePeriodicRender();

  const offerQuery = useMemo(() => {
    tick;

    return query(
      collection(getFirestore(), "jobs"),
      where("customer", "==", userReference),
      orderBy("createTime", "desc"),
    );
  }, [tick, userReference]);

  const [count = 0] = useFirestoreCount(offerQuery);

  return count;
}
