import {
  collection,
  FirestoreError,
  getFirestore,
  Query,
} from "firebase/firestore";
import {
  useCollection,
  useCollectionOnce,
} from "react-firebase-hooks/firestore";
import { Job } from "../../models/job/job";

/** @deprecated Use `useCollection` directly instead. */
export const useJobs = (
  query?: Query,
): { jobs?: Job[]; loading: Boolean; error?: FirestoreError } => {
  const _collection = query || collection(getFirestore(), "jobs");

  const [response, loading, error] = useCollection(_collection);

  return {
    jobs: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as Job,
    ),
    loading,
    error,
  };
};

/** @deprecated Use `useCollectionOnce` directly instead. */
export const useJobsOnce = (
  query?: Query,
): { jobs?: Job[]; loading: Boolean } => {
  const _collection = query || collection(getFirestore(), "jobs");

  const [response, loading] = useCollectionOnce(_collection);

  return {
    jobs: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as Job,
    ),
    loading,
  };
};
