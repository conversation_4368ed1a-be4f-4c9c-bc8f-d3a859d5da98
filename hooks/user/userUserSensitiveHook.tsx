import { collection, getFirestore, doc } from "firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";
import { SensitiveUserData } from "../../models/user/sensitive-user-data";

export const useUserSensitiveData = (
  userId: string,
): { userSensitiveData?: SensitiveUserData; loading: boolean } => {
  const [response, loading] = useDocument(
    doc(
      collection(
        doc(collection(getFirestore(), "users"), userId),
        "_sensitive",
      ),
      userId,
    ),
  );

  return {
    userSensitiveData:
      response?.data() &&
      ({ ...response.data(), reference: response.ref } as SensitiveUserData),
    loading,
  };
};
