import { collection, getFirestore, Query } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import { User } from "../../models/user/user";

export const useUsers = (
  query?: Query,
): { users?: User[]; loading: boolean } => {
  const _collection = query || collection(getFirestore(), "users");

  const [response, loading] = useCollection(_collection);

  return {
    users: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as User,
    ),
    loading,
  };
};
