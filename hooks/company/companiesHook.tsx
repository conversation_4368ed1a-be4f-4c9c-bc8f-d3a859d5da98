import { collection, getFirestore, Query } from "firebase/firestore";
import {
  useCollection,
  useCollectionOnce,
} from "react-firebase-hooks/firestore";
import { Company } from "../../models/company/company";

export const useCompanies = (
  query?: Query,
): { companies?: Company[]; loading: boolean } => {
  const _collection = query || collection(getFirestore(), "companies");

  const [response, loading] = useCollection(_collection);

  return {
    companies: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as Company,
    ),
    loading,
  };
};

export const useCompaniesOnce = (
  query?: Query,
): { companies?: Company[]; loading: boolean } => {
  const _collection = query || collection(getFirestore(), "companies");

  const [response, loading] = useCollectionOnce(_collection);

  return {
    companies: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as Company,
    ),
    loading,
  };
};
