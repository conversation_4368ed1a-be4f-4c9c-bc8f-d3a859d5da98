import { collection, getFirestore, doc } from "firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";
import { Company } from "../../models/company/company";

export const useCompany = (
  companyId?: string,
): { company?: Company; loading: Boolean } => {
  const [response, loading] = useDocument(
    companyId
      ? doc(collection(getFirestore(), "companies"), companyId)
      : undefined,
  );

  return {
    company:
      response?.data() &&
      ({ ...response.data(), reference: response.ref } as Company),
    loading,
  };
};
