import { CsmDashboardJobEntry } from "../../models/company/csm_dashboard_job_entry";
import { useBigQuery } from "../other/bigQueryHook";
import { PROJECT_ID } from "../../lib/initFirebase";

export const useCsmDashboard = (): {
  data?: { [companyId: string]: CsmDashboardJobEntry[] };
  loading: boolean;
} => {
  const query = `SELECT
    company,
    ARRAY_AGG(STRUCT( document_id,
        createTime,
        jobs_info )) AS documents_info
    FROM \`${PROJECT_ID}.csm.csm_main\`
    GROUP BY
    company`;

  const { rows, loading } = useBigQuery(query);
  if (!rows) return { data: undefined, loading: true };
  const data = rows.reduce((obj, entry) => {
    obj[entry.company] = entry.documents_info.map(
      (documentInfo: { [key: string]: any }) => {
        const jobs_info = documentInfo.jobs_info[0];
        const rules = documentInfo.jobs_info.map(
          (jobInfo: { [key: string]: any }) => jobInfo.rule,
        );
        return {
          jobId: documentInfo.document_id,
          createTime: documentInfo.createTime.value,
          companyName: jobs_info.company_name,
          rules,
          customer: jobs_info.customer,
          customerName: jobs_info.customer_name,
          matchTime: jobs_info.match_time.value,
        };
      },
    );
    return obj;
  }, {});

  return {
    data,
    loading,
  };
};
