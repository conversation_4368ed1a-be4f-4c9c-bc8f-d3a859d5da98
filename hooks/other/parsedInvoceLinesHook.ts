import { getFirestore, collection, Query } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";

import { ParsedInvoiceLine } from "../../models/invoice/parsed_invoice_line";

export const useParsedInvoiceLines = (
  query: Query,
): { parsedInvoiceLines?: ParsedInvoiceLine[]; loading: Boolean } => {
  const _collection =
    query || collection(getFirestore(), "admin/billecta/parsedInvoiceLines/");

  const [response, loading] = useCollection(_collection);

  return {
    parsedInvoiceLines: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as ParsedInvoiceLine,
    ),
    loading,
  };
};
