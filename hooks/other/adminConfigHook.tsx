import { collection, getFirestore, doc } from "firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";
import { AdminAppConfiguration } from "../../models/other/admin-config";
import { AdminData } from "../../models/admin/admin-data";

export const useAdminConfig = (): {
  config?: AdminAppConfiguration;
  loading: Boolean;
} => {
  const [response, loading] = useDocument(
    doc(collection(getFirestore(), "admin"), "configuration"),
  );

  return {
    config:
      response?.data() &&
      ({
        ...response.data(),
      } as AdminAppConfiguration),
    loading,
  };
};

export const useAdmin = (): {
  admin?: AdminData;
  loading: Boolean;
} => {
  const [response, loading] = useDocument(
    doc(collection(getFirestore(), "admin"), "admin"),
  );

  return {
    admin:
      response?.data() &&
      ({
        ...response.data(),
      } as AdminData),
    loading,
  };
};
