import { getFirestore, doc } from "firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";

import { DiscountCode } from "../../models/other/discount-code";

export const useDiscountCode = (
  discountCodeId?: string,
): { discountCode?: DiscountCode; loading: Boolean } => {
  const [response, loading] = useDocument(
    doc(getFirestore(), `admin/codes/discountCodes/${discountCodeId}`),
  );

  if (!discountCodeId) return { discountCode: undefined, loading: false };

  return {
    discountCode:
      response?.data() &&
      ({ ...response.data(), reference: response.ref } as DiscountCode),
    loading,
  };
};
