import { ChatMessage } from "../../models/other/message";
import { useCollection } from "react-firebase-hooks/firestore";
import {
  collectionGroup,
  getFirestore,
  query,
  Query,
  orderBy,
  limit,
} from "firebase/firestore";

export const useChatMessages = (
  queryReference?: Query,
): { messages?: ChatMessage[]; loading: Boolean } => {
  const _collection =
    queryReference ||
    query(
      query(
        collectionGroup(getFirestore(), "messages"),
        orderBy("createTime", "desc"),
        limit(40),
      ),
    );

  const [response, loading] = useCollection(_collection);

  return {
    messages: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as ChatMessage,
    ),
    loading,
  };
};
