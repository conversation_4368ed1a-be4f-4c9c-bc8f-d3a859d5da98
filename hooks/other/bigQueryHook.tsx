import { useHttpsCallable } from "react-firebase-hooks/functions";
import { getFunctions } from "firebase/functions";
import { getApp } from "firebase/app";
import { useCallback, useEffect, useState } from "react";

export const useBigQuery = (
  query: string,
): { rows?: any[]; loading: boolean } => {
  const [fetchCsmDashboardData, loading] = useHttpsCallable(
    getFunctions(getApp(), "europe-west1"),
    "fetchBigQueryData",
  );
  const [rows, setRows] = useState<any[]>();

  const fetchData = useCallback(async () => {
    const result = await fetchCsmDashboardData({ query });
    if (!result?.data) return;
    const rows = result!.data as any[];
    setRows(rows);
  }, [fetchCsmDashboardData, query]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    rows,
    loading,
  };
};
