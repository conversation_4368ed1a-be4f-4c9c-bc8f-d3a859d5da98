import { CompletedPartnerInstallation } from "../../models/other/completed-partner-installation";
import { collection, getFirestore, Query } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";

export const useCompletedPartnerInstallations = (
  query?: Query,
): {
  completedPartnerInstallations?: CompletedPartnerInstallation[];
  loading: Boolean;
} => {
  const _collection =
    query || collection(getFirestore(), "completedPartnerInstallations");

  const [response, loading] = useCollection(_collection);

  return {
    completedPartnerInstallations: response?.docs.map(
      (doc) =>
        ({ ...doc.data(), reference: doc.ref }) as CompletedPartnerInstallation,
    ),
    loading,
  };
};
