import { collection, getFirestore, Query } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";

import { Sms64elks } from "../../models/sms/sms";

export const useSmses = (
  query?: Query,
): { smses?: Sms64elks[]; loading: Boolean } => {
  const _collection = query || collection(getFirestore(), "46elks");

  const [response, loading] = useCollection(_collection);

  return {
    smses: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as Sms64elks,
    ),
    loading,
  };
};
