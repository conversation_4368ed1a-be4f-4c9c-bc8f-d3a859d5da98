import { collection, getFirestore, Query } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";

import { Partner } from "../../models/other/partner";

export const usePartners = (
  query?: Query,
): { partners?: Partner[]; loading: boolean } => {
  const _collection = query || collection(getFirestore(), "partners");

  const [response, loading] = useCollection(_collection);

  return {
    partners: response?.docs.map(
      (doc) => ({ ...doc.data(), reference: doc.ref }) as Partner,
    ),
    loading,
  };
};
