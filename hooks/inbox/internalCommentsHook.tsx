import {
  DocumentReference,
  limit as firestoreLimit,
  orderBy,
  query,
  where,
  FieldPath,
} from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import { useMemo } from "react";

import { useAuthUser } from "../../components/auth/AuthProvider";
import { useInfiniteScrollQuery } from "../../lib/hooks/use-infinite-scroll-query";
import { lastKeyCursor } from "../../lib/hooks/firestore-cursors";
import { internalCommentsCollection } from "../../models/inbox/internal-comment";

const useInternalCommentsBaseQuery = (subject: DocumentReference) => {
  const authUser = useAuthUser();

  const visibleFor = authUser.partner
    ? "visibleFor.partner"
    : "visibleFor.done";

  return useMemo(
    () =>
      query(
        internalCommentsCollection(subject),
        where(visibleFor, "==", true),
        orderBy("createTime", "desc"),
      ),
    [subject, visibleFor],
  );
};

export const useInternalCommentsExistOnSubject = (
  subject: DocumentReference,
): { exists: boolean; loading: boolean } => {
  const baseQuery = useInternalCommentsBaseQuery(subject);
  // There is a function that returns just the count instead of
  // querying with a limit of 1. But it always makes a roundtrip
  // to the server. Querying normally can untilize local cache.
  const q = useMemo(() => query(baseQuery, firestoreLimit(1)), [baseQuery]);
  const [snapshot, loading] = useCollection(q);

  return {
    exists: !!snapshot && snapshot.docs.length > 0,
    loading,
  };
};

export const useInfiniteScrollingInternalComments = (
  subject: DocumentReference,
  pageSize: number = 30,
) => {
  const baseQuery = useInternalCommentsBaseQuery(subject);

  const cursorFunction = useMemo(
    () => lastKeyCursor(new FieldPath("createTime")),
    [],
  );

  const [documents, loading, loadMore, hasMore] = useInfiniteScrollQuery(
    baseQuery,
    cursorFunction,
    pageSize,
  );

  const internalComments = useMemo(
    () => documents.map((doc) => doc.data()),
    [documents],
  );

  return {
    internalComments,
    loading,
    hasMorePages: hasMore,
    loadMore,
  };
};
