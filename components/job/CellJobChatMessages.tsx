import { CircularProgress, Tooltip, Typography } from "@mui/material";
import ComponentContainer from "../common/ComponentContainer";
import { useChatMessages } from "../../hooks/other/chatMessagesHook";
import {
  collection,
  getFirestore,
  limit,
  orderBy,
  query,
} from "firebase/firestore";
import ChatMessages from "../chat/ChatMessages";

const MessagesTooltip = ({ jobId }: { jobId: string }) => {
  const messagesRef = query(
    collection(getFirestore(), `jobs/${jobId}/messages`),
    orderBy("createTime", "desc"),
    limit(8),
  );

  const { messages, loading } = useChatMessages(messagesRef);

  if (loading) {
    return <CircularProgress color="info" />;
  }

  if (!messages || messages.length === 0) {
    return <Typography variant="body2">No messages</Typography>;
  }

  return (
    <ComponentContainer title={"Job chat messages"}>
      <ChatMessages messages={messages} />
    </ComponentContainer>
  );
};

export function CellJobChatMessages({
  jobId,
  lastChatMessage,
}: {
  jobId: string;
  lastChatMessage?: string;
}) {
  return (
    <Tooltip
      enterDelay={300}
      enterNextDelay={300}
      title={<MessagesTooltip jobId={jobId} />}
      arrow
    >
      <Typography variant="body2" noWrap>
        {lastChatMessage ?? "-"}
      </Typography>
    </Tooltip>
  );
}
