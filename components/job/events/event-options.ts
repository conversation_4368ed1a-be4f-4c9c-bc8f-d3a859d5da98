import {
  Call,
  Cancel,
  Done,
  Login,
  Receipt,
  ReceiptLong,
  RequestQuote,
  RequestQuoteOutlined,
  SearchOutlined,
  Star,
  HourglassBottom,
  Clear,
  Checklist,
  Schedule,
  Remove,
  HomeRepairService,
  Try,
  Public,
  QuestionMark,
  Verified,
  Assignment,
} from "@mui/icons-material";
import { Job, JobEvents } from "../../../models/job/job";
import { CalendarMonth, CheckCircle } from "mdi-material-ui";
import React from "react";
import { SvgIconProps } from "@mui/material";
import { Timestamp } from "firebase/firestore";
import _ from "lodash";
import { PartnerTier } from "../../../models/other/partner";
import { isOlderThanDays } from "../../../lib/date-compare";

type Color = "primary" | "secondary" | "error" | "info" | "success" | "warning";

/**
 * List of groups that events can belong to.
 */
export const eventGroups = [
  "login",
  "prechecks",
  "prerequisites",
  "matching",
  "messaging",
  "call",
  "quote",
  "work",
  "report",
  "state",
  "invoice",
  "review",
  "installer-invoice",
  "partner-invoice",
] as const;

export type EventGroup = (typeof eventGroups)[number];

export interface JobEventOption {
  /** Group that this event belongs to. Only the latest event in each group is shown. */
  group: EventGroup;

  /** Title of the event. */
  title: string;

  /** Whether the event should be hidden from the UI. */
  hidden?: boolean;

  /** Visibility of the event for partners. */
  visibleFor?: PartnerTier;

  /** Title of the event for partners. */
  partnerTitle?: string;

  /** Icon to display for the event. */
  icon: React.ComponentType<SvgIconProps>;

  /** Color of the event. If no color is specified, primary color will be used */
  color?: Color;

  /** Function to determine if the event is old. */
  isOld: (time: Timestamp) => boolean;

  /** Function to get the timestamp of the event used for overriding the set timestamp for the event. */
  getTimestamp?: (job: Job) => Timestamp | null | undefined;

  /** Badge to display for the event. */
  badge?: {
    icon: React.ComponentType<SvgIconProps>;
    color: Color;
  };
}

const defaultIsOld = isOlderThanDays(3, false);
const neverOld = () => false;

export type EventId = keyof JobEvents;

/**
 * List of all events that can happen to a job.
 *
 * **Important**: Must be in expected order of events.
 */
const events: Record<EventId, JobEventOption> = {
  customerLoggedIn: {
    title: "Customer logged in",
    group: "login",
    icon: Login,
    isOld: defaultIsOld,
    visibleFor: PartnerTier.Standard,
  },
  partnershipPrechecksReported: {
    title: "Prechecks reported",
    group: "prechecks",
    icon: Checklist,
    isOld: defaultIsOld,
    visibleFor: PartnerTier.Standard,
  },
  matchInitiated: {
    title: "Match initiated",
    group: "matching",
    icon: HomeRepairService,
    isOld: defaultIsOld,
    visibleFor: PartnerTier.Standard,
    badge: {
      icon: SearchOutlined,
      color: "primary",
    },
  },
  companyMatched: {
    title: "Installer matched",
    group: "matching",
    icon: HomeRepairService,
    isOld: defaultIsOld,
    visibleFor: PartnerTier.Standard,
    badge: {
      icon: Done,
      color: "success",
    },
  },
  firstMessageSentByCraftsman: {
    title: "First message sent by installer",
    group: "messaging",
    icon: Try,
    isOld: defaultIsOld,
    visibleFor: PartnerTier.Standard,
  },
  prerequisitesPending: {
    title: "Prerequisites pending",
    group: "prerequisites",
    icon: Assignment,
    isOld: defaultIsOld,
    visibleFor: PartnerTier.Standard,
    badge: {
      icon: HourglassBottom,
      color: "primary",
    },
  },
  prerequisitesFulfilled: {
    title: "Prerequisites fulfilled",
    group: "prerequisites",
    icon: Assignment,
    isOld: defaultIsOld,
    visibleFor: PartnerTier.Standard,
    badge: {
      icon: Done,
      color: "success",
    },
  },
  callSkipped: {
    title: "Call skipped",
    group: "call",
    hidden: true,
    icon: Call,
    isOld: defaultIsOld,
    badge: {
      icon: Remove,
      color: "primary",
    },
  },
  callScheduled: {
    title: "Call scheduled",
    group: "call",
    icon: Call,
    isOld: defaultIsOld,
    getTimestamp: (job) => job.scheduledCallTime,
    badge: {
      icon: Schedule,
      color: "primary",
    },
  },
  scheduledCallCancelled: {
    title: "Scheduled call cancelled",
    group: "call",
    icon: Call,
    isOld: defaultIsOld,
    badge: {
      icon: Clear,
      color: "error",
    },
  },
  callMade: {
    title: "Call made",
    group: "call",
    icon: Call,
    isOld: defaultIsOld,
    badge: {
      icon: Done,
      color: "success",
    },
  },
  offerSent: {
    title: "Quote sent",
    group: "quote",
    partnerTitle: "Extras quote sent",
    visibleFor: PartnerTier.Standard,
    icon: Receipt,
    isOld: defaultIsOld,
    badge: {
      icon: HourglassBottom,
      color: "primary",
    },
  },
  offerAccepted: {
    title: "Quote accepted",
    group: "quote",
    partnerTitle: "Extras quote accepted",
    visibleFor: PartnerTier.Standard,
    icon: Receipt,
    isOld: defaultIsOld,
    badge: {
      icon: Done,
      color: "success",
    },
  },
  offerDeclined: {
    title: "Quote declined",
    group: "quote",
    partnerTitle: "Extras quote declined",
    visibleFor: PartnerTier.Standard,
    icon: Receipt,
    isOld: defaultIsOld,
    badge: {
      icon: Clear,
      color: "error",
    },
  },
  offersChecked: {
    title: "Quotes checked",
    group: "quote",
    hidden: true,
    icon: Receipt,
    isOld: neverOld,
    visibleFor: PartnerTier.Standard,
    badge: {
      icon: Verified,
      color: "primary",
    },
  },
  workTimeScheduled: {
    title: "Work scheduled",
    group: "work",
    visibleFor: PartnerTier.Standard,
    icon: CalendarMonth,
    isOld: isOlderThanDays(1),
    getTimestamp: (job) => job.scheduledWorkStartTime,
    badge: {
      icon: Schedule,
      color: "primary",
    },
  },
  scheduledWorkTimeCancelled: {
    title: "Scheduled work cancelled",
    group: "work",
    icon: CalendarMonth,
    isOld: defaultIsOld,
    badge: {
      icon: Clear,
      color: "error",
    },
  },
  partnershipInstallationReported: {
    title: "Installation reported",
    group: "report",
    visibleFor: PartnerTier.Standard,
    icon: ReceiptLong,
    isOld: defaultIsOld,
    badge: {
      icon: Done,
      color: "success",
    },
  },
  jobDone: {
    title: "Job done",
    group: "state",
    visibleFor: PartnerTier.Standard,
    icon: CheckCircle,
    isOld: neverOld,
    color: "success",
  },
  invoiceBasisSent: {
    title: "Invoice basis sent",
    group: "invoice",
    visibleFor: PartnerTier.Platform,
    icon: RequestQuoteOutlined,
    isOld: defaultIsOld,
  },
  invoiceSent: {
    title: "Customer invoice sent",
    group: "invoice",
    visibleFor: PartnerTier.Platform,
    icon: RequestQuote,
    isOld: defaultIsOld,
    badge: {
      icon: HourglassBottom,
      color: "primary",
    },
  },
  invoicePaid: {
    title: "Customer invoice paid",
    group: "invoice",
    icon: RequestQuote,
    isOld: neverOld,
    badge: {
      icon: Done,
      color: "success",
    },
  },
  craftsmanBilled: {
    title: "Craftsman billed",
    group: "installer-invoice",
    icon: RequestQuote,
    isOld: neverOld,
    badge: {
      icon: HomeRepairService,
      color: "primary",
    },
  },
  partnerBilled: {
    title: "Partner billed",
    partnerTitle: "Invoice sent",
    group: "partner-invoice",
    icon: RequestQuote,
    isOld: neverOld,
    badge: {
      icon: Public,
      color: "primary",
    },
  },
  companyReviewed: {
    title: "Customer left review",
    group: "review",
    visibleFor: PartnerTier.Platform,
    icon: Star,
    isOld: neverOld,
  },
  jobCancelled: {
    title: "Job cancelled",
    group: "state",
    icon: Cancel,
    visibleFor: PartnerTier.Standard,
    isOld: neverOld,
    color: "error",
  },
  companyClosedProject: {
    title: "Company closed project",
    group: "state",
    hidden: true,
    icon: CheckCircle,
    isOld: neverOld,
  },
  customerClosedProject: {
    title: "Customer closed project",
    group: "state",
    hidden: true,
    icon: CheckCircle,
    isOld: neverOld,
  },
  jobClosed: {
    title: "Job closed",
    group: "state",
    hidden: true,
    icon: CheckCircle,
    isOld: neverOld,
  },
};

const fallbackOptions: JobEventOption = {
  title: "",
  group: "state",
  hidden: true,
  icon: QuestionMark,
  isOld: neverOld,
};

const eventsOrder = Object.keys(events).filter(
  (id) => !events[id as EventId].hidden,
) as ReadonlyArray<EventId>;

export const optionsForEvent = (eventId: EventId): JobEventOption =>
  events[eventId] ?? fallbackOptions;

export const isVisibleForTier = (eventId: EventId, tier?: PartnerTier) => {
  const options = optionsForEvent(eventId);
  if (options.hidden) return false;
  if (!tier) return true;
  return options.visibleFor && options.visibleFor <= tier;
};

export const eventsVisibleForTier = (tier?: PartnerTier) =>
  eventsOrder.filter((eventId) => isVisibleForTier(eventId, tier));

export const titleForEvent = (eventId: EventId, isPartner: boolean) => {
  const { title, partnerTitle } = optionsForEvent(eventId);
  return isPartner ? (partnerTitle ?? title) : title;
};
