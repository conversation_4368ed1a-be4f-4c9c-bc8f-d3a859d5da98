import { isDefined } from "../../../lib/filter-undefined";
import { JobEvents } from "../../../models/job/job";
import { PartnerTier } from "../../../models/other/partner";
import { EventId, optionsForEvent } from "./event-options";
import _ from "lodash";

/** Sort job events in cronological order  */
export function sortedEvents(events: Partial<JobEvents>, tier?: PartnerTier) {
  return Object.entries(events)
    .filter(([_, timestamp]) => isDefined(timestamp))
    .sort(
      ([_, timestampA], [__, timestampB]) =>
        (timestampB?.toMillis() ?? 0) - (timestampA?.toMillis() ?? 0), // Newest first
    )
    .map(([eventId]) => eventId as EventId)
    .filter((eventId) => {
      const options = optionsForEvent(eventId);
      if (options.hidden) return false;
      if (!tier) return true;
      return options.visibleFor && options.visibleFor <= tier;
    });
}

/** Get the latest event that has occurred */
export function latestEvent(events: Partial<JobEvents>, tier?: PartnerTier) {
  return _.first(sortedEvents(events, tier));
}
