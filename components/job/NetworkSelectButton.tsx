import { Avatar, ListItemAvatar, ListItemText } from "@mui/material";
import NetworkChip from "../company/NetworkChip";
import SelectButton, {
  SelectButtonItem,
  SelectButtonProps,
} from "../menu/SelectButton";
import { Partner, partnerRef } from "../../models/other/partner";
import { useMemoDocument } from "../../lib/hooks/use-memo-document";

interface NetworkSelectButtonProps
  extends Omit<SelectButtonProps<string>, "title" | "onSelection"> {
  value: string;
  choices: string[];
  onSelection?: (network: string) => void;
}

function NetworkSelectButtonItem({ network }: { network: string }) {
  const [partner] = useMemoDocument<Partner>(partnerRef(network));
  return (
    <SelectButtonItem value={network}>
      <ListItemAvatar sx={{ minWidth: 0, marginRight: 2 }}>
        <Avatar sx={{ width: 24, height: 24 }} src={partner?.smallLogo} />
      </ListItemAvatar>
      <ListItemText
        primary={partner?.displayName ?? partner?.name ?? network}
      />
    </SelectButtonItem>
  );
}

export default function NetworkSelectButton({
  value,
  choices,
  onSelection,
}: NetworkSelectButtonProps) {
  return (
    <SelectButton
      title={<NetworkChip network={value} />}
      onSelection={(choice) => onSelection?.(choice)}
      initialSelection={value}
    >
      {choices.map((choice) => (
        <NetworkSelectButtonItem network={choice} key={choice} />
      ))}
    </SelectButton>
  );
}
