import {
  DirectionsCar,
  DirectionsCarOutlined,
  Directions,
} from "@mui/icons-material";
import { Company, CompanyWithDistance } from "../../models/company/company";
import { CompanySuggestion, JobOffer } from "../../models/job/offer/job-offer";
import { Chip, CircularProgress, Tooltip } from "@mui/material";
import { Job } from "../../models/job/job";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { useSnackbar } from "notistack";
import { useState } from "react";

export default function DistanceChip({
  companySuggested,
  company,
  job,
  jobOffer,
}: {
  companySuggested?: CompanySuggestion;
  company: CompanyWithDistance;
  job: Job;
  jobOffer: JobOffer;
}) {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);

  const distanceInKm = companySuggested?.distanceInMeters
    ? companySuggested.distanceInMeters / 1000
    : company.distanceFromWork;

  const isSuggestion = companySuggested?.distanceInMeters === undefined;

  if (distanceInKm === undefined) {
    return null;
  }

  return (
    <Chip
      deleteIcon={
        loading ? (
          <CircularProgress size={16} />
        ) : (
          <Tooltip title="Calculate driving distance" arrow>
            <Directions />
          </Tooltip>
        )
      }
      onDelete={
        companySuggested
          ? undefined
          : async () => {
              setLoading(true);
              try {
                await calculateDrivingDistance(company, job, jobOffer);
              } catch (e) {
                enqueueSnackbar(`Failed to calculate driving distance: ${e}`, {
                  variant: "error",
                });
              } finally {
                setLoading(false);
              }
            }
      }
      color={
        distanceInKm !== undefined
          ? distanceInKm > 40
            ? "error"
            : "success"
          : undefined
      }
      icon={isSuggestion ? <DirectionsCarOutlined /> : <DirectionsCar />}
      size="small"
      label={distanceLabel(
        distanceInKm,
        companySuggested?.drivingTimeInSeconds,
        isSuggestion,
      )}
    />
  );
}

const distanceLabel = (
  distanceInKm: number,
  distanceInSeconds: number | undefined,
  isSuggestion: boolean,
) =>
  `${isSuggestion ? "~ " : ""}${distanceInKm.toFixed(0)} km${
    distanceInSeconds !== undefined
      ? ` (${Math.round(distanceInSeconds / 60)} min)`
      : ""
  }`;

async function calculateDrivingDistance(
  company: Company,
  job: Job,
  jobOffer: JobOffer,
) {
  const payload = {
    jobId: job.reference.id,
    jobOfferId: jobOffer.reference.id,
    companyId: company.reference.id,
  };

  await httpsCallable(
    getFunctions(getApp(), "europe-west1"),
    "calculateDrivingDistance",
  )(payload);
}
