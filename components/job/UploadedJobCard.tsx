import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Stack, Box } from "@mui/material";
import { FC } from "react";
import { Job } from "../../models/job/job";
import InfoItemRow from "../common/InfoItemRow";
import { Check } from "@mui/icons-material";

interface UploadedJobCardProps {
  jobs: Job[];
}

const UploadedJobCard: FC<UploadedJobCardProps> = ({ jobs }) => {
  const [job] = jobs;

  return (
    <Card
      variant="outlined"
      sx={{
        padding: 4,
        margin: 2,
      }}
    >
      <Stack spacing={3} direction="row">
        <Check color="success" />

        <Stack maxWidth={600} spacing={1}>
          {jobs.length > 1 && (
            <Alert severity="error">
              <Stack spacing={1}>
                Multiple jobs created with same partner reference
                {jobs.map((job) => {
                  return (
                    <Link
                      key={job.reference.id}
                      href={`/backoffice/jobs/${job.reference.id}`}
                      passHref
                    >
                      <Button size="small" color="inherit">
                        Go to: {job.reference.id}
                      </Button>
                    </Link>
                  );
                })}
              </Stack>
            </Alert>
          )}

          <InfoItemRow title="Customer name" value={job.cache?.customerName} />

          <InfoItemRow
            title="Company name"
            value={job.cache?.companyName || "Not matched"}
          />

          <InfoItemRow
            title="External reference number"
            value={job.externalReference}
          />

          <InfoItemRow title="Reference number" value={job.referenceNumber} />
        </Stack>

        <Box flex={1} />

        <Link
          target="_blank"
          rel="noopener noreferrer"
          passHref
          href={`/jobs/job/?id=${job.reference.id}`}
        >
          <Button variant="outlined" color="inherit">
            Go to job
          </Button>
        </Link>
      </Stack>
    </Card>
  );
};

export default UploadedJobCard;
