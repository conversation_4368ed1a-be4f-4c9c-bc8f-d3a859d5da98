import FileCopyIcon from "@mui/icons-material/FileCopy";
import { But<PERSON> } from "@mui/material";
import { Job } from "../../models/job/job";
import { useDialog } from "../dialogs/DialogManager";

const DuplicateJobButton = ({ job }: { job: Job }) => {
  const { open: openCreateJobDialog } = useDialog("create-job");

  const isPending = job.status === "pending";

  return (
    <Button
      disabled={isPending}
      style={{ marginRight: "4px" }}
      startIcon={<FileCopyIcon />}
      size="small"
      color="primary"
      onClick={() =>
        openCreateJobDialog({
          copiedJob: job,
        })
      }
    >
      Duplicate
    </Button>
  );
};

export default DuplicateJobButton;
