import {
  Ava<PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ack,
  Checkbox,
  Divider,
  Alert,
  AlertTitle,
  Box,
  Button,
  IconButton,
  Grid2,
} from "@mui/material";
import { query, collection, getFirestore, where } from "firebase/firestore";
import { FC } from "react";
import { useJobs } from "../../hooks/job/jobsHook";
import { BackofficeUploadJobData } from "../../models/job/job";
import { doneColor } from "../../theme/colors";
import UploadedJobCard from "./UploadedJobCard";
import InfoItemRow from "../common/InfoItemRow";
import { Delete } from "@mui/icons-material";
import { partnerRef } from "../../models/other/partner";

interface UploadJobCardProps {
  index: number;
  data: BackofficeUploadJobData;
  selectedJobs: number[];
  onChange: (checked: boolean, index: number) => void;
  onDeleteCraftsmanNote: (index: number) => void;
  onDeleteImage: (imageUrl: string) => void;
  partner: string;
}

const UploadJobCard: FC<UploadJobCardProps> = ({
  data,
  selectedJobs,
  index,
  onChange,
  onDeleteImage,
  onDeleteCraftsmanNote,
  partner,
}) => {
  const { jobs, loading } = useJobs(
    query(
      collection(getFirestore(), "jobs"),
      where("merchant", "==", partnerRef(partner)),
      where("externalReference", "==", data.jobData.externalReference),
    ),
  );

  if (loading) return null;

  if (jobs && jobs?.length !== 0) return <UploadedJobCard jobs={jobs} />;

  return (
    <Card
      variant="outlined"
      sx={{
        margin: 2,
        padding: 2,
      }}
    >
      <Stack alignItems="baseline" direction="row" spacing={1}>
        <Checkbox
          checked={selectedJobs.includes(index)}
          onChange={(event) => {
            onChange(event.target.checked, index);
          }}
        />

        <Grid2 container>
          <Grid2 m={4}>
            <Stack maxWidth={400} divider={<Divider />} spacing={1}>
              <Typography variant="h6">Customer</Typography>

              <InfoItemRow
                title="First name"
                value={data.customerData.firstName}
              />

              <InfoItemRow
                title="Last name"
                value={data.customerData.lastName}
              />

              <InfoItemRow
                title="Phone number"
                value={data.customerData.phoneNumber}
              />

              <InfoItemRow title="Email" value={data.customerData.email} />

              <InfoItemRow
                title="Address line"
                value={data.customerData.address?.streetAddress}
              />

              {data.customerData.address?.zip && (
                <InfoItemRow
                  title="Zip"
                  value={data.customerData.address.zip}
                />
              )}

              {data.customerData.address?.city && (
                <InfoItemRow
                  title="City"
                  value={data.customerData.address.city}
                />
              )}

              {data.jobData.pdfLink && (
                <Typography>
                  <strong>
                    <a
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ color: doneColor }}
                      href={data.jobData.pdfLink.url}
                    >
                      Pdf link
                    </a>
                  </strong>
                </Typography>
              )}
            </Stack>
          </Grid2>

          <Grid2 m={4}>
            <Stack maxWidth={400} divider={<Divider />} spacing={1}>
              <Typography variant="h6">Installation details</Typography>

              <InfoItemRow
                title={`${partner.toUpperCase()} reference`}
                value={data.jobData.externalReference}
              />

              <InfoItemRow
                title={"Description"}
                value={data.jobData.description}
              />
            </Stack>
          </Grid2>

          {data.jobData.images && (
            <Grid2 m={4} maxWidth={300}>
              <Stack divider={<Divider />} spacing={1}>
                <Typography variant="h6">Images</Typography>

                <Box display="flex" flexWrap="wrap">
                  {data.jobData.images?.map((e, index) => (
                    <Box key={index} sx={{ position: "relative" }}>
                      <Avatar
                        onClick={() => {
                          window.open(e.url, "_blank");
                        }}
                        sx={{ marginTop: 2, marginRight: 1, marginBottom: 1 }}
                        src={e.url}
                        variant="rounded"
                        style={{ width: 80, height: 80, marginRight: 20 }}
                      />
                      <IconButton
                        onClick={() => onDeleteImage(e.url)}
                        size="small"
                        sx={{
                          background: "background.default",
                          borderRadius: 16,
                          position: "absolute",
                          top: 0,
                          right: 0,
                        }}
                      >
                        <Delete />
                      </IconButton>
                    </Box>
                  ))}
                </Box>
              </Stack>
            </Grid2>
          )}
        </Grid2>
      </Stack>

      {data.jobData.noteForCraftsman && (
        <Alert
          action={
            <Button
              onClick={() => {
                onDeleteCraftsmanNote(index);
              }}
              size="small"
              color="inherit"
            >
              Remove
            </Button>
          }
          variant="standard"
          severity="warning"
          icon={false}
        >
          <AlertTitle>Company internal note:</AlertTitle>
          {data.jobData.noteForCraftsman}
        </Alert>
      )}
    </Card>
  );
};

export default UploadJobCard;
