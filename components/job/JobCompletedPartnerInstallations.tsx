import {
  collection,
  DocumentReference,
  getFirestore,
  query,
  where,
} from "firebase/firestore";
import { FC } from "react";
import { useCompletedPartnerInstallations } from "../../hooks/completedPartnerInstallations/completedPartnerInstallationsHook";
import { useAuthUser } from "../auth/AuthProvider";

import Loading from "../common/Loading";
import CompletedPartnerInstallationCard from "./CompletedPartnerInstallationCard";

interface JobCompletedPartnerInstallationsProps {
  jobReference: DocumentReference;
}

const JobCompletedPartnerInstallations: FC<
  JobCompletedPartnerInstallationsProps
> = ({ jobReference }) => {
  const authUser = useAuthUser();

  const jobsQuery = authUser.partner
    ? query(
        collection(getFirestore(), "completedPartnerInstallations"),
        where("job", "==", jobReference),
        where("partnership", "==", authUser.partner),
      )
    : query(
        collection(getFirestore(), "completedPartnerInstallations"),
        where("job", "==", jobReference),
      );

  const { completedPartnerInstallations, loading } =
    useCompletedPartnerInstallations(jobsQuery);

  if (loading) return <Loading />;

  return (
    <>
      {completedPartnerInstallations &&
      completedPartnerInstallations.length != 0 ? (
        completedPartnerInstallations?.map((completedPartnerInstallation) => (
          <CompletedPartnerInstallationCard
            key={completedPartnerInstallation.reference.id}
            installation={completedPartnerInstallation}
          />
        ))
      ) : (
        <p>No completed partner installations reported.</p>
      )}
    </>
  );
};

export default JobCompletedPartnerInstallations;
