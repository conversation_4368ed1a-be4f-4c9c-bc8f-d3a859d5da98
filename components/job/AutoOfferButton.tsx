import { <PERSON>Mode, Cancel } from "@mui/icons-material";
import { But<PERSON>, DialogContentText, Stack, Alert } from "@mui/material";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { JobOffer } from "../../models/job/offer/job-offer";
import ConfirmDialog, { ConfirmActions } from "../dialogs/ConfirmDialog";
import { useDialog } from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import { DialogProps } from "../dialogs/default-dialog-props";
import { cancelAutoOffer } from "./JobOfferQueuedOfferRow";
import { useJob } from "../../hooks/job/jobHook";

export default function AutoOfferButton({ offer }: { offer: JobOffer }) {
  const { open: initiateOfferDialog } = useDialog("initiate-auto-offer");
  const { open: cancelAllAutoOffersDialog } = useDialog(
    "cancel-all-auto-offers",
  );

  const { job } = useJob(offer.reference.id);

  if (!job) return null;

  const hasScheduledOffer =
    offer.queuedOffers &&
    Object.values(offer.queuedOffers).filter((e) => e.status === "scheduled")
      .length;

  const numberOfAvailableScheduledOffers =
    (offer.suggestedCompanies &&
      Object.keys(offer.suggestedCompanies)
        .filter((e) => !offer.offeredTo?.map((e) => e.id).includes(e))
        .map((e) => offer.queuedOffers?.[e]?.status)
        .filter((e) => e !== "sent").length) ||
    0;

  const autoOffersAvailable = Boolean(
    job.articles?.length &&
      job.cache?.customerLocation?.coordinates &&
      offer.suggestedCompanies &&
      Object.values(offer.suggestedCompanies).length &&
      numberOfAvailableScheduledOffers,
  );

  return (
    <Stack spacing={1}>
      <ManagedDialog
        id="initiate-auto-offer"
        component={InitiateAutoOfferDialog}
        jobOffer={offer}
      />
      <ManagedDialog
        id="cancel-all-auto-offers"
        component={CancelAllAutoOffersDialog}
        jobOffer={offer}
      />

      {hasScheduledOffer ? (
        <Button
          startIcon={<Cancel />}
          sx={{
            justifyContent: "flex-start",
          }}
          onClick={() => cancelAllAutoOffersDialog()}
        >
          Stop automatic offers (
          {offer.queuedOffers &&
            Object.values(offer.queuedOffers).filter(
              (e) => e.status === "scheduled",
            ).length}{" "}
          scheduled)
        </Button>
      ) : (
        <Button
          variant="contained"
          disabled={!autoOffersAvailable}
          onClick={() => initiateOfferDialog()}
          sx={{
            justifyContent: "flex-start",
          }}
          startIcon={<AutoMode />}
        >
          Initiate auto offers ({numberOfAvailableScheduledOffers} companies)
        </Button>
      )}
    </Stack>
  );
}

interface CancelAllAutoOffersDialogProps extends DialogProps {
  jobOffer: JobOffer;
}

export function CancelAllAutoOffersDialog({
  jobOffer,
  isOpen,
  close,
}: CancelAllAutoOffersDialogProps) {
  const scheduledAutoOffers =
    jobOffer.queuedOffers &&
    Object.values(jobOffer.queuedOffers).filter(
      (e) => e.status === "scheduled",
    );

  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={close}
      title={"Stop auto offers"}
      actions={[
        ConfirmActions.cancel,
        {
          title: "Stop all",
          onClick: async () => {
            await Promise.all(
              scheduledAutoOffers!.map(async (queuedOffer) => {
                const payload = {
                  taskId: queuedOffer.taskId,
                  jobOfferId: jobOffer.reference.id,
                  companyId: queuedOffer.company.id,
                };

                await cancelAutoOffer(payload);
              }),
            );
          },
        },
      ]}
    >
      <DialogContentText>Stop all scheduled auto offers.</DialogContentText>
    </ConfirmDialog>
  );
}

interface InitiateAutoOfferDialogProps extends DialogProps {
  jobOffer: JobOffer;
}

export function InitiateAutoOfferDialog({
  jobOffer,
  isOpen,
  close,
}: InitiateAutoOfferDialogProps) {
  const hasScheduledOffer =
    jobOffer.queuedOffers &&
    Object.values(jobOffer.queuedOffers).filter((e) => e.status === "scheduled")
      .length;

  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={close}
      title={"Initiate auto offers"}
      actions={[
        ConfirmActions.cancel,
        {
          title: "Initiate",
          onClick: async () => {
            await initiateOfferQueue(jobOffer);
          },
        },
      ]}
    >
      {hasScheduledOffer ? (
        <Alert severity="warning">
          Auto offers are already in progress. By continuing, this will replace
          scheduled offers with new offers in the queue
        </Alert>
      ) : (
        <></>
      )}

      <DialogContentText>
        Initiate auto offers, will queue new offers automatically.
      </DialogContentText>
    </ConfirmDialog>
  );
}

async function initiateOfferQueue(jobOffer: JobOffer) {
  const payload = {
    jobOfferId: jobOffer.reference.id,
  };

  await httpsCallable(
    getFunctions(getApp(), "europe-west1"),
    "jobOfferInitiateTaskQueue",
  )(payload);
}
