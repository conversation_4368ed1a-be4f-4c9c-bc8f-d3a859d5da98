import PaginatedDataGrid from "../../common/table/PaginatedDataGrid";
import { useMemo, useState } from "react";
import {
  capitalize,
  Chip,
  Divider,
  IconButton,
  List,
  ListItemButton,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { generateJobRow } from "../../../models/job/job-row";
import { GridCellParams } from "@mui/x-data-grid";
import { getFirestore, query, collection, orderBy } from "@firebase/firestore";
import PartnersJob from "../PartnersJob";
import { where } from "firebase/firestore";
import JobEventsProgress from "../JobEventsProgress";
import {
  ExtendedGridColDef,
  getColumnVisibilityModel,
  optional,
  relativeDateTimeColumn,
  stringColumn,
} from "../../common/table/columns";
import { partnerRef } from "../../../models/other/partner";
import {
  Content,
  DetailsPanel,
  <PERSON><PERSON>,
  MainPanel,
  MenuPanel,
} from "../../layout/panels";
import { assertNever } from "../../../lib/assert-never";
import { Job } from "../../../models/job/job";
import OfferChip from "./lib/OfferChip";
import { colorForScheduledDate } from "./lib/color-for-date";
import Communication from "./lib/Communication";
import TicketInfo from "./lib/TicketInfo";
import { Close, Verified } from "@mui/icons-material";
import { Subheader } from "../../common/lists/Subheader";
import { useFirestoreCount } from "../../../lib/hooks/use-firestore-count";
import { formatTime } from "../../../lib/format-time-relative";
import { isDefined } from "../../../lib/filter-undefined";
import { usePeriodicRender } from "../../../lib/hooks/periodic-render";
import { isAfter } from "date-fns";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { usePanelSelection } from "../../../lib/hooks/use-panel-selection";

interface PartnerJobsTableProps {
  partner: string;
}

const orderStatusForJob = (job: Job) => {
  switch (job.status) {
    case undefined:
    case "pending":
    case "inbox":
      return "Not matched";
    case "open": {
      return "Matched";
    }
    case "closed":
      return "Closed";
    case "trashed":
      return "Closed";
    default:
      assertNever(job.status);
  }
};

const orderStatusColorForJob = (job: Job) => {
  switch (job.status) {
    case undefined:
    case "pending":
    case "inbox":
      return "info";
    case "open": {
      return "success";
    }
    case "closed":
      return "default";
    case "trashed":
      return "default";
    default:
      assertNever(job.status);
  }
};

const columns = (filter: Filter | undefined): ExtendedGridColDef[] =>
  [
    stringColumn({
      field: "referenceNumber",
      headerName: "Done ref",
      width: 100,
      sortable: false,
      visibility: false,
    }),
    stringColumn({
      field: "externalReference",
      headerName: "Order #",
      width: 120,
      sortable: false,
    }),
    stringColumn({
      field: "customerName",
      headerName: "Customer",
      width: 180,
      sortable: false,
    }),
    stringColumn({
      field: "status",
      headerName: "Status",
      width: 120,
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Chip
            color={orderStatusColorForJob(row.data)}
            size="small"
            label={orderStatusForJob(row.data)}
          />
        );
      },
      sortable: false,
    }),
    relativeDateTimeColumn({
      field: "createTime",
      headerName: "Created",
      width: 160,
      sortable: false,
    }),
    optional(
      !["invoiced", "closed", "not-invoiced"].includes(filter ?? ""),
      relativeDateTimeColumn({
        field: "doneAt",
        headerName: "Done",
        width: 160,
        sortable: false,
      }),
    ),
    optional(
      filter !== "invoiced",
      relativeDateTimeColumn({
        field: "invoicedAt",
        headerName: "Invoiced",
        width: 160,
        sortable: false,
      }),
    ),
    stringColumn({
      headerName: "Latest event",
      disableExport: true,
      field: "events",
      renderCell: (params: GridCellParams) => {
        return (
          <Stack direction="row" alignItems="center" spacing={1}>
            <JobEventsProgress job={params.row.data} showOnlyLatest />
            <Communication
              lastCommunicatedTime={params.row.data.lastCommunicatedTime}
            />
          </Stack>
        );
      },
      sortable: false,
    }),
    stringColumn({
      headerName: "Extras",
      disableExport: true,
      sortable: false,
      width: 145,
      field: "offerStatus",
      renderCell: ({ row, value }) => {
        const isChecked = Boolean(row.offersCheckedAt);
        const status = value?.status ?? (isChecked ? "noOffer" : null);
        return (
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
            height={"100%"}
          >
            {isChecked && (
              <Tooltip
                title={`Extras verified ${formatTime(row.offersCheckedAt)}`}
                arrow
              >
                <Verified
                  color={
                    !row.offerSentAt ||
                    isAfter(row.offersCheckedAt, row.offerSentAt)
                      ? "primary"
                      : "warning"
                  }
                  fontSize="small"
                />
              </Tooltip>
            )}
            {status && <OfferChip status={status} date={value?.date} />}
          </Stack>
        );
      },
    }),
    relativeDateTimeColumn({
      field: "scheduledWorkStartTime",
      headerName: "Scheduled",
      sortable: false,
      width: 150,
      color: colorForScheduledDate,
    }),
    stringColumn({
      field: "Inbox",
      headerName: "Inbox",
      sortable: false,
      width: 250,
      renderCell: (params: GridCellParams) => {
        return (
          <TicketInfo
            subjectRef={params.row.data.reference}
            participant="partner"
          />
        );
      },
    }),
  ].filter(isDefined);

interface CountedListItemProps {
  title: string;
  count: number;
  selected?: boolean;
  onClick: () => void;
}

function CountedListItem({
  title,
  count,
  selected = false,
  onClick,
}: CountedListItemProps) {
  return (
    <ListItemButton
      onClick={onClick}
      selected={selected}
      sx={{ backgroundColor: "background.paper" }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        width="100%"
      >
        <Typography variant="body2">{title}</Typography>
        <Typography variant="body2" textAlign="right" color="text.secondary">
          {count}
        </Typography>
      </Stack>
    </ListItemButton>
  );
}

interface FilterListItemProps extends Omit<CountedListItemProps, "count"> {
  partner: string;
  filter: Filter;
}

function FilterListItem({
  title,
  filter,
  selected,
  onClick,
  partner,
}: FilterListItemProps) {
  usePeriodicRender(10000);
  const query = useJobsQuery(partner, filter);
  const [count] = useFirestoreCount(query);

  return (
    <CountedListItem
      title={title}
      count={count ?? 0}
      selected={selected}
      onClick={onClick}
    />
  );
}

function useJobsQuery(partner: string, filter?: Filter) {
  return useMemo(() => {
    const queryRef = query(
      collection(getFirestore(), "jobs"),
      where("merchant", "==", partnerRef(partner)),
    );

    switch (filter) {
      case "all":
        return query(
          queryRef,
          where("status", "in", ["open", "closed", "inbox"]),
          orderBy("createTime", "desc"),
        );
      case "open":
        return query(
          queryRef,
          where("status", "in", ["open", "inbox"]),
          orderBy("createTime", "desc"),
        );
      case "not-matched":
        return query(
          queryRef,
          where("status", "in", ["inbox"]),
          orderBy("createTime", "desc"),
        );
      case "matched":
        return query(
          queryRef,
          where("status", "in", ["open"]),
          orderBy("createTime", "desc"),
        );
      case "scheduled":
        return query(
          queryRef,
          where("status", "in", ["open", "inbox"]),
          where("scheduledWorkStartTime", "!=", null),
          orderBy("scheduledWorkStartTime", "desc"),
        );
      case "accepted-extras":
        return query(
          queryRef,
          where("status", "in", ["open", "inbox", "closed"]),
          where("events.offerAccepted", "!=", null),
          orderBy("events.offerAccepted", "desc"),
        );
      case "verified-extras":
        return query(
          queryRef,
          where("status", "in", ["open", "inbox", "closed"]),
          where("events.offersChecked", "!=", null),
          orderBy("events.offersChecked", "desc"),
        );
      case "closed":
        return query(
          queryRef,
          where("status", "==", "closed"),
          orderBy("events.jobDone", "desc"),
        );
      case "not-invoiced":
        return query(
          queryRef,
          where("status", "==", "closed"),
          where("events.partnerBilled", "==", null),
          orderBy("events.jobDone", "desc"),
        );
      case "invoiced":
        return query(
          queryRef,
          where("status", "==", "closed"),
          where("events.partnerBilled", "!=", null),
          orderBy("events.partnerBilled", "desc"),
        );
      default:
        return queryRef;
    }
  }, [partner, filter]);
}

type Filter =
  | "all"
  | "open"
  | "not-matched"
  | "matched"
  | "scheduled"
  | "closed"
  | "not-invoiced"
  | "invoiced"
  | "extras"
  | "accepted-extras"
  | "verified-extras";

export default function PartnerOrdersPage({ partner }: PartnerJobsTableProps) {
  const [filter, setFilter] = useState<Filter>("open");
  const [jobId, , detailsPanelRef, handleJobSelect, handleClose] =
    usePanelSelection<string>();

  return (
    <PanelGroup autoSaveId="partner-orders" direction="horizontal">
      <MenuPanel>
        <Header title="Orders" />
        <Content>
          <List disablePadding>
            <FilterListItem
              title="All orders"
              selected={filter === "all"}
              filter="all"
              onClick={() => setFilter("all")}
              partner={partner}
            />
            <Subheader>Open</Subheader>
            <FilterListItem
              title="All open"
              filter="open"
              selected={filter === "open"}
              onClick={() => setFilter("open")}
              partner={partner}
            />
            <FilterListItem
              title="Not matched"
              filter="not-matched"
              selected={filter === "not-matched"}
              onClick={() => setFilter("not-matched")}
              partner={partner}
            />
            <FilterListItem
              title="Matched"
              filter="matched"
              selected={filter === "matched"}
              onClick={() => setFilter("matched")}
              partner={partner}
            />
            <FilterListItem
              title="Scheduled"
              filter="scheduled"
              selected={filter === "scheduled"}
              onClick={() => setFilter("scheduled")}
              partner={partner}
            />
            <Subheader>Extras</Subheader>
            <FilterListItem
              title="Accepted"
              filter="accepted-extras"
              selected={filter === "accepted-extras"}
              onClick={() => setFilter("accepted-extras")}
              partner={partner}
            />
            <FilterListItem
              title="Verified"
              filter="verified-extras"
              selected={filter === "verified-extras"}
              onClick={() => setFilter("verified-extras")}
              partner={partner}
            />
            <Subheader>Closed</Subheader>
            <FilterListItem
              title="All closed"
              filter="closed"
              selected={filter === "closed"}
              onClick={() => setFilter("closed")}
              partner={partner}
            />
            <FilterListItem
              title="Not invoiced"
              filter="not-invoiced"
              selected={filter === "not-invoiced"}
              onClick={() => setFilter("not-invoiced")}
              partner={partner}
            />
            <FilterListItem
              title="Invoiced"
              filter="invoiced"
              selected={filter === "invoiced"}
              onClick={() => setFilter("invoiced")}
              partner={partner}
            />
            <Divider />
          </List>
        </Content>
      </MenuPanel>
      <PanelResizeHandle />
      <MainPanel>
        <Header title={`${capitalize(filter.replace("-", " "))} orders`} />
        <Content>
          <JobsDataGrid
            partner={partner}
            filter={filter}
            selectedJobId={jobId ?? undefined}
            onRowSelected={handleJobSelect}
          />
        </Content>
      </MainPanel>
      <PanelResizeHandle />
      <DetailsPanel ref={detailsPanelRef} collapsible>
        <Header title="Details">
          <IconButton onClick={handleClose}>
            <Close />
          </IconButton>
        </Header>
        <Content>{jobId && <PartnersJob jobId={jobId} />}</Content>
      </DetailsPanel>
    </PanelGroup>
  );
}

interface JobsDataGridProps {
  partner: string;
  onRowSelected: (id: string) => void;
  filter?: Filter;
  selectedJobId?: string;
}

function JobsDataGrid({
  partner,
  onRowSelected,
  filter,
  selectedJobId,
}: JobsDataGridProps) {
  const jobsQuery = useJobsQuery(partner, filter);
  const visibilityColumns = useMemo(
    () => getColumnVisibilityModel(columns(filter)),
    [filter],
  );

  return (
    <PaginatedDataGrid
      sx={{ border: "none" }}
      density="compact"
      onRowSelected={onRowSelected}
      rowGenerator={generateJobRow}
      query={jobsQuery}
      columns={columns(filter)}
      pageSize={50}
      initialState={{
        columns: {
          columnVisibilityModel: visibilityColumns,
        },
      }}
    />
  );
}
