import { Avatar, Box, Button, Grid2, Stack } from "@mui/material";
import Company from "../../company/CompanyDetails";
import { DataGrid, GridCellParams } from "@mui/x-data-grid";
import TableLoadingOverlay from "../../tables/TableLoadingOverlay";
import { useCollection } from "react-firebase-hooks/firestore";
import { useState } from "react";

import { collection, getFirestore } from "@firebase/firestore";

import PageContainer from "../../layout/PageContainer";
import { FetchAirtableButton } from "../../settings/CmsAirtable";
import { ExtendedGridColDef } from "../../common/table/columns";

const FixedPriceJobsTable = () => {
  const db = getFirestore();

  const [value, loading, error] = useCollection(
    collection(db, "fixed-price-jobs"),
  );
  const [companyId, setCompanyId] = useState<null | string>();

  if (error) return <p>{`ERROR ${error}`}</p>;

  const rows =
    !value || loading
      ? []
      : value.docs?.map((doc) => ({
          image: doc.data().imageUrl,
          data: doc.data(),
          id: doc.id,
          title: doc.data().title,
        }));

  const columns: ExtendedGridColDef[] = [
    {
      field: "image",
      headerName: "Image",
      width: 80,
      renderCell: (params: GridCellParams) => {
        return (
          <Avatar sizes="small" variant="rounded" src={params.row.image} />
        );
      },
    },
    { field: "title", headerName: "Title", width: 200 },
    { field: "id", headerName: "Id", width: 800 },
  ];

  return (
    <PageContainer>
      <Stack direction="row">
        <Box marginTop={1} width={400}>
          <FetchAirtableButton
            buttonTitle="Update fixed price jobs"
            functionName="fetchFixedPriceJobsFromAirtable"
          />
        </Box>
      </Stack>

      <Grid2 container>
        <Grid2 size={{ xs: 12, md: companyId ? 4 : 12 }}>
          <DataGrid
            sx={{ backgroundColor: "paper" }}
            style={{
              height: "calc(100vh - 80px)",
              width: "100%",
            }}
            slots={{
              loadingOverlay: TableLoadingOverlay,
            }}
            disableRowSelectionOnClick
            loading={loading}
            rows={rows}
            columns={columns}
          />
        </Grid2>

        {companyId && (
          <Grid2
            size={{ xs: 12, md: 8 }}
            style={{
              height: "calc(100vh - 55px)",
              overflow: "scroll",
            }}
          >
            <Button
              onClick={() => {
                setCompanyId(null);
              }}
            >
              Close
            </Button>
            <Company companyId={companyId as string} />
          </Grid2>
        )}
      </Grid2>
    </PageContainer>
  );
};

export default FixedPriceJobsTable;
