import { ChatOutlined } from "@mui/icons-material";
import { isOlderThanDays } from "../../../../lib/date-compare";
import { DateOrTimestamp } from "../../../../lib/date-or-timestamp";
import { Tooltip } from "@mui/material";
import formatTimeRelative from "../../../../lib/format-time-relative";

const isOld = isOlderThanDays(3, false);
const isVeryOld = isOlderThanDays(7, false);

const colorForDate = (value: DateOrTimestamp) => {
  if (!value) return "disabled";
  if (isVeryOld(value)) return "error";
  if (isOld(value)) return "warning";
  return "primary";
};

/**
 * An icon that displays the last communication time of a job.
 */
export default function Communication({
  lastCommunicatedTime,
}: {
  lastCommunicatedTime: Date;
}) {
  return (
    <Tooltip
      title={`Last communication ${formatTimeRelative(lastCommunicatedTime)}`}
      arrow
    >
      <ChatOutlined color={colorForDate(lastCommunicatedTime)} />
    </Tooltip>
  );
}
