import { isToday } from "date-fns";
import { isOlderThanDays } from "../../../../lib/date-compare";

import { DateOrTimestamp, getDate } from "../../../../lib/date-or-timestamp";

const isOld = isOlderThanDays(1, false);
const isVeryOld = isOlderThanDays(5, false);

/**
 * Get the color for a scheduled date.
 */
export const colorForScheduledDate = (value: DateOrTimestamp) => {
  const date = getDate(value);
  if (!date) return "disabled";
  if (isToday(date)) return "success";
  if (isVeryOld(date)) return "error";
  if (isOld(date)) return "warning";
  return "primary";
};
