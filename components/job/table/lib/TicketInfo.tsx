import { formatTinyDistance } from "../../../../lib/format-time-relative";
import { <PERSON><PERSON>, Tooltip, Chip, Skeleton, Typography } from "@mui/material";

import formatTimeRelative from "../../../../lib/format-time-relative";

import { DocumentReference } from "firebase/firestore";
import { issueForSubjectQuery } from "../../../../models/inbox/provider";

import { useCollection } from "react-firebase-hooks/firestore";
import UserAvatar from "../../../user/UserAvatar";
import { ErrorOutline, PersonOutline } from "@mui/icons-material";
import { IssueParticipant, IssueStatus } from "../../../../models/inbox/issue";
import { useAuthUser } from "../../../auth/AuthProvider";

const colorForIssueStatus = (status?: IssueStatus) => {
  switch (status) {
    case "open":
      return "warning";
    case "snoozed":
      return "info";
    case "closed":
      return "primary";
    default:
      return "default";
  }
};

interface TicketInfoProps {
  /** Reference to the subject */
  subjectRef: DocumentReference;
  /** Participant of the ticket */
  participant: IssueParticipant;
}

/**
 * A component that displays summary of any ticket associated with a subject
 */
export default function TicketInfo({
  subjectRef,
  participant,
}: TicketInfoProps) {
  const { partnerDoc } = useAuthUser();
  const [issues, loading, error] = useCollection(
    issueForSubjectQuery(subjectRef, partnerDoc?.reference),
  );
  const issue = issues?.docs[0]?.data();
  const info = issue?.participants?.[participant];

  if (loading)
    return (
      <Stack
        direction="row"
        alignItems="center"
        spacing={0.5}
        sx={{ width: "100%" }}
      >
        <Skeleton variant="text" width={150} sx={{ fontSize: "1rem" }} />
      </Stack>
    );

  if (error)
    return (
      <Tooltip title="Failed to load ticket" arrow>
        <ErrorOutline color="disabled" />
      </Tooltip>
    );

  if (!info) return null;

  return (
    <Tooltip
      title={`${info.status}${
        info.snoozedUntil
          ? ` - ends ${formatTimeRelative(info.snoozedUntil)}`
          : ""
      } - last update ${formatTimeRelative(info.lastUpdateTime)} - ${
        info.cache?.lastMessage
      }`}
      arrow
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={0.5}
        height={"100%"}
        width={"100%"}
      >
        <Chip
          label={formatTinyDistance(info.lastUpdateTime)}
          size="small"
          variant="outlined"
          color={colorForIssueStatus(info.status)}
          avatar={
            info.cache?.lastAuthor ? (
              <UserAvatar userRef={info.cache?.lastAuthor} size="tiny" />
            ) : undefined
          }
          icon={info.cache?.lastAuthor ? undefined : <PersonOutline />}
        />
        <Typography variant="body2" noWrap>
          {info.cache?.lastMessage}
        </Typography>
      </Stack>
    </Tooltip>
  );
}
