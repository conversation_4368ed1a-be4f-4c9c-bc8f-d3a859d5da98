import { formatTinyDistance } from "../../../../lib/format-time-relative";
import { Chip } from "@mui/material";
import { Tooltip } from "@mui/material";
import { formatTime } from "../../../../lib/format-time-relative";

interface OfferChipProps {
  status: "offerSent" | "offerAccepted" | "offerDeclined" | "noOffer";
  date?: Date;
}

/**
 * A chip that displays the status of an offer.
 */
export default function OfferChip({ status, date }: OfferChipProps) {
  switch (status) {
    case "offerSent":
      return (
        <Tooltip title={`Sent to customer ${formatTime(date)}`} arrow>
          <Chip
            label={`Sent ${formatTinyDistance(date)}`}
            size="small"
            variant="outlined"
            color={"warning"}
          />
        </Tooltip>
      );
    case "offerAccepted":
      return (
        <Tooltip title={`Accepted by customer ${formatTime(date)}`} arrow>
          <Chip
            label={`Accepted ${formatTinyDistance(date)}`}
            size="small"
            variant="outlined"
            color={"success"}
          />
        </Tooltip>
      );
    case "offerDeclined":
      return (
        <Tooltip title={`Declined by customer ${formatTime(date)}`} arrow>
          <Chip
            label={`Declined ${formatTinyDistance(date)}`}
            size="small"
            variant="outlined"
            color={"error"}
          />
        </Tooltip>
      );
    case "noOffer":
      return (
        <Tooltip title={`No extras expected`} arrow>
          <Chip
            label={`No extras`}
            size="small"
            variant="outlined"
            color={"primary"}
          />
        </Tooltip>
      );
  }
  return null;
}
