import PaginatedDataGrid from "../../common/table/PaginatedDataGrid";
import React, { useMemo, useState } from "react";
import {
  Box,
  Button,
  Chip,
  Dialog,
  Grid2,
  Tab,
  Tabs,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
} from "@mui/material";
import { generateJobRow } from "../../../models/job/job-row";
import { GridCellParams } from "@mui/x-data-grid";
import { useMediaQuery } from "react-responsive";
import {
  getFirestore,
  query,
  collection,
  orderBy,
  Query,
} from "@firebase/firestore";
import JobContainer from "../Job";
import UserContainer from "../../user/UserContainer";
import CompanyContainer from "../../company/details/CompanyDetails";
import { FilterTabs } from "../../company/CompaniesTable";
import {
  COMPANY_SERVICES,
  JOB_STATUSES,
  PARTNER_TYPE_OPTION,
  JOBS_TABLE_TYPES,
} from "../../../config/constants";
import { Timestamp, where } from "firebase/firestore";
import PageContainer from "../../layout/PageContainer";
import PageGridContainer from "../../layout/PageGridContainer";
import ToolsStack from "../../layout/ToolsStack";
import formatTimeRelative, {
  formatTime,
} from "../../../lib/format-time-relative";
import { getDate } from "../../../lib/date-or-timestamp";
import { Job } from "../../../models/job/job";
import { differenceInHours } from "date-fns";
import { usePartners } from "../../../hooks/partner/partnersHook";
import Loading from "../../common/Loading";
import { CellJobInternalComments } from "../CellJobInternalComments";
import {
  dateTimeColumn,
  ExtendedGridColDef,
  getColumnVisibilityModel,
  stringColumn,
} from "../../common/table/columns";
import { CellJobChatMessages } from "../CellJobChatMessages";
import {
  DonePartnerReference,
  PartnerSegment,
} from "../../../models/other/partner";
import JobEventsProgress from "../JobEventsProgress";

export type JobsTableType =
  | "marketPlace"
  | "partner"
  | "fixedPrice"
  | "subcontractor";
interface JobsTableProps {
  initCollection?: Query;
  defaultStatusFilter?: "inbox" | "open" | "closed" | "trashed" | null;
}

function tableColumns(statusFilter: string | null): ExtendedGridColDef[] {
  const now = new Date();

  const inboxColumns: ExtendedGridColDef[] =
    statusFilter !== "inbox"
      ? [
          {
            field: "",
            headerName: "Com…",
            renderCell: (params: GridCellParams) => {
              const jobId = params.row.id;
              return <CellJobInternalComments jobId={jobId} />;
            },
            width: 60,
          },
          {
            field: "lastChatMessage",
            headerName: "Last chat messages",
            renderCell: (params: GridCellParams) => {
              const jobId = params.row.id;
              const lastChatMessage = params.row.data.lastChatMessage;
              return (
                <CellJobChatMessages
                  jobId={jobId}
                  lastChatMessage={lastChatMessage}
                />
              );
            },
            width: 250,
          },
        ]
      : [];

  const closedColumns: ExtendedGridColDef[] =
    statusFilter == "closed"
      ? [
          {
            field: "derived_status",
            headerName: "Result",
            width: 150,
            renderCell: (params: GridCellParams) => {
              return params.row.data.events?.jobDone ? (
                <Chip
                  color="success"
                  size="small"
                  style={{
                    width: 100,
                  }}
                  label="Done"
                />
              ) : (
                <Chip
                  size="small"
                  color="error"
                  style={{
                    width: 100,
                  }}
                  label="Cancelled"
                />
              );
            },
          },
          {
            field: "closeReason",
            headerName: "Close reason",
            width: 200,
            renderCell: (params: GridCellParams) => {
              return <> {params.row.data.closeReason} </>;
            },
          },
        ]
      : [];

  const notInboxColumns: ExtendedGridColDef[] =
    statusFilter !== "inbox"
      ? [
          stringColumn({
            field: "companyName",
            headerName: "Company",
            width: 250,
          }),
          {
            headerName: "Last Event",
            disableExport: true,
            field: "events",
            renderCell: (params: GridCellParams) => {
              return <JobEventsProgress job={params.row.data} showOnlyLatest />;
            },
          },
          {
            field: "lastCommunicatedTime",
            headerName: "Last message",
            type: "dateTime",
            width: 160,
            valueFormatter: (value: Timestamp) => formatTimeRelative(value),
            renderCell: ({
              row,
              value,
              formattedValue,
            }: GridCellParams<{ data: Job }, Timestamp, string>) => {
              const isActive =
                row.data.status !== "closed" &&
                row.data.status !== "trashed" &&
                !Boolean(row.data.events?.jobDone);
              const warn = differenceInHours(now, getDate(value) ?? now) > 6;
              const error = differenceInHours(now, getDate(value) ?? now) > 24;
              const level = isActive
                ? error
                  ? "error"
                  : warn
                    ? "warning"
                    : "primary"
                : "primary";
              return (
                <Tooltip title={formatTime(value)} arrow>
                  <Typography variant="body2" color={level}>
                    {formattedValue ?? ""}
                  </Typography>
                </Tooltip>
              );
            },
          },
        ]
      : [];

  return [
    dateTimeColumn({
      field: "createTime",
      headerName: "Created at",
      width: 160,
    }),
    {
      field: "partner",
      headerName: "Partner",
      width: 160,
    },
    {
      field: "status",
      headerName: "Status",
      width: 160,
    },

    ...inboxColumns,

    stringColumn({
      field: "description",
      headerName: "Description",
      width: 300,
    }),
    ...closedColumns,
    {
      field: "customerLocation",
      headerName: "Service Area",
      width: 150,
      visibility: false,
    },
    stringColumn({
      field: "location",
      headerName: "Location",
      width: 160,
    }),
    stringColumn({
      field: "customerName",
      headerName: "Customer",
      width: 220,
    }),

    ...notInboxColumns,

    {
      field: "operationTags",
      headerName: "Operation tags",
      width: 200,
      visibility: false,
    },
    { field: "services", headerName: "Services", width: 160 },
    { field: "tags", headerName: "Tags", width: 160, visibility: false },
    {
      field: "discountCode",
      headerName: "Discount code",
      width: 160,
      visibility: false,
    },
    {
      field: "utm_source",
      headerName: "Utm source",
      width: 160,
      visibility: false,
    },
    {
      field: "utm_category",
      headerName: "Utm category",
      width: 160,
      visibility: false,
    },
    {
      field: "utm_campaign",
      headerName: "Utm campaign",
      width: 160,
      visibility: false,
    },
  ];
}

export default function JobsTable({
  initCollection,
  defaultStatusFilter = null,
}: JobsTableProps) {
  const [tableType, setTableType] = useState<JobsTableType | undefined>(
    undefined,
  );
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 960px)" });
  const [companyId, setCompanyId] = useState<null | string>(null);
  const [customerId, setCustomerId] = useState<null | string>(null);
  const [jobId, setJobId] = useState<null | string>();
  const [tabValue, setTabValue] = React.useState(0);

  const { partners, loading } = usePartners();

  const [statusFilter, setStatusFilter] = useState<string | null>(
    defaultStatusFilter,
  );

  const [partnerFilter, setPartnerFilter] = useState<string | null>(null);

  const [partnerSegment, setPartnerSegment] =
    useState<PartnerSegment>("ev-chargers");

  const [serviceTypeFilter, setServiceTypeFilter] = useState<string | null>(
    null,
  );

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setTableType(JOBS_TABLE_TYPES[newValue].value);
  };

  const jobCollection = useMemo(() => {
    let jobsQuery = query(
      collection(getFirestore(), "jobs"),
      where("network", "==", DonePartnerReference()),
      orderBy("createTime", "desc"),
    );

    if (initCollection) {
      jobsQuery = initCollection;
    }

    switch (tableType) {
      case "partner":
        jobsQuery = query(
          jobsQuery,
          partnerFilter
            ? where("tags", "array-contains", partnerFilter)
            : where("tags", "array-contains", partnerSegment),
        );
        break;

      case "fixedPrice":
        jobsQuery = query(jobsQuery, where("tags", "==", ["fixedPrice"]));
        break;

      case "subcontractor":
        jobsQuery = query(
          jobsQuery,
          where("merchant", "==", DonePartnerReference()),
        );
        break;

      case "marketPlace":
        jobsQuery = query(jobsQuery, where("tags", "==", []));
        break;
    }

    if (serviceTypeFilter) {
      jobsQuery = query(
        jobsQuery,
        where("services", "array-contains", serviceTypeFilter),
      );
    }

    if (statusFilter) {
      jobsQuery = query(jobsQuery, where("status", "==", statusFilter));
    }

    return jobsQuery;
  }, [
    initCollection,
    tableType,
    partnerFilter,
    partnerSegment,
    serviceTypeFilter,
    statusFilter,
  ]);

  const columns = useMemo(() => tableColumns(statusFilter), [statusFilter]);

  const visibilityColumns = useMemo(
    () => getColumnVisibilityModel(columns),
    [],
  );

  if (loading) return <Loading />;

  return (
    <PageContainer>
      <PageGridContainer>
        <Grid2>
          <ToolsStack>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
              <Tabs
                onChange={handleChange}
                value={tabValue}
                aria-label="basic tabs example"
              >
                {JOBS_TABLE_TYPES.map((e) => {
                  return <Tab id={e.value} key={e.value} label={e.label} />;
                })}
              </Tabs>
            </Box>
            <FilterTabs
              label="Status"
              filter={statusFilter}
              onFilterChange={(filter) => setStatusFilter(filter)}
              filterConfig={JOB_STATUSES}
            />
            {tableType === "partner" && (
              <ToggleButtonGroup
                color="primary"
                size="small"
                value={partnerSegment}
                exclusive
                onChange={(
                  event: React.MouseEvent<HTMLElement>,
                  newAlignment: string,
                ) => {
                  setPartnerSegment(newAlignment as PartnerSegment);
                  setPartnerFilter(null);
                }}
                aria-label="Platform"
              >
                {PARTNER_TYPE_OPTION.map((type) => {
                  return (
                    <ToggleButton key={type.value} value={type.value}>
                      {type.label}
                    </ToggleButton>
                  );
                })}
              </ToggleButtonGroup>
            )}

            {tableType === "marketPlace" ||
              (tableType === "fixedPrice" && (
                <FilterTabs
                  label="Service type"
                  filter={serviceTypeFilter}
                  onFilterChange={(filter) => setServiceTypeFilter(filter)}
                  filterConfig={COMPANY_SERVICES}
                />
              ))}

            {tableType === "partner" && partnerSegment === "marketplace" && (
              <FilterTabs
                label="Partner"
                filter={partnerFilter}
                onFilterChange={(filter) => setPartnerFilter(filter)}
                filterConfig={partners
                  ?.filter((e) => e.segment === "marketplace")
                  .map((e) => ({ value: e.reference.id, label: e.name }))}
              />
            )}

            {tableType === "partner" && partnerSegment !== "marketplace" && (
              <FilterTabs
                label="Partner"
                filter={partnerFilter}
                onFilterChange={(filter) => setPartnerFilter(filter)}
                filterConfig={partners
                  ?.filter((e) => e.segment === "ev-chargers")
                  .map((e) => ({ value: e.reference.id, label: e.name }))}
              />
            )}
          </ToolsStack>
        </Grid2>
        <Dialog
          fullWidth
          maxWidth="lg"
          fullScreen={isTabletOrMobile}
          open={Boolean(jobId) && isTabletOrMobile}
          onClose={() => {
            setJobId(null);
          }}
        >
          <Button
            style={{ width: 100 }}
            onClick={() => {
              setJobId(null);
            }}
          >
            Close
          </Button>
          {jobId && <JobContainer jobId={jobId} />}
        </Dialog>

        <Dialog
          maxWidth="lg"
          fullWidth
          fullScreen={isTabletOrMobile}
          open={Boolean(customerId)}
          onClose={() => {
            setCustomerId(null);
          }}
        >
          <Button
            style={{ width: 100 }}
            onClick={() => {
              setCustomerId(null);
            }}
          >
            Close
          </Button>
          {customerId && <UserContainer userId={customerId} />}
        </Dialog>

        <Dialog
          maxWidth="lg"
          fullWidth
          fullScreen={isTabletOrMobile}
          open={Boolean(companyId)}
          onClose={() => {
            setCompanyId(null);
          }}
        >
          <Button
            style={{ width: 100 }}
            onClick={() => {
              setCompanyId(null);
            }}
          >
            Close
          </Button>
          {companyId && <CompanyContainer companyId={companyId} />}
        </Dialog>
        <Grid2 size="grow" container spacing={1}>
          <Grid2 size="grow">
            <PaginatedDataGrid
              onRowSelected={(id) => {
                setJobId(jobId === id ? null : id);
              }}
              rowGenerator={generateJobRow}
              query={jobCollection}
              columns={columns}
              initialState={{
                columns: {
                  columnVisibilityModel: visibilityColumns,
                },
              }}
            />
          </Grid2>
          {Boolean(jobId) && !isTabletOrMobile && (
            <Grid2
              size={7}
              style={{
                height: "70vh", // TODO: Remove this workaround when layout issues in JobContainer is fixed.
              }}
            >
              {jobId && (
                <JobContainer
                  onClose={() => {
                    setJobId(null);
                  }}
                  jobId={jobId as string}
                />
              )}
            </Grid2>
          )}
        </Grid2>
      </PageGridContainer>
    </PageContainer>
  );
}
