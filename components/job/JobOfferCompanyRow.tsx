import {
  Warning,
  CancelRounded,
  Info,
  Sd<PERSON>ard<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@mui/icons-material";
import {
  Typography,
  Stack,
  Tooltip,
  Chip,
  Theme,
  useTheme,
  Box,
  Card,
} from "@mui/material";
import {} from "@mui/system";
import { differenceInDays } from "date-fns";
import { Timestamp } from "firebase/firestore";
import { Check } from "mdi-material-ui";
import formatTimeRelative from "../../lib/format-time-relative";
import { CompanyWithDistance } from "../../models/company/company";
import { Job } from "../../models/job/job";
import { JobOffer, JobOfferReply } from "../../models/job/offer/job-offer";
import { CompanyAvatarWithImage } from "../company/CompanyName";
import JobOfferQueuedOfferRow from "./JobOfferQueuedOfferRow";
import DistanceChip from "./JobSendOfferDistanceChip";
import { assertNever } from "../../lib/assert-never";
import { startCase } from "lodash";
import Internal from "../auth/Internal";
import { PlatformTier } from "../auth/Tiered";
import { useAuthUser } from "../auth/AuthProvider";
import { CompanyNetworks } from "../company/CompanyNetworks";
import { JSX } from "react";

export default function OfferCompanyRow({
  company,
  job,
  jobOffer,
  showDrivingDistance = true,
}: {
  company: CompanyWithDistance;
  job: Job;
  jobOffer: JobOffer;
  showDrivingDistance?: boolean;
}) {
  const { partnerDoc } = useAuthUser();
  const companySuggestionValues =
    jobOffer?.suggestedCompanies?.[company.reference.id];

  const haveDistance = Boolean(
    companySuggestionValues
      ? companySuggestionValues.distanceInMeters
      : company.distanceFromWork,
  );

  const activeSince =
    company.statistics?.lastOfferReplyTime?.toDate() &&
    differenceInDays(
      new Date(),
      company.statistics?.lastOfferReplyTime?.toDate(),
    );

  return (
    <Stack
      width="100%"
      overflow="clip"
      display={"flex"}
      spacing={1}
      paddingRight={1}
    >
      <Stack width="100%" alignItems="center" spacing={1} direction="row">
        <CompanyAvatarWithImage company={company} />

        <Typography fontWeight={600}>{company.name}</Typography>

        <Box flexGrow={1} />

        <OfferCompanyResponseRow
          offeredAt={jobOffer.offeredAt?.[company.reference.id]}
          lastSeenAt={jobOffer.lastSeenAt?.[company.reference.id]}
          reply={jobOffer.replies?.[company.reference.id]}
        />
      </Stack>
      <Stack spacing={1} direction="row">
        {showDrivingDistance && haveDistance && (
          <DistanceChip
            companySuggested={companySuggestionValues}
            company={company}
            job={job}
            jobOffer={jobOffer}
          />
        )}

        <Internal>
          {(activeSince ?? 31) > 30 && (
            <Tooltip
              arrow
              title={`Last response ${
                company.statistics?.lastOfferReplyTime
                  ? differenceInDays(
                      new Date(),
                      company.statistics?.lastOfferReplyTime?.toDate(),
                    )
                  : "N/A"
              } days ago`}
            >
              <Chip
                size="small"
                variant="outlined"
                color={"error"}
                label={"Unresponsive"}
              />
            </Tooltip>
          )}

          {company.status === "new" && (
            <Tooltip
              arrow
              title={
                <Stack spacing={1}>
                  {company.checks &&
                    Object.entries(company.checks)
                      .filter(([_, value]) => value === false)
                      .map(([key, _]) => {
                        return (
                          <Card variant="outlined" key={key}>
                            <Stack padding={1} direction="row" spacing={1}>
                              <SdCardAlert color="error" />{" "}
                              <Typography variant="body2" color="error.main">
                                {startCase(key)} is missing!
                              </Typography>
                            </Stack>
                          </Card>
                        );
                      })}
                </Stack>
              }
            >
              <Chip
                size="small"
                variant="outlined"
                color="warning"
                icon={<Warning />}
                label="Checks failed"
              />
            </Tooltip>
          )}

          {job.tags?.includes("ev-chargers") &&
            !company.tags?.includes("EV installer") && (
              <Chip
                icon={<Warning />}
                size="small"
                variant="outlined"
                label="EV installer tag missing"
                color="warning"
              />
            )}
          {company.services?.map((service) => (
            <Chip key={service} size="small" label={service} />
          ))}
        </Internal>
        <PlatformTier>
          <CompanyNetworks
            company={company}
            visibleNetworks={partnerDoc?.networks}
          />
        </PlatformTier>
      </Stack>

      {company.autoOffer && (
        <JobOfferQueuedOfferRow company={company} jobOffer={jobOffer} />
      )}
    </Stack>
  );
}

function OfferCompanyResponseRow({
  offeredAt,
  lastSeenAt,
  reply,
}: {
  offeredAt?: Timestamp;
  lastSeenAt?: Timestamp;
  reply?: JobOfferReply | null;
}) {
  const theme = useTheme();

  if (reply) {
    const answerValues = offerReplyValues(theme, reply);

    return (
      <Tooltip arrow title={answerValues.label}>
        <Chip
          size="small"
          color={answerValues.color}
          variant="outlined"
          label={answerValues.title}
        />
      </Tooltip>
    );
  }

  if (offeredAt) {
    return (
      <Tooltip
        arrow
        title={`Sent ${formatTimeRelative(offeredAt)}, ${
          lastSeenAt
            ? `last seen ${formatTimeRelative(lastSeenAt)}`
            : "not seen yet"
        }`}
      >
        <Chip
          icon={lastSeenAt && <DoneAll />}
          size="small"
          label="Sent"
          variant="outlined"
          color={"info"}
        />
      </Tooltip>
    );
  }

  return null;
}

export function offerReplyValues(
  theme: Theme,
  answer: JobOfferReply,
): {
  color: "success" | "error" | "info" | "warning";
  icon: JSX.Element;
  label: string;
  title: string;
  backgroundColor: string;
} {
  switch (answer.answer) {
    case "accept":
      return {
        color: "success",
        title: "Accepted",
        backgroundColor: theme.palette.success.main,
        icon: <Check />,
        label: `Offer accepted at ${formatTimeRelative(answer.createdAt)}`,
      };

    case "decline":
      return {
        color: "error",
        title: "Declined",
        backgroundColor: theme.palette.error.main,
        icon: <CancelRounded />,
        label: `Offer declined at ${formatTimeRelative(
          answer.createdAt,
        )} reason: ${answer.declineReasonOther ?? answer.declineReason}`,
      };

    case "conditionallyAccepted":
      return {
        color: "warning",
        backgroundColor: theme.palette.info.main,
        icon: <Info />,
        title: "Conditionally accepted",
        label: `Offer conditionally accepted at ${formatTimeRelative(
          answer.createdAt,
        )} with condition: ${
          answer.acceptConditionReasonOther ?? answer.acceptCondition
        }`,
      };
    default:
      assertNever(answer.answer);
  }
}
