import { CompletedPartnerInstallation } from "../../models/other/completed-partner-installation";
import { Card, Divider, Stack, Typography } from "@mui/material";
import { FC } from "react";
import { useAuthUser } from "../auth/AuthProvider";

interface CompletedPartnerInstallationCardProps {
  installation: CompletedPartnerInstallation;
}

const CompletedPartnerInstallationCard: FC<
  CompletedPartnerInstallationCardProps
> = ({ installation }) => {
  const currentUser = useAuthUser();

  return (
    <Card style={{ padding: 12, marginBottom: 12 }} variant="outlined">
      <Stack spacing={1} divider={<Divider />}>
        {installation.partnerInvoiceNumber && (
          <Typography>
            Partner invoice number: {installation.partnerInvoiceNumber}
          </Typography>
        )}

        {!currentUser.partner && installation.selfInvoiceNumber && (
          <Typography>
            Done invoice number: {installation.selfInvoiceNumber}
          </Typography>
        )}

        {installation.installationParameters &&
          Object.entries(installation.installationParameters).map(
            ([key, value], index) => (
              <Typography key={index}>
                {key}: <strong>{JSON.stringify(value)}</strong>
              </Typography>
            ),
          )}
      </Stack>
    </Card>
  );
};

export default CompletedPartnerInstallationCard;
