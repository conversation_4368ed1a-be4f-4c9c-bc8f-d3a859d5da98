import { Job } from "../../models/job/job";
import { useDocumentData } from "react-firebase-hooks/firestore";
import { Typography, Stack, Divider } from "@mui/material";
import { doc } from "@firebase/firestore";
import { jobOfferCollection } from "../../models/job/offer/job-offer-provider";
import { useCompany } from "../../hooks/company/companyHook";
import { JobOffer } from "../../models/job/offer/job-offer";
import { DocumentReference } from "firebase/firestore";
import OfferCompanyRow from "./JobOfferCompanyRow";

const SentOffers = ({ job }: { job: Job }) => {
  const [jobOffer, loadingJobOffer] = useDocumentData(
    doc(jobOfferCollection(), job.reference.id),
  );

  if (loadingJobOffer || !jobOffer || !jobOffer?.offeredTo?.length) return null;

  return (
    <Stack width={"100%"} spacing={1}>
      <Typography>
        Offer was sent to <strong> {jobOffer.offeredTo.length}</strong>{" "}
        companies.
      </Typography>
      <Stack display={"flex"} divider={<Divider />} spacing={2}>
        {jobOffer.offeredTo.map((companyReference) => (
          <SentOfferCompanyAnswersRow
            job={job}
            jobOffer={jobOffer}
            companyReference={companyReference}
            key={companyReference.id}
          />
        ))}
      </Stack>
    </Stack>
  );
};

function SentOfferCompanyAnswersRow({
  job,
  jobOffer,
  companyReference,
}: {
  job: Job;
  jobOffer: JobOffer;
  companyReference: DocumentReference;
}) {
  const { company } = useCompany(companyReference.id);

  if (!company) return null;

  return (
    <OfferCompanyRow
      showDrivingDistance={false}
      company={company}
      job={job}
      jobOffer={jobOffer}
    />
  );
}

export default SentOffers;
