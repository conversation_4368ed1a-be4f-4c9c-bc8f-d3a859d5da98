import { Icon<PERSON>utton, Avatar, Divider } from "@mui/material";
import { Stack } from "@mui/system";
import { UploadImage } from "./dialogs/CreateOrderDialog";
import TextField from "../form-fields/TextField";
import { Delete } from "@mui/icons-material";

export default function CreateJobImagePreview({
  image,
  onImageDelete,
  uid,
}: {
  image: UploadImage;
  onImageDelete: (uid: string) => void;
  uid: string;
}) {
  return (
    <Stack spacing={1}>
      <Stack alignItems="center" direction="row" spacing={2}>
        <Avatar
          sx={{
            width: 60,
            height: 60,
          }}
          variant="rounded"
          src={URL.createObjectURL(image.file)}
        />

        <TextField
          label="Description"
          fullWidth
          name={`images.${uid}.description`}
        />

        <IconButton
          size="small"
          onClick={() => onImageDelete(uid)}
          aria-label="upload picture"
          component="span"
        >
          <Delete />
        </IconButton>
      </Stack>
      <Divider />
    </Stack>
  );
}
