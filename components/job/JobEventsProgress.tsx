import {
  Avatar,
  Badge,
  BadgeProps,
  Box,
  styled,
  Tooltip,
  useTheme,
} from "@mui/material";
import { Job, JobEvents } from "../../models/job/job";
import { Timestamp } from "firebase/firestore";
import formatTimeRelative from "../../lib/format-time-relative";
import _ from "lodash";
import { useAuthUser } from "../auth/AuthProvider";
import {
  eventsVisibleForTier,
  optionsForEvent,
  titleForEvent,
} from "./events/event-options";
import { createElement, memo, useMemo } from "react";
import { latestEvent } from "./events/timed-events";

interface JobEventProgressProps {
  job: Job;
  showOnlyLatest?: boolean;
}

export default function JobEventsProgress({
  job,
  showOnlyLatest = false,
}: JobEventProgressProps) {
  const authUser = useAuthUser();

  const jobEvents = useMemo(
    () => ({
      ...job.events,
      workTimeScheduled: job.scheduledWorkStartTime ?? null,
      callScheduled: job.scheduledCallTime,
    }),
    [job],
  );

  const { latest, events } = useMemo(() => {
    const tier = authUser.partnerDoc?.tier;
    const events = eventsVisibleForTier(tier);
    return {
      latest: latestEvent(jobEvents, tier) ?? events[0],
      events,
    };
  }, [jobEvents, authUser.partnerDoc?.tier]);

  const allEvents = showOnlyLatest ? [latest] : events;

  return (
    <Box display="flex">
      {allEvents.map((event) => (
        <JobEventIcon
          key={event}
          isLatest={event === latest}
          eventId={event}
          time={jobEvents?.[event]}
        />
      ))}
    </Box>
  );
}

interface JobEventIconProps {
  eventId: keyof JobEvents;
  time?: Timestamp | null;
  isLatest?: boolean;
}

export function JobEventIcon({ eventId, time, isLatest }: JobEventIconProps) {
  const theme = useTheme();
  const authUser = useAuthUser();

  const title = useMemo(
    () => titleForEvent(eventId, Boolean(authUser.partner)),
    [eventId, authUser],
  );

  const options = useMemo(() => optionsForEvent(eventId), [eventId]);

  const haveOccurred = Boolean(time);
  const isOld = useMemo(() => {
    if (!time || !isLatest) return false;
    return options.isOld?.(time) ?? false;
  }, [time, isLatest, options]);

  const color = useMemo(
    () =>
      haveOccurred
        ? theme.palette[options.color ?? (isOld ? "warning" : "primary")].main
        : theme.palette.grey[400],
    [haveOccurred, theme, options, isOld],
  );

  return (
    <EventBadge eventId={eventId} haveOccurred={haveOccurred}>
      <Tooltip
        title={
          formatTimeRelative(time)
            ? `${title} ${formatTimeRelative(time)}`
            : title
        }
        arrow
      >
        <Avatar
          variant="square"
          sx={{
            borderRadius: 2,
            backgroundColor: color,
            width: 30,
            height: 30,
            margin: 0.5,
          }}
        >
          <EventIcon eventId={eventId} />
        </Avatar>
      </Tooltip>
    </EventBadge>
  );
}

const EventIcon = memo(function EventIcon({
  eventId,
}: {
  eventId: keyof JobEvents;
}) {
  return createElement(optionsForEvent(eventId).icon, { fontSize: "inherit" });
});

const StyledBadge = styled(Badge)<BadgeProps>(({ theme }) => ({
  "& .MuiBadge-badge": {
    right: 7,
    bottom: 10,
    border: `1.5px solid ${theme.palette.background.paper}`,
    padding: "0px",
    fontSize: "0.7rem",
    minWidth: "17px",
    height: "17px",
  },
}));

function EventBadge({
  eventId,
  haveOccurred,
  children,
}: {
  eventId: keyof JobEvents;
  haveOccurred: boolean;
  children: React.ReactNode;
}) {
  const badge = optionsForEvent(eventId).badge;
  const theme = useTheme();

  return (
    <StyledBadge
      invisible={!badge}
      badgeContent={<EventBadgeIcon eventId={eventId} />}
      color={badge?.color}
      sx={{
        "& .MuiBadge-badge": {
          backgroundColor: haveOccurred
            ? badge?.color
            : theme.palette.grey[400],
        },
      }}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
    >
      {children}
    </StyledBadge>
  );
}

const EventBadgeIcon = memo(function EventBadgeIcon({
  eventId,
}: {
  eventId: keyof JobEvents;
}) {
  const badge = optionsForEvent(eventId).badge;
  if (!badge) return null;

  return createElement(badge.icon, {
    fontSize: "inherit",
  });
});
