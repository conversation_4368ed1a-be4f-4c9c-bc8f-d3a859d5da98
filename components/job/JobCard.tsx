import { <PERSON><PERSON>, <PERSON>, Stack, Typography } from "@mui/material";
import { DocumentReference } from "firebase/firestore";
import Link from "next/link";
import { FC } from "react";
import { useJob } from "../../hooks/job/jobHook";
import { Check } from "@mui/icons-material";

interface JobCardProps {
  jobReference: DocumentReference;
}

const JobCard: FC<JobCardProps> = ({ jobReference }) => {
  const { job, loading } = useJob(jobReference.id);

  if (loading) return <></>;

  return (
    <Link passHref href={`/jobs/job/?id=${job?.reference.id}`}>
      <Alert
        icon={false}
        severity="warning"
        style={{
          cursor: "pointer",
        }}
      >
        <Stack spacing={1}>
          <Typography variant="body2" noWrap>
            {job?.description}
          </Typography>

          <Typography variant="caption">
            Reference: {job?.referenceNumber}
          </Typography>

          <Stack spacing={1} direction="row">
            {job?.company && (
              <Chip
                icon={<Check />}
                style={{ borderRadius: 4 }}
                size="small"
                color="success"
                label="Matched"
              />
            )}

            {job?.cache?.customerRegion && (
              <Chip
                style={{ borderRadius: 4 }}
                size="small"
                label={job.cache?.customerRegion}
              />
            )}
            {job?.services?.map((e) => (
              <Chip
                style={{ borderRadius: 4 }}
                size="small"
                key={e}
                label={e}
              />
            ))}
            {job?.tags?.map((e) => (
              <Chip
                style={{ borderRadius: 4 }}
                size="small"
                key={e}
                label={e}
              />
            ))}
            {job?.operationTags?.map((e) => (
              <Chip
                style={{ borderRadius: 4 }}
                size="small"
                key={e}
                label={e}
              />
            ))}
          </Stack>

          {job?.company && (
            <Typography variant="caption">
              Company matched: {job.cache?.companyName}
            </Typography>
          )}

          {job?.events?.offerSent && (
            <Typography variant="caption">Offer sent</Typography>
          )}
        </Stack>
      </Alert>
    </Link>
  );
};

export default JobCard;
