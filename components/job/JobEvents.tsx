import { getProperty } from "dot-prop";
import { formatTime } from "../../lib/format-time-relative";

import { Timestamp, updateDoc } from "@firebase/firestore";
import {
  Button,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
} from "@mui/material";
import { useAuthUser } from "../auth/AuthProvider";

import { DeleteOutlined } from "@mui/icons-material";
import { Job, JobEvents } from "../../models/job/job";
import {
  addDoc,
  collection,
  deleteField,
  serverTimestamp,
} from "firebase/firestore";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import DatePickerDialog from "../dialogs/DatePickerDialog";
import { titleForEvent } from "./events/event-options";
import { PartnerTier } from "../../models/other/partner";
import { JobEventIcon } from "./JobEventsProgress";

export default function JobEventsList({ job }: { job: Job }) {
  const authContext = useAuthUser();
  if (!job.cache) return null;

  const isPartner = Boolean(authContext.partner);
  const isPlatformUser = isPartner
    ? (authContext.partnerDoc?.tier ?? PartnerTier.None) >= PartnerTier.Platform
    : true;

  return (
    <DialogManager>
      <ManagedDialog
        id={"job-events-date-picker"}
        defaultNow
        component={DatePickerDialog}
      />

      <List dense>
        <JobEvent job={job} eventId="customerLoggedIn" />

        <JobEvent editable={false} job={job} eventId="companyMatched" />

        <JobEvent
          editable={!isPartner}
          visible={Boolean(job.partner)}
          job={job}
          eventId="partnershipPrechecksReported"
        />

        <JobEvent
          editable={!isPartner}
          visible={job.requiresPrerequisitesFulfilled ?? false}
          job={job}
          eventId="prerequisitesPending"
        />

        <JobEvent
          editable={!isPartner}
          visible={job.requiresPrerequisitesFulfilled ?? false}
          job={job}
          eventId="prerequisitesFulfilled"
        />
        <ScheduleCall job={job} editable={isPlatformUser} />

        <JobEvent visible={!isPartner} editable job={job} eventId="callMade" />

        <JobEvent editable={!isPartner} job={job} eventId="offerSent" />

        <JobEvent editable={!isPartner} job={job} eventId="offerAccepted" />

        <JobEvent
          editable={!isPartner}
          visible={Boolean(job.events?.offerDeclined)}
          job={job}
          eventId="offerDeclined"
        />

        <JobEvent job={job} eventId="offersChecked" editable={isPlatformUser} />

        <ScheduleWork job={job} editable={isPlatformUser} />

        <JobEvent editable={isPlatformUser} job={job} eventId="jobDone" />

        <JobEvent
          visible={job.invoicedThroughPartnership}
          editable={!isPartner}
          job={job}
          eventId="partnershipInstallationReported"
        />

        <JobEvent editable={false} job={job} eventId="companyReviewed" />

        <JobEvent
          visible={!job.invoicedThroughPartnership}
          editable={!isPartner}
          job={job}
          eventId="invoiceSent"
        />

        <JobEvent
          visible={!job.invoicedThroughPartnership}
          editable={!isPartner}
          job={job}
          eventId="invoicePaid"
        />

        <JobEvent
          visible={job.invoicedThroughPartnership}
          editable={!isPartner}
          job={job}
          eventId="partnerBilled"
        />

        <JobEvent
          visible={!isPartner}
          editable={!isPartner}
          job={job}
          eventId="craftsmanBilled"
        />

        <JobEvent
          visible={!isPartner}
          editable={!isPartner}
          job={job}
          eventId="jobCancelled"
        />
      </List>
    </DialogManager>
  );
}

function JobEvent({
  job,
  eventId,
  editable,
  visible = true,
}: {
  job: Job;
  eventId: keyof JobEvents;
  editable?: boolean;
  visible?: boolean;
}) {
  const authUser = useAuthUser();

  const title = titleForEvent(eventId, Boolean(authUser.partner));

  if (!visible) return null;

  const jobEvent = getProperty(job.events, eventId) as
    | Timestamp
    | undefined
    | null;

  function onEventSet(date: Date | "custom") {
    if (date === "custom") return;

    updateDoc(job.reference, {
      [`events.${eventId}`]: Timestamp.fromDate(date),
    });
  }

  function onEventDelete() {
    updateDoc(job.reference, {
      [`events.${eventId}`]: deleteField(),
    });
  }

  return (
    <JobEventItem
      eventId={eventId}
      timestamp={jobEvent}
      editable={editable}
      title={title}
      onEventSet={onEventSet}
      onEventDelete={onEventDelete}
    />
  );
}

function ScheduleWork({
  job,
  editable,
  visible = true,
}: {
  job: Job;
  editable?: boolean;
  visible?: boolean;
}) {
  const authUser = useAuthUser();

  if (!visible) return null;
  const messagesRef = collection(job.reference, "messages");

  return (
    <JobEventItem
      eventId="workTimeScheduled"
      scheduled
      editable={editable}
      timestamp={job.scheduledWorkStartTime}
      title={
        job.scheduledWorkStartTime
          ? "Work start time scheduled"
          : "Schedule work start time"
      }
      onEventSet={async (date: Date | "custom") => {
        if (date == "custom") return;

        await updateDoc(job.reference, {
          scheduledWorkStartTime: Timestamp.fromDate(date),
          "events.workTimeScheduled": serverTimestamp(),
          "events.scheduledWorkTimeCancelled": deleteField(),
        });
      }}
      onEventDelete={async () => {
        if (window.confirm("Are you sure?"))
          await updateDoc(job.reference, {
            "events.scheduledWorkTimeCancelled": serverTimestamp(),
            scheduledWorkStartTime: null,
          });
        await addDoc(messagesRef, {
          type: "cancelled-scheduled-work-time",
          sender: authUser?.userDoc?.reference,
          timestamp: serverTimestamp(),
          createTime: serverTimestamp(),
        });
      }}
    />
  );
}

function ScheduleCall({
  job,
  editable,
  visible = true,
}: {
  job: Job;
  editable?: boolean;
  visible?: boolean;
}) {
  const authUser = useAuthUser();

  if (!visible) return null;
  const messagesRef = collection(job.reference, "messages");

  return (
    <JobEventItem
      eventId="callScheduled"
      scheduled
      editable={editable}
      title={job.scheduledCallTime ? "Call scheduled" : "Scheduled call time"}
      timestamp={job.scheduledCallTime}
      onEventSet={async (date: Date | "custom") => {
        if (date == "custom") return;

        await updateDoc(job.reference, {
          scheduledCallTime: Timestamp.fromDate(date),
          "events.scheduledCallCancelled": deleteField(),
        });
      }}
      onEventDelete={async () => {
        if (!window.confirm("Are you sure?")) return null;
        await updateDoc(job.reference, {
          "events.sameDayCallReminderSent": null,
          "events.shortCallReminderSent": null,
          "events.scheduledCallCancelled": serverTimestamp(),
          scheduledCallTime: null,
        });
        await addDoc(messagesRef, {
          type: "cancelled-booked-call",
          sender: authUser?.userDoc?.reference,
          timestamp: serverTimestamp(),
          createTime: serverTimestamp(),
        });
        return null;
      }}
    />
  );
}

function JobEventItem({
  eventId,
  editable,
  title,
  timestamp,
  scheduled,
  onEventSet,
  onEventDelete,
}: {
  eventId: keyof JobEvents;
  scheduled?: boolean;
  editable?: boolean;
  timestamp?: Timestamp | null;
  title: string;
  onEventSet: (date: Date | "custom") => void;
  onEventDelete: () => void;
}) {
  const { open: openDatePickerDialog } = useDialog(`job-events-date-picker`);

  return (
    <>
      <ListItem
        divider
        secondaryAction={
          editable ? (
            timestamp ? (
              <IconButton color="inherit" size="small" onClick={onEventDelete}>
                <DeleteOutlined />
              </IconButton>
            ) : (
              <Button
                sx={{ mr: -1 }}
                onClick={() => {
                  openDatePickerDialog({ onDatePicked: onEventSet });
                }}
              >
                {scheduled ? "Schedule" : "Set event"}
              </Button>
            )
          ) : null
        }
      >
        <ListItemAvatar>
          <JobEventIcon eventId={eventId} time={timestamp} />
        </ListItemAvatar>

        <ListItemText primary={title} secondary={formatTime(timestamp)} />
      </ListItem>
    </>
  );
}
