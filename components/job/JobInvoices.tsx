import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardActions,
  CardContent,
  Divider,
  Grid2,
  IconButton,
  ListItem,
  ListItemText,
  Theme,
  Typography,
  useTheme,
} from "@mui/material";
import {
  collection,
  DocumentReference,
  getFirestore,
  query,
  Timestamp,
  updateDoc,
  where,
} from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import {
  Invoice,
  InvoiceEvents,
  invoiceContainsNonArticleLines,
} from "../../models/invoice/invoice";
import { formatTime } from "../../lib/format-time-relative";
import { getFileUrl } from "../../lib/get-file-url";
import {
  Download,
  Whatshot,
  Send,
  AccountBalance,
  CopyAll,
  HomeWork,
  Description,
} from "@mui/icons-material";
import { BILLECTA_URL, FIREBASE_URL } from "../../config/constants";
import formatAmount from "../../lib/format-amount";
import { useDialog } from "../dialogs/DialogManager";
import { useUserSensitiveData } from "../../hooks/user/userUserSensitiveHook";
import { Stack } from "@mui/system";
import { startCase } from "lodash";
import { isDefined } from "../../lib/filter-undefined";
import { useAuthUser } from "../auth/AuthProvider";
import Internal from "../auth/Internal";
import { DonePartnerReference, partnerRef } from "../../models/other/partner";

function backgroundColor(invoice: Invoice, theme: Theme) {
  switch (invoice.status) {
    case "paid":
      return theme.palette.success.light;
    case "refuted":
    case "creditLoss":
      return theme.palette.error.light;
    case "credited":
      return theme.palette.warning.light;
    case "sent":
      return theme.palette.info.light;
    case "draft":
      return theme.palette.divider;
  }
}

const displayedEvents: (keyof InvoiceEvents)[] = [
  "sent",
  "viewedByCustomer",
  "fullyPaid",
];

function InvoiceCard({
  invoice,
  enableCraftsmanPayouts = false,
  craftsmanIsBilled = false,
}: {
  invoice: Invoice;
  enableCraftsmanPayouts?: boolean;
  craftsmanIsBilled?: boolean;
}) {
  const { open } = useDialog("send-invoice");
  const { open: openRotInfoEditor } = useDialog("edit-rot-info");
  const { open: openEditInvoice } = useDialog("edit-invoice");
  const theme = useTheme();
  const color = backgroundColor(invoice, theme);

  const { userSensitiveData, loading: userSensitiveDataLoading } =
    useUserSensitiveData(invoice.customer.reference.id);

  const containsNonArticleLines =
    enableCraftsmanPayouts && invoiceContainsNonArticleLines(invoice);

  const containsDeduction = Boolean(invoice.deductionTypes?.length);
  const containsCustomerDeductionInfo = Boolean(
    invoice.customerSensitiveData?.rotInfo?.personIdNumber,
  );

  return (
    <Grid2 size={12}>
      <Card variant="outlined" sx={{ borderColor: color }}>
        <Grid2 size={12} container spacing={2} alignItems={"center"}>
          <Grid2 size={"grow"}>
            <Typography style={{ fontWeight: 600, margin: 12 }} variant="h6">
              To pay:{" "}
              {formatAmount(
                (invoice?.calculatedValues?.totalNetAfterDeductions ||
                  invoice?.calculatedValues?.centAmountToPay ||
                  0) / 100,
              )}
            </Typography>
          </Grid2>
          <Grid2>
            <InvoiceStatus invoice={invoice} />
          </Grid2>
        </Grid2>

        <Divider />
        <Internal>
          <CardContent>
            <Stack
              direction="row"
              spacing={2}
              divider={<Divider orientation="vertical" flexItem />}
            >
              <ListItem disableGutters dense alignItems="flex-start">
                <ListItemText
                  primary={"Status: " + invoice.status}
                  secondary={invoice.number}
                />
              </ListItem>

              {displayedEvents.map((event) => (
                <EventItem
                  key={event}
                  title={startCase(event)}
                  time={invoice.events?.[event]}
                />
              ))}

              {invoice.status === "credited" && invoice.events?.credited && (
                <ListItem disableGutters dense alignItems="flex-start">
                  <ListItemText
                    primary={"Credited"}
                    secondary={formatTime(invoice.events?.credited?.toDate())}
                  />
                </ListItem>
              )}
            </Stack>
          </CardContent>
        </Internal>

        {!containsCustomerDeductionInfo && containsDeduction && (
          <Alert
            sx={{ borderRadius: 0 }}
            action={
              !userSensitiveDataLoading &&
              userSensitiveData?.rotInfo && (
                <Button
                  startIcon={<CopyAll />}
                  size="small"
                  fullWidth
                  color="inherit"
                  onClick={async () => {
                    await updateDoc(invoice.reference, {
                      "customerSensitiveData.rotInfo":
                        userSensitiveData.rotInfo,
                    });
                  }}
                >
                  Copy from user
                </Button>
              )
            }
            severity="error"
          >
            Missing ROT information
          </Alert>
        )}
        {containsNonArticleLines && (
          <Alert sx={{ borderRadius: 0 }} severity="warning">
            <AlertTitle>Invoice contain lines without article</AlertTitle>
            Self invoice cannot be automatically generated
          </Alert>
        )}
        {!containsDeduction && (
          <Alert sx={{ borderRadius: 0 }} severity="info">
            The invoice contain no deductions
          </Alert>
        )}
        <Divider />
        <CardActions>
          <Internal>
            {invoice.status === "draft" && (
              <Button
                disabled={!containsCustomerDeductionInfo && containsDeduction}
                startIcon={<Send />}
                size="small"
                onClick={() => {
                  open({
                    invoice,
                    variant: "customer",
                  });
                }}
              >
                Send to customer
              </Button>
            )}

            {enableCraftsmanPayouts && !craftsmanIsBilled && (
              <Button
                disabled={containsNonArticleLines}
                color="warning"
                startIcon={<Send />}
                size="small"
                onClick={() => {
                  open({
                    invoice,
                    variant: "self-invoice",
                  });
                }}
              >
                Send self invoice
              </Button>
            )}

            {invoice.billectaId !== undefined && (
              <Button
                startIcon={<AccountBalance />}
                size="small"
                target="_blank"
                rel="noopener noreferrer"
                href={`${BILLECTA_URL}/in/View/${invoice.billectaId}`}
              >
                Billecta
              </Button>
            )}
          </Internal>

          {Boolean(invoice.pdfFilePath) && (
            <Button
              startIcon={<Download />}
              size="small"
              onClick={() => {
                if (!invoice.pdfFilePath) return;

                getFileUrl(invoice.pdfFilePath).then((url) => window.open(url));
              }}
            >
              Download
            </Button>
          )}

          {invoice.status === "draft" && (
            <Button
              startIcon={<HomeWork />}
              size="small"
              onClick={() => {
                openRotInfoEditor({
                  invoice,
                  usesDeduction: containsDeduction,
                });
              }}
            >
              Deduction details
            </Button>
          )}

          <Button
            startIcon={<Description />}
            size="small"
            onClick={() => {
              openEditInvoice({ invoice });
            }}
          >
            {invoice.status === "draft" ? "Edit" : "View"}
          </Button>

          <Internal>
            <IconButton
              target="_blank"
              rel="noopener noreferrer"
              href={`${FIREBASE_URL}/firestore/data/~2Finvoices~2F${invoice.reference.id}`}
              style={{
                color: "#f59842",
                borderColor: "#f59842",
                marginLeft: "auto",
              }}
              size="small"
            >
              <Whatshot />
            </IconButton>
          </Internal>
        </CardActions>
      </Card>
    </Grid2>
  );
}

function EventItem({ title, time }: { title: string; time?: Timestamp }) {
  return (
    <ListItem disableGutters dense alignItems="flex-start">
      <ListItemText
        primary={title}
        secondary={time ? formatTime(time.toDate()) : "-"}
      />
    </ListItem>
  );
}

function BasisCard({ basis }: { basis: Invoice }) {
  const { open } = useDialog("send-invoice");
  return (
    <Grid2 size={12}>
      <Card
        variant="outlined"
        style={{
          borderStyle: "dashed",
          opacity: 0.5,
        }}
      >
        <CardContent>
          <Grid2 alignItems="center" container spacing={2}>
            <Grid2 size={8}>
              To pay:{" "}
              {formatAmount(
                (basis?.calculatedValues?.totalNetAfterDeductions ||
                  basis?.calculatedValues?.centAmountToPay ||
                  0) / 100,
              )}
            </Grid2>
            <Grid2 size={4}>
              <InvoiceStatus invoice={basis} />
            </Grid2>
          </Grid2>
        </CardContent>
        <CardActions>
          <Button
            startIcon={<Send />}
            size="small"
            onClick={() => {
              open({ invoice: basis });
            }}
          >
            Submit
          </Button>
        </CardActions>
      </Card>
    </Grid2>
  );
}

function InvoiceStatus({ invoice }: { invoice: Invoice }) {
  if (invoice.type === "basis") {
    return (
      <Typography>
        {invoice.basisStatus === "sent" ? "Submitted basis" : "Draft basis"}
      </Typography>
    );
  }
  return (
    <Typography>
      {invoice.status == "draft"
        ? "Draft invoice"
        : invoice.number
          ? "Generated invoice"
          : "PDF invoice"}
    </Typography>
  );
}

const JobInvoices = ({
  jobRef,
  enableCraftsmanPayouts,
  showBasis,
  craftsmanIsBilled,
}: {
  jobRef: DocumentReference;
  enableCraftsmanPayouts: boolean;
  showBasis: boolean;
  craftsmanIsBilled: boolean;
}) => {
  const db = getFirestore();
  const { partner } = useAuthUser();

  const [value] = useCollection(
    query(
      collection(db, "invoices"),
      ...[
        where("job", "==", jobRef),
        showBasis ? undefined : where("type", "!=", "basis"),
        where("partner", "==", partnerRef(partner) ?? DonePartnerReference()),
      ].filter(isDefined),
    ),
  );

  const invoices = value?.docs.map((invoiceSnap) => {
    const invoice: Invoice = {
      ...invoiceSnap.data(),
      reference: invoiceSnap.ref,
    } as Invoice;
    return invoice;
  });

  return (
    <Grid2 container spacing={1}>
      {invoices?.map((invoice) => {
        if (invoice.type === "basis") {
          return <BasisCard basis={invoice} key={invoice.reference.id} />;
        }

        return (
          <InvoiceCard
            invoice={invoice}
            key={invoice.reference.id}
            enableCraftsmanPayouts={enableCraftsmanPayouts}
            craftsmanIsBilled={craftsmanIsBilled}
          />
        );
      })}
    </Grid2>
  );
};

export default JobInvoices;
