import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Box,
  Button,
  Stack,
  Typography,
} from "@mui/material";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import { Job } from "../../models/job/job";
import { useAuthUser } from "../auth/AuthProvider";
import ManagedDialog from "../dialogs/ManagedDialog";
import EditLocationDialog from "../dialogs/EditLocationDialog";
import MapBox from "../map/MapBox";
import JobMarker from "../map/markers/JobMarker";
import React from "react";

export default function JobLocation({
  job,
  zoom = 10,
  height = 250,
  children,
}: {
  job: Job;
  zoom?: number;
  height?: number;
  children?: React.ReactNode;
}) {
  const location = job.cache?.customerLocation;

  if (!location || !location.coordinates) {
    return (
      <DialogManager>
        <>
          <ManagedDialog
            id="edit-job-location"
            reference={job.reference}
            path={"cache.customerLocation"}
            location={job.cache?.customerLocation}
            userReference={job.customer}
            jobReference={job.reference}
            userName={job.cache?.customerName}
            component={EditLocationDialog}
          />
          <MissingLocationAlert />
        </>
      </DialogManager>
    );
  }

  return (
    <>
      <Box sx={{ height }}>
        <MapBox
          defaultZoom={zoom}
          disableDefaultUI
          defaultCenter={{
            lat: location.coordinates.latitude,
            lng: location.coordinates.longitude,
          }}
        >
          <JobMarker job={job} />
          {children}
        </MapBox>
      </Box>

      <Stack alignItems="center" direction="row">
        <LocationOnIcon />
        <Typography variant="body2"> {location.addressLine}</Typography>
      </Stack>
    </>
  );
}

function MissingLocationAlert() {
  const { open: openJobLocation } = useDialog("edit-job-location");
  const authUser = useAuthUser();

  return (
    <Alert
      severity="error"
      action={
        !authUser.partner ? (
          <Button color="inherit" onClick={() => openJobLocation()}>
            Add location
          </Button>
        ) : undefined
      }
    >
      <AlertTitle>Location is missing</AlertTitle>
      Either the location was not provided or it was invalid. This needs to be
      fixed before sending job offer.
    </Alert>
  );
}
