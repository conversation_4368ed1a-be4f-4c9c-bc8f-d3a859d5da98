import { useDocumentData } from "react-firebase-hooks/firestore";
import { ListItem, List, Rating, ListItemText } from "@mui/material";
import {
  collection,
  doc,
  getFirestore,
  DocumentReference,
} from "firebase/firestore";
import ImageWithReference from "../common/ImageWithReference";

const JobCompanyReviews = ({
  companyRef,
  jobRef,
}: {
  companyRef: DocumentReference;
  jobRef: DocumentReference;
}) => {
  const db = getFirestore();
  const [review, loading] = useDocumentData(
    doc(
      collection(
        doc(collection(db, "companies"), companyRef.id),
        "companyReviews",
      ),
      jobRef.id,
    ),
  );

  if (loading || !review) return null;

  return (
    <List dense component="nav" aria-label="mailbox folders">
      <ListItem divider>
        <ListItemText primary="Rating:" />
        <Rating name="disabled" value={review.rating * 5} disabled />
      </ListItem>

      {review.publicFeedback == null || review.publicFeedback.length === 0 ? (
        <div />
      ) : (
        <ListItem divider>
          <ListItemText
            primary="Public feedback:"
            secondary={review.publicFeedback}
          />
        </ListItem>
      )}

      {review.privateFeedback == null || review.privateFeedback.length === 0 ? (
        <div />
      ) : (
        <ListItem divider>
          <ListItemText
            primary="Private feedback:"
            secondary={review.privateFeedback}
          />
        </ListItem>
      )}

      {review.reviewerRecommends == null || !review.reviewerRecommends ? (
        <div />
      ) : (
        <ListItem divider>
          <ListItemText
            primary="Recommended by customer 👍"
            secondary={review.reviewerRecommends}
          />
        </ListItem>
      )}

      {!review.complaints?.length ? (
        <div />
      ) : (
        <ListItem divider>
          <ListItemText
            primary="Complaints"
            secondary={review.complaints.join(" ")}
          />
        </ListItem>
      )}

      {!review.compliments?.length ? (
        <div />
      ) : (
        <ListItem divider>
          <ListItemText
            primary="Compliments"
            secondary={review.compliments.join(" ")}
          />
        </ListItem>
      )}

      {review.images != null && review.images.length >= 0 && (
        <ListItem divider>
          <ListItemText primary="Images" />
          {review.images.map((element: any) => (
            <ImageWithReference key={"image"} imageRef={element} />
          ))}
        </ListItem>
      )}
    </List>
  );
};

export default JobCompanyReviews;
