import { LoadingButton } from "@mui/lab";
import { Typo<PERSON>, Chip, Stack } from "@mui/material";
import { useSnackbar } from "notistack";
import { useMemo, useState } from "react";
import { formatTinyDistance } from "../../lib/format-time-relative";
import { CompanyWithDistance } from "../../models/company/company";
import { JobOffer, QueuedOffer } from "../../models/job/offer/job-offer";
import UserChip from "../user/UserChip";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { usePeriodicRender } from "../../lib/hooks/periodic-render";
import { AutoAwesome } from "@mui/icons-material";
import { min } from "date-fns";

export default function JobOfferQueuedOfferRow({
  company,
  jobOffer,
}: {
  company: CompanyWithDistance;
  jobOffer: JobOffer;
}) {
  const [cancelingAutoOffer, setCancelingAutoOffer] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  if (!company.autoOffer) return null;

  return (
    <Stack direction="row" spacing={1} alignItems="center">
      <AutoOfferStatusChip autoOffer={company.autoOffer} />
      {company.autoOffer.status === "scheduled" && (
        <LoadingButton
          color="error"
          loading={cancelingAutoOffer}
          onClick={async () => {
            setCancelingAutoOffer(true);

            try {
              await cancelAutoOffer({
                taskId: company.autoOffer!.taskId,
                jobOfferId: jobOffer.reference.id,
                companyId: company.reference.id,
              });
            } catch (e) {
              enqueueSnackbar(`Failed to cancel auto offer: ${e}`, {
                variant: "error",
              });
            }

            setCancelingAutoOffer(false);
          }}
          size="small"
        >
          Stop auto offer
        </LoadingButton>
      )}

      {company.autoOffer.status === "cancelled" && (
        <Stack direction="row" spacing={2}>
          <Typography variant="body2">by</Typography>
          <UserChip size="small" userId={company.autoOffer.cancelledBy!.id} />
        </Stack>
      )}
    </Stack>
  );
}

function AutoOfferStatusChip({ autoOffer }: { autoOffer: QueuedOffer }) {
  const tick = usePeriodicRender(1000);

  const time = useMemo(() => {
    tick;
    return formatTinyDistance(
      autoOffer.scheduledTime.toDate(),
      min([new Date(), autoOffer.scheduledTime.toDate()]),
    );
  }, [tick, autoOffer.scheduledTime]);

  return (
    <Chip
      icon={<AutoAwesome />}
      size="small"
      label={`Auto offer ${autoOffer.status}${
        autoOffer.status === "scheduled" ? ` in ${time}` : ""
      } `}
    />
  );
}

interface JobOfferCancelTaskQueuePayload {
  taskId: string;
  jobOfferId: string;
  companyId: string;
}

export async function cancelAutoOffer(payload: JobOfferCancelTaskQueuePayload) {
  await httpsCallable(
    getFunctions(getApp(), "europe-west1"),
    "jobOfferCancelTaskQueue",
  )(payload);
}
