import { <PERSON><PERSON><PERSON>, Whatshot } from "@mui/icons-material";
import {
  IconButton,
  Button,
  ListItemText,
  Alert,
  Stack,
  Typography,
} from "@mui/material";
import { updateDoc } from "firebase/firestore";
import { FIREBASE_URL } from "../../config/constants";
import formatAmount from "../../lib/format-amount";
import { getFileUrl } from "../../lib/get-file-url";
import { QuoteCardData } from "../../models/quote/quote";
import { useAuthUser } from "../auth/AuthProvider";
import UserChip from "../user/UserChip";

interface QuoteCardProps {
  quoteCardData: QuoteCardData;
  allowStatusChange?: boolean;
  onAcceptClicked?: () => void;
}

export default function QuoteCard({
  quoteCardData,
  allowStatusChange = false,
  onAcceptClicked,
}: QuoteCardProps) {
  const {
    reference,
    jobRef,
    fileRef,
    status,
    amountAfterTaxDeductions,
    acceptedReason,
    acceptedBy,
    calculatedVal<PERSON>,
    partnerRef,
  } = quoteCardData;
  const authUser = useAuthUser();

  return (
    <Alert
      icon={false}
      severity={
        status === "accepted"
          ? "success"
          : status === "declined" || status === "cancelled"
            ? "error"
            : "warning"
      }
      action={
        <Stack>
          {!authUser.partner && (
            <IconButton
              onClick={() => {
                window.open(
                  `${FIREBASE_URL}/firestore/data/~2Fjobs~2F${jobRef.id}~2Fquotes~2F${reference.id}`,
                );
              }}
              color="warning"
              size="small"
            >
              <Whatshot />
            </IconButton>
          )}
          <IconButton
            size="small"
            onClick={() => {
              if (!fileRef) return;
              getFileUrl(fileRef).then((url) => window.open(url));
            }}
          >
            <FileOpen />
          </IconButton>

          {status === "pending" && (
            <Button
              size="small"
              disabled={!allowStatusChange}
              onClick={() => {
                if (!allowStatusChange) return;
                onAcceptClicked?.();
              }}
            >
              Accept
            </Button>
          )}

          {(status === "accepted" || status === "pending") &&
            !authUser.partner && (
              <Button
                size="small"
                disabled={!allowStatusChange}
                onClick={async () => {
                  if (
                    allowStatusChange &&
                    window.confirm(
                      "Are you sure you want to cancel this quote?",
                    )
                  ) {
                    await updateDoc(reference, { status: "cancelled" });
                  }
                }}
              >
                Cancel
              </Button>
            )}
        </Stack>
      }
    >
      {`After Tax: ${
        formatAmount(
          amountAfterTaxDeductions ?? (calculatedValues?.totalNet ?? 0) / 100,
        ) || "Not set"
      }`}
      <ListItemText
        secondary={`${status || "-"} ${
          partnerRef ? ` - ${partnerRef.id}` : ""
        }`}
      />
      {status === "accepted" && (
        <Stack alignItems="center" direction="row" spacing={0.5}>
          <Typography variant="body2"> by </Typography>

          {acceptedBy ? (
            <UserChip size="small" userId={acceptedBy.id} />
          ) : (
            <Typography variant="body2"> customer </Typography>
          )}

          {acceptedReason && (
            <Typography variant="body2"> - {acceptedReason}</Typography>
          )}
        </Stack>
      )}
    </Alert>
  );
}
