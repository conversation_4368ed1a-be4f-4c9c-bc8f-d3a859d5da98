import DuplicateJobButton from "./JobDuplicateButton";
import EditIcon from "@mui/icons-material/Edit";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import JobStatusSelect from "./JobStatusSelect";
import Link from "next/link";
import LinkIcon from "@mui/icons-material/Link";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { Button, Card, Grid2 } from "@mui/material";
import { FIREBASE_URL } from "../../config/constants";
import { Job } from "../../models/job/job";
import { useDialog } from "../dialogs/DialogManager";
import { Map } from "@mui/icons-material";
import { PlatformTier } from "../auth/Tiered";
import Internal from "../auth/Internal";

const JobToolbar = ({
  onClose,
  isPartnerJob = false,
  job,
  displayGoToDetailButton = true,
}: {
  onClose?: () => void;
  isPartnerJob?: boolean;
  job: Job;
  displayGoToDetailButton: boolean;
}) => {
  const { open: openJobEdit } = useDialog("edit-job");
  const { open: openJobEditLocation } = useDialog("edit-job-location");

  const isPending = job.status === "pending";

  return (
    <Card square style={{ padding: 6 }} variant="outlined">
      <Grid2 container>
        {onClose && (
          <Button
            onClick={onClose}
            size="small"
            style={{ marginRight: "4px", marginBottom: "4px" }}
          >
            Close
          </Button>
        )}

        <PlatformTier>
          <JobStatusSelect job={job} />
        </PlatformTier>

        {displayGoToDetailButton && !isPartnerJob && (
          <Link passHref href={`/jobs/job/?id=${job.reference.id}`}>
            <Button
              style={{ marginRight: "4px", marginBottom: "4px" }}
              startIcon={<FullscreenIcon />}
              size="small"
            >
              Go
            </Button>
          </Link>
        )}
        <Internal>
          {job.cache?.customerLocation?.coordinates && (
            <Link passHref href={`/map/?jobId=${job.reference.id}`}>
              <Button
                disabled={isPending}
                style={{ marginRight: "4px", marginBottom: "4px" }}
                startIcon={<Map />}
                size="small"
              >
                See on map
              </Button>
            </Link>
          )}
        </Internal>
        <Internal>
          <Button
            style={{
              marginRight: "4px",
              marginBottom: "4px",
            }}
            startIcon={<LinkIcon />}
            size="small"
            onClick={() => {
              navigator.clipboard.writeText(
                `https://backoffice.doneservices.co/jobs/job/?id=${job.reference.id}`,
              );
            }}
          >
            Url
          </Button>
        </Internal>

        <Internal>
          <Button
            style={{
              marginRight: "4px",
              marginBottom: "4px",
            }}
            startIcon={<LinkIcon />}
            size="small"
            onClick={() => {
              navigator.clipboard.writeText(`${job.reference.id}`);
            }}
          >
            Id
          </Button>
        </Internal>

        <PlatformTier>
          <Button
            disabled={isPending}
            style={{ marginRight: "4px", marginBottom: "4px" }}
            startIcon={<EditIcon />}
            size="small"
            onClick={() => {
              openJobEdit();
            }}
          >
            Edit
          </Button>
        </PlatformTier>

        <PlatformTier>
          <Button
            disabled={isPending}
            style={{ marginRight: "4px", marginBottom: "4px" }}
            startIcon={<EditIcon />}
            size="small"
            onClick={() => {
              openJobEditLocation();
            }}
          >
            Location
          </Button>
        </PlatformTier>

        <Internal>
          <DuplicateJobButton job={job} />
        </Internal>

        <Internal>
          <a
            target="_blank"
            rel="noopener noreferrer"
            href={`${FIREBASE_URL}/firestore/data/~2Fjobs~2F${job.reference.id}`}
          >
            <Button
              style={{
                marginRight: "4px",
              }}
              color="warning"
              size="small"
              startIcon={<WhatshotIcon />}
            >
              Job
            </Button>
          </a>
        </Internal>

        <Internal>
          <a
            target="_blank"
            rel="noopener noreferrer"
            href={`${FIREBASE_URL}/firestore/data/~2FjobOffers~2F${job.reference.id}`}
          >
            <Button
              style={{
                marginRight: "4px",
              }}
              color="warning"
              size="small"
              startIcon={<WhatshotIcon />}
            >
              Offer
            </Button>
          </a>
        </Internal>
      </Grid2>
    </Card>
  );
};

export default JobToolbar;
