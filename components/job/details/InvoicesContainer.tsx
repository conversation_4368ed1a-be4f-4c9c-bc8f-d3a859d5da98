import { FormControlLabel, Switch } from "@mui/material";
import { useState } from "react";
import { Job } from "../../../models/job/job";
import ComponentContainer from "../../common/ComponentContainer";
import JobInvoices from "../JobInvoices";
import GenerateButton from "./GenerateButton";
import Internal from "../../auth/Internal";

export default function InvoicesContainer({
  job,
  isSubcontractorJob,
}: {
  job: Job;
  isSubcontractorJob: boolean;
}) {
  const [showBasis, setShowBasis] = useState(false);
  return (
    <ComponentContainer
      title="Customer Invoices"
      actions={
        <Internal>
          {isSubcontractorJob ? (
            <FormControlLabel
              onChange={() => {
                setShowBasis(!showBasis);
              }}
              control={<Switch size="small" />}
              label="Show basises"
            />
          ) : null}
          <GenerateButton
            dialog="generate-invoice"
            disabled={!<PERSON><PERSON><PERSON>(job.partner) || !<PERSON><PERSON>an(job.company)}
          />
        </Internal>
      }
    >
      <JobInvoices
        jobRef={job.reference}
        enableCraftsmanPayouts={isSubcontractorJob}
        showBasis={showBasis}
        craftsmanIsBilled={Boolean(job.events?.craftsmanBilled)}
      />
    </ComponentContainer>
  );
}
