import { Alert, Box } from "@mui/material";
import { Job } from "../../../models/job/job";
import InstallationInvoiceReport from "../../invoicing/InstallationInvoiceReport";
import { installationReportsForJobQuery } from "../../../models/installation-report/provider";
import { useCollection } from "react-firebase-hooks/firestore";

export default function InstallerInvoicesContainer({ job }: { job: Job }) {
  const [value] = useCollection(installationReportsForJobQuery(job.reference));
  const installationReports = value?.docs.map((doc) => doc.data()) ?? [];

  // TODO: Add a switch to show all installations together with delete and merge buttons
  return (
    <>
      {installationReports.length > 1 && (
        <Alert severity="warning">
          This job has multiple installations reported, displaying the first
          one.
        </Alert>
      )}
      <Box padding={0.5}>
        <InstallationInvoiceReport
          job={job}
          installationReport={installationReports[0]}
          articleGroupId={job.articleGroups?.[0]?.reference.id}
        />
      </Box>
    </>
  );
}
