import { But<PERSON>, Divider, Typography } from "@mui/material";
import AddShoppingCartIcon from "@mui/icons-material/AddShoppingCart";
import { Job } from "../../../models/job/job";
import ComponentContainer from "../../common/ComponentContainer";
import JobAttachments from "../JobAttachments";
import { useDialog } from "../../dialogs/DialogManager";
import JobArticles from "../JobArticles";

function AddAttachmentButton({ disabled }: { disabled: boolean }) {
  const { open } = useDialog("add-attachment");
  return (
    <Button
      disabled={disabled}
      onClick={() => open()}
      startIcon={<AddShoppingCartIcon />}
    >
      Add articles
    </Button>
  );
}

export default function AttachmentsContainer({
  job,
  padding = 4,
  interactive = false,
}: {
  job: Job;
  padding?: number;
  interactive?: boolean;
}) {
  return (
    <ComponentContainer
      padding={padding}
      title="Articles & Attachments"
      actions={
        interactive ? (
          <AddAttachmentButton disabled={!<PERSON><PERSON><PERSON>(job.partner)} />
        ) : undefined
      }
    >
      <Divider textAlign="left">
        <Typography variant="caption" color={"text.secondary"}>
          Articles
        </Typography>
      </Divider>
      <JobArticles job={job} interactive={interactive} />
      {job.attachments && (
        <>
          <Divider textAlign="left">
            <Typography variant="caption" color={"text.secondary"}>
              Attachments
            </Typography>
          </Divider>
        </>
      )}
      <JobAttachments job={job} interactive={interactive} />
    </ComponentContainer>
  );
}
