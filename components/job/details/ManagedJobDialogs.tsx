import { arrayUnion, updateDoc } from "firebase/firestore";
import { Job } from "../../../models/job/job";
import EditLocationDialog from "../../dialogs/EditLocationDialog";
import ManagedDialog from "../../dialogs/ManagedDialog";
import CreateJobDialog from "../CreateJobDialog";
import AddAttachmentDialog from "../dialogs/AddAttachmentDialog";
import DeleteAttachmentDialog from "../dialogs/DeleteAttachmentDialog";
import { EditInvoiceDialog } from "../../invoice/editing";
import EditInvoiceRotInfoDialog from "../dialogs/EditInvoiceRotInfoDialog";
import GenerateInvoiceDialog from "../dialogs/GenerateInvoiceDialog";
import GenerateQuoteDialog from "../dialogs/GenerateQuoteDialog";
import JobEditDialog from "../dialogs/JobEditDialog";
import SendInvoiceDialog from "../dialogs/SendInvoiceDialog";
import SetEventDateDialog from "../dialogs/SetEventDateDialog";
import { JobArticle } from "../../../models/job/job-article";

export default function ManagedJobDialogs({ job }: { job: Job }) {
  return (
    <>
      <ManagedDialog id="send-invoice" component={SendInvoiceDialog} />
      <ManagedDialog id="edit-invoice" component={EditInvoiceDialog} />
      <ManagedDialog id="edit-rot-info" component={EditInvoiceRotInfoDialog} />
      <ManagedDialog id="create-job" component={CreateJobDialog} />
      <ManagedDialog
        id="edit-job"
        jobId={job.reference.id}
        component={JobEditDialog}
      />
      <ManagedDialog
        id="add-attachment"
        settings={{
          partner: job.partner,
          services: job.services,
          activeAt: job.createTime,
          allowCustomArticle: job.invoicedThroughPartnership,
        }}
        onSubmit={async (article: JobArticle) => {
          await updateDoc(job.reference, {
            articles: arrayUnion(article),
          });
        }}
        component={AddAttachmentDialog}
      />
      <ManagedDialog id="add-article" component={AddAttachmentDialog} />
      <ManagedDialog
        id="delete-attachment"
        job={job}
        component={DeleteAttachmentDialog}
      />
      <ManagedDialog
        id="generate-quote"
        job={job}
        component={GenerateQuoteDialog}
      />
      <ManagedDialog
        id="generate-invoice"
        job={job}
        component={GenerateInvoiceDialog}
      />
      <ManagedDialog
        id="set-event-date"
        job={job}
        component={SetEventDateDialog}
      />
      <ManagedDialog
        id="edit-job-location"
        reference={job.reference}
        path={"cache.customerLocation"}
        location={job.cache?.customerLocation}
        userReference={job.customer}
        jobReference={job.reference}
        userName={job.cache?.customerName}
        component={EditLocationDialog}
      />
    </>
  );
}
