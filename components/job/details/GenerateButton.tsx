import { Button } from "@mui/material";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import { useDialog } from "../../dialogs/DialogManager";

export default function GenerateButton({
  dialog,
  disabled,
}: {
  dialog: string;
  disabled: boolean;
}) {
  const { open } = useDialog(dialog);
  return (
    <Button
      disabled={disabled}
      onClick={() => open()}
      startIcon={<AutoFixHighIcon />}
    >
      Generate
    </Button>
  );
}
