import { Job } from "../../models/job/job";

import { useState } from "react";
import { Button, Dialog, DialogTitle } from "@mui/material";
import { updateDoc } from "@firebase/firestore";

import CompanyPicker from "../company/CompanyPicker";
import { Company, fetchCompany } from "../../models/company/company";
import {
  assignCompanyToJob,
  removeCompanyFromJob,
} from "../../models/job/job-mutations";

const JobCompanyCard = ({ job }: { job: Job }) => {
  const [open, setOpen] = useState(false);

  const onOpen = () => setOpen(true);
  const onClose = () => setOpen(false);

  const removeCompany = async () => {
    if (
      window.confirm(
        "Are you sure you want to remove the installer from this job?",
      )
    ) {
      await removeCompanyFromJob(job);
    }
  };

  const connectCompanyToJob = async (company: Company) => {
    await assignCompanyToJob(job, company);
    onClose();
  };

  const generateQuoteAutomatically = async () => {
    await updateDoc(job.reference, { sendAutomaticRawQuoteDraft: true });
    onClose();
  };

  const ignore = () => {
    onClose();
  };

  return (
    <>
      {job.fixedPriceJobs?.length ? (
        <Dialog open={open}>
          <DialogTitle>Generate quote automatically?</DialogTitle>

          <Button
            style={{ margin: "12px" }}
            variant="contained"
            color="primary"
            onClick={generateQuoteAutomatically}
          >
            Generate
          </Button>

          <Button
            style={{ margin: "12px" }}
            variant="outlined"
            color="primary"
            onClick={ignore}
          >
            Ignore
          </Button>
        </Dialog>
      ) : null}

      <CompanyPicker
        companyReference={job.company}
        onChange={async (companyReference) => {
          if (companyReference) {
            const company = await fetchCompany(companyReference.id);
            await connectCompanyToJob(company);
            onOpen();
          } else {
            await removeCompany();
          }
        }}
      />
    </>
  );
};

export default JobCompanyCard;
