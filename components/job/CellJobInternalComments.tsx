import { CircularProgress, IconButton, Tooltip } from "@mui/material";
import { useInternalCommentsExistOnSubject } from "../../hooks/inbox/internalCommentsHook";
import { jobReference } from "../../models/job/job-provider";
import React from "react";
import ComponentContainer from "../common/ComponentContainer";
import CommentIcon from "@mui/icons-material/Comment";
import SubjectInternalCommentsList from "./internal-comments/SubjectInternalCommentsList";

export function CellJobInternalComments({ jobId }: { jobId: string }) {
  const jobRef = jobReference(jobId);
  const { exists, loading } = useInternalCommentsExistOnSubject(jobRef);

  if (loading) return <CircularProgress color="info" />;

  if (!exists) {
    return <React.Fragment>-</React.Fragment>;
  }

  return (
    <Tooltip
      title={
        <ComponentContainer title={"Job internal comments"}>
          <SubjectInternalCommentsList subject={jobRef} />
        </ComponentContainer>
      }
      arrow
    >
      <IconButton color="info">
        <CommentIcon />
      </IconButton>
    </Tooltip>
  );
}
