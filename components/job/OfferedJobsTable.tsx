import { Typography } from "@mui/material";
import { useCollection } from "react-firebase-hooks/firestore";
import { DataGrid, GridCellParams } from "@mui/x-data-grid";
import {
  DocumentReference,
  query,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { JobOfferAnswer } from "../../models/job/offer/job-offer";
import { jobOfferCollection } from "../../models/job/offer/job-offer-provider";
import Link from "next/link";
import { maxLengthWithEllipsis } from "../../lib/max-length-with-ellipses";
import { ExtendedGridColDef } from "../common/table/columns";

export const OfferedJobsTable = ({
  companyReference,
}: {
  companyReference: DocumentReference;
}) => {
  const queryReference = query(
    jobOfferCollection(),
    where("offeredTo", "array-contains", companyReference),
    orderBy("createTime", "desc"),
    limit(50),
  );

  const [jobOffers, loading, error] = useCollection(queryReference);

  if (error) <Typography>{error.message}</Typography>;

  const rows = jobOffers?.docs?.map((doc) => {
    const offer = doc.data();
    const companyReply = offer.replies[`${companyReference.id}`];

    return {
      id: doc.id,
      doc: doc.ref,
      answer: companyReply?.answer,
      answeredAt: companyReply?.createdAt?.toDate(),
      jobStatus: offer.jobStatus,
      jobDescription: offer.description,
    };
  });

  const columns: ExtendedGridColDef[] = [
    {
      headerName: "Reply",
      field: "reply",
      width: 200,
      renderCell: (params: GridCellParams) => {
        return (
          <Typography color={getAnswerColor(params.row.answer)}>
            {getAnswerLabel(params.row.answer)}
          </Typography>
        );
      },
    },
    {
      field: "answeredAt",
      headerName: "Answered at",
      width: 160,
      type: "date",
    },
    {
      headerName: "Job description",
      field: "jobDescription",
      width: 400,
      renderCell: (params: GridCellParams) => {
        return (
          <Link passHref href={`/jobs/job/?id=${params.row.id}`}>
            <Typography
              variant="body2"
              sx={{ textDecoration: "underline" }}
              textOverflow="ellipsis"
              color="primary.main"
            >
              {maxLengthWithEllipsis(44, params.row.jobDescription)}
            </Typography>
          </Link>
        );
      },
    },
    {
      headerName: "Job Status",
      field: "jobStatus",
      width: 160,
    },
  ];

  return (
    <DataGrid
      style={{
        height: "780px",
      }}
      disableRowSelectionOnClick
      loading={loading}
      rows={rows ?? []}
      columns={columns}
    />
  );
};

export const getAnswerColor = (answer: JobOfferAnswer) => {
  switch (answer) {
    case "decline":
      return "error.main";
    case "accept":
      return "success.main";
    case "conditionallyAccepted":
      return "warning.main";
    default:
      return undefined;
  }
};

export const getAnswerLabel = (answer: JobOfferAnswer) => {
  switch (answer) {
    case "decline":
      return "Declined";
    case "accept":
      return "Accepted";
    case "conditionallyAccepted":
      return "Conditionally accepted";
    default:
      return "Not answered";
  }
};

export default OfferedJobsTable;
