import Loading from "../common/Loading";
import { deleteField, doc, getFirestore, updateDoc } from "firebase/firestore";
import { FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import { formsQuery } from "../../models/job/report/form-converter";
import { Job } from "../../models/job/job";
import { LabeledValue } from "../../config/constants";
import { Stack } from "@mui/system";
import { startCase } from "lodash";
import { useCollectionData } from "react-firebase-hooks/firestore";
import { FormConfiguration } from "../../models/job/report/report";
import { useMemo } from "react";

interface JobFormsProps {
  job: Job;
  showAll?: boolean;
}

const formLabeledValue =
  (showAll: boolean) =>
  (form: FormConfiguration): LabeledValue<string> => ({
    label: `${form.formSchema.title ?? form.reference.id}${
      showAll ? ` (${form.reference.parent.parent?.id})` : ""
    }`,
    value: form.reference.path,
  });

export default function JobForms({ job, showAll = false }: JobFormsProps) {
  const [forms, loading] = useCollectionData(
    formsQuery(showAll ? undefined : job.merchant?.id),
  );

  if (loading) return <Loading />;

  if (!forms) return null;

  const preCheckOptions = forms
    .filter((e) => e.type === "precheck")
    .map(formLabeledValue(showAll));

  const installationOptions = forms
    .filter((e) => e.type === "installation")
    .map(formLabeledValue(showAll));

  return (
    <Stack spacing={2} marginTop={2}>
      <SelectForms
        options={preCheckOptions}
        job={job}
        path="prechecksReportForm"
        disabled={
          Boolean(job.events?.partnershipPrechecksReported) ||
          Boolean(job.events?.jobDone)
        }
      />
      <SelectForms
        options={installationOptions}
        job={job}
        path="installationReportForm"
        disabled={
          Boolean(job.events?.partnershipInstallationReported) ||
          Boolean(job.events?.jobDone)
        }
      />
    </Stack>
  );
}

function SelectForms({
  options,
  job,
  path,
  disabled = false,
}: {
  options: LabeledValue<string>[];
  job: Job;
  path: "prechecksReportForm" | "installationReportForm";
  disabled?: boolean;
}) {
  const labelId = `${path}-select`;
  const label = startCase(path);
  const selectedValue = job[path]?.reference?.path;

  const selectedExists = useMemo(() => {
    const selectedItem = options.find((e) => e.value === selectedValue);
    return Boolean(selectedItem);
  }, [options, selectedValue]);

  return (
    <FormControl disabled={disabled}>
      <InputLabel id={labelId}>{label}</InputLabel>
      <Select
        fullWidth
        style={{ minWidth: 200, marginTop: 8 }}
        value={selectedValue}
        size="small"
        labelId={labelId}
        label={label}
        variant="outlined"
        onChange={async ({ target }) => {
          if (target.value) {
            await updateDoc(job.reference, {
              [`${path}.reference`]: doc(getFirestore(), target.value),
            });
          } else {
            await updateDoc(job.reference, {
              [path]: deleteField(),
            });
          }
        }}
      >
        {[
          ...options,
          {
            value: null,
            label: "None",
          },
        ].map((tag: any) => (
          <MenuItem value={tag.value} key={tag.value}>
            {tag.label}
          </MenuItem>
        ))}
        {selectedExists ? null : (
          <MenuItem value={selectedValue} key="failover">
            ({selectedValue})
          </MenuItem>
        )}
      </Select>
    </FormControl>
  );
}
