import JobChat from "./JobChat";
import ComponentContainer from "../common/ComponentContainer";
import JobDetail from "./JobDetail";
import JobToolbar from "./JobToolbar";
import Loading from "../common/Loading";
import { Alert, AlertTitle, Box } from "@mui/material";
import { useMediaQuery } from "react-responsive";
import { useJob } from "../../hooks/job/jobHook";
import JobInvoiceLines from "./JobInvoiceLines";
import JobEventsList from "./JobEvents";
import JobReports from "./JobReports";
import JobQuotes from "./JobQuotes";
import UserCard from "../user/UserCard";
import AttachmentsContainer from "./details/AttachmentsContainer";
import FileQueue from "../upload/FileQueue";
import JobCompanyReviews from "./JobCompanyReviews";
import { PlatformTier, StandardTier } from "../auth/Tiered";
import JobCompanyCard from "./JobCompanyCard";
import SendOfferCard from "./PartnerJobMatchInstallers";
import { useAuthUser } from "../auth/AuthProvider";
import { isPlatformPartner } from "../../models/other/partner";
import DialogManager from "../dialogs/DialogManager";
import ManagedJobDialogs from "./details/ManagedJobDialogs";
import NetworkChip from "../company/NetworkChip";
import NetworkSelectButton from "./NetworkSelectButton";
import { setJobNetwork } from "../../models/job/job-mutations";
import InboxContainer from "./details/InboxContainer";
import JobForms from "./JobForms";
import EventSettings from "./EventSettings";

interface PartnersJobProps {
  jobId: string;
  height?: number;
  mapIsActivated?: boolean;
  displayGoToDetailButton?: boolean;
  collaborationsTable?: boolean;
  hideInternalComments?: boolean;
  hideMatching?: boolean;
  fixedToolbar?: boolean;
  hideToolbar?: boolean;
  onClose?: () => void;
}

function OverflowContainer({
  children,
  fixed,
}: {
  children: React.ReactNode;
  fixed?: boolean;
}) {
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 700px)" });

  if (fixed) {
    return <Box>{children}</Box>;
  }

  return (
    <Box
      overflow={"hidden"}
      flexDirection="column"
      display={"flex"}
      height={"calc(100vh - 60px)"}
    >
      <Box
        flexGrow={1}
        display={isTabletOrMobile ? undefined : "flex"}
        overflow={isTabletOrMobile ? "auto" : "hidden"}
      >
        {children}
      </Box>
    </Box>
  );
}

export default function PartnersJob({
  jobId,
  displayGoToDetailButton = true,
  onClose,
  hideInternalComments = false,
  hideMatching = false,
  fixedToolbar = false,
  hideToolbar = false,
}: PartnersJobProps) {
  const { job, loading } = useJob(jobId);
  const { partnerDoc } = useAuthUser();

  if (loading || !job) return <Loading />;
  if (!partnerDoc) return null;

  const isPlatform = isPlatformPartner(partnerDoc);

  const isLocked = Boolean(
    job.status === "closed" ||
      job?.events?.jobDone ||
      job?.events?.craftsmanBilled ||
      (!isPlatform && job?.events?.partnerBilled),
  );

  return (
    <DialogManager>
      <ManagedJobDialogs job={job} />
      <FileQueue>
        <Box>
          {!hideToolbar && (
            <JobToolbar
              isPartnerJob={true}
              onClose={onClose}
              displayGoToDetailButton={displayGoToDetailButton}
              job={job}
            />
          )}

          <OverflowContainer fixed={fixedToolbar}>
            <Box flex={1} flexBasis={0} flexGrow={1} overflow="auto">
              {job.events?.jobDone && (
                <Alert style={{ margin: 4 }}>Job done</Alert>
              )}

              {job.partnerNotice?.message?.length && (
                <Alert severity="info">
                  <AlertTitle>Notice</AlertTitle> {job.partnerNotice.message}
                </Alert>
              )}

              {!job.events?.jobDone && job.closeReason && (
                <Alert style={{ margin: 4 }} severity="error">
                  <AlertTitle>Closed</AlertTitle>
                  Reason:{" "}
                  <strong>
                    {job.userCloseReasonInput
                      ? `Other, ${job.userCloseReasonInput}`
                      : job.closeReason}
                  </strong>
                </Alert>
              )}

              <ComponentContainer title="Customer">
                {job.customer ? (
                  <UserCard
                    showDeductionWarning={false}
                    userId={job.customer.id}
                  />
                ) : (
                  <Alert severity="info"> No customer set for this job </Alert>
                )}
              </ComponentContainer>

              <PlatformTier>
                <ComponentContainer
                  title="Installer"
                  actions={
                    job.company || !isPlatform ? (
                      <NetworkChip network={job.network.id} />
                    ) : (
                      <NetworkSelectButton
                        value={job.network.id}
                        choices={partnerDoc.networks}
                        onSelection={async (network) => {
                          await setJobNetwork(job, network);
                        }}
                      />
                    )
                  }
                >
                  <JobCompanyCard job={job} />
                </ComponentContainer>
              </PlatformTier>

              <ComponentContainer title="Details">
                <JobDetail job={job} />
              </ComponentContainer>

              {(job.attachments?.length || isPlatform) && (
                <AttachmentsContainer
                  job={job}
                  interactive={isPlatform && !isLocked}
                />
              )}

              <PlatformTier>
                <ComponentContainer title="Report forms">
                  <JobForms job={job} />
                </ComponentContainer>
              </PlatformTier>

              {!job.company && !hideMatching && (
                <PlatformTier>
                  <ComponentContainer title="Match">
                    <SendOfferCard job={job} />
                  </ComponentContainer>
                </PlatformTier>
              )}

              <ComponentContainer
                title="Events"
                actions={<EventSettings job={job} />}
              >
                <JobEventsList job={job} />
              </ComponentContainer>

              {job.company && (
                <ComponentContainer title="Quotes">
                  <JobQuotes jobRef={job.reference} />
                </ComponentContainer>
              )}

              {!hideInternalComments && (
                <InboxContainer
                  job={job.reference}
                  customer={job.customer}
                  company={job.company}
                />
              )}

              <StandardTier exact>
                <ComponentContainer title="Invoice lines">
                  <JobInvoiceLines jobReference={job.reference} />
                </ComponentContainer>
              </StandardTier>

              {job.installationReportForm && (
                <ComponentContainer title="Reports">
                  <JobReports jobReference={job.reference} />
                </ComponentContainer>
              )}

              <ComponentContainer title="Chat">
                <PlatformTier>
                  <JobChat job={job} jobId={jobId} />
                </PlatformTier>
                <StandardTier exact>
                  <JobChat readOnly job={job} jobId={jobId} />
                </StandardTier>
              </ComponentContainer>

              {job.events && job.events.companyReviewed && job.company && (
                <ComponentContainer title="Review">
                  <JobCompanyReviews
                    jobRef={job.reference}
                    companyRef={job.company}
                  />
                </ComponentContainer>
              )}
            </Box>
          </OverflowContainer>
        </Box>
      </FileQueue>
    </DialogManager>
  );
}
