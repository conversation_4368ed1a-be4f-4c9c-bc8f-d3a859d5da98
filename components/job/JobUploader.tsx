import { getFunctions } from "@firebase/functions";
import { LoadingButton } from "@mui/lab";
import { getApp } from "firebase/app";
import { httpsCallable } from "firebase/functions";
import { useSnackbar } from "notistack";
import <PERSON> from "papaparse";
import { Dispatch, SetStateAction, useState } from "react";
import { FileDrop } from "react-file-drop";
import { parseFullName } from "../../lib/name-parser";
import { BackofficeUploadJobData, RawImage } from "../../models/job/job";
import UploadJobCard from "./UploadJobCard";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Tab,
  Tabs,
} from "@mui/material";
import { DialogProps } from "../dialogs/default-dialog-props";
import { isDefined } from "../../lib/filter-undefined";
import { extractLinksFromMarkdownUsingMarked } from "../../lib/extract-links-from-markdown";
import { normalizePhoneNumber } from "../../lib/normalize-phone-number";
import { useAuthUser } from "../auth/AuthProvider";
import { usePartner } from "../../hooks/partner/use-partner";

interface EvifyJobParse {
  ["Vad är ditt fullständiga namn?"]: string;
  ["Och slutligen - vad är ditt telefonnummer?"]: string;
  ["Vad är din e-postadress?"]: string;
  ["Vad är adressen som installationen ska utföras på?"]: string;
  ["Och vad är adressens postnummer?"]: string;
  ["Och i vilken stad?"]: string;
  ["Vad som ska installeras"]: string;
  ["Tack! Hur många meter är det, mer exakt?"]: string;
  ["Beskriv gärna installationsplatsen så gott det går"]: string;
  ["Vilket material består fasaden av?"]: string;
  ["Behöver vi borra igenom någon vägg för att genomföra kabeldragningen?"]: string;
  ["Vill du att vi monterar laddboxen på stolpe eller fasad?"]: string;
  ["Har du önskemål om att dra kabeln i befintligt kabelrör? "]: string;
  ["Hur stor är huvudsäkringen?"]: string;
  ["Vilken mailadress vill du använda till din lastbalanserare?"]: string;
  ["Bifoga gärna ett foto på installationsplatsen"]: string;
  ["Bifoga foto på el-centralen"]: string;
  ["Och bifoga gärna ett foto på fasadmätarskåpet"]: string;
  ["Ordernummer"]: string;
}

interface LaddboxbolagetParse {
  ["Namn"]: string;
  ["Adress, Postnr, Ort"]: string;
  ["Mailadress"]: string;
  ["Telefonnummer"]: string;
  ["Order"]: string;
  ["Ordernr - Märkning"]: string;
  ["Kommentar"]: string;
  ["Förutsättningar"]: string;
}

interface ChargehomeParse {
  ["Namn"]: string;
  ["Adress, Postnr, Ort"]: string;
  ["Mailadress"]: string;
  ["Telefonnummer"]: string;
  ["Order"]: string;
  ["Ordernr - Märkning"]: string;
  ["Kommentar"]: string;
  ["Förutsättningar"]: string;
}

interface EvoboxParse {
  ["Namn"]: string;
  ["Adress, Postnr, Ort"]: string;
  ["Mailadress"]: string;
  ["Telefonnummer"]: string;
  ["Order"]: string;
  ["Ordernr - Märkning"]: string;
  ["Kommentar från kund via Evobox:"]: string;
  ["Förutsättningar"]: string;
}

interface EvfuelParse {
  ["Ordernummer"]: string;
  ["Ange ditt telefonnummer"]: string;
  ["Installatör"]: string;
  ["Namn"]: string;
  ["Vilken laddbox har du beställt?"]: string;
  ["Status installatör"]: string;
  ["Ange din email"]: string;
  ["Vad har du för huvudsäkring?"]: string;
  ["Fastighetsbeteckningen"]: string;
  ["Gatuadress"]: string;
  ["Postnummer och ort"]: string;
  ["Kabelavstånd"]: string;
  ["Material"]: string;
  ["Installationsplats för laddbox"]: string;
  ["Elcentral"]: string;
  ["Fasadskåp"]: string;
  ["Installationsväg"]: string;
  ["Övrigt"]: string;
  ["Lastbalansering"]: string;
  ["Finns vägguttag i fasadmätarskåp?"]: string;
}

const JobUploadPartners = [
  "laddboxbolaget",
  "evfuel",
  "evobox",
  "chargehome",
  "evify",
];

const JobUploader = ({ isOpen, close }: DialogProps) => {
  const authUser = useAuthUser();

  const [tabValue, setTabValue] = useState(0);
  const [partnerFromUpload, setPartnerFromUpload] = useState("laddboxbolaget");
  const [partner] = usePartner(partnerFromUpload);
  const [jobsList, setJobsList] = useState<BackofficeUploadJobData[]>([]);
  const [checkedJobs, setCheckedJobs] = useState<number[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const { enqueueSnackbar } = useSnackbar();

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPartnerFromUpload(JobUploadPartners[newValue]);
    setJobsList([]);
    setCheckedJobs([]);
  };

  const createJobsHandler = async () => {
    setLoading(true);

    try {
      // We expect that partner is not null here
      if (!partner) throw new Error("Partner not found");

      await httpsCallable(
        getFunctions(getApp(), "europe-west1"),
        "backofficeUploadJobs",
      )({
        jobRequests: jobsList.filter((_, index) => checkedJobs.includes(index)),
        services: partner.apiSettings.defaultServices,
        createdById: authUser.userDoc!.reference.id,
        partner: partnerFromUpload,
      });
      enqueueSnackbar("Job was created successfully", {
        variant: "success",
      });
      setCheckedJobs([]);
      setLoading(false);
    } catch (e) {
      enqueueSnackbar(e ? `${e}` : "Error on upload job/s!", {
        variant: "error",
      });
      setCheckedJobs([]);
      setLoading(false);
    }
  };

  const handleDrop = (files: FileList | null) => {
    if (!files || files.length == 0) return;

    if (partnerFromUpload === "evify") {
      evifyDrop(files[0], setJobsList);
    } else if (partnerFromUpload === "chargehome") {
      chargehomeDrop(files[0], setJobsList);
    } else if (partnerFromUpload === "laddboxbolaget") {
      laddboxbolagetDrop(files[0], setJobsList);
    } else if (partnerFromUpload === "evfuel") {
      evfuelDrop(files[0], setJobsList);
    } else if (partnerFromUpload === "evobox") {
      evoboxDrop(files[0], setJobsList);
    }
  };

  return (
    <Dialog
      fullWidth
      maxWidth={"xl"}
      open={isOpen}
      onClose={() => {
        setJobsList([]);
        setCheckedJobs([]);
        close();
      }}
    >
      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs
            onChange={handleChange}
            value={tabValue}
            aria-label="basic tabs example"
          >
            {JobUploadPartners.map((e) => {
              return <Tab id={e} key={e} label={e.toUpperCase()} />;
            })}
          </Tabs>
        </Box>

        <FileDrop onDrop={handleDrop}>
          <div
            style={{
              margin: 20,
              borderRadius: "8px",
              border: "2px solid gray",
              borderStyle: "dotted",
              padding: "20px",
            }}
          >
            Drop {partnerFromUpload} csv file here!
          </div>
        </FileDrop>

        {jobsList.map((e, index) => (
          <UploadJobCard
            onDeleteImage={(imageUrl) => {
              const newJobs = [...jobsList];
              newJobs[index].jobData.images = newJobs[
                index
              ].jobData.images?.filter((e) => e.url !== imageUrl);
              setJobsList(newJobs);
            }}
            onDeleteCraftsmanNote={(index) => {
              const newJobs = [...jobsList];
              newJobs[index].jobData.noteForCraftsman = undefined;
              setJobsList(newJobs);
            }}
            partner={partnerFromUpload}
            key={index}
            data={e}
            selectedJobs={checkedJobs}
            index={index}
            onChange={(checked, index) => {
              if (checked) {
                setCheckedJobs([...checkedJobs, index]);
              } else {
                setCheckedJobs(checkedJobs.filter((e) => e !== index));
              }
            }}
          />
        ))}
      </DialogContent>

      <DialogActions>
        <Button
          onClick={() => {
            close();
          }}
        >
          Close
        </Button>
        <LoadingButton
          variant={checkedJobs.length === 0 ? "outlined" : "contained"}
          disabled={checkedJobs.length === 0}
          loading={loading}
          onClick={createJobsHandler}
        >
          Upload {checkedJobs.length} jobs
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

function evfuelDrop(
  file: File,
  setJobsList: Dispatch<SetStateAction<BackofficeUploadJobData[]>>,
) {
  Papa.parse<EvfuelParse>(file, {
    transformHeader: function (h) {
      return h.trim();
    },
    header: true,
    complete: function (results) {
      const parsedData = results.data
        .map((item) => {
          if (item["Installatör"] !== "Doneservices - 1337 Marknadsplatser AB")
            return undefined;

          const fullName = parseFullName(item["Namn"].trim());

          const orderNumber = item["Ordernummer"].toLocaleLowerCase().trim();

          const images: RawImage[] = [
            ...extractLinksFromMarkdownUsingMarked(
              item["Installationsplats för laddbox"],
            ),
            ...extractLinksFromMarkdownUsingMarked(
              item["Elcentral"],
              "Elcentral",
            ),
            ...extractLinksFromMarkdownUsingMarked(
              item["Fasadskåp"],
              "Fasadskåp",
            ),
            ...extractLinksFromMarkdownUsingMarked(
              item["Installationsväg"],
              "Installationsväg",
            ),
          ];

          const streetAddress = [item["Gatuadress"], item["Postnummer och ort"]]
            .filter(isDefined)
            .join(", ");
          const description = `Installation av elbilsladdare från EV FUEL.  
        
Laddbox: ${item["Vilken laddbox har du beställt?"]}
Lastbalanserare: ${item["Lastbalansering"]}
Huvudsäkring: ${item["Vad har du för huvudsäkring?"]}
Finns vägguttag ${item["Finns vägguttag i fasadmätarskåp?"]}
Kabelavstånd: ${item["Kabelavstånd"]}
Fasadmaterial: ${item["Material"]}
Övrigt: ${item["Övrigt"]}`;

          return {
            customerData: {
              firstName: fullName.firstName,
              lastName: fullName.lastName,
              phoneNumber: normalizePhoneNumber(
                item["Ange ditt telefonnummer"],
              ),
              email: item["Ange din email"],
              address: { streetAddress },
            },
            jobData: {
              images,
              externalReference: orderNumber,
              description,
            },
          };
        })
        .filter(isDefined);

      setJobsList(parsedData as BackofficeUploadJobData[]);
    },
  });
}

function laddboxbolagetDrop(
  file: File,
  setJobsList: Dispatch<SetStateAction<BackofficeUploadJobData[]>>,
) {
  Papa.parse<LaddboxbolagetParse>(file, {
    transformHeader: function (h) {
      return h.trim();
    },
    header: true,
    complete: function (results) {
      const parsedData = results.data.map((item) => {
        const fullName = parseFullName(item["Namn"]);

        const orderNumber = item["Ordernr - Märkning"]
          .toLocaleLowerCase()
          .replace("Laddboxbolaget", "")
          .trim();

        return {
          customerData: {
            firstName: fullName.firstName,
            lastName: fullName.lastName,
            phoneNumber: normalizePhoneNumber(item["Telefonnummer"]),
            email: item["Mailadress"],
            address: { streetAddress: item["Adress, Postnr, Ort"] },
          },
          jobData: {
            noteForCraftsman: item["Kommentar"],
            externalReference: orderNumber,
            description: item["Order"],
            pdfLink: {
              url: item["Förutsättningar"],
              title: "Projektbeskrivning",
              description: "Din bokning från Laddboxbolaget",
              image:
                "https://laddboxbolaget.se/wp-content/uploads/2021/07/green-discount-384.jpg",
              logo: "https://laddboxbolaget.se/wp-content/uploads/2020/11/Laddboxbolaget_logo_turkos.png",
            },
          },
        };
      });
      setJobsList(parsedData as BackofficeUploadJobData[]);
    },
  });
}

function chargehomeDrop(
  file: File,
  setJobsList: Dispatch<SetStateAction<BackofficeUploadJobData[]>>,
) {
  Papa.parse<ChargehomeParse>(file, {
    transformHeader: function (h) {
      return h.trim();
    },
    header: true,
    complete: function (results) {
      const parsedData = results.data
        .map((item) => {
          const fullName = parseFullName(item["Namn"]);

          if (!item["Namn"]) return undefined;

          return {
            customerData: {
              firstName: fullName.firstName,
              lastName: fullName.lastName,
              phoneNumber: normalizePhoneNumber(item["Telefonnummer"]),
              email: item["Mailadress"],
              address: { streetAddress: item["Adress, Postnr, Ort"] },
            },
            jobData: {
              noteForCraftsman: item["Kommentar"],
              externalReference: item["Ordernr - Märkning"],
              description: item["Order"],
            },
          };
        })
        .filter((e) => e);

      setJobsList(parsedData as BackofficeUploadJobData[]);
    },
  });
}

function evoboxDrop(
  file: File,
  setJobsList: Dispatch<SetStateAction<BackofficeUploadJobData[]>>,
) {
  Papa.parse<EvoboxParse>(file, {
    transformHeader: function (h) {
      return h.trim();
    },
    header: true,
    complete: function (results) {
      const parsedData = results.data
        .map((item) => {
          const fullName = parseFullName(item["Namn"]);

          if (!item["Namn"]) return undefined;

          return {
            customerData: {
              firstName: fullName.firstName,
              lastName: fullName.lastName,
              phoneNumber: normalizePhoneNumber(item["Telefonnummer"]),
              email: item["Mailadress"],
              address: { streetAddress: item["Adress, Postnr, Ort"] },
            },
            jobData: {
              noteForCraftsman: item["Kommentar från kund via Evobox:"],
              externalReference: item["Ordernr - Märkning"],
              description: item["Order"],
            },
          };
        })
        .filter((e) => e);

      setJobsList(parsedData as BackofficeUploadJobData[]);
    },
  });
}

function evifyDrop(
  file: File,
  setJobsList: Dispatch<SetStateAction<BackofficeUploadJobData[]>>,
) {
  Papa.parse<EvifyJobParse>(file, {
    transformHeader: function (h) {
      return h.trim();
    },
    header: true,
    complete: function (results) {
      const parsedData = results.data.map((item) => {
        const fullName = parseFullName(item["Vad är ditt fullständiga namn?"]);

        return {
          customerData: {
            firstName: fullName.firstName,
            lastName: fullName.lastName,
            // Evify table present phone numbers without plus!
            phoneNumber: normalizePhoneNumber(
              item["Och slutligen - vad är ditt telefonnummer?"],
            ),
            email: item["Vad är din e-postadress?"],
            address: {
              city: item["Och i vilken stad?"],
              streetAddress:
                item["Vad är adressen som installationen ska utföras på?"],
              zip: item["Och vad är adressens postnummer?"],
            },
          },
          jobData: {
            externalReference: item["Ordernummer"],
            description: `
${item["Vad som ska installeras"]}
Evify ordernummer: ${item["Ordernummer"]}               
Ort: ${item["Och i vilken stad?"]}
Avstånd till elcentral: ${
              item["Tack! Hur många meter är det, mer exakt?"]
            } meter
Installationsplats: ${
              item["Beskriv gärna installationsplatsen så gott det går"]
            }
Fasadmaterial: ${item["Vilket material består fasaden av?"]}
Håltagning: ${
              item[
                "Behöver vi borra igenom någon vägg för att genomföra kabeldragningen?"
              ] === "TRUE"
                ? "Ja"
                : "Nej"
            }
Montering: ${item["Vill du att vi monterar laddboxen på stolpe eller fasad?"]}
Kabeldragning i befintligt kabelrör: ${
              item[
                "Har du önskemål om att dra kabeln i befintligt kabelrör? "
              ] === "TRUE"
                ? "Ja"
                : "Nej"
            }
Huvudsäkring: ${item["Hur stor är huvudsäkringen?"]}
${item["Vilken mailadress vill du använda till din lastbalanserare?"] || ""}
                `,
            images: [
              {
                url: item["Bifoga gärna ett foto på installationsplatsen"],
                description: "Bifoga gärna ett foto på installationsplatsen",
              },
              {
                url: item["Bifoga foto på el-centralen"],
                description: "Bifoga foto på el-centralen",
              },
              {
                url: item["Och bifoga gärna ett foto på fasadmätarskåpet"],
                description: "Och bifoga gärna ett foto på fasadmätarskåpet",
              },
            ].filter((img) => Boolean(img)),
          },
        };
      });

      setJobsList(parsedData as BackofficeUploadJobData[]);
    },
  });
}

export default JobUploader;
