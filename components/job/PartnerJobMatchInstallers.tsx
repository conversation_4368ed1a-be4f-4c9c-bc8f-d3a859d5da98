import ClearIcon from "@mui/icons-material/Clear";
import SearchIcon from "@mui/icons-material/Search";
import { COMPANY_SERVICES } from "../../config/constants";
import { Job } from "../../models/job/job";
import { useMemo, useState } from "react";
import {
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Box,
  TextField,
  Checkbox,
  IconButton,
  useTheme,
  Theme,
} from "@mui/material";
import { doc, query, where } from "@firebase/firestore";
import { LoadingButton } from "@mui/lab";
import { tagToLabeledValue } from "../../models/other/admin-config";
import JobLocation from "./JobLocation";
import {
  useCollectionData,
  useDocumentData,
} from "react-firebase-hooks/firestore";
import { jobOfferCollection } from "../../models/job/offer/job-offer-provider";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import { companiesSortedByDistanceToWork } from "../../lib/sort-companies-by-distance-to-work";
import {
  companyCollection,
  CompanyWithDistance,
} from "../../models/company/company";
import { JobOffer } from "../../models/job/offer/job-offer";
import OfferCompanyRow, { offerReplyValues } from "./JobOfferCompanyRow";
import { useAuthUser } from "../auth/AuthProvider";
import Internal from "../auth/Internal";
import { PlatformTier } from "../auth/Tiered";
import { assignCompanyToJob } from "../../models/job/job-mutations";
import Calendar from "../calendar/Calendar";
import { eventForJob } from "../calendar/event-from-job";
import { openJobsQuery } from "../../models/job/job-provider";
import { useJobs } from "../../hooks/job/jobsHook";
import { formatTitleRelative } from "../../lib/format-time-relative";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { Visible } from "../common/Conditional";
import ManagedConfirmDialog from "../dialogs/ManagedConfirmDialog";
import EmptyState from "../empty-state/EmptyState";
import CompanyMarkers from "../map/markers/CompanyMarkers";

function toggle(set: any, value: any, toggle: any) {
  const next = new Set([...set]);
  next[toggle ? "add" : "delete"](value);
  return next;
}

export default function PartnerJobMatchInstallers({ job }: { job: Job }) {
  const isInInbox = job.status === "inbox";
  const theme = useTheme();
  const authUser = useAuthUser();
  const canAssignInstallers =
    authUser?.partnerDoc?.features?.assignInstallers ?? false;

  const [selected, setSelected] = useState(new Set<string>());
  const [sending, setSending] = useState(false);

  const [scheduledWorkStartTime, setScheduledWorkStartTime] = useState<Date>();

  const targetTime = scheduledWorkStartTime ?? job.scheduledWorkStartTime;

  const [serviceFilter, setServiceFilter] = useState<undefined | string | null>(
    job.services?.[0],
  );
  const [tagsFilter, setTagsFilter] = useState<string | null>();
  const [search, setSearch] = useState("");
  const [text, setText] = useState("");
  const [jobOffer, loadingJobOffer] = useDocumentData(
    doc(jobOfferCollection(), job.reference.id),
  );

  const tags = tagToLabeledValue({}, ["companies"]);

  const requestSearch = (searchValue: string) => {
    setSearch(searchValue);
  };

  const [companies, loading] = useCollectionData(
    query(
      companyCollection(),
      where("status", "in", ["active", "new"]),
      where(
        "memberOf",
        "array-contains-any",
        authUser?.partnerDoc?.networks ?? [],
      ),
    ),
  );

  const reset = () => {
    setSelected(new Set());
    setScheduledWorkStartTime(undefined);
    setText("");
    setServiceFilter(job.services?.[0]);
    setTagsFilter(undefined);
    setSearch("");
  };

  const companiesWithDistance = useMemo(() => {
    if (!companies || !jobOffer || !job) return [];
    return companiesSortedByDistanceToWork(companies, jobOffer, job)
      .filter((company) => {
        if (!serviceFilter && !tagsFilter) return true;
        const services = company.services || [];
        const tags = company.tags || [];

        return (
          (!serviceFilter || services.includes(serviceFilter)) &&
          (!tagsFilter || tags.includes(tagsFilter))
        );
      })
      .filter((company) =>
        company.name.toLowerCase().startsWith(search.toLowerCase()),
      )
      .slice(0, 10);
  }, [companies, jobOffer, job, serviceFilter, tagsFilter, search]);

  if (loadingJobOffer || loading || !companies || !jobOffer) return null;

  const selectedCompanies = companiesWithDistance.filter((company) =>
    selected.has(company.reference.id),
  );

  return (
    <DialogManager>
      <ManagedConfirmDialog />
      <Stack spacing={2}>
        <JobLocation height={280} job={job} zoom={8}>
          <CompanyMarkers
            companies={companiesWithDistance}
            selected={selectedCompanies.map((company) => company.reference.id)}
            onClick={(id) =>
              setSelected(toggle(selected, id, !selected.has(id)))
            }
          />
        </JobLocation>

        <Visible if={canAssignInstallers}>
          <OfferCalendar
            isReadOnly={!isInInbox}
            partner={authUser.partner}
            onlyCompanies={Array.from(selected)}
            onSelectedStart={(startDate) => {
              setScheduledWorkStartTime(startDate);
            }}
          />
        </Visible>

        {job.cache?.customerLocation?.coordinates && (
          <Stack spacing={2}>
            <TextField
              variant="outlined"
              onChange={(event: any) => requestSearch(event.target.value)}
              value={search}
              fullWidth
              placeholder="Search"
              size="small"
              slotProps={{
                input: {
                  startAdornment: <SearchIcon fontSize="small" />,
                  endAdornment: (
                    <IconButton
                      title="Clear"
                      aria-label="Clear"
                      size="small"
                      style={{
                        visibility: search.length != 0 ? "visible" : "hidden",
                      }}
                      onClick={() => {
                        setSearch("");
                      }}
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  ),
                },
              }}
            />

            <Internal>
              <Stack direction="row" spacing={1}>
                <FormControl>
                  <InputLabel id="service-select">Services</InputLabel>
                  <Select
                    fullWidth
                    style={{ minWidth: 200, marginTop: 8 }}
                    value={serviceFilter}
                    size="small"
                    labelId="service-select"
                    label="Services"
                    variant="outlined"
                    onChange={(value) => setServiceFilter(value.target.value)}
                  >
                    {[
                      ...COMPANY_SERVICES,
                      {
                        value: null,
                        label: "All",
                      },
                    ].map((item: any, index: any) => (
                      <MenuItem value={item.value} key={index}>
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl>
                  <InputLabel id="tag-select">Tags</InputLabel>
                  <Select
                    fullWidth
                    style={{ minWidth: 200, marginTop: 8 }}
                    value={tagsFilter}
                    size="small"
                    labelId="tag-select"
                    label="Tags"
                    variant="outlined"
                    onChange={(value) => setTagsFilter(value.target.value)}
                  >
                    {[
                      ...tags,
                      {
                        value: null,
                        label: "All",
                      },
                    ].map((tag: any, index: any) => (
                      <MenuItem value={tag.value} key={index}>
                        {tag.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
            </Internal>
            <Box
              width={"100%"}
              sx={{
                maxHeight: "500px",
                overflowX: "auto",
                marginTop: 2,
                marginBottom: 2,
              }}
            >
              {companiesWithDistance.length === 0 ? (
                <EmptyState
                  icon={<SearchIcon />}
                  title="No matching installers found"
                />
              ) : (
                companiesWithDistance.map((company) => {
                  return (
                    <Stack
                      paddingTop={2}
                      sx={{
                        backgroundColor: offerStatusColor(
                          theme,
                          company,
                          jobOffer,
                        ),
                      }}
                      key={company.reference.id}
                    >
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Checkbox
                          sx={{
                            opacity: (jobOffer?.offeredTo || [])
                              .map((e) => e.id)
                              .includes(company.reference.id)
                              ? 0.1
                              : 1,
                          }}
                          disabled={loading || !isInInbox}
                          checked={
                            selected.has(company.reference.id) ||
                            job.company?.id === company.reference.id
                          }
                          onChange={(ev) =>
                            setSelected(
                              toggle(
                                selected,
                                company.reference.id,
                                ev.target.checked,
                              ),
                            )
                          }
                        />

                        <OfferCompanyRow
                          job={job}
                          jobOffer={jobOffer}
                          company={company}
                        />
                      </Stack>

                      <Divider style={{ paddingBottom: 12 }} />
                    </Stack>
                  );
                })
              )}
            </Box>

            <Internal>
              <TextField
                size="small"
                multiline
                maxRows={5}
                label="Offer sms"
                variant="outlined"
                sx={{ width: "100%" }}
                value={text}
                onChange={(ev) => setText(ev.target.value)}
              />
            </Internal>
            <PlatformTier>
              <Stack direction="row" spacing={1}>
                <Visible if={canAssignInstallers}>
                  <ConfirmMatchButton
                    loading={sending}
                    disabled={
                      selected.size !== 1 ||
                      !isInInbox ||
                      !scheduledWorkStartTime ||
                      !selectedCompanies[0]?.memberOf?.includes(
                        authUser.partner ?? "",
                      )
                    }
                    hasArticles={!!job.articles?.length}
                    onSend={async () => {
                      setSending(true);
                      await assignCompanyToJob(
                        job,
                        companiesWithDistance.find((c) =>
                          selected.has(c.reference.id),
                        )!,
                        scheduledWorkStartTime,
                      );
                      reset();
                      setSending(false);
                    }}
                  >
                    {isInInbox ? "Schedule for " : "Scheduled at "}
                    {formatTitleRelative(targetTime)}
                  </ConfirmMatchButton>
                </Visible>
                <ConfirmMatchButton
                  loading={sending}
                  disabled={selected.size < 1 || !isInInbox}
                  hasArticles={!!job.articles?.length}
                  onSend={async () => {
                    setSending(true);
                    await httpsCallable(
                      getFunctions(getApp(), "europe-west1"),
                      "sendJobOffers",
                    )({
                      jobId: job.reference.id,
                      companies: Array.from(selected),
                      smsBody: `Nytt uppdrag: ${job.description}. Svara i appen.`,
                    });

                    reset();
                    setSending(false);
                  }}
                >
                  Send offer
                </ConfirmMatchButton>
              </Stack>
            </PlatformTier>
          </Stack>
        )}
      </Stack>
    </DialogManager>
  );
}

export function offerStatusColor(
  theme: Theme,
  company: CompanyWithDistance,
  jobOffer: JobOffer,
) {
  if (jobOffer.replies?.[company.reference.id]) {
    const replyValues = offerReplyValues(
      theme,
      jobOffer.replies[company.reference.id]!,
    );

    return `${replyValues.backgroundColor}20`;
  }

  if (jobOffer.offeredTo?.find((e) => e.id === company.reference.id)) {
    return `${theme.palette.info.main}20`;
  }

  return undefined;
}

function OfferCalendar({
  partner,
  onlyCompanies,
  onSelectedStart,
  isReadOnly = false,
}: {
  partner?: string;
  onlyCompanies: string[];
  onSelectedStart?: (start: Date) => void;
  isReadOnly?: boolean;
}) {
  const jobsQuery = openJobsQuery(partner);
  const { jobs } = useJobs(jobsQuery);
  const affectedJobs = jobs?.filter(
    (e) =>
      e.scheduledWorkStartTime &&
      (onlyCompanies.length
        ? onlyCompanies.includes(e.company?.id ?? "")
        : true),
  );
  const events = affectedJobs?.map(eventForJob);

  return (
    <Calendar
      isReadOnly={isReadOnly}
      height="40vh"
      events={events}
      onSelectDateTime={(start) => onSelectedStart?.(start)}
      initialView="week"
    />
  );
}

interface MatchOrderButtonProps {
  disabled: boolean;
  loading: boolean;
  hasArticles: boolean;
  onSend: () => Promise<void>;
  children: React.ReactNode;
}

function ConfirmMatchButton({
  disabled,
  loading,
  hasArticles,
  onSend,
  children,
}: MatchOrderButtonProps) {
  const { open } = useDialog("confirm-dialog");
  return (
    <LoadingButton
      loading={loading}
      disabled={disabled}
      variant="contained"
      color={hasArticles ? "primary" : "warning"}
      onClick={async () => {
        if (!hasArticles) {
          open({
            title: "No articles added",
            description:
              "There are no articles added to this order. Are you sure you want to continue?",
            onConfirm: onSend,
          });
        } else {
          await onSend();
        }
      }}
    >
      {children}
    </LoadingButton>
  );
}
