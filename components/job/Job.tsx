import JobCompanyNotes from "./JobCompanyNotes";
import JobCompanyReviews from "./JobCompanyReviews";
import ComponentContainer from "../common/ComponentContainer";
import JobDetail from "./JobDetail";
import JobEvents from "./JobEvents";
import JobQuotes from "./JobQuotes";
import JobToolbar from "./JobToolbar";
import Loading from "../common/Loading";
import SendOfferCard from "./JobSendOfferCard";
import SentOffers from "./JobsSentOffers";
import {
  Alert,
  AlertTitle,
  Box,
  Button,
  IconButton,
  LinearProgress,
  Stack,
  Tooltip,
  Typography,
  useTheme,
} from "@mui/material";
import Image from "next/image";
import { useMediaQuery } from "react-responsive";
import UserCard from "../user/UserCard";
import { useJob } from "../../hooks/job/jobHook";
import JobCard from "./JobCard";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import DialogManager from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import JobChat from "./JobChat";
import JobCompanyCard from "./JobCompanyCard";
import { useCompany } from "../../hooks/company/companyHook";
import {
  Partner,
  isPartnerApplicable,
  isPlatformPartner,
  partnerRef,
} from "../../models/other/partner";
import { Company } from "../../models/company/company";
import GlobalSearch from "../search/GlobalSearchModel";
import GenerateButton from "./details/GenerateButton";
import InvoicesContainer from "./details/InvoicesContainer";
import JobReportsContainer from "./details/JobReportsContainer";
import InvoiceLinesContainer from "./details/InvoiceLinesContainer";
import ManagedJobDialogs from "./details/ManagedJobDialogs";
import AttachmentsContainer from "./details/AttachmentsContainer";
import { LocationOn } from "@mui/icons-material";
import { upperFirst } from "lodash";
import FileQueue from "../upload/FileQueue";
import InboxContainer from "./details/InboxContainer";
import UserChip from "../user/UserChip";
import TextAreaEditor from "../common/TextAreaEditor";
import NetworkChip from "../company/NetworkChip";
import NetworkSelectButton from "./NetworkSelectButton";
import { setJobNetwork } from "../../models/job/job-mutations";
import { useDocumentData } from "react-firebase-hooks/firestore";
import EventSettings from "./EventSettings";
import JobFormsContainer from "./JobFormsContainer";
import formatAmount from "../../lib/format-amount";
import { toNumber } from "../../models/finance/cent-value";
function PartnerInfo({
  partner,
  company,
  usesPartnerSettings,
}: {
  partner?: Partner;
  company?: Company;
  usesPartnerSettings?: boolean;
}) {
  if (!partner) return null;
  if (!partner.logo) return <>{partner.name}</>;

  return (
    <Stack direction={"row"}>
      {!usesPartnerSettings ? (
        <Tooltip
          arrow
          title={`Matched company "${company?.name}" is not configured for this partner\nPartner settings does not apply`}
        >
          <IconButton size="small" color="warning">
            <WarningAmberIcon />
          </IconButton>
        </Tooltip>
      ) : null}
      {!company && partner.jobSettings?.requireMatchingCompanyTag ? (
        <Tooltip
          arrow
          title={
            "Partner configuration is conditional and pending company match"
          }
        >
          <IconButton size="small" color="info">
            <InfoOutlinedIcon />
          </IconButton>
        </Tooltip>
      ) : null}
      <Image
        alt={partner.name}
        width={60}
        height={30}
        src={partner.logo}
        style={{ objectFit: "contain" }}
      />
    </Stack>
  );
}

interface JobProps {
  jobId: string;
  height?: number;
  mapIsActivated?: boolean;
  displayGoToDetailButton?: boolean;
  collaborationsTable?: boolean;
  onClose?: () => void;
  slim?: boolean;
  hideOffer?: boolean;
  hideInternalComments?: boolean;
}

export default function Job({
  jobId,
  height,
  collaborationsTable = false,
  mapIsActivated = false,
  displayGoToDetailButton = true,
  slim = false,
  hideOffer = false,
  hideInternalComments = false,
  onClose,
}: JobProps) {
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 700px)" });
  const { job, loading } = useJob(jobId);
  const [partner] = useDocumentData(partnerRef(job?.merchant?.id));
  const { company } = useCompany(job?.company?.id);

  const isPending = job?.status === "pending";

  const usesPartnerSettings =
    partner && company ? isPartnerApplicable(partner, company) : true;

  const isSubcontractorJob =
    job?.partner === "subcontractor" && usesPartnerSettings;

  const isPlatform = isPlatformPartner(partner);

  const isLocked = Boolean(
    job?.events?.partnershipInstallationReported ||
      job?.events?.craftsmanBilled ||
      (!isPlatform && job?.events?.partnerBilled),
  );

  const theme = useTheme();

  if (loading || !job) return <Loading />;

  return (
    <FileQueue>
      <DialogManager>
        <ManagedDialog id="global-search" component={GlobalSearch} />
        <ManagedJobDialogs job={job} />
        <>
          <JobToolbar
            onClose={onClose}
            displayGoToDetailButton={displayGoToDetailButton}
            job={job}
          />

          {isPending && (
            <>
              <Alert square severity="warning">
                Job is not fully created, please wait...
              </Alert>
              <LinearProgress sx={{ height: 3 }} color="warning" />
            </>
          )}

          <Box
            overflow={"hidden"}
            flexDirection="column"
            display={"flex"}
            sx={{
              opacity: isPending ? 0.3 : 1,
              pointerEvents: isPending ? "none" : undefined,
            }}
            height={
              !displayGoToDetailButton
                ? "calc(100vh - 50px)"
                : height
                  ? height - 50
                  : mapIsActivated
                    ? `calc(100vh - ${collaborationsTable ? 650 : 600}px)`
                    : `calc(100vh - ${collaborationsTable ? 150 : 100}px)`
            }
          >
            <Box style={{ height: 32 }} />

            <Box
              flexGrow={1}
              display={isTabletOrMobile || slim ? undefined : "flex"}
              overflow={isTabletOrMobile || slim ? "auto" : "hidden"}
            >
              <Box flexBasis={0} flexGrow={slim ? 1 : 0.5} overflow="auto">
                <ComponentContainer
                  title={
                    job.services?.map((e) => upperFirst(e)).join(", ") ?? ""
                  }
                  actions={
                    <PartnerInfo
                      partner={partner}
                      company={company}
                      usesPartnerSettings={usesPartnerSettings}
                    />
                  }
                >
                  <Stack alignItems="center" direction="row" spacing={1}>
                    <LocationOn />
                    <Typography variant="body2">
                      {job.cache?.customerLocation?.addressLine}
                    </Typography>
                  </Stack>
                </ComponentContainer>

                {job.events?.jobDone && (
                  <Alert style={{ margin: 4 }}>Job done</Alert>
                )}

                {!job.events?.jobDone && job.closeReason && (
                  <>
                    <Alert icon={false} style={{ margin: 4 }} severity="error">
                      <AlertTitle>Job closed</AlertTitle>
                      Reason:{" "}
                      <strong>
                        {job.userCloseReasonInput
                          ? `Other, ${job.userCloseReasonInput}`
                          : job.closeReason}
                      </strong>
                      {job.closedBy && (
                        <Stack direction="row" spacing={1}>
                          <Typography
                            variant="body2"
                            style={{ marginBottom: 8 }}
                          >
                            by:
                          </Typography>

                          <UserChip size="small" userId={job.closedBy.id} />
                        </Stack>
                      )}
                    </Alert>
                  </>
                )}

                {!hideOffer && !job.company && !job.events?.matchInitiated && (
                  <Alert
                    variant={
                      theme.palette.mode === "dark" ? "outlined" : "standard"
                    }
                    action={
                      <Button
                        size="small"
                        color="inherit"
                        onClick={() => {
                          const divElement =
                            document.getElementById("jobsOfferReference");
                          divElement?.scrollIntoView({ behavior: "smooth" });
                        }}
                      >
                        Jump to offers
                      </Button>
                    }
                    style={{ margin: 4 }}
                    severity="warning"
                  >
                    Job not offered to any company
                  </Alert>
                )}

                {!hideOffer && (
                  <ComponentContainer
                    title="Installer"
                    actions={
                      job.company ? (
                        <NetworkChip network={job.network.id} />
                      ) : (
                        <NetworkSelectButton
                          value={job.network.id}
                          choices={partner?.networks ?? []}
                          onSelection={async (network) => {
                            await setJobNetwork(job, network);
                          }}
                        />
                      )
                    }
                  >
                    <JobCompanyCard job={job} />
                  </ComponentContainer>
                )}

                <ComponentContainer title="Customer">
                  {job.customer ? (
                    <UserCard
                      showDeductionWarning={
                        !Boolean(job.invoicedThroughPartnership)
                      }
                      userId={job.customer.id}
                    />
                  ) : (
                    <Alert severity="info">
                      {" "}
                      No customer set for this job{" "}
                    </Alert>
                  )}
                </ComponentContainer>

                <ComponentContainer title="Details">
                  <JobDetail job={job} hideOffer={hideOffer} />
                </ComponentContainer>

                {job.copiedFrom && (
                  <ComponentContainer title="Copied from">
                    <Box style={{ opacity: 0.8 }}>
                      <JobCard jobReference={job.copiedFrom} />
                    </Box>
                  </ComponentContainer>
                )}

                {!hideOffer && <JobFormsContainer job={job} />}

                {job.copiedTo && job.copiedTo.length > 0 && (
                  <ComponentContainer title="Copied to">
                    <div style={{ opacity: 0.8 }}>
                      {job.copiedTo.map((e) => (
                        <div key={e.id} style={{ marginBottom: 8 }}>
                          <JobCard jobReference={e} />
                        </div>
                      ))}
                    </div>
                  </ComponentContainer>
                )}

                {!hideOffer && (
                  <AttachmentsContainer job={job} interactive={!isLocked} />
                )}

                {job.company && (
                  <ComponentContainer
                    title="Quotes"
                    actions={
                      <GenerateButton
                        dialog="generate-quote"
                        disabled={
                          isLocked ||
                          !Boolean(job.partner) ||
                          !Boolean(job.company)
                        }
                      />
                    }
                  >
                    <JobQuotes
                      jobRef={job.reference}
                      allowStatusChange={!isLocked}
                    />
                  </ComponentContainer>
                )}

                {job.company && job?.craftsmanProceeds && (
                  <ComponentContainer title="Installer earnings">
                    <Stack direction={"row"} spacing={1} alignItems="center">
                      <Typography>
                        {formatAmount(
                          toNumber(job.craftsmanProceeds.value ?? 0),
                          2,
                        )}
                      </Typography>
                      <Typography variant="caption">
                        {job.craftsmanProceeds.isEstimation && "(Estimated)"}
                      </Typography>
                    </Stack>
                  </ComponentContainer>
                )}

                <ComponentContainer title="Chat">
                  <JobChat job={job} jobId={jobId} />
                </ComponentContainer>

                {job.partner && (
                  <ComponentContainer title="Partner notice ">
                    <div>
                      <TextAreaEditor
                        minRows={1}
                        docRef={job.reference}
                        dataKey="partnerNotice.message"
                      />
                    </div>
                  </ComponentContainer>
                )}

                {!hideOffer && isTabletOrMobile && (
                  <ComponentContainer
                    title="Events"
                    actions={<EventSettings job={job} />}
                  >
                    <JobEvents job={job} />
                  </ComponentContainer>
                )}

                {job.company && (
                  <InvoicesContainer
                    job={job}
                    isSubcontractorJob={isSubcontractorJob}
                  />
                )}

                {!hideOffer && (
                  <JobReportsContainer jobReference={job.reference} />
                )}

                {job.company && (
                  <ComponentContainer title="Company notes">
                    <JobCompanyNotes
                      jobRef={job.reference}
                      companyRef={job.company}
                    />
                  </ComponentContainer>
                )}
                {!hideOffer && (
                  <ComponentContainer title="Offers">
                    <SentOffers job={job} />
                  </ComponentContainer>
                )}
              </Box>

              <Box flexBasis={0} flexGrow={slim ? 1 : 0.5} overflow="auto">
                {job.events && job.events.companyReviewed && job.company && (
                  <ComponentContainer title="Review">
                    <JobCompanyReviews
                      jobRef={job.reference}
                      companyRef={job.company}
                    />
                  </ComponentContainer>
                )}

                {!hideOffer && !isTabletOrMobile && (
                  <ComponentContainer
                    title="Events"
                    actions={<EventSettings job={job} />}
                  >
                    <JobEvents job={job} />
                  </ComponentContainer>
                )}

                {!hideInternalComments && (
                  <InboxContainer
                    job={job.reference}
                    customer={job.customer}
                    company={job.company}
                  />
                )}

                {!hideOffer && (
                  <InvoiceLinesContainer jobReference={job.reference} />
                )}

                {!hideOffer && !job.company && job.customer && (
                  <div id="jobsOfferReference">
                    <ComponentContainer title="Send offer">
                      <SendOfferCard job={job} />
                    </ComponentContainer>
                  </div>
                )}
              </Box>
            </Box>
          </Box>
        </>
      </DialogManager>
    </FileQueue>
  );
}
