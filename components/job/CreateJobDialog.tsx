import CancelIcon from "@mui/icons-material/Cancel";
import FixedPriceInfoCard from "../common/FixedPriceInfoCard";
import TooltipImagePreview from "../common/TooltipImagePreview";
import { ChangeEvent, useState } from "react";
import Router from "next/router";
import { AddPhotoAlternate, PersonSearch } from "@mui/icons-material";
import { DocumentReference, Timestamp } from "firebase/firestore";
import { DoneLocation } from "../../models/user/user";
import { Field, FormSpy } from "react-final-form";
import { getStorage, ref, uploadBytes } from "firebase/storage";
import { initialJobEvents, Job } from "../../models/job/job";
import { useCollection } from "react-firebase-hooks/firestore";

import {
  ACCEPTED_IMAGE_FILE_TYPES,
  COMPANY_SERVICES,
  JOB_TAGS,
} from "../../config/constants";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  styled,
  Badge,
  IconButton,
  Avatar,
  LinearProgress,
  Stack,
  Typography,
  Box,
  Card,
  Grid2,
} from "@mui/material";
import {
  getFirestore,
  collection,
  addDoc,
  updateDoc,
  arrayUnion,
  GeoPoint,
} from "@firebase/firestore";
import PartnerSelectField from "../form-fields/PartnerSelectField";
import { useGoogleGeocoding } from "../../hooks/useGoogleGeocoding";
import _ from "lodash";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";
import { tagToLabeledValue } from "../../models/other/admin-config";
import GoogleLocationSearch from "../search/GoogleLocationSearch";
import AutocompleteLabeledValueField from "../form-fields/AutocompleteField";
import TextField from "../form-fields/TextField";
import { valueToLabeledValue } from "../../lib/value-to-labeled-value";
import FormDialog from "../dialogs/FormDialog";
import { DialogProps } from "../dialogs/default-dialog-props";
import { GlobalSearchFormField } from "../search/GlobalSearcField";
import UserCard from "../user/UserCard";
import { useAuthUser } from "../auth/AuthProvider";
import { DoneAddress } from "../../models/other/done_address";
import removeUndefined from "../../lib/remove-undefined";
import { DonePartnerReference, partnerRef } from "../../models/other/partner";
import MapBox from "../map/MapBox";

const Input = styled("input")({
  display: "none",
});

interface CreateJobDialogProps extends DialogProps {
  copiedJob?: Job;
}

export default function CreateJobDialog({
  isOpen,
  close,
  copiedJob,
}: CreateJobDialogProps) {
  const db = getFirestore();

  const authUser = useAuthUser();

  const { config, loading: configLoading } = useAdminConfig();

  const [fixedPriceJobSnaps, loadingFixedPriceJobs] = useCollection(
    collection(db, "fixed-price-jobs"),
  );

  const [copiedJobImages, setCopiedJobImages] = useState(
    copiedJob?.images ?? undefined,
  );
  const [images, setImages] = useState<File[]>([]);

  const [geoCode, setGeoCode] = useState<
    google.maps.GeocoderResult | undefined
  >();

  const [noZipCodeError, setNoZipCodeError] = useState(false);
  const [wrongTypeError, setWrongTypeError] = useState(false);
  const { geocodeByPlaceId } = useGoogleGeocoding();

  const handleAddNewImage = (event: ChangeEvent<any>) => {
    if (event?.target?.files && event?.target?.files?.length > 0) {
      const newImageList = [...images, ...event?.target?.files];

      const wrongTypedImages = newImageList.filter((file) => {
        const fileType = file.name?.split(".")?.pop();

        if (!fileType) return true;

        return !ACCEPTED_IMAGE_FILE_TYPES.includes(fileType ?? "");
      });

      const correctTypedImages = newImageList.filter((file) => {
        const fileType = file.name?.split(".")?.pop();
        if (!fileType) return false;

        return ACCEPTED_IMAGE_FILE_TYPES.includes(fileType ?? "");
      });

      setImages(correctTypedImages);
      setWrongTypeError(wrongTypedImages && wrongTypedImages.length > 0);
    }
  };

  const removeImageFromList = (index: number) => {
    const newImageList = images.filter((_, i) => i != index);
    setImages(newImageList);
    setWrongTypeError(false);
  };

  if (configLoading || loadingFixedPriceJobs) return <LinearProgress />;

  const operationTagOptions = tagToLabeledValue(config?.tags, ["jobs"]);
  const fixedPriceJobOptions = fixedPriceJobSnaps?.docs.map((fixedPriceDoc) => {
    return {
      value: fixedPriceDoc.id,
      label: fixedPriceDoc.data().name,
      data: fixedPriceDoc.data(),
    };
  });

  return (
    <FormDialog
      positiveButtonText={copiedJob ? "Duplicate job" : "Create job"}
      maxWidth="lg"
      close={() => {
        setImages([]);
        setGeoCode(undefined);
        close();
      }}
      title={copiedJob ? "Duplicate job" : "Create job"}
      isOpen={isOpen}
      initialValues={
        copiedJob
          ? {
              partner: copiedJob?.partner,
              merchant: copiedJob?.merchant,
              network: copiedJob?.network,
              externalReference: copiedJob?.externalReference,
              services: copiedJob?.services,
              tags: copiedJob?.tags,
              customer: copiedJob?.customer,
              cache: {
                customerName: copiedJob?.cache?.customerName,
                customerLocation: copiedJob?.cache?.customerLocation,
                customerRegion: copiedJob?.cache?.customerRegion,
              },
              operationTags: copiedJob?.operationTags,
              description: copiedJob?.description,
            }
          : {}
      }
      onSubmit={async (newJob) => {
        const fixedPriceJob = fixedPriceJobOptions?.find(
          (e) => e.value === newJob.fixedPriceJob,
        );

        if ((newJob.services && newJob.services.length > 0) || fixedPriceJob) {
          // update user address.
          if (newJob.customer && geoCode) {
            const location = generateLocationFromAddressComponents(geoCode);

            const postalCode = geoCode.address_components.filter((e) =>
              e.types.includes("postal_code"),
            )[0]?.long_name;

            await updateDoc(newJob.customer, {
              location,
              address: {
                streetAddress: location.addressLine,
                zip: postalCode.replace(" ", ""),
              },
            });
          }

          // undefined check
          const job = removeUndefined({
            ...newJob,
            merchant: newJob.partner
              ? partnerRef(newJob.partner)
              : DonePartnerReference(),
            network: newJob.network ?? DonePartnerReference(),
            images: copiedJobImages ?? null,
            services: fixedPriceJob
              ? fixedPriceJob.data.services
              : newJob.services,
            operationTags: newJob.operationTags ? newJob.operationTags : [],
            tags: _.uniq(
              newJob.tags
                ? [
                    ...newJob.tags
                      .filter((tag: any) => tag)
                      .map((tags: any) => tags.value),
                    newJob.partner,
                  ]
                : newJob.partner
                  ? [newJob.partner]
                  : [],
            ).filter((e) => Boolean(e)),
            company: null,
            status: "pending",
            scheduledCallTime: null,
            isDemo: Boolean(newJob.isDemo),
            copiedFrom: copiedJob?.reference,
            createdBy: authUser.userDoc?.reference,
            createTime: Timestamp.now(),
            fixedPriceJobs: fixedPriceJob
              ? [{ id: fixedPriceJob.value, quantity: 1 }]
              : undefined,
            events: initialJobEvents,
          });

          const ref = await addDoc(collection(db, "jobs"), job);

          if (copiedJob) {
            updateDoc(copiedJob.reference, { copiedTo: arrayUnion(ref) });
          }

          if (images && images.length > 0) {
            for (let index = 0; index < images.length; index++) {
              const imageReferences = await uploadJobImage(
                images[index],
                ref,
                index,
              );
              await addImageReferencesToJob(ref, imageReferences);
            }
          }

          setImages([]);

          Router.push(`/jobs/job/?id=${ref.id}`);
        }
      }}
    >
      <Grid2 spacing={2} container>
        <Grid2 size={{ xs: 6 }}>
          <Stack spacing={2}>
            <Typography>Customer</Typography>

            <Card variant="outlined">
              <Stack spacing={1} padding={1}>
                <GlobalSearchFormField
                  icon={<PersonSearch />}
                  label="Select customer"
                  name="customer"
                  searchOptions={["users"]}
                />

                <Field
                  name={"customer"}
                  render={({ input }) => (
                    <>
                      {input.value ? (
                        <>
                          <UserCard
                            showExtras={false}
                            userId={input.value.id}
                          />
                          <Button
                            variant="outlined"
                            size="small"
                            color="error"
                            sx={{
                              justifyContent: "flex-start",
                            }}
                            onClick={() => {
                              input.onChange(undefined);
                            }}
                          >
                            Remove
                          </Button>
                        </>
                      ) : (
                        <Alert severity="info">No customer selected</Alert>
                      )}
                    </>
                  )}
                />
              </Stack>
            </Card>

            <Typography>Job location</Typography>

            <Card variant="outlined">
              <Stack spacing={1} padding={1}>
                <GoogleLocationSearch
                  placeholder="Set job location"
                  onLocationChanged={async (location) => {
                    const geoCodeResults = await geocodeByPlaceId(
                      location.place_id,
                    );
                    setGeoCode(geoCodeResults[0]);
                    const postalCode =
                      geoCodeResults[0].address_components.filter(
                        (e: google.maps.GeocoderAddressComponent) =>
                          e.types.includes("postal_code"),
                      )[0]?.long_name;

                    if (!postalCode) {
                      setNoZipCodeError(true);
                    } else {
                      setNoZipCodeError(false);
                    }
                  }}
                />

                {noZipCodeError ? (
                  <Alert style={{ marginTop: 8 }} severity="error">
                    No zip code in address
                  </Alert>
                ) : null}

                {geoCode && !noZipCodeError ? (
                  <MapBox center={geoCode.geometry.location} />
                ) : null}
              </Stack>
            </Card>

            <Typography>Job details</Typography>

            <AutocompleteLabeledValueField
              name="fixedPriceJob"
              label={"Fixed price"}
              items={valueToLabeledValue(
                fixedPriceJobOptions?.map((e: any) => e.value),
              )}
            />
            <FormSpy>
              {(props) => (
                <>
                  {props.values?.fixedPriceJob && (
                    <FixedPriceInfoCard
                      fixedPriceId={props.values?.fixedPriceJob}
                    />
                  )}
                </>
              )}
            </FormSpy>
            <FormSpy>
              {(props) => (
                <>
                  {!props.values?.fixedPriceJob && (
                    <AutocompleteLabeledValueField
                      name="services"
                      multiple
                      label={"Services"}
                      items={COMPANY_SERVICES}
                    />
                  )}
                </>
              )}
            </FormSpy>
            <TextField
              name="description"
              type="text"
              multiline
              label="Description"
              required
            />
            <TextField
              name="preferredCallTime"
              type="text"
              label="Preferred call time"
            />
            <FormSpy>
              {(props) => (
                <>
                  {!props.values?.fixedPriceJob && (
                    <TextField
                      name="preferredStartDate"
                      type="text"
                      label="Preferred start date"
                    />
                  )}
                </>
              )}
            </FormSpy>
            {!copiedJobImages?.length ? (
              <></>
            ) : (
              <div style={{ overflow: "hidden" }}>
                <div style={{ display: "flex", overflow: "scroll" }}>
                  {copiedJobImages.map((image: any, index: number) => (
                    <div key={index}>
                      <TooltipImagePreview key={image} image={image} />
                      <Button
                        onClick={() => {
                          const images = copiedJobImages.filter(
                            (_, i) => i != index,
                          );
                          setCopiedJobImages(images);
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
            {!images?.length ? (
              <label htmlFor="create-job-icon-button-file">
                <Input
                  accept="image/*"
                  id="create-job-icon-button-file"
                  type="file"
                  multiple={true}
                  onChange={handleAddNewImage}
                />
                <Button
                  fullWidth
                  color="primary"
                  aria-label="upload picture"
                  component="span"
                  variant="outlined"
                  startIcon={<AddPhotoAlternate />}
                >
                  Images
                </Button>
              </label>
            ) : (
              <></>
            )}
            {wrongTypeError ? (
              <Alert severity="error">
                Wrong format! Only jpeg and png allowed to upload.
              </Alert>
            ) : (
              <></>
            )}
            {images?.length ? (
              <Box sx={{ marginTop: 2 }}>
                {images.map((image, index) => {
                  return (
                    <Badge
                      key={index}
                      style={{
                        marginRight: 18,
                        marginBottom: 8,
                        display: "inline-block",
                      }}
                      overlap="circular"
                      badgeContent={
                        <IconButton
                          style={{ marginRight: -8, marginTop: -8 }}
                          size="small"
                          onClick={() => removeImageFromList(index)}
                          color="primary"
                          aria-label="upload picture"
                          component="span"
                        >
                          <CancelIcon
                            style={{
                              color: "red",
                              backgroundColor: "white",
                              borderRadius: 16,
                            }}
                          />
                        </IconButton>
                      }
                    >
                      <Avatar
                        style={{
                          border: "2px solid #e3e3e3",
                          borderRadius: 8,
                          width: 52,
                          height: 52,
                        }}
                        variant="rounded"
                        src={URL.createObjectURL(image)}
                      />
                    </Badge>
                  );
                })}
                <label
                  style={{ marginRight: 8, marginTop: 8 }}
                  htmlFor="icon-button-file"
                >
                  <Input
                    accept="image/*"
                    id="icon-button-file"
                    type="file"
                    multiple={true}
                    onChange={handleAddNewImage}
                  />
                  <IconButton aria-label="upload picture" component="span">
                    <AddPhotoAlternate />
                  </IconButton>
                </label>
              </Box>
            ) : (
              <></>
            )}
          </Stack>
        </Grid2>

        <Grid2 size={{ xs: 6 }}>
          <Stack spacing={2}>
            <Typography>Operation details</Typography>

            <PartnerSelectField />

            <TextField
              name="externalReference"
              type="text"
              label="External reference"
            />

            <AutocompleteLabeledValueField
              name="tags"
              multiple
              label={"Tags"}
              items={JOB_TAGS}
            />

            <AutocompleteLabeledValueField
              name="operationTags"
              multiple
              label={"Operation tags"}
              items={operationTagOptions}
            />
          </Stack>
        </Grid2>
      </Grid2>
    </FormDialog>
  );
}

export async function uploadJobImage(
  originalImage: File,
  jobRef: DocumentReference,
  index: number,
): Promise<{ original: string; thumb: string }> {
  const storageRef = getStorage();

  const originalRef = ref(
    storageRef,
    `job-images-v1/${jobRef.id}/orginal/original-${index}.jpg`,
  );

  const thumbRef = ref(
    storageRef,
    `job-images-v1/${jobRef.id}/thumb/thumb-${index}.jpg`,
  );

  const original = (await uploadBytes(originalRef, originalImage)).ref.fullPath;
  // TODO: Figure out resize image
  const thumb = (await uploadBytes(thumbRef, originalImage)).ref.fullPath;

  return { original, thumb };
}

async function addImageReferencesToJob(
  ref: DocumentReference,
  imageReferences: { original: string; thumb: string },
) {
  await updateDoc(ref, { images: arrayUnion(imageReferences) });
}

export function generateLocationFromAddressComponents(
  geoCodeResult: google.maps.GeocoderResult,
): DoneLocation {
  const postalTown = geoCodeResult.address_components.filter((e) =>
    e.types.includes("postal_town"),
  )[0]?.long_name;
  const subLocality = geoCodeResult.address_components.filter((e) =>
    e.types.includes("sublocality"),
  )[0]?.long_name;

  return removeUndefined({
    postalTown,
    coordinates: new GeoPoint(
      geoCodeResult.geometry.location.lat(),
      geoCodeResult.geometry.location.lng(),
    ),
    addressLine: geoCodeResult.formatted_address,
    subLocality,
  });
}

export function generateDoneAddressFromAddressComponents(
  geoCodeResult: google.maps.GeocoderResult,
): DoneAddress {
  return {
    streetAddress: geoCodeResult.formatted_address,
  };
}
