import { Content, DetailsPanel, MainPanel } from "../../layout/panels";
import { Header } from "../../layout/panels";
import { MenuPanel } from "../../layout/panels";
import { useState } from "react";
import {
  List,
  Stack,
  IconButton,
  Tooltip,
  Divider,
  ToggleButtonGroup,
  ToggleButton,
} from "@mui/material";
import { Job } from "../../../models/job/job";
import {
  Close,
  FolderShared,
  FolderSpecial,
  StarOutline,
  People,
  Star,
  PeopleOutlineOutlined,
  TableRows,
  Map as MapIcon,
} from "@mui/icons-material";
import _ from "lodash";
import JobDetails from "../Job";
import {
  RunnableFilter,
  FilterCollection,
  FilterOnClickHandler,
  FilterGroup,
  compileGroup,
  joinedFilter,
  compile,
} from "../../filtering";
import { useSettings } from "../../../lib/hooks/use-settings";
import {
  addFilterInput,
  removeFilterInput,
  SavedFilter,
  Settings,
  SettingsInput,
} from "../../../models/settings/settings";
import { useAuthUser } from "../../auth/AuthProvider";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { usePanelSelection } from "../../../lib/hooks/use-panel-selection";
import JobsDataGrid, { ColumnConfiguration } from "./JobsDataGrid";
import OperationsMap from "../../map/OperationsMap";
import { useCollectionDataOnce } from "react-firebase-hooks/firestore";
import { companiesInNetworkQuery } from "../../../models/company/company";
import { DonePartnerId } from "../../../models/other/partner";

interface FilterableOpenJobsPageProps {
  initialFilterIds?: string[];
  jobs: Job[];
  libraryFilterGroups: FilterGroup[];
  configuration: ColumnConfiguration;
}

/**
 * Page where user can filter jobs CLIENT SIDE.
 */
export default function FilterableOpenJobsPage({
  jobs,
  libraryFilterGroups,
  configuration,
  initialFilterIds = ["all"],
}: FilterableOpenJobsPageProps) {
  const { userDoc } = useAuthUser();
  const [
    [userSettings, partnerSettings],
    [updateUserSettings, updatePartnerSettings],
  ] = useSettings();

  const runnableGroups = libraryFilterGroups.map(compileGroup);

  const [selectedFilterIds, setSelectedFilterIds] =
    useState<string[]>(initialFilterIds);

  const isFilterSelected = (filter: RunnableFilter) =>
    selectedFilterIds.includes(filter.id);

  const userFilters = compileSettingFilters(userSettings);
  const partnerFilters = compileSettingFilters(partnerSettings);

  const selectedFilters = [
    ...runnableGroups.flatMap((group) => group.filters),
    ...userFilters,
    ...partnerFilters,
  ].filter(isFilterSelected);

  const filteredJobs = jobs.filter((model) =>
    selectedFilters.every((filter) => filter.filter(model)),
  );

  const isUserFilterSelected = Boolean(
    selectedFilters.length === 1
      ? userFilters.find((filter) => filter.id === selectedFilters[0].id)
      : false,
  );

  const isSharedFilterSelected = Boolean(
    selectedFilters.length === 1
      ? partnerFilters.find((filter) => filter.id === selectedFilters[0].id)
      : false,
  );

  const handleFilterClicked: FilterOnClickHandler = (filter, additive) => {
    if (additive) {
      if (isFilterSelected(filter)) {
        const filtered = selectedFilterIds.filter((id) => id !== filter.id);
        setSelectedFilterIds(filtered.length ? filtered : ["all"]);
      } else {
        setSelectedFilterIds([...selectedFilterIds, filter.id]);
      }
    } else {
      setSelectedFilterIds([filter.id]);
    }
  };

  const [selectedJob, , detailsPanelRef, handleJobSelect, handleClose] =
    usePanelSelection<string>();

  /** Creates a function that combines all selected filters into one and saves it using the provided saver function */
  const buildFilterSaver =
    (saver: (input: SettingsInput) => Promise<void>) => async () => {
      if (!userDoc) return;
      const newFilter = joinedFilter(selectedFilters);
      const savedFilter: SavedFilter = {
        createdBy: userDoc.reference,
        config: newFilter,
      };
      await saver(addFilterInput("jobs", savedFilter));
      setSelectedFilterIds([savedFilter.config.id]);
    };

  /** Creates a function that deletes a selected filter using the provided saver function */
  const buildFilterDestroyer =
    (
      settings: Settings | undefined,
      saver: (input: SettingsInput) => Promise<void>,
    ) =>
    async () => {
      if (!window.confirm("Are you sure you want to delete this filter?"))
        return;
      if (selectedFilters.length !== 1) return;
      const filter = findFilter(settings, selectedFilters[0].id);
      if (!filter) return;
      await saver(removeFilterInput("jobs", filter));
      setSelectedFilterIds(["all"]);
    };

  const saveUserFilter = buildFilterSaver(updateUserSettings);
  const saveSharedFilter = buildFilterSaver(updatePartnerSettings);

  const deleteUserFilter = buildFilterDestroyer(
    userSettings,
    updateUserSettings,
  );
  const deleteSharedFilter = buildFilterDestroyer(
    partnerSettings,
    updatePartnerSettings,
  );

  const additive = selectedFilterIds.length > 1;

  const [mode, setMode] = useState<Mode>("grid");

  return (
    <PanelGroup autoSaveId="filterable-open-jobs" direction="horizontal">
      <MenuPanel>
        <Header title="Open orders" />
        <Content>
          <List dense disablePadding>
            <FilterCollection
              title="My filters"
              icon={<FolderSpecial />}
              models={jobs}
              filters={userFilters}
              additive={additive}
              isFilterSelected={isFilterSelected}
              onFilterClick={handleFilterClicked}
            />
            <Divider />
            <FilterCollection
              title="Shared filters"
              icon={<FolderShared />}
              models={jobs}
              filters={partnerFilters}
              additive={additive}
              isFilterSelected={isFilterSelected}
              onFilterClick={handleFilterClicked}
            />
            <Divider />
            <FilterCollection
              title="Library"
              groups={runnableGroups}
              models={jobs}
              additive={additive}
              isFilterSelected={isFilterSelected}
              onFilterClick={handleFilterClicked}
            />
            <Divider />
          </List>
        </Content>
      </MenuPanel>
      <PanelResizeHandle />
      <MainPanel>
        <Header
          title={`${filteredJobs.length} ${selectedFilters.map((filter) => filter.title).join(", ")} orders`}
          titleSecondary={
            <Stack direction={"row"}>
              {isUserFilterSelected ? (
                <Tooltip title="Remove from My filters" arrow>
                  <IconButton
                    onClick={deleteUserFilter}
                    sx={{ color: "star.main" }}
                  >
                    <Star />
                  </IconButton>
                </Tooltip>
              ) : (
                <Tooltip title="Save to My filters" arrow>
                  <IconButton onClick={saveUserFilter}>
                    <StarOutline />
                  </IconButton>
                </Tooltip>
              )}
              {isSharedFilterSelected ? (
                <Tooltip title="Remove from Shared filters" arrow>
                  <IconButton onClick={deleteSharedFilter} color="primary">
                    <People />
                  </IconButton>
                </Tooltip>
              ) : (
                <Tooltip title="Save to Shared filters" arrow>
                  <IconButton onClick={saveSharedFilter}>
                    <PeopleOutlineOutlined />
                  </IconButton>
                </Tooltip>
              )}
            </Stack>
          }
        >
          <ModeToggle value={mode} onChange={setMode} />
        </Header>
        <Content>
          <MainContent
            mode={mode}
            configuration={configuration}
            filteredJobs={filteredJobs}
            handleJobSelect={handleJobSelect}
            selectedJob={selectedJob}
          />
        </Content>
      </MainPanel>
      <PanelResizeHandle />
      <DetailsPanel ref={detailsPanelRef} collapsible>
        <Header title="Details">
          <IconButton onClick={handleClose}>
            <Close />
          </IconButton>
        </Header>
        <Content>
          {selectedJob && <JobDetails slim jobId={selectedJob} />}
        </Content>
      </DetailsPanel>
    </PanelGroup>
  );
}

function compileSettingFilters(settings: Settings | undefined) {
  const filters = settings?.backoffice?.savedFilters?.jobs;
  if (!filters) return [];
  return filters.map((filter) => compile(filter.config));
}

function findFilter(settings: Settings | undefined, id: string) {
  const filters = settings?.backoffice?.savedFilters?.jobs;
  if (!filters) return;
  return filters.find((filter) => filter.config.id === id);
}

type Mode = "grid" | "map";

function ModeToggle({
  value,
  onChange,
}: {
  value: Mode;
  onChange: (mode: Mode) => void;
}) {
  return (
    <ToggleButtonGroup
      size="small"
      value={value}
      exclusive
      onChange={(_, value) => value && onChange(value)}
    >
      <Tooltip title="View as table" arrow>
        <ToggleButton value="grid">
          <TableRows />
        </ToggleButton>
      </Tooltip>
      <Tooltip title="View as map" arrow>
        <ToggleButton value="map">
          <MapIcon />
        </ToggleButton>
      </Tooltip>
    </ToggleButtonGroup>
  );
}

interface MainContentProps {
  mode: Mode;
  configuration: ColumnConfiguration;
  filteredJobs: Job[];
  handleJobSelect: (id: string) => void;
  selectedJob: string | null;
}

function MainContent({
  mode,
  filteredJobs,
  handleJobSelect,
  selectedJob,
  configuration,
}: MainContentProps) {
  const [companies] = useCollectionDataOnce(
    companiesInNetworkQuery(DonePartnerId),
  );

  if (mode === "map") {
    return (
      <OperationsMap
        jobs={filteredJobs}
        selectedJobs={selectedJob ? [selectedJob] : []}
        onJobSelect={handleJobSelect}
        companies={companies}
      />
    );
  }

  return (
    <JobsDataGrid
      jobs={filteredJobs}
      onSelected={handleJobSelect}
      configuration={configuration}
    />
  );
}
