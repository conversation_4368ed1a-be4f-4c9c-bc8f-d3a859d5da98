import { I<PERSON><PERSON>utton, List<PERSON>temIcon, Menu, MenuItem } from "@mui/material";
import { Check, MoreVert } from "@mui/icons-material";
import { Job as JobModel } from "../../models/job/job";
import {
  usePopupState,
  bindTrigger,
  bindMenu,
} from "material-ui-popup-state/hooks";
import { setJobPrerequisitesFulfilled } from "../../models/job/job-provider";
import { PlatformTier } from "../auth/Tiered";

export default function EventSettings({ job }: { job?: JobModel }) {
  const popupState = usePopupState({
    variant: "popover",
    popupId: "job-events-settings",
  });

  if (!job) return null;

  return (
    <PlatformTier>
      <IconButton {...bindTrigger(popupState)}>
        <MoreVert />
      </IconButton>
      <Menu {...bindMenu(popupState)}>
        <TogglePrerequisitesFulfilled
          job={job}
          onClick={() => popupState.close()}
        />
      </Menu>
    </PlatformTier>
  );
}

interface MenuItemProps {
  job: JobModel;
  onClick?: () => void;
}

function TogglePrerequisitesFulfilled({ job, onClick }: MenuItemProps) {
  return (
    <MenuItem
      onClick={() => {
        setJobPrerequisitesFulfilled(
          job.reference.id,
          !job.requiresPrerequisitesFulfilled,
        );
        onClick?.();
      }}
    >
      <Checkmark checked={job.requiresPrerequisitesFulfilled} />
      Require prerequisites fulfilled
    </MenuItem>
  );
}

function Checkmark({ checked }: { checked?: boolean }) {
  if (!checked) return null;
  return (
    <ListItemIcon>
      <Check />
    </ListItemIcon>
  );
}
