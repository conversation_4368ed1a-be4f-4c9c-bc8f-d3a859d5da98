import { DocumentReference } from "firebase/firestore";
import Loading from "../common/Loading";
import { Stack, Typography, Alert, Card, Grid2 } from "@mui/material";
import {
  acceptedReportsQuery,
  JobReport,
} from "../../models/job/report/report";
import { useCollectionData } from "react-firebase-hooks/firestore";
import UserChip from "../user/UserChip";
import { StorageFilePreview } from "../files/StorageFilePreview";
import { isString } from "lodash";

export default function JobReports({
  jobReference,
}: {
  jobReference: DocumentReference;
}) {
  const [jobReports, loading] = useCollectionData(
    acceptedReportsQuery(jobReference.id),
  );

  if (loading) return <Loading />;

  if (!jobReports || jobReports?.length === 0)
    return <Alert severity="info">Job not reported yet</Alert>;

  return (
    <Stack spacing={1}>
      {jobReports.map((report) => (
        <Card
          variant="outlined"
          sx={{ padding: 2, paddingLeft: 1 }}
          key={report.reference.id}
        >
          <Stack direction="row" spacing={1}>
            <StorageFilePreview
              name={`${report.type ?? report.reference.id}.pdf`}
              path={`job-reports-v1/${jobReference.id}/${report.reference.id}/report.pdf`}
            />

            <Stack spacing={0.5}>
              <Typography variant="body2">
                {report.cache?.formSchema?.title ?? report.type ?? "Untitled"}
              </Typography>

              <Typography variant="caption">
                Created by:{" "}
                <UserChip size="small" userId={report.createdBy.id} />
              </Typography>

              <ReportImages report={report} />
            </Stack>
          </Stack>
        </Card>
      ))}
    </Stack>
  );
}

function ReportImages({ report }: { report: JobReport }) {
  const properties = report.cache?.formSchema?.properties;
  const order = report.cache?.uiSchema["ui:order"] ?? [];
  if (!properties) return null;

  const dataUrls = Object.entries(report.data)
    .filter(
      ([key, value]) =>
        properties[key]?.format === "data-url" && isString(value),
    )
    .sort(([a], [b]) => order.indexOf(a) - order.indexOf(b))
    .map(([key, value]) => ({
      key,
      title: properties[key]?.title ?? "Attachment",
      value: value as string, // Type-checked in the filter above
    }));

  return (
    <Grid2 container spacing={1} direction="row">
      {dataUrls.map(({ key, title, value }) => (
        <Grid2 key={key}>
          <StorageFilePreview
            name={title}
            path={value}
            height={80}
            width={65}
          />
        </Grid2>
      ))}
    </Grid2>
  );
}
