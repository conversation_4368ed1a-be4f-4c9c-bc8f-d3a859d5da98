import { useState, useRef, useEffect } from "react";
import { ListItem, List, ListItemText, ListItemIcon } from "@mui/material";
import { useJobs } from "../../hooks/job/jobsHook";
import Loading from "../common/Loading";
import PartnersJob from "./PartnersJob";
import { useAuthUser } from "../auth/AuthProvider";
import Calendar from "../calendar/Calendar";
import { eventForJob } from "../calendar/event-from-job";
import { openJobsQuery } from "../../models/job/job-provider";
import {
  Content,
  DetailsPanel,
  Header,
  MainPanel,
  MenuPanel,
} from "../layout/panels";
import { useCollectionData } from "react-firebase-hooks/firestore";
import { DonePartnerId } from "../../models/other/partner";
import { companiesInNetworkQuery, Company } from "../../models/company/company";
import { CalendarToday, Circle } from "@mui/icons-material";
import { Job as JobModel } from "../../models/job/job";
import Job from "./Job";
import EmptyState from "../empty-state/EmptyState";
import uniqolor from "uniqolor";
import {
  PanelGroup,
  PanelResizeHandle,
  ImperativePanelHandle,
} from "react-resizable-panels";

export default function ScheduledJobsCalendar() {
  const authUser = useAuthUser();
  const partner = authUser.partner;

  const [jobId, setJobId] = useState<string>();
  const [closedJobId, setClosedJobId] = useState<string>();
  const detailsPanelRef = useRef<ImperativePanelHandle>(null);

  useEffect(() => {
    // Collapse the details panel on initial render
    detailsPanelRef.current?.collapse();
  }, []);

  const handleJobSelect = (id: string) => {
    if (jobId === id) {
      setJobId(undefined);
      setClosedJobId(undefined);
      detailsPanelRef.current?.collapse();
    } else {
      setJobId(id);
      setClosedJobId(undefined);
      detailsPanelRef.current?.expand();
    }
  };

  // FIXME: This is a query for all open jobs, it should query within calendar date range
  const jobsQuery = openJobsQuery(partner);

  const { jobs, loading } = useJobs(jobsQuery);

  const jobsWithScheduledStartTime = jobs?.filter(
    (e) => e.scheduledWorkStartTime,
  );

  const events = jobsWithScheduledStartTime?.map(eventForJob);

  // FIXME: This is a query for all companies in the network, it should be memoized together with the jobs
  const [companies] = useCollectionData(companiesInNetworkQuery(DonePartnerId));

  if (loading) return <Loading />;

  return (
    <PanelGroup autoSaveId="scheduled-jobs-calendar" direction="horizontal">
      <MenuPanel>
        <Header title="Installers" />
        <Content>
          <CompanyList companies={companies} jobs={jobs} />
        </Content>
      </MenuPanel>
      <PanelResizeHandle />
      <MainPanel>
        <Calendar
          isReadOnly
          height="95vh"
          selectedEventId={jobId}
          closedEventId={closedJobId}
          events={events}
          onEventClick={handleJobSelect}
        />
      </MainPanel>
      <PanelResizeHandle />
      <DetailsPanel ref={detailsPanelRef} collapsible>
        <Header title="Details" />
        <Content>
          {jobId &&
            (partner ? (
              <PartnersJob jobId={jobId} />
            ) : (
              <Job slim jobId={jobId} />
            ))}
        </Content>
      </DetailsPanel>
    </PanelGroup>
  );
}

function CompanyList({
  companies,
  jobs,
}: {
  companies?: Company[];
  jobs?: JobModel[];
}) {
  // FIXME: This should be memoized together with the jobs once they are fetched by date range
  const jobCompanies = jobs
    ?.filter((job) => job.scheduledWorkStartTime)
    .reduce((acc, job) => {
      if (job.company) return acc.add(job.company.id);
      return acc;
    }, new Set<string>());

  const displayCompanies = companies
    ?.filter((company) => jobCompanies?.has(company.reference.id))
    .sort((a, b) => a.name.localeCompare(b.name));

  if (!displayCompanies?.length)
    return <EmptyState title="No scheduled jobs" icon={<CalendarToday />} />;

  return (
    <List dense>
      {displayCompanies.map((company) => (
        <ListItem key={company.reference.id}>
          <ListItemIcon>
            <Circle
              sx={{
                fontSize: 10,
                color: uniqolor(company.reference.id).color,
              }}
            />
          </ListItemIcon>
          <ListItemText>{company.name}</ListItemText>
        </ListItem>
      ))}
    </List>
  );
}
