import ClearIcon from "@mui/icons-material/Clear";
import SearchIcon from "@mui/icons-material/Search";
import { COMPANY_SERVICES } from "../../config/constants";
import { fetchFixedPriceJobData } from "../../models/job/fixed_price_job";
import { fixedPriceJobOfferSmsMessage } from "../../lib/fixed-price-offer-sms";
import { FixedPriceJobParameters, Job } from "../../models/job/job";
import { getApp } from "@firebase/app";
import { getFunctions, httpsCallable } from "firebase/functions";
import { Send } from "mdi-material-ui";
import { FC, useEffect, useState } from "react";
import {
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Box,
  TextField,
  Checkbox,
  IconButton,
  useTheme,
  Theme,
} from "@mui/material";
import {
  getFirestore,
  collection,
  doc,
  query,
  where,
} from "@firebase/firestore";
import { maxLengthWithEllipsis } from "../../lib/max-length-with-ellipses";
import { useCompanies } from "../../hooks/company/companiesHook";
import { LoadingButton } from "@mui/lab";
import { User } from "../../models/user/user";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";
import { useUser } from "../../hooks/user/use-user";
import {
  AdminAppConfiguration,
  tagToLabeledValue,
} from "../../models/other/admin-config";
import JobLocation from "./JobLocation";
import { useDocumentData } from "react-firebase-hooks/firestore";
import { jobOfferCollection } from "../../models/job/offer/job-offer-provider";
import DialogManager from "../dialogs/DialogManager";
import { companiesSortedByDistanceToWork } from "../../lib/sort-companies-by-distance-to-work";
import { CompanyWithDistance } from "../../models/company/company";
import { JobOffer } from "../../models/job/offer/job-offer";
import OfferCompanyRow, { offerReplyValues } from "./JobOfferCompanyRow";
import { DonePartnerId } from "../../models/other/partner";
import { useAuthUser } from "../auth/AuthProvider";
import CompanyMarkers from "../map/markers/CompanyMarkers";

function getAddress(job: Job, user: User) {
  if (job.cache?.customerLocation?.subLocality) {
    return job.cache.customerLocation.subLocality;
  }

  if (job.cache?.customerLocation?.postalTown) {
    return job.cache.customerLocation.postalTown;
  }

  return user.address ? (user.address?.zip ?? "-") : "-";
}

function toggle(set: any, value: any, toggle: any) {
  const next = new Set([...set]);
  next[toggle ? "add" : "delete"](value);
  return next;
}

interface SendOfferCardProps {
  job: Job;
}

const SendOfferCard: FC<SendOfferCardProps> = ({ job }) => {
  const [user, userLoading] = useUser(job.customer!.id);

  const configSnap = useAdminConfig();

  if (userLoading || configSnap.loading) return null;

  const hasCompany = job.company;
  if (hasCompany) return null;

  return (
    <InnerSendOfferCard job={job} config={configSnap.config!} user={user!} />
  );
};

const InnerSendOfferCard = ({
  job,
  user,
  config,
}: {
  job: Job;
  config: AdminAppConfiguration;
  user: User;
}) => {
  const { partner } = useAuthUser();
  const db = getFirestore();
  const theme = useTheme();

  const [selected, setSelected] = useState(new Set<string>());
  const [sending, setSending] = useState(false);
  const selectedIds = Array.from(selected);

  const [serviceFilter, setServiceFilter] = useState<undefined | string | null>(
    job.services?.[0],
  );
  const [tagsFilter, setTagsFilter] = useState<undefined | string | null>(
    undefined,
  );
  const [search, setSearch] = useState("");
  const [text, setText] = useState("");
  const [jobOffer, loadingJobOffer] = useDocumentData(
    doc(jobOfferCollection(), job.reference.id),
  );

  const [maxRange, setMaxRange] = useState(100);

  const tags = tagToLabeledValue(config?.tags, ["companies"]);

  const requestSearch = (searchValue: string) => {
    setSearch(searchValue);
  };

  useEffect(() => {
    async function fetchData(
      fixedPriceJobs: FixedPriceJobParameters[],
    ): Promise<string> {
      return await fixedPriceJobOfferSmsMessage(
        job,
        await fetchFixedPriceJobData(fixedPriceJobs),
      );
    }

    if (job.fixedPriceJobs && job.fixedPriceJobs.length > 0) {
      fetchData(job.fixedPriceJobs).then((message: string) => {
        setText(message);
      });
    } else {
      setText(`Nytt uppdrag från Done!
${job.tags?.includes("express") ? "EXPRESS - Extra ersättning\n" : ""}
Beskrivning: ${maxLengthWithEllipsis(100, job.description)}${
        job.budget ? `\nBudget: ${job.budget}` : ""
      }${
        job.preferredCallTime
          ? `\nFöredrar samtal: ${job.preferredCallTime}`
          : ""
      }${
        job.preferredStartDate
          ? `\nÖnskad start: ${job.preferredStartDate}`
          : ""
      }
Område: ${!user ? "-" : getAddress(job, user)}

Vänligen svara på uppdraget i appen.
doneservices://app/jobOffer?jobOfferId=${job.reference.id}`);
    }
  }, [job, job.fixedPriceJobs, user]);

  const { companies, loading } = useCompanies(
    query(
      collection(db, "companies"),
      where("status", "in", ["active", "new"]),
      where("memberOf", "array-contains", partner ?? DonePartnerId),
    ),
  );

  if (loadingJobOffer || loading || !companies || !jobOffer) return null;

  const sendSms = async () => {
    setSending(true);
    await httpsCallable(
      getFunctions(getApp(), "europe-west1"),
      "sendJobOffers",
    )({
      jobId: job.reference.id,
      companies: Array.from(selected),
      smsBody: text,
    });

    setSending(false);
  };

  const companiesWithDistance = companiesSortedByDistanceToWork(
    companies,
    jobOffer,
    job,
  );

  const filteredCompanies = companiesWithDistance
    .filter(
      (company) =>
        company.distanceFromWork && company.distanceFromWork < maxRange,
    )
    .filter((company) => {
      if (!serviceFilter && !tagsFilter) return true;
      const services = company.services || [];
      const tags = company.tags || [];

      return (
        (!serviceFilter || services.includes(serviceFilter)) &&
        (!tagsFilter || tags.includes(tagsFilter))
      );
    })
    .filter((company) =>
      company.name.toLowerCase().startsWith(search.toLowerCase()),
    );

  return (
    <DialogManager>
      <Stack spacing={2}>
        <JobLocation height={280} job={job} zoom={8}>
          <CompanyMarkers
            companies={filteredCompanies}
            selected={selectedIds}
            onClick={(id) =>
              setSelected(toggle(selected, id, !selected.has(id)))
            }
          />
        </JobLocation>

        {job.cache?.customerLocation?.coordinates && (
          <Stack spacing={2}>
            <TextField
              variant="outlined"
              onChange={(event: any) => requestSearch(event.target.value)}
              value={search}
              fullWidth
              placeholder="Search companies"
              size="small"
              slotProps={{
                input: {
                  startAdornment: <SearchIcon fontSize="small" />,
                  endAdornment: (
                    <IconButton
                      title="Clear"
                      aria-label="Clear"
                      size="small"
                      style={{
                        visibility: search.length != 0 ? "visible" : "hidden",
                      }}
                      onClick={() => {
                        setSearch("");
                      }}
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  ),
                },
              }}
            />

            <Stack direction="row" spacing={1}>
              <FormControl>
                <InputLabel id="service-select">Services</InputLabel>
                <Select
                  fullWidth
                  style={{ minWidth: 200, marginTop: 8 }}
                  value={serviceFilter}
                  size="small"
                  labelId="service-select"
                  label="Services"
                  variant="outlined"
                  onChange={(value) => setServiceFilter(value.target.value)}
                >
                  {[
                    ...COMPANY_SERVICES,
                    {
                      value: null,
                      label: "All",
                    },
                  ].map((item: any, index: any) => (
                    <MenuItem value={item.value} key={index}>
                      {item.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl>
                <InputLabel id="tag-select">Tags</InputLabel>
                <Select
                  fullWidth
                  style={{ minWidth: 200, marginTop: 8 }}
                  value={tagsFilter}
                  size="small"
                  labelId="tag-select"
                  label="Tags"
                  variant="outlined"
                  onChange={(value) => setTagsFilter(value.target.value)}
                >
                  {[
                    ...tags,
                    {
                      value: null,
                      label: "All",
                    },
                  ].map((tag: any, index: any) => (
                    <MenuItem value={tag.value} key={index}>
                      {tag.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl>
                <InputLabel id="range-select">Max range</InputLabel>
                <Select
                  fullWidth
                  style={{ minWidth: 200, marginTop: 8 }}
                  value={maxRange}
                  size="small"
                  labelId="range-select"
                  label="Max range"
                  variant="outlined"
                  onChange={(value) => setMaxRange(Number(value.target.value))}
                >
                  <MenuItem value={50}>50 km</MenuItem>
                  <MenuItem value={100}>100 km</MenuItem>
                  <MenuItem value={200}>200 km</MenuItem>
                  <MenuItem value={300}>300 km</MenuItem>
                  <MenuItem value={400}>400 km</MenuItem>
                  <MenuItem value={500}>500 km</MenuItem>
                </Select>
              </FormControl>
            </Stack>

            <Box
              width={"100%"}
              sx={{
                maxHeight: "500px",
                overflowX: "auto",
                marginTop: 2,
                marginBottom: 2,
              }}
            >
              {filteredCompanies.map((company) => {
                return (
                  <Stack
                    paddingTop={2}
                    sx={{
                      backgroundColor: offerStatusColor(
                        theme,
                        company,
                        jobOffer,
                      ),
                    }}
                    key={company.reference.id}
                  >
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Checkbox
                        sx={{
                          opacity: (jobOffer?.offeredTo || [])
                            .map((e) => e.id)
                            .includes(company.reference.id)
                            ? 0.1
                            : 1,
                        }}
                        disabled={loading}
                        checked={selected.has(company.reference.id)}
                        onChange={(ev) =>
                          setSelected(
                            toggle(
                              selected,
                              company.reference.id,
                              ev.target.checked,
                            ),
                          )
                        }
                      />

                      <OfferCompanyRow
                        job={job}
                        jobOffer={jobOffer}
                        company={company}
                      />
                    </Stack>

                    <Divider style={{ paddingBottom: 12 }} />
                  </Stack>
                );
              })}
            </Box>

            <TextField
              size="small"
              multiline
              maxRows={5}
              label="Offer sms"
              variant="outlined"
              sx={{ width: "100%" }}
              value={text}
              onChange={(ev) => setText(ev.target.value)}
            />

            <LoadingButton
              fullWidth
              loading={sending}
              style={{ marginTop: 16 }}
              variant="contained"
              color="primary"
              onClick={sendSms}
              disabled={selected.size === 0}
            >
              Send offers to {selected.size} companies
              <Send style={{ marginLeft: "1ex" }} />
            </LoadingButton>
          </Stack>
        )}
      </Stack>
    </DialogManager>
  );
};

export function offerStatusColor(
  theme: Theme,
  company: CompanyWithDistance,
  jobOffer: JobOffer,
) {
  if (jobOffer.replies?.[company.reference.id]) {
    const replyValues = offerReplyValues(
      theme,
      jobOffer.replies[company.reference.id]!,
    );

    return `${replyValues.backgroundColor}20`;
  }

  if (jobOffer.offeredTo?.find((e) => e.id === company.reference.id)) {
    return `${theme.palette.info.main}20`;
  }

  return undefined;
}

export default SendOfferCard;
