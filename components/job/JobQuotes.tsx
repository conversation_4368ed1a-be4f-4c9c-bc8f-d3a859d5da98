import { Stack } from "@mui/material";
import { DocumentReference } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import ManagedDialog from "../dialogs/ManagedDialog";
import { jobQuotes, QuoteCardData } from "../../models/quote/quote";
import { jobDraftQuotes } from "../../models/quote/raw-quote";
import { useDialog } from "../dialogs/DialogManager";
import { QuoteAcceptReasonDialog } from "./dialogs/QuoteAcceptReasonDialog";
import QuoteCard from "./JobQuoteCard";

interface JobQuotesProps {
  jobRef: DocumentReference;
  allowStatusChange?: boolean;
}

export default function JobQuotes({
  jobRef,
  allowStatusChange = false,
}: JobQuotesProps) {
  const { open } = useDialog("accept-reason-dialog");

  const [quotesData, quotesLoading] = useCollection(jobQuotes(jobRef));

  const [rawQuotesData, rawQuotesLoading] = useCollection(
    jobDraftQuotes(jobRef),
  );

  if (quotesLoading || rawQuotesLoading) return null;
  if (!quotesData && !rawQuotesData) return null;

  const quotes: QuoteCardData[] = (
    quotesData?.docs.map((e) => e.data()) ?? []
  ).map(
    (e) =>
      ({
        reference: e.reference,
        jobRef: jobRef,
        fileRef: e.fileRef,
        status: e.status,
        acceptedBy: e.acceptedBy,
        acceptedReason: e.acceptedReason,
        amountAfterTaxDeductions: e.amountAfterTaxDeductions,
      }) as QuoteCardData,
  );

  const rawQuotes: QuoteCardData[] = (
    rawQuotesData?.docs.map((e) => e.data()) ?? []
  ).map(
    (e) =>
      ({
        reference: e.reference,
        jobRef: e.jobRef,
        fileRef: e.fileRef,
        status: e.status,
        calculatedValues: e.calculatedValues,
        partnerRef: e.partnerRef,
      }) as QuoteCardData,
  );

  const allQuotes = [...rawQuotes, ...quotes];

  return allQuotes.length === 0 ? (
    <>No quote created</>
  ) : (
    <Stack spacing={1}>
      <ManagedDialog
        id="accept-reason-dialog"
        component={QuoteAcceptReasonDialog}
      />
      {allQuotes.map((doc) => {
        return (
          <QuoteCard
            key={doc.reference.id}
            quoteCardData={doc}
            allowStatusChange={allowStatusChange}
            onAcceptClicked={() => {
              open({
                quoteReference: doc.reference,
              });
            }}
          />
        );
      })}
    </Stack>
  );
}
