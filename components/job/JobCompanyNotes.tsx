import {
  Alert,
  Avatar,
  Card,
  CardContent,
  LinearProgress,
  List,
  ListItemAvatar,
  ListItemButton,
  ListItemText,
} from "@mui/material";
import AttachmentIcon from "@mui/icons-material/Attachment";
import { collection, DocumentReference } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import { JobNote } from "../../models/job/job-note";

const JobCompanyNotes = ({
  jobRef,
  companyRef,
}: {
  jobRef: DocumentReference;
  companyRef?: DocumentReference;
}) => {
  const [value, loading] = useCollection(collection(jobRef, "notes"));

  if (loading) return <LinearProgress />;
  if (!value?.docs?.length)
    return (
      <Alert severity="info">No company notes were entered by craftsman</Alert>
    );

  const notes = value.docs
    .filter((doc) => (companyRef ? doc.id === companyRef.id : true))
    .map((doc) => ({ id: doc.id, data: doc.data() as JobNote }));

  return (
    <>
      {notes.map(({ id, data }) => {
        const { content, attachments } = data;
        return (
          <Card variant="outlined" key={`note-${id}`}>
            {content ? <CardContent>{content}</CardContent> : null}
            <List>
              {attachments?.map((attachment, index) => (
                <ListItemButton
                  key={`note-${id}-attachment-${index}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  href={attachment?.url ?? ""}
                >
                  <ListItemAvatar>
                    <Avatar src={attachment.image}>
                      <AttachmentIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={attachment.title}
                    secondary={attachment.description}
                  />
                </ListItemButton>
              ))}
            </List>
          </Card>
        );
      })}
    </>
  );
};

export default JobCompanyNotes;
