import {
  ListI<PERSON>,
  ListItemText,
  ListItemButton,
  ListItemButtonProps,
} from "@mui/material";

import React from "react";
import AttachmentImage, { AttachmentType } from "./AttachmentImage";

export interface AttachmentListItemProps
  extends Omit<ListItemButtonProps, "title" | "children"> {
  image?: React.ReactNode;
  title: string | React.ReactNode;
  description?: string;
  secondaryAction?: React.ReactNode;
  children?: React.ReactNode;
  selected?: boolean;
  type?: AttachmentType;
}

export default function AttachmentListItem({
  title,
  description,
  image,
  type,
  secondaryAction,
  children,
  ...buttonProps
}: AttachmentListItemProps) {
  return (
    <ListItem disablePadding secondaryAction={secondaryAction}>
      <ListItemButton {...buttonProps}>
        {image ? image : <AttachmentImage type={type} />}
        {typeof title === "string" ? (
          <ListItemText primary={title} secondary={description} />
        ) : (
          title
        )}
        {children}
      </ListItemButton>
    </ListItem>
  );
}
