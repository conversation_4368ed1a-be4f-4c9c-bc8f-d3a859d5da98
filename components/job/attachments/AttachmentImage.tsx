import {
  ListItemAvatar,
  Avatar,
  Badge,
  BadgeProps,
  SvgIconProps,
} from "@mui/material";
import {
  Password,
  Attachment,
  ShoppingCartOutlined,
} from "@mui/icons-material";
import { styled } from "@mui/material/styles";

const StyledBadge = styled(Badge)<BadgeProps>(({ theme }) => ({
  "& .MuiBadge-badge": {
    right: 0,
    top: "80%",
    border: `2px solid ${theme.palette.background.paper}`,
    padding: "0 4px",
  },
}));

export type AttachmentType = "attachment" | "article" | "installation" | "note";

interface AttachmentIconProps extends SvgIconProps {
  type: AttachmentType;
}

export function AttachmentIcon({ type, ...iconProps }: AttachmentIconProps) {
  switch (type) {
    case "article":
      return <ShoppingCartOutlined {...iconProps} />;
    case "installation":
      return <Password {...iconProps} />;
    default:
      return <Attachment {...iconProps} />;
  }
}

interface AttachmentImageProps {
  image?: string;
  quantity?: number;
  article?: boolean;
  type?: AttachmentType;
}

export default function AttachmentImage({
  image,
  quantity,
  type = "attachment",
}: AttachmentImageProps) {
  return (
    <ListItemAvatar>
      <StyledBadge badgeContent={quantity && `${quantity}x`} color="primary">
        <Avatar src={image}>
          {image ? null : <AttachmentIcon type={type} />}
        </Avatar>
      </StyledBadge>
    </ListItemAvatar>
  );
}
