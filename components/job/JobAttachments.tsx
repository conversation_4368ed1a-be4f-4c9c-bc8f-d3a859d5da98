import { IconButton, List } from "@mui/material";
import { Job } from "../../models/job/job";
import AttachmentListItem from "./attachments/AttachmentListItem";
import AttachmentImage from "./attachments/AttachmentImage";
import { useDialog } from "../dialogs/DialogManager";
import { Delete } from "@mui/icons-material";

interface JobAttachmentsProps {
  job: Job;
  interactive?: boolean;
}

export default function JobAttachments({
  job,
  interactive = false,
}: JobAttachmentsProps) {
  const { open } = useDialog("delete-attachment");

  if (!job.attachments?.length) return null;

  return (
    <List dense>
      {job.attachments?.map((attachment, index) => (
        <AttachmentListItem
          key={`attachment-${index}`}
          title={attachment.title ?? "Attachment"}
          image={
            <AttachmentImage
              image={attachment.image}
              quantity={attachment.quantity}
              type={"attachment"}
            />
          }
          secondaryAction={
            interactive ? (
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => {
                  open({ attachment });
                }}
              >
                <Delete />
              </IconButton>
            ) : null
          }
        />
      ))}
    </List>
  );
}
