import { useState } from "react";
import { Job } from "../../models/job/job";
import ComponentContainer from "../common/ComponentContainer";
import JobForms from "./JobForms";
import { FormControlLabel } from "@mui/material";
import { Switch } from "@mui/material";
import Internal from "../auth/Internal";

export default function JobFormsContainer({ job }: { job: Job }) {
  const [showAll, setShowAll] = useState(false);
  return (
    <ComponentContainer
      title="Report forms"
      actions={
        <Internal>
          <FormControlLabel
            checked={showAll}
            onChange={() => {
              setShowAll(!showAll);
            }}
            control={<Switch size="small" />}
            label="Show all"
          />
        </Internal>
      }
    >
      <JobForms job={job} showAll={showAll} />
    </ComponentContainer>
  );
}
