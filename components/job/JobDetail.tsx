import FixedPriceInfoCard from "../common/FixedPriceInfoCard";
import TooltipImagePreview from "../common/TooltipImagePreview";
import { Job } from "../../models/job/job";
import {
  Card,
  LinearProgress,
  Typography,
  Chip,
  Stack,
  Box,
  Divider,
  Button,
  styled,
} from "@mui/material";
import { useDiscountCode } from "../../hooks/other/discountCodeHook";
import { formatDate } from "../../lib/format-time-relative";
import { ChangeEvent, FC } from "react";
import JobLocation from "./JobLocation";
import { Add } from "@mui/icons-material";
import { uploadJobImage } from "./CreateJobDialog";
import { arrayUnion, updateDoc } from "@firebase/firestore";
import { useAuthUser } from "../auth/AuthProvider";
import InfoItemRow from "../common/InfoItemRow";
import UserChip from "../user/UserChip";

const Input = styled("input")({
  display: "none",
});

type Props = { job: Job; hideOffer?: boolean };

const JobDetail: FC<Props> = ({ job, hideOffer = false }) => {
  const authUser = useAuthUser();
  const { discountCode, loading } = useDiscountCode(job.discountCode);

  if (loading) <LinearProgress />;

  if (!job.cache) return null;

  const handleAddNewImage = async (event: ChangeEvent<any>) => {
    if (event?.target?.files && event?.target?.files?.length > 0) {
      const newImageList = [...event?.target?.files];

      for (const [index, image] of newImageList.entries()) {
        const imageReferences = await uploadJobImage(
          image as File,
          job.reference,
          (job.images?.length ?? 0) + index - 1, // -1 for fix index of the image
        );

        await updateDoc(job.reference, { images: arrayUnion(imageReferences) });
      }
    }
  };

  return (
    <Stack spacing={1}>
      <Stack overflow="scroll" spacing={1} direction="column">
        <Stack direction="row" spacing={1}>
          {job.tags?.map((e) => (
            <Chip sx={{ borderRadius: 1 }} size="small" key={e} label={e} />
          ))}
          {job.operationTags?.map((e) => (
            <Chip sx={{ borderRadius: 1 }} size="small" key={e} label={e} />
          ))}
        </Stack>
      </Stack>

      {job.createdBy && (
        <Card variant="outlined" style={{ padding: 12 }}>
          <Stack direction="row" spacing={0.5}>
            <Typography color="text.secondary" variant="body2">
              Created by:
            </Typography>
            <Box flex={1} />
            <UserChip size="small" userId={job.createdBy.id} />
          </Stack>
        </Card>
      )}

      <Card variant="outlined" style={{ padding: 12 }}>
        <InfoItemRow title="Description" value={job.description} />
      </Card>

      <Card variant="outlined" style={{ padding: 12 }}>
        <Stack spacing={2}>
          <Stack alignItems="center" direction="row">
            <Typography style={{ fontWeight: "bold" }}>Images</Typography>
            <Box flex={1} />
            {!authUser.partner && (
              <label style={{ marginRight: 8 }} htmlFor="icon-button-file">
                <Input
                  accept="image/*"
                  id="icon-button-file"
                  type="file"
                  multiple={true}
                  onChange={handleAddNewImage}
                />
                <Button size="small" startIcon={<Add />} component="span">
                  Add image
                </Button>
              </label>
            )}
          </Stack>

          {job.images?.length ? (
            <Box display="flex" overflow="scroll">
              {job.images.map((image: any) => (
                <TooltipImagePreview marginTop={0} key="any" image={image} />
              ))}
            </Box>
          ) : (
            <Typography variant="caption">No images uploaded yet.</Typography>
          )}
        </Stack>
      </Card>

      <Card variant="outlined" style={{ padding: 12 }}>
        <Stack divider={<Divider flexItem />} spacing={1}>
          <InfoItemRow
            title="Create time"
            value={formatDate(job.createTime?.toDate())}
          />

          <InfoItemRow title="Reference number" value={job.referenceNumber} />

          {job.externalReference && (
            <InfoItemRow
              title="External reference"
              value={job.externalReference}
            />
          )}

          <InfoItemRow
            title="Job location"
            value={job.cache?.customerLocation?.addressLine}
          />

          {job.utm_source && (
            <InfoItemRow title="Utm source" value={job.utm_source} />
          )}

          {job.utm_content && (
            <InfoItemRow title="Utm content" value={job.utm_content} />
          )}

          {job.utm_campaign && (
            <InfoItemRow title="Utm campaign" value={job.utm_campaign} />
          )}

          {job.webReferrer && (
            <InfoItemRow title="Utm campaign" value={job.webReferrer} />
          )}

          {job.preferredCallTime && (
            <InfoItemRow
              noCopy
              title="Preferred call time"
              value={job.preferredCallTime}
            />
          )}

          {job.preferredStartDate && (
            <InfoItemRow
              noCopy
              title="Preferred start date"
              value={job.preferredStartDate}
            />
          )}
        </Stack>
      </Card>

      {job.fixedPriceJobs &&
        job.fixedPriceJobs.map((e) => {
          return (
            <div key={e.id}>
              <FixedPriceInfoCard
                quantity={e.quantity}
                fixedPriceId={e.id}
                products={e.products}
              />
            </div>
          );
        })}

      {discountCode && (
        <Card
          variant="outlined"
          style={{
            padding: 12,
          }}
        >
          <Stack spacing={0.5}>
            <Typography variant="body2">
              <strong>Discount code</strong>
            </Typography>

            <Typography variant="body2">{`Active: ${discountCode.active}`}</Typography>

            <Typography variant="body2">{`Value: ${
              discountCode.value || "-"
            } / Percentage: ${discountCode.percentage || "-"}`}</Typography>

            <Typography variant="body2">{`Description: ${
              discountCode.description || "-"
            }`}</Typography>
          </Stack>
        </Card>
      )}

      {!hideOffer && job.company && <JobLocation job={job} />}
    </Stack>
  );
};

export default JobDetail;
