import Chat from "../chat/Chat";
import UploadButton from "../common/UploadButton";
import { collection, getFirestore } from "@firebase/firestore";
import { Job } from "../../models/job/job";
import Internal from "../auth/Internal";

const JobChat = ({
  job,
  jobId,
  readOnly = false,
}: {
  jobId: string;
  job: Job;
  height?: string;
  readOnly?: boolean;
}) => {
  const db = getFirestore();
  const messagesRef = collection(db, `jobs/${jobId}/messages`);

  return (
    <Chat job={job} messagesRef={messagesRef} readOnly={readOnly}>
      <Internal>
        {!readOnly && <UploadButton messagesRef={messagesRef} jobId={jobId} />}
      </Internal>
    </Chat>
  );
};

export default JobChat;
