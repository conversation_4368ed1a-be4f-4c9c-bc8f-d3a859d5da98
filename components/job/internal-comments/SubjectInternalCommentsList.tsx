import { DocumentReference } from "firebase/firestore";
import { useInfiniteScrollingInternalComments } from "../../../hooks/inbox/internalCommentsHook";
import { InternalComment } from "../../../models/inbox/internal-comment";
import { useInfiniteScroll } from "../../../lib/hooks/use-infinite-scroll";
import { Stack } from "@mui/material";
import InternalCommentItem from "../../internal-comments/InternalCommentItem";
import ScrollIntoView from "../../common/ScrollIntoView";
import { useCallback, useLayoutEffect, useMemo, useRef } from "react";
import Loading from "../../common/Loading";

interface SubjectInternalCommentsListProps {
  subject: DocumentReference;
  scrollToBottom?: boolean;
  onDelete?: (comment: InternalComment) => void;
}

export default function SubjectInternalCommentsList({
  subject,
  scrollToBottom = false,
  onDelete,
}: SubjectInternalCommentsListProps) {
  const pageSize = 30;
  const { internalComments, loading, hasMorePages, loadMore } =
    useInfiniteScrollingInternalComments(subject, pageSize);

  const scrollableRootRef = useRef<HTMLDivElement | null>(null);
  const lastScrollDistanceToBottomRef = useRef<number>(0);

  // Reverse the items so latest comments appear at the bottom
  const reversedComments = useMemo(
    () => [...(internalComments || [])].reverse(),
    [internalComments],
  );

  // Infinite scroll upwards (when approaching top)
  const handleScroll = useInfiniteScroll({
    hasMorePages,
    loading,
    onLoadMore: loadMore,
    direction: "up",
  });

  useLayoutEffect(() => {
    const scrollableRoot = scrollableRootRef.current;
    if (!scrollableRoot) return;

    // On first page, scroll to bottom. Otherwise, maintain scroll position.
    const isFirstPage = reversedComments.length <= pageSize;
    if (isFirstPage) {
      scrollableRoot.scrollTop = scrollableRoot.scrollHeight;
    } else {
      // Keep scroll position when new pages are loaded
      scrollableRoot.scrollTop =
        scrollableRoot.scrollHeight - lastScrollDistanceToBottomRef.current;
    }
  }, [subject, reversedComments, pageSize]);

  const handleRootScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const rootNode = event.currentTarget;
      if (rootNode) {
        const scrollDistanceToBottom =
          rootNode.scrollHeight - rootNode.scrollTop;
        lastScrollDistanceToBottomRef.current = scrollDistanceToBottom;
      }

      // Also call the infinite scroll handler
      handleScroll(event);
    },
    [handleScroll],
  );

  if (!reversedComments?.length) return null;

  return (
    <Stack
      ref={scrollableRootRef}
      spacing={1}
      padding={2}
      onScroll={handleRootScroll}
      sx={{
        overflowY: "auto",
        height: "100%",
      }}
    >
      {loading && <Loading />}
      {reversedComments.map((comment) => (
        <InternalCommentItem
          key={comment.reference?.id ?? comment.note}
          comment={comment}
          onDeleteClick={() => onDelete?.(comment)}
        />
      ))}
      {scrollToBottom ? <ScrollIntoView /> : null}
    </Stack>
  );
}
