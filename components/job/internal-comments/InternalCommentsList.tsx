import { List } from "@mui/material";
import { Issue } from "../../../models/inbox/issue";
import { InternalCommentParticipant } from "../../../models/inbox/internal-comment";
import EmptyState from "../../empty-state/EmptyState";
import { useAuthUser } from "../../auth/AuthProvider";
import { isDefined } from "../../../lib/filter-undefined";
import TaskListItem from "../../common/tasks/TaskListItem";
import Markdown from "../../common/Markdown";

interface InternalCommentsListProps {
  participant: InternalCommentParticipant;
  issues: Issue[];
  onSelected: (issue: Issue) => void;
  selected?: Issue;
}

/**
 * Displays a scrollable list of internal comments for a job.
 *
 * Features:
 * - Empty state handling
 * - Selection tracking
 * - Read/unread status indication
 *
 * @component
 * @param {Object} props
 * @param {InternalCommentParticipant} props.participant - The participant viewing the comments
 * @param {Issue[]} props.issues - Array of issues/comments to display
 * @param {(issue: Issue) => void} props.onSelected - Callback when an issue is selected
 * @param {Issue} [props.selected] - Currently selected issue (optional)
 */
export default function InternalCommentsList({
  participant,
  issues,
  selected,
  onSelected,
}: InternalCommentsListProps) {
  if (issues?.length === 0) return <EmptyState />;

  return (
    <List dense disablePadding>
      {issues?.map((issue) => (
        <InternalCommentsListItem
          participant={participant}
          key={issue.reference.id}
          issue={issue}
          selected={selected?.reference.id === issue.reference.id}
          onClick={() => {
            onSelected(issue);
          }}
        />
      ))}
    </List>
  );
}

interface InternalCommentsListItemProps {
  participant: InternalCommentParticipant;
  issue: Issue;
  selected?: boolean;
  onClick: () => void;
}

function InternalCommentsListItem({
  participant,
  issue,
  onClick,
  selected,
}: InternalCommentsListItemProps) {
  const { participants, viewedBy, subject } = issue;
  const { lastUpdateTime, cache } = participants[participant] ?? {};

  const auth = useAuthUser();
  const read = viewedBy.some(
    (viewer) => viewer.id === auth.userDoc?.reference.id,
  );

  return (
    <TaskListItem
      onClick={() => onClick()}
      selected={selected}
      dimmed={read}
      title={cache?.customerName ?? cache?.companyName ?? "Missing customer"}
      secondaryTitle={[cache?.jobExternalReference, cache?.jobReferenceNumber]
        .filter(isDefined)
        .join(" - ")}
      text={<Markdown>{cache?.lastMessage ?? "..."}</Markdown>}
      userRef={cache?.lastAuthor}
      timestamp={lastUpdateTime}
      type={subject.parent.id}
    />
  );
}
