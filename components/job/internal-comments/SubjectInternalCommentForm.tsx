import {
  serverTimestamp,
  addDoc,
  collection,
  DocumentReference,
  doc,
  getFirestore,
} from "firebase/firestore";
import removeUndefined from "../../../lib/remove-undefined";
import {
  InternalCommentFile,
  InternalCommentInput,
  InternalCommentParticipant,
} from "../../../models/inbox/internal-comment";
import InternalNoteForm, {
  InternalCommentFormProps,
} from "../../internal-comments/InternalCommentForm";
import { useAuthUser } from "../../auth/AuthProvider";
import { useAdmin } from "../../../hooks/other/adminConfigHook";
import { marked } from "marked";
import _ from "lodash";
import { useMemo } from "react";
import FilePreview from "../../upload/FilePreview";
import { Stack } from "@mui/material";
import { useFileQueue } from "../../upload/FileQueueContext";
import { isUploadedFile } from "../../upload/UploadableFile";
import { FileUploadButton } from "../../upload/FileUploadButton";
import { usePartner } from "../../../hooks/partner/use-partner";
import { PartnerTier } from "../../../models/other/partner";

interface SubjectInternalCommentFormProps
  extends Omit<InternalCommentFormProps, "onSubmit" | "possibleRecipients"> {
  subject: DocumentReference;
}

export default function SubjectInternalCommentForm({
  subject,
  ...formProps
}: SubjectInternalCommentFormProps) {
  const authContext = useAuthUser();
  const { admin } = useAdmin();
  const [partner] = usePartner(authContext.partner);
  const isPartner = Boolean(authContext.partner);
  const [queuedFiles, { remove, upload, clear }] = useFileQueue();

  // FIXME: Rewrite with support for checking subject ownership. That way network-merchant comms could occur on non-job subjects.
  const possibleRecipients = useMemo<InternalCommentParticipant[]>(() => {
    const type = subject.parent.id;

    if (isPartner) {
      const tier = partner?.tier ?? PartnerTier.Standard;

      // Platform partners can also communicate with users.
      if (tier >= PartnerTier.Platform && type === "users") {
        return ["user"];
      }

      // Partners can always communicate with done on jobs for now until FIXME above is resolved.
      if (type === "jobs") {
        return ["done"];
      }

      return [];
    }

    switch (type) {
      case "jobs":
        return ["partner"];
      case "companies":
        return [];
      case "users":
        return ["user"];
      default:
        return [];
    }
  }, [isPartner, subject.parent.id, partner?.tier]);

  return (
    <InternalNoteForm
      {...formProps}
      possibleRecipients={possibleRecipients}
      mentionable={[
        ...(partner?.users ? Object.values(partner?.users) : []),
        ...(admin?.superAdminUsers
          ? Object.values(admin?.superAdminUsers)
          : []),
      ]}
      tools={<FileUploadButton />}
      onSubmit={async (values) => {
        if (!values.note && !queuedFiles.length) {
          return;
        }

        const uploadedFiles = await upload(
          `internal-comments-v1/${subject.path}`,
        );

        const files = uploadedFiles
          .filter(isUploadedFile)
          .map<InternalCommentFile>((file) => ({
            name: file.file.name,
            path: file.ref.fullPath,
          }));

        const internalComment: InternalCommentInput = {
          createTime: serverTimestamp(),
          createdBy: authContext.userDoc!.reference,
          createdByType: isPartner ? "partner" : "done",
          mentioned: usersReferencedInMarkdown(values.note),
          ...values,
          visibleFor: {
            ...values.visibleFor,
            partner: isPartner || (values.visibleFor?.partner ?? false),
            done: !isPartner || (values.visibleFor?.done ?? false),
          },
          files,
        };

        await addDoc(
          collection(subject, "internalComments"),
          removeUndefined(internalComment),
        );

        clear();
      }}
    >
      <Stack direction="row" spacing={1}>
        {queuedFiles.map(({ uuid, file }) => (
          <FilePreview
            key={uuid}
            file={file}
            onDelete={() => {
              remove({ uuid });
            }}
          />
        ))}
      </Stack>
    </InternalNoteForm>
  );
}

function usersReferencedInMarkdown(markdownText: string): DocumentReference[] {
  if (!markdownText) {
    return [];
  }

  const links: { text: string; url: string }[] = [];

  marked.use({
    renderer: {
      link({ href, title, text }) {
        if (href.startsWith("/users/")) {
          links.push({ text, url: href });
        }
        return `<a href="${href}"${title ? ` title="${title}"` : ""}>${text}</a>`;
      },
    },
  });

  marked.parse(markdownText);

  return _.uniqBy(
    links.map(({ url }) =>
      doc(getFirestore(), `users/${url.replace("/users/", "")}`),
    ),
    "id",
  );
}
