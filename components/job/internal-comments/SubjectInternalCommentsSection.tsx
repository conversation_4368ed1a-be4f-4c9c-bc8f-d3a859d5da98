import { Divider, Grid2, <PERSON>arProgress, Typography } from "@mui/material";
import SubjectInternalCommentForm from "./SubjectInternalCommentForm";
import SubjectInternalCommentsList from "./SubjectInternalCommentsList";
import { useCollection } from "react-firebase-hooks/firestore";
import { issueForSubjectQuery } from "../../../models/inbox/provider";
import IssueToolbar from "../../issues/IssueToolbar";
import { useAuthUser } from "../../auth/AuthProvider";
import { DocumentReference } from "firebase/firestore";
import { useDialog } from "../../dialogs/DialogManager";
import { InternalComment } from "../../../models/inbox/internal-comment";

interface SubjectInternalCommentsSectionProps {
  subject: DocumentReference;
  autoFocus?: boolean;
  tools?: React.ReactNode;
}

export default function SubjectInternalCommentsSection({
  subject,
  autoFocus = false,
  tools,
}: SubjectInternalCommentsSectionProps) {
  const { open } = useDialog("confirm-delete-comment");

  const handleDelete = (comment: InternalComment) => {
    open({ comment });
  };

  return (
    <Grid2 container spacing={2} justifyContent={"flex-end"}>
      {tools ? <Grid2 size={12}>{tools}</Grid2> : null}
      <SubjectIssueToolbar subject={subject} />
      <Grid2 size={12}>
        <Divider />
      </Grid2>
      <Grid2 size={12} sx={{ maxHeight: 400, overflow: "auto" }}>
        <SubjectInternalCommentsList
          subject={subject}
          onDelete={handleDelete}
        />
      </Grid2>
      <Grid2 size={12}>
        <SubjectInternalCommentForm autoFocus={autoFocus} subject={subject} />
      </Grid2>
    </Grid2>
  );
}

function SubjectIssueToolbar({ subject }: { subject: DocumentReference }) {
  const authUser = useAuthUser();
  const [result, loading] = useCollection(
    issueForSubjectQuery(subject, authUser.partnerDoc?.reference),
  );
  const [issueDoc] = result?.docs ?? [];
  const issue = issueDoc?.data();

  const participant = authUser.partner ? "partner" : "done";

  if (loading) return <LinearProgress />;

  if (!issue)
    return (
      <Grid2 size={"auto"} alignContent={"center"}>
        <Typography variant="body2" color={"textSecondary"}>
          Add a note to create a ticket
        </Typography>
      </Grid2>
    );

  return (
    <Grid2 size={"auto"}>
      <IssueToolbar issue={issue} participant={participant} />
    </Grid2>
  );
}
