import { Alert } from "@mui/material";

import { query, where, orderBy, Timestamp } from "firebase/firestore";

import { subHours } from "date-fns";
import JobsTable from "./table/JobsTable";
import { DonePartnerReference } from "../../models/other/partner";
import { jobsCollection } from "../../models/job/job-provider";

export default function HomeContainer() {
  const jobCollection = query(
    jobsCollection(),
    where("status", "in", ["open", "inbox"]),
    where("network", "==", DonePartnerReference()),
    where("createTime", ">", Timestamp.fromDate(subHours(Date.now(), 48))),
    orderBy("createTime", "desc"),
  );

  return (
    <>
      <Alert style={{ position: "fixed", right: 32, top: -4 }} severity="info">
        Jobs in 48 hours
      </Alert>
      <JobsTable initCollection={jobCollection} />
    </>
  );
}
