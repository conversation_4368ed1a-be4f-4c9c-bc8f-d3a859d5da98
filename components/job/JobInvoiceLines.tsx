import {
  DocumentReference,
  Timestamp,
  collection,
  getFirestore,
  query,
  updateDoc,
  where,
} from "firebase/firestore";

import Loading from "../common/Loading";
import {
  <PERSON><PERSON>,
  Button,
  Chip,
  IconButton,
  Tooltip,
  styled,
} from "@mui/material";
import ArrowCircleUpIcon from "@mui/icons-material/ArrowCircleUp";
import ArrowCircleDownIcon from "@mui/icons-material/ArrowCircleDown";
import BalanceIcon from "@mui/icons-material/Balance";
import { useParsedInvoiceLines } from "../../hooks/other/parsedInvoceLinesHook";
import { DataGrid, GridColDef, GridValidRowModel } from "@mui/x-data-grid";
import { Box, Stack, darken, lighten } from "@mui/system";
import formatAmount from "../../lib/format-amount";
import { useAuthUser } from "../auth/AuthProvider";
import React, { useMemo } from "react";
import { ParsedInvoiceLine } from "../../models/invoice/parsed_invoice_line";
import ToolsStack from "../layout/ToolsStack";
import { Edit } from "@mui/icons-material";
import { jobReference as getJobReference } from "../../models/job/job-provider";

const getBackgroundColor = (color: string, mode: string) =>
  mode === "dark" ? darken(color, 0.7) : lighten(color, 0.9);

const getHoverBackgroundColor = (color: string, mode: string) =>
  mode === "dark" ? darken(color, 0.6) : lighten(color, 0.6);

const getSelectedBackgroundColor = (color: string, mode: string) =>
  mode === "dark" ? darken(color, 0.5) : lighten(color, 0.5);

const getSelectedHoverBackgroundColor = (color: string, mode: string) =>
  mode === "dark" ? darken(color, 0.4) : lighten(color, 0.4);

const JobInvoiceLinesDataGrid = styled(DataGrid)(({ theme }) => ({
  "& .super-app-theme--customerInvoice": {
    backgroundColor: getBackgroundColor(
      theme.palette.success.light,
      theme.palette.mode,
    ),
    "&:hover": {
      backgroundColor: getHoverBackgroundColor(
        theme.palette.success.light,
        theme.palette.mode,
      ),
    },
    "&.Mui-selected": {
      backgroundColor: getSelectedBackgroundColor(
        theme.palette.success.light,
        theme.palette.mode,
      ),
      "&:hover": {
        backgroundColor: getSelectedHoverBackgroundColor(
          theme.palette.success.light,
          theme.palette.mode,
        ),
      },
    },
  },
  "& .super-app-theme--selfInvoice": {
    backgroundColor: getBackgroundColor(
      theme.palette.error.light,
      theme.palette.mode,
    ),
    "&:hover": {
      backgroundColor: getHoverBackgroundColor(
        theme.palette.error.light,
        theme.palette.mode,
      ),
    },
    "&.Mui-selected": {
      backgroundColor: getSelectedBackgroundColor(
        theme.palette.error.light,
        theme.palette.mode,
      ),
      "&:hover": {
        backgroundColor: getSelectedHoverBackgroundColor(
          theme.palette.error.light,
          theme.palette.mode,
        ),
      },
    },
  },
}));

interface JobInvoiceLinesProps {
  jobReference: DocumentReference;
}

const jobInvoiceLinesColumns = (
  isPartner?: boolean,
): GridColDef<GridValidRowModel>[] => {
  return [
    { field: "line", headerName: "Line", width: 300 },
    {
      field: "count",
      headerName: "Count",
      width: 20,
    },
    {
      field: "perAmount",
      headerName: "Per amount",
      width: 100,
      type: "number",
    },
    {
      field: "totalAmount",
      headerName: "Total amount",
      width: 110,
      type: "number",
    },
    {
      field: "type",
      headerName: "Type",
      width: 160,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Chip
            variant="outlined"
            size="small"
            label={
              row.type === "selfInvoice" ? "Self invoice" : "Customer invoice"
            }
          />
        );
      },
    },
    { field: "invoiceNumber", headerName: "Invoice number", width: 130 },
    ...(!isPartner
      ? [
          {
            field: "invoiceBillectaId",
            headerName: "Billecta id",
            width: 140,
            renderCell: ({ row }: { row: any }) => {
              return (
                <BillectaLink
                  isSelfInvoice={row.type === "selfInvoice"}
                  invoiceId={row.invoiceBillectaId}
                >
                  {row.invoiceBillectaId}
                </BillectaLink>
              );
            },
          },
        ]
      : []),
    {
      field: "invoiceDate",
      headerName: "Invoice date",
      width: 120,
      type: "date",
    },
    {
      field: "createTime",
      headerName: "Reported",
      width: 120,
      type: "date",
    },
    ...(!isPartner
      ? [
          {
            field: "edit_job",
            headerName: "Change job",
            sortable: false,
            width: 100,
            renderCell: (params: any) => {
              const onClick = async () => {
                let newJobId = window.prompt("Enter new Job ID");
                newJobId = newJobId ? newJobId.match(/[^/]*$/)![0] : null;

                if (newJobId) {
                  await updateDoc(params.row.reference, {
                    job: getJobReference(newJobId),
                  });
                  alert("Job ID updated successfully");
                }
              };

              return (
                <IconButton onClick={onClick}>
                  <Edit />
                </IconButton>
              );
            },
          },
        ]
      : []),
  ];
};

function BillectaLink({
  children,
  invoiceId,
  isSelfInvoice,
}: {
  children: React.ReactNode;
  invoiceId: string;
  isSelfInvoice: boolean;
}) {
  return (
    <Button
      onClick={() => {
        window.open(
          `https://app.billecta.com/${
            isSelfInvoice ? "sfi" : "in"
          }/View/${invoiceId}`,
          "_blank",
        );
      }}
      style={{ textDecoration: "underlined" }}
      size="small"
      color={isSelfInvoice ? "error" : "success"}
    >
      {children}
    </Button>
  );
}

function BalanceSummary({
  parsedInvoiceLines,
}: {
  parsedInvoiceLines: ParsedInvoiceLine[];
}) {
  const { incoming, outgoing, balance } = useMemo(() => {
    const outgoing =
      parsedInvoiceLines
        .filter((l) => l.type === "selfInvoice")
        .reduce((f, l) => f + (l.totalAmount ?? 0), 0) / 100;

    const incoming =
      parsedInvoiceLines
        .filter((l) => l.type === "customerInvoice")
        .reduce((f, l) => f + (l.totalAmount ?? 0), 0) / 100;

    const balance = incoming - outgoing;

    return { incoming, outgoing, balance };
  }, [parsedInvoiceLines]);

  return (
    <ToolsStack>
      <Tooltip title="Balance" arrow>
        <Chip
          variant="outlined"
          label={formatAmount(balance)}
          icon={<BalanceIcon />}
          color={balance <= 0 ? "error" : "success"}
        />
      </Tooltip>
      <Tooltip title="Amount Invoiced" arrow>
        <Chip
          variant="outlined"
          label={formatAmount(incoming)}
          icon={<ArrowCircleDownIcon />}
          color={incoming <= 0 ? "error" : "default"}
        />
      </Tooltip>
      <Tooltip title="Amount Paid" arrow>
        <Chip
          variant="outlined"
          label={formatAmount(outgoing * -1)}
          icon={<ArrowCircleUpIcon />}
          color={outgoing === 0 ? "warning" : "default"}
        />
      </Tooltip>
    </ToolsStack>
  );
}

export default function JobInvoiceLines({
  jobReference,
}: JobInvoiceLinesProps) {
  const authUser = useAuthUser();
  const isPartner = Boolean(authUser.partner);

  const _collection = collection(
    getFirestore(),
    "admin/billecta/parsedInvoiceLines/",
  );

  const invoiceLinesQuery = isPartner
    ? query(
        _collection,
        where("job", "==", jobReference),
        where("partner", "==", authUser.partnerDoc!.reference),
        where("type", "==", "customerInvoice"),
      )
    : query(_collection, where("job", "==", jobReference));

  const { parsedInvoiceLines, loading } =
    useParsedInvoiceLines(invoiceLinesQuery);

  if (loading) return <Loading />;

  if (!parsedInvoiceLines?.length)
    return <Alert severity="info">Job not invoiced yet</Alert>;

  return (
    <Stack spacing={1}>
      {isPartner ? null : (
        <BalanceSummary parsedInvoiceLines={parsedInvoiceLines} />
      )}

      <Box style={{ height: 400, width: "100%" }}>
        <JobInvoiceLinesDataGrid
          density="compact"
          getRowHeight={() => "auto"}
          hideFooter
          disableRowSelectionOnClick
          disableColumnMenu
          rows={parsedInvoiceLines.map((e) => ({
            id: e.reference.id,
            ...e,
            invoiceDate: e.invoiceDate.toDate(),
            createTime: (e.createTime as Timestamp).toDate(),
            totalAmount: formatAmount((e.totalAmount ?? 0) / 100),
            perAmount: formatAmount((e.perAmount ?? 0) / 100),
          }))}
          getRowClassName={(params) => `super-app-theme--${params.row.type}`}
          columns={jobInvoiceLinesColumns(isPartner)}
        />
      </Box>
    </Stack>
  );
}
