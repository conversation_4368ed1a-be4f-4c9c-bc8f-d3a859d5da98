import { Select } from "@mui/material";
import { Job } from "../../models/job/job";
import { JOB_STATUSES } from "../../config/constants";
import { updateDoc } from "@firebase/firestore";
import { useAuthUser } from "../auth/AuthProvider";
import removeUndefined from "../../lib/remove-undefined";
import { deleteField } from "firebase/firestore";
import ManagedDialog from "../dialogs/ManagedDialog";
import { useDialog } from "../dialogs/DialogManager";
import JobCloseReasonDialog from "./dialogs/JobCloseReasonDialog";

export default function JobStatusSelect({ job }: { job: Job }) {
  const authUser = useAuthUser();

  const { open } = useDialog("close-reason");

  const setStatus = async (status?: string) => {
    if (status === "closed" || status === "trashed") {
      open({ status: status });
    } else {
      if (
        !window.confirm(
          `Confirm status change!  from: '${job.status}' to: '${status}'`,
        )
      )
        return;

      await updateDoc(
        job.reference,
        removeUndefined({
          status,
          closedBy: deleteField(),
          closeReason: deleteField(),
        }),
      );
    }
  };

  return (
    <>
      <ManagedDialog
        id="close-reason"
        component={JobCloseReasonDialog}
        jobReference={job.reference}
        closedBy={authUser.userDoc?.reference}
      />
      <Select
        variant="standard"
        native
        style={{ marginRight: "4px", marginBottom: "4px", width: 90 }}
        size="small"
        value={job.status}
        onChange={async (event) =>
          await setStatus(event.target.value as string)
        }
      >
        {JOB_STATUSES.map((status) => (
          <option value={status.value} key={status.label}>
            {status.label}
          </option>
        ))}
      </Select>
    </>
  );
}
