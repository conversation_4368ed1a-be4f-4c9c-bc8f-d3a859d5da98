import { List, Alert, I<PERSON><PERSON><PERSON>on } from "@mui/material";
import { Job } from "../../models/job/job";
import AttachmentListItem from "./attachments/AttachmentListItem";
import AttachmentImage from "./attachments/AttachmentImage";
import { isCustomArticle } from "../../models/job/job-article";
import { useDialog } from "../dialogs/DialogManager";
import { Delete } from "@mui/icons-material";
import {
  AmountInfo,
  isCentAmount,
  isPercentageAmount,
} from "../../models/article-groups/article";
import { formatPercentage } from "../../lib/format-percentage";
import { formatCentAmount } from "../common/table/utils";

interface JobArticlesProps {
  job: Job;
  interactive?: boolean;
}

export default function JobArticles({
  job,
  interactive = false,
}: JobArticlesProps) {
  const { open } = useDialog("delete-attachment");

  if (!job.articles?.length)
    return (
      <Alert severity="error">No articles have been added to this job</Alert>
    );

  return (
    <List dense>
      {job.articles?.map((article, index) => (
        <AttachmentListItem
          key={`article-${index}`}
          title={article.title ?? "Attachment"}
          description={
            isCustomArticle(article)
              ? `Customer ${formattedAmount(
                  article.prices?.customerPrice,
                )} / Installer ${formattedAmount(
                  article.prices?.craftsmanProceeds,
                )} / Partner ${formattedAmount(article.prices?.partnerPrice)}`
              : ""
          }
          image={
            <AttachmentImage quantity={article.quantity} type={"article"} />
          }
          secondaryAction={
            interactive ? (
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => {
                  open({ article });
                }}
              >
                <Delete />
              </IconButton>
            ) : null
          }
        />
      ))}
    </List>
  );
}

export function formattedAmount(value: AmountInfo | undefined) {
  if (isPercentageAmount(value) && value.amount.percentage !== undefined) {
    return formatPercentage(value.amount.percentage) ?? "-";
  }

  if (isCentAmount(value) && value.amount.value !== undefined) {
    return formatCentAmount(value.amount.value) ?? "-";
  }

  return "-";
}
