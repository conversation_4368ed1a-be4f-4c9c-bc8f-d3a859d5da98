import CreateJobImagePreview from "../CreateJobImagePreview";
import FormDialog from "../../dialogs/FormDialog";
import GoogleLocationSearch from "../../search/GoogleLocationSearch";
import removeUndefined from "../../../lib/remove-undefined";
import Router from "next/router";
import TextField from "../../form-fields/TextField";
import {
  AddPhotoAlternate,
  AddShoppingCart,
  Delete,
} from "@mui/icons-material";
import { useState } from "react";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { FormSpy } from "react-final-form";
import { generateDoneAddressFromAddressComponents } from "../CreateJobDialog";
import { getApps } from "firebase/app";
import { useGoogleGeocoding } from "../../../hooks/useGoogleGeocoding";
import { getStorage, ref, uploadBytes } from "firebase/storage";
import { OrderInput, OrderTypes } from "../../../models/job/order";
import { uniqueId } from "lodash";
import { UploadJobData } from "../../../models/job/job";
import { useAuthUser } from "../../auth/AuthProvider";
import { useJobs } from "../../../hooks/job/jobsHook";
import { UserInput } from "../../../models/user/user";
import {
  Stack,
  Card,
  Alert,
  Typography,
  AlertTitle,
  Button,
  IconButton,
  Divider,
} from "@mui/material";
import {
  DocumentReference,
  Timestamp,
  collection,
  doc,
  getFirestore,
  query,
  serverTimestamp,
  setDoc,
  where,
} from "firebase/firestore";

import {
  ACCEPTED_IMAGE_FILE_TYPES,
  TEMP_BUCKET,
} from "../../../config/constants";
import AutocompleteLabeledValueField from "../../form-fields/AutocompleteField";
import AddAttachmentDialog, {
  AddAttachmentDialogProps,
} from "./AddAttachmentDialog";
import ManagedDialog from "../../dialogs/ManagedDialog";
import { useDialog } from "../../dialogs/DialogManager";
import { isCustomArticle, JobArticle } from "../../../models/job/job-article";
import AttachmentImage from "../attachments/AttachmentImage";
import AttachmentListItem from "../attachments/AttachmentListItem";
import { formattedAmount } from "../JobArticles";
import {
  Partner,
  partnerRef,
  PartnerTier,
} from "../../../models/other/partner";
import { StandardTier } from "../../auth/Tiered";
import PhoneNumberField from "../../form-fields/PhoneNumberField";
import EmailField from "../../form-fields/EmailField";
import FileDropArea from "../../upload/FileDropArea";
import FileButton from "../../common/FileButton";
import MapBox from "../../map/MapBox";

export interface UploadImage {
  file: File;
  description?: string;
}

export interface UploadImages {
  [key: string]: UploadImage;
}

export default function CreateOrderDialog({ isOpen, close }: DialogProps) {
  const authUser = useAuthUser();
  const [images, setImages] = useState<UploadImages>({});
  const [geoCode, setGeoCode] = useState<google.maps.GeocoderResult>();
  const [articles, setArticles] = useState<JobArticle[]>([]);

  const canCreateOrder = authUser.partnerDoc?.orderCreationEnabled ?? true;

  return (
    <FormDialog
      allowSubmit={canCreateOrder}
      positiveButtonText={"Create order"}
      maxWidth="md"
      close={() => {
        setImages({});
        setArticles([]);
        setGeoCode(undefined);
        close();
      }}
      initialValues={{
        typeTag: "type:installation",
      }}
      onSubmit={async (data: any) => {
        if (!canCreateOrder) return;

        const address = geoCode
          ? generateDoneAddressFromAddressComponents(geoCode)
          : undefined;

        if (!address) {
          throw new Error("No address");
        }

        const orderDoc = doc(
          collection(
            getFirestore(),
            `${authUser.partnerDoc!.reference.path}/orders`,
          ),
        );

        const imageDescriptions = data.images;

        const imagesWithDescription = Object.keys(images).map((key) => {
          return {
            file: images[key].file,
            description: imageDescriptions?.[key]?.description,
          };
        });

        const uploadImages = await uploadImagesToTempBucket(
          imagesWithDescription,
          orderDoc,
        );

        const order: OrderInput = {
          data: {
            customerData: removeUndefined<UserInput>({
              firstName: data.firstName?.trim(),
              lastName: data.lastName?.trim(),
              phoneNumber: data.phoneNumber?.trim(),
              email: data.email?.trim(),
              address,
            }),
            jobData: removeUndefined<UploadJobData>({
              externalReference: data.externalReference?.trim(),
              description: data.description?.trim(),
              noteForCraftsman: data.installerNote?.trim(),
              images: uploadImages,
              articles: articles?.length ? articles : undefined,
            }),
          },
          createTime: serverTimestamp(),
          createdBy: authUser.userDoc!.reference,
          partner: authUser.partnerDoc!.reference,
          status: "pending",
          typeTag: data.typeTag,
        };

        await setDoc(orderDoc, removeUndefined<OrderInput>(order));

        setArticles([]);
        setImages({});
      }}
      title={"Create order"}
      isOpen={isOpen}
    >
      {canCreateOrder ? (
        <OrderFormFields
          partner={authUser.partnerDoc}
          images={images}
          setImages={setImages}
          geoCode={geoCode}
          setGeoCode={setGeoCode}
          articles={articles}
          setArticles={setArticles}
        />
      ) : (
        <Alert severity="error">
          Creating orders is disabled for your account. Please contact support.
        </Alert>
      )}
    </FormDialog>
  );
}

interface CreateOrderDialogProps {
  partner?: Partner;
  images: UploadImages;
  setImages: (images: UploadImages) => void;
  geoCode: google.maps.GeocoderResult | undefined;
  setGeoCode: (geoCode: google.maps.GeocoderResult | undefined) => void;
  articles: JobArticle[];
  setArticles: (article: JobArticle[]) => void;
}

function OrderFormFields({
  partner,
  images,
  setImages,
  geoCode,
  setGeoCode,
  articles,
  setArticles,
}: CreateOrderDialogProps) {
  const [wrongTypeError, setWrongTypeError] = useState(false);
  const { geocodeByPlaceId } = useGoogleGeocoding();

  const { open } = useDialog<AddAttachmentDialogProps>("add-article");
  const handleAddNewImage = (files: FileList | null) => {
    if (!files) return;

    const imageFiles: File[] = Array.from(files);

    const wrongTypedImages = imageFiles.filter((imageFile) => {
      const fileType = imageFile.name?.split(".")?.pop();

      if (!fileType) return true;

      return !ACCEPTED_IMAGE_FILE_TYPES.includes(fileType ?? "");
    });

    const correctTypedImages = imageFiles.filter((imageFile) => {
      const fileType = imageFile.name?.split(".")?.pop();
      if (!fileType) return false;

      return ACCEPTED_IMAGE_FILE_TYPES.includes(fileType ?? "");
    });

    const newImages: UploadImages = (correctTypedImages as File[]).reduce(
      (a, v) => ({ ...(a as any), [uniqueId("image")]: { file: v as File } }),
      {},
    );

    const newImageList: UploadImages = {
      ...images,
      ...newImages,
    };

    setImages(newImageList);
    setWrongTypeError(wrongTypedImages && wrongTypedImages.length > 0);
  };

  const removeImageFromList = (uid: string) => {
    const tempImages = { ...images };
    delete tempImages[uid];
    setImages(tempImages);
    setWrongTypeError(false);
  };
  const [noZipCodeError, setNoZipCodeError] = useState(false);

  if (!partner) return null;

  return (
    <>
      <ManagedDialog id="add-article" component={AddAttachmentDialog} />
      <FileDropArea
        onDrop={(files) => {
          handleAddNewImage(files);
        }}
      >
        <Typography variant="subtitle2">Customer</Typography>
        <Card variant="outlined">
          <Stack spacing={2} padding={2}>
            <Stack direction="row" spacing={2}>
              <TextField
                autoFocus
                name="firstName"
                type="text"
                label="First name"
                required
              />
              <TextField
                name="lastName"
                type="text"
                label="Last name"
                required
              />
            </Stack>
            <Stack direction="row" spacing={2}>
              <PhoneNumberField
                name="phoneNumber"
                label="Phone number"
                required
              />
              <EmailField name="email" label="E-mail" required />
            </Stack>
          </Stack>
        </Card>

        <Stack spacing={2}>
          <Typography variant="subtitle2" paddingTop={2}>
            Installation
          </Typography>

          <Stack spacing={2}>
            <GoogleLocationSearch
              placeholder="Address"
              onLocationChanged={async (location) => {
                const geoCodeResults = await geocodeByPlaceId(
                  location.place_id,
                );
                setGeoCode(geoCodeResults[0]);
                const postalCode = geoCodeResults[0].address_components.filter(
                  (e: google.maps.GeocoderAddressComponent) =>
                    e.types.includes("postal_code"),
                )[0]?.long_name;

                if (!postalCode) {
                  setNoZipCodeError(true);
                } else {
                  setNoZipCodeError(false);
                }
              }}
            />

            {noZipCodeError ? (
              <Alert style={{ marginTop: 8 }} severity="error">
                No zip code in address
              </Alert>
            ) : null}

            {geoCode && !noZipCodeError ? (
              <MapBox center={geoCode.geometry.location} />
            ) : null}
          </Stack>

          <StandardTier exact>
            <AutocompleteLabeledValueField
              name="typeTag"
              label={"Order type"}
              items={OrderTypes}
            />
          </StandardTier>

          <TextField
            name="externalReference"
            type="text"
            label="Order number"
          />

          <FormSpy>
            {(props) => (
              <>
                {props.values?.externalReference && (
                  <JobFromOrderDetail
                    externalReference={props.values?.externalReference}
                    partner={partner.reference.id}
                  />
                )}
              </>
            )}
          </FormSpy>

          <TextField
            name="description"
            type="text"
            multiline
            label="Order details"
            required
          />
          <Divider />
          <Button
            variant="outlined"
            startIcon={<AddShoppingCart />}
            onClick={() => {
              open({
                settings: {
                  allowCustomArticle: partner.tier === PartnerTier.Platform,
                  partner: partner.reference.id,
                  services: partner.apiSettings.defaultServices,
                  activeAt: Timestamp.fromDate(new Date()),
                },
                onSubmit: (line) => {
                  if (!line) return;

                  setArticles([...(articles ?? []), line]);
                },
              });
            }}
          >
            Add articles
          </Button>

          {articles?.map((article, index) => {
            return (
              <AttachmentListItem
                key={`article-${index}`}
                title={article.title ?? "Attachment"}
                description={
                  isCustomArticle(article)
                    ? `Customer ${formattedAmount(
                        article.prices?.customerPrice,
                      )} / Installer ${formattedAmount(
                        article.prices?.craftsmanProceeds,
                      )} / Partner ${formattedAmount(
                        article.prices?.partnerPrice,
                      )}`
                    : ""
                }
                image={
                  <AttachmentImage
                    quantity={article.quantity}
                    type={"article"}
                  />
                }
                secondaryAction={
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={() => {
                      setArticles(
                        articles.filter(
                          (_, articleToRemoveIndex) =>
                            articleToRemoveIndex !== index,
                        ),
                      );
                    }}
                  >
                    <Delete />
                  </IconButton>
                }
              />
            );
          })}
          <Divider />
          <FileButton
            variant="outlined"
            accept="image/*"
            startIcon={<AddPhotoAlternate />}
            onChange={(files) => handleAddNewImage(files)}
          >
            Add images
          </FileButton>

          {wrongTypeError ? (
            <Alert severity="error">
              Wrong format! Only jpeg and png allowed to upload.
            </Alert>
          ) : (
            <></>
          )}

          {Object.keys(images).length ? (
            <Stack spacing={2} marginTop={2}>
              {Object.entries(images).map(([uid, image]) => (
                <CreateJobImagePreview
                  key={uid}
                  uid={uid}
                  image={image}
                  onImageDelete={function (uid: string): void {
                    removeImageFromList(uid);
                  }}
                />
              ))}
            </Stack>
          ) : (
            <></>
          )}
          <Typography variant="subtitle2">Internal</Typography>
          <TextField
            name="installerNote"
            type="text"
            multiline
            label="Note for installer"
          />
        </Stack>
      </FileDropArea>
    </>
  );
}

function JobFromOrderDetail({
  externalReference,
  partner,
}: {
  externalReference: string;
  partner: string;
}) {
  const { jobs, loading } = useJobs(
    query(
      collection(getFirestore(), "jobs"),
      where("externalReference", "==", externalReference),
      where("merchant", "==", partnerRef(partner)),
    ),
  );

  if (loading || !jobs?.length) return null;

  const [job] = jobs;

  return (
    <Alert
      severity="warning"
      action={
        <Button
          color="inherit"
          size="small"
          onClick={() => {
            Router.push(`/jobs/job/?id=${job.reference.id}`);
          }}
        >
          Go to order
        </Button>
      }
    >
      <AlertTitle>Order already sent</AlertTitle>
      Order created for customer {job.cache?.customerName}
    </Alert>
  );
}

async function uploadImagesToTempBucket(
  images: UploadImage[],
  orderReference: DocumentReference,
) {
  const rawImages = [];

  const storageRef = getStorage(getApps()[0], TEMP_BUCKET);

  for (let index = 0; index < images.length; index++) {
    const originalTempReference = ref(
      storageRef,
      `order-images-v1/${orderReference.id}/original-${index}.jpg`,
    );

    const original = (
      await uploadBytes(originalTempReference, images[index].file)
    ).ref.fullPath;

    rawImages.push(
      removeUndefined({
        url: original,
        description: images[index]?.description,
      }),
    );
  }

  return rawImages;
}
