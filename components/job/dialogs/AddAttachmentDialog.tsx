import { DialogProps } from "../../dialogs/default-dialog-props";
import NumberField from "../../form-fields/NumberField";
import ArticlesFormFields from "../../article-groups/forms/ArticleFormFields";
import { ToggleButton, ToggleButtonGroup } from "@mui/material";
import FormDialog from "../../dialogs/FormDialog";
import { useState } from "react";
import ArticlesSelectField, {
  PriceListFilter,
} from "../../form-fields/ArticlesSelectField";
import {
  JobArticle,
  JobCustomArticle,
  isCustomArticle,
} from "../../../models/job/job-article";
import { assertNever } from "../../../lib/assert-never";
import lodash from "lodash";
import { Article } from "../../../models/article-groups/article";
import { Promisable } from "type-fest";

const { omit } = lodash;

type Mode = "pricelist" | "custom";
const defaultMode: Mode = "pricelist";

interface Settings extends PriceListFilter {
  allowCustomArticle?: boolean;
}

export interface AddAttachmentDialogProps {
  settings?: Settings;

  /**
   * Returns the new article. If not provided, the job will be uped immediately.
   * @param article
   * @returns
   */
  onSubmit?: (
    article: JobArticle,
    priceListArticle?: Article,
  ) => Promisable<void>;
}

type FullAddAttachmentDialogProps = AddAttachmentDialogProps & DialogProps;

interface PricelistFormValues {
  article: Article;
  quantity: number;
}

type FormValues = PricelistFormValues | JobCustomArticle;

export default function AddAttachmentDialog({
  settings,
  isOpen,
  close,
  onSubmit,
}: FullAddAttachmentDialogProps) {
  const { partner, activeAt, services, allowCustomArticle, groupId } =
    settings ?? {};
  const [mode, setMode] = useState<Mode>(defaultMode);
  return (
    <FormDialog
      isOpen={isOpen}
      close={() => {
        close();
        setMode(defaultMode);
      }}
      title={"Add article"}
      initialValues={{ quantity: 1 }}
      onSubmit={async (values: FormValues) => {
        let jobArticle: JobArticle;
        let priceListArticle: Article | undefined;

        switch (mode) {
          case "pricelist":
            priceListArticle = (values as PricelistFormValues)?.article;
            if (!priceListArticle) {
              throw new Error("Invalid format for pricelist articles");
            }
            jobArticle = {
              article: priceListArticle.reference,
              title: priceListArticle.title,
              quantity: values.quantity,
            };
            break;
          case "custom":
            if (!isCustomArticle(values)) {
              throw new Error("Invalid format for custom articles");
            }
            const newArticle = omit(values, "article"); // Make sure that any reference don't slip through.
            jobArticle = {
              ...newArticle,
              prices: {
                ...newArticle.prices,
                craftsmanProceeds: newArticle.prices?.craftsmanProceeds || {},
              },
            };
            break;
          default:
            assertNever(mode);
        }

        await onSubmit?.(jobArticle, priceListArticle);
      }}
    >
      <ToggleButtonGroup
        fullWidth
        color="primary"
        exclusive
        value={mode}
        onChange={(_, value) => {
          if (!value) return;
          setMode(value);
        }}
        size="small"
      >
        <ToggleButton value="pricelist">From price list</ToggleButton>
        <ToggleButton value="custom" disabled={!allowCustomArticle}>
          Custom
        </ToggleButton>
      </ToggleButtonGroup>
      <NumberField name="quantity" label="Quantity" min={1} required />
      {mode === "pricelist" ? (
        <ArticlesSelectField
          partner={partner}
          activeAt={activeAt}
          services={services}
          groupId={groupId}
        />
      ) : null}
      {mode === "custom" ? <ArticlesFormFields /> : null}
    </FormDialog>
  );
}
