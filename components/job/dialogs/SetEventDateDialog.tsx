import { DateTimePicker } from "mui-rff";
import { Job, JobEvents } from "../../../models/job/job";
import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { startCase } from "lodash";
import { Timestamp, updateDoc } from "firebase/firestore";

interface SetEventDateDialogProps extends DialogProps {
  job: Job;
  event?: keyof JobEvents;
  defaultToNow?: boolean;
}

export default function SetEventDateDialog({
  isOpen,
  close,
  job,
  event,
  defaultToNow = false,
}: SetEventDateDialogProps) {
  const initialDate = event ? job.events?.[event]?.toDate() : undefined;

  return (
    <FormDialog
      title={`Set ${startCase(event ?? "event")}`}
      isOpen={isOpen}
      close={close}
      initialValues={{
        eventDate: initialDate
          ? initialDate
          : defaultToNow
            ? new Date()
            : undefined,
      }}
      onSubmit={async ({ eventDate }) => {
        if (!event) return;
        await updateDoc(job.reference, {
          [`events.${event}`]: Timestamp.fromDate(eventDate),
        });
      }}
    >
      <DateTimePicker name="eventDate" required autoFocus />
    </FormDialog>
  );
}
