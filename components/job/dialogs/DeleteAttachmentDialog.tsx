import { DialogContentText } from "@mui/material";
import { arrayRemove, updateDoc } from "firebase/firestore";
import { Attachment, Job } from "../../../models/job/job";
import ConfirmDialog, { ConfirmActions } from "../../dialogs/ConfirmDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import removeUndefined from "../../../lib/remove-undefined";
import { JobArticle } from "../../../models/job/job-article";

interface DeleteAttachmmentDialogProps extends DialogProps {
  job: Job;
  attachment?: Attachment;
  article?: JobArticle;
}

export default function DeleteAttachmmentDialog({
  job,
  attachment,
  article,
  isOpen,
  close,
}: DeleteAttachmmentDialogProps) {
  return (
    <ConfirmDialog
      title={`Delete ${article ? "article" : "attachment"}?`}
      isOpen={isOpen}
      onClose={close}
      actions={[
        ConfirmActions.cancel,
        {
          title: "Delete",
          onClick: async () => {
            await updateDoc(
              job.reference,
              removeUndefined({
                attachments: attachment ? arrayRemove(attachment) : undefined,
                articles: article ? arrayRemove(article) : undefined,
              }),
            );
          },
        },
      ]}
    >
      <DialogContentText>
        Are you sure you want to delete this
        {article ? " article" : " attachment"}?
      </DialogContentText>
    </ConfirmDialog>
  );
}
