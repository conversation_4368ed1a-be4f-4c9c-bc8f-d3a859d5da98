import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { Invoice } from "../../../models/invoice/invoice";
import { DocumentData, deleteField, updateDoc } from "firebase/firestore";
import EditDeductionInfoContent from "./RotInfoDialogContent";
import TextField from "../../form-fields/TextField";
import { Divider } from "@mui/material";
import { Switches } from "mui-rff";
import Condition from "../../form-fields/Condition";
import { flattenInput } from "../../../lib/flatten-input";

interface EditInvoiceRotInfoDialogProps extends DialogProps {
  invoice?: Invoice;
  usesDeduction?: boolean;
}

export default function EditInvoiceRotInfoDialog({
  invoice,
  usesDeduction,
  isOpen,
  close,
}: EditInvoiceRotInfoDialogProps) {
  return (
    <FormDialog
      isOpen={isOpen}
      close={close}
      title={"Edit customer info"}
      initialValues={{
        ...(invoice?.customerSensitiveData ?? {}),
        usesDeduction,
      }}
      onSubmit={async (changes) => {
        const { usesDeduction, rotInfo, address } = changes;

        // Need to make sure to remove unused fields.
        const cleanedRotInfo = {
          personIdNumber: rotInfo.personIdNumber,
          propertyType: rotInfo.propertyType,
          propertyDesignation: rotInfo.propertyDesignation ?? deleteField(),
          housingCooperative: rotInfo.housingCooperative ?? deleteField(),
          apartmentNumber: rotInfo.apartmentNumber ?? deleteField(),
        };

        // Flatten the input.
        const customerSensitiveData: DocumentData = flattenInput({
          customerSensitiveData: {
            rotInfo: usesDeduction ? cleanedRotInfo : deleteField(),
            address,
          },
        });

        // Update invoice cache
        await updateDoc(invoice?.reference!, customerSensitiveData);
      }}
    >
      <Switches
        name="usesDeduction"
        data={{ value: true, label: "Uses deduction" }}
      />
      <Condition when="usesDeduction" is={true}>
        <EditDeductionInfoContent />
      </Condition>
      <Divider />
      <TextField required name="address.streetAddress" label="Street" />
      <TextField required name="address.postalCode" label="Postal code" />
      <TextField required name="address.city" label="City" />
    </FormDialog>
  );
}
