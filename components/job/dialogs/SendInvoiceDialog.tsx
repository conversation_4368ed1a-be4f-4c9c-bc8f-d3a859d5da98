import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import ConfirmDialog, { ConfirmActions } from "../../dialogs/ConfirmDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { Invoice } from "../../../models/invoice/invoice";
import { Alert, AlertTitle } from "@mui/material";
import removeUndefined from "../../../lib/remove-undefined";

interface SendInvoiceDialogProps extends DialogProps {
  invoice?: Invoice;
  variant?: "customer" | "self-invoice";
}

export default function SendInvoiceDialog({
  invoice,
  isOpen,
  close,
  variant,
}: SendInvoiceDialogProps) {
  let title = "Send invoice";
  let description = "";
  let variants: string[] | undefined;

  if (invoice?.basisStatus) {
    title = "Submit basis";
  } else {
    switch (variant) {
      case "customer":
        title = "Send invoice";
        description = "This will send the invoice to the customer";
        variants = ["customer"];
        break;
      case "self-invoice":
        title = "Send self invoice";
        description = "This will send the self invoice to the craftsman";
        variants = ["self-invoice"];
        break;
    }
  }

  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={close}
      title={title}
      description={description}
      actions={[
        ConfirmActions.cancel,
        {
          title: "Send",
          onClick: async () => {
            if (!invoice) throw new Error("Invoice is not defined");
            await httpsCallable(
              getFunctions(getApp(), "europe-west1"),
              "sendInvoice",
            )(
              removeUndefined({
                invoiceId: invoice.reference.id,
                variants,
              }),
            );
          },
        },
      ]}
    >
      {invoice?.basisStatus === "draft" ? (
        <Alert severity="warning">
          <AlertTitle>Unvalidated basis</AlertTitle>
          Submitting the basis will not perform basic validation. It is
          recommended to submit the basis in the app.
        </Alert>
      ) : undefined}
    </ConfirmDialog>
  );
}
