import { <PERSON><PERSON>, Al<PERSON>Title, DialogContentText } from "@mui/material";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { Job } from "../../../models/job/job";
import ConfirmDialog, { ConfirmActions } from "../../dialogs/ConfirmDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";

interface GenerateInvoiceDialogProps extends DialogProps {
  job: Job;
}

function JobNotDoneWarning({ show }: { show: boolean }) {
  if (!show) return null;
  return (
    <Alert severity="warning">
      <AlertTitle>Job is not done</AlertTitle>
      Invoices might be also be generated automatically once the job is marked
      as done. Are you sure you want to generate an invoice?
    </Alert>
  );
}

export default function GenerateInvoiceDialog({
  job,
  isOpen,
  close,
}: GenerateInvoiceDialogProps) {
  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={close}
      title="Generate Invoice"
      actions={[
        ConfirmActions.cancel,
        {
          title: "Generate",
          onClick: async () => {
            await httpsCallable(
              getFunctions(getApp(), "europe-west1"),
              "createAutomaticInvoice",
            )({
              jobId: job.reference.id,
            });
          },
        },
      ]}
    >
      <DialogContentText>
        This will generate an invoiced based on partner settings and accepted
        quotes.
        <br />
        Note that the invoice may be automatically sent to the customer
        depending on partner settings.
      </DialogContentText>
      <JobNotDoneWarning show={!Boolean(job.events?.jobDone)} />
    </ConfirmDialog>
  );
}
