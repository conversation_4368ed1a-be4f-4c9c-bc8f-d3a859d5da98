import { DocumentReference, updateDoc } from "firebase/firestore";
import { TextField } from "mui-rff";
import removeUndefined from "../../../lib/remove-undefined";
import { useAuthUser } from "../../auth/AuthProvider";
import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";

interface QuoteAcceptReasonDialogProps extends DialogProps {
  quoteReference?: DocumentReference;
}

export function QuoteAcceptReasonDialog({
  quoteReference,
  isOpen,
  close,
}: QuoteAcceptReasonDialogProps) {
  const authUser = useAuthUser();

  return (
    <FormDialog
      positiveButtonText={"Accept"}
      close={() => {
        close();
      }}
      description="Quote will be accepted on behalf of the customer"
      title={`Accept quote`}
      isOpen={isOpen}
      initialValues={{ acceptedReason: "" }}
      onSubmit={async (data) => {
        quoteReference &&
          (await updateDoc(
            quoteReference,
            removeUndefined({
              status: "accepted",
              acceptedBy: authUser.userDoc?.reference,
              acceptedReason: data.acceptedReason,
            }),
          ));
      }}
    >
      <TextField
        name="acceptedReason"
        type="text"
        multiline
        label="Accept reason"
        required
      />
    </FormDialog>
  );
}
