import { DocumentReference, updateDoc } from "firebase/firestore";
import removeUndefined from "../../../lib/remove-undefined";
import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import TextField from "../../form-fields/TextField";

interface JobCloseReasonDialogProps extends DialogProps {
  jobReference: DocumentReference;
  status?: string;
  closedBy?: DocumentReference;
}

export default function JobCloseReasonDialog({
  jobReference,
  status,
  closedBy,
  isOpen,
  close,
}: JobCloseReasonDialogProps) {
  return (
    <FormDialog
      positiveButtonText={`Change status to ${status}`}
      close={() => {
        close();
      }}
      title={`Add ${status} reason`}
      isOpen={isOpen}
      initialValues={{ closeReason: "" }}
      onSubmit={async (data) => {
        await updateDoc(
          jobReference,
          removeUndefined({
            status,
            closedBy: closedBy,
            closeReason: data.closeReason,
          }),
        );
      }}
    >
      <TextField
        name="closeReason"
        type="text"
        multiline
        label="Close reason"
        required
      />
    </FormDialog>
  );
}
