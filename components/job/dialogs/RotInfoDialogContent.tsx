import { TextField } from "mui-rff";
import LabeledValueField from "../../form-fields/LabeledValueField";
import Condition from "../../form-fields/Condition";

export default function EditDeductionInfoContent() {
  return (
    <>
      <TextField
        required
        name="rotInfo.personIdNumber"
        label="National id number"
      />
      <LabeledValueField
        name="rotInfo.propertyType"
        label="Property type"
        items={[
          { label: "Apartment owner", value: "apartmentOwner" },
          { label: "House", value: "house" },
          {
            label:
              "Rented apartment (Tax deduction is not available for rented apartments)",
            value: "rentedApartment",
          },
        ]}
      />
      <Condition when="rotInfo.propertyType" is="house">
        <TextField
          required
          name="rotInfo.propertyDesignation"
          label="Property designation"
        />
      </Condition>

      <Condition when="rotInfo.propertyType" is="rentedApartment">
        <p>Tax deduction is not available for rented apartments</p>
      </Condition>

      <Condition when="rotInfo.propertyType" is="apartmentOwner">
        <TextField
          required
          name="rotInfo.apartmentNumber"
          label="Apartment number"
        />
        <TextField
          required
          name="rotInfo.housingCooperative"
          label="Brf org. number"
        />
      </Condition>
    </>
  );
}
