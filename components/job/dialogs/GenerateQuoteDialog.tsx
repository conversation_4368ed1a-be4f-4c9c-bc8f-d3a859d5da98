import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { Job } from "../../../models/job/job";
import ConfirmDialog, { ConfirmActions } from "../../dialogs/ConfirmDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";

interface GenerateQuoteDialogProps extends DialogProps {
  job: Job;
}

export default function GenerateQuoteDialog({
  job,
  isOpen,
  close,
}: GenerateQuoteDialogProps) {
  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={close}
      title="Generate Quote"
      description="This will generate a quote based on partner settings. Note that the quote may be automatically sent to and/or approved by the customer depending on partner settings."
      actions={[
        ConfirmActions.cancel,
        {
          title: "Generate",
          onClick: async () => {
            await httpsCallable(
              getFunctions(getApp(), "europe-west1"),
              "createAutomaticQuote",
            )({
              jobId: job.reference.id,
            });
          },
        },
      ]}
    />
  );
}
