import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { deleteField, updateDoc } from "firebase/firestore";
import { SensitiveUserData } from "../../../models/user/sensitive-user-data";
import EditDeductionInfoContent from "./RotInfoDialogContent";

interface EditUserRotInfoDialogProps extends DialogProps {
  userSensitiveData?: SensitiveUserData;
}

export default function EditUserRotInfoDialog({
  userSensitiveData,
  isOpen,
  close,
}: EditUserRotInfoDialogProps) {
  return (
    <FormDialog
      isOpen={isOpen}
      close={close}
      title={"Edit ROT info"}
      initialValues={userSensitiveData}
      onSubmit={async (changes) => {
        const customerSensitiveData = {
          "rotInfo.personIdNumber": changes.rotInfo.personIdNumber,
          "rotInfo.propertyType": changes.rotInfo.propertyType,
          "rotInfo.propertyDesignation":
            changes.rotInfo.propertyDesignation ?? deleteField(),
          "rotInfo.housingCooperative":
            changes.rotInfo.housingCooperative ?? deleteField(),
          "rotInfo.apartmentNumber":
            changes.rotInfo.apartmentNumber ?? deleteField(),
        };

        // Update job cache
        await updateDoc(userSensitiveData?.reference!, customerSensitiveData);

        await updateDoc(userSensitiveData?.reference.parent.parent!, {
          hasEnteredROTInformation: true,
        });
      }}
    >
      <EditDeductionInfoContent />
    </FormDialog>
  );
}
