import { LinearProgress, Typography } from "@mui/material";
import { updateDoc } from "@firebase/firestore";
import { COMPANY_SERVICES, JOB_TAGS } from "../../../config/constants";
import { useJob } from "../../../hooks/job/jobHook";
import { useAdminConfig } from "../../../hooks/other/adminConfigHook";
import PartnerSelectField from "../../form-fields/PartnerSelectField";
import { tagToLabeledValue } from "../../../models/other/admin-config";
import FormDialog from "../../dialogs/FormDialog";
import AutocompleteLabeledValueField from "../../form-fields/AutocompleteField";
import { DialogProps } from "../../dialogs/default-dialog-props";
import TextField from "../../form-fields/TextField";
import { deleteField } from "firebase/firestore";
import Internal from "../../auth/Internal";

interface JobEditDialogProps extends DialogProps {
  jobId: string;
}

export default function JobEditDialog({
  isOpen,
  close,
  jobId,
}: JobEditDialogProps) {
  const { job, loading: jobLoading } = useJob(jobId);
  const { config, loading: configLoading } = useAdminConfig();

  if (configLoading || jobLoading) return <LinearProgress />;

  if (!config) return <Typography>No config</Typography>;
  if (!job) return <Typography>Job not found</Typography>;

  const operationTagOptions = tagToLabeledValue(config?.tags, ["jobs"]);

  return (
    <FormDialog
      isOpen={isOpen}
      title="Edit job"
      close={close}
      onSubmit={async (updateJob) => {
        await updateDoc(job.reference, {
          ...updateJob,
          partner: updateJob.partner ?? deleteField(),
        });
      }}
      initialValues={{ ...job }}
    >
      <TextField name="description" type="text" label="Description" multiline />

      <PartnerSelectField />

      <TextField
        name="externalReference"
        type="text"
        label="External reference"
      />

      <Internal>
        <AutocompleteLabeledValueField
          name="services"
          multiple
          label={"Services"}
          items={COMPANY_SERVICES}
        />

        <AutocompleteLabeledValueField
          name="operationTags"
          multiple
          label={"Operation tags"}
          items={operationTagOptions}
        />

        <AutocompleteLabeledValueField
          name="tags"
          multiple
          label={"Tags"}
          items={JOB_TAGS}
        />

        <TextField name="budget" type="text" label="Budget" />

        {!job.fixedPriceJobs?.length ? (
          <TextField
            label="Preferred call time"
            name="preferredCallTime"
            type="text"
          />
        ) : (
          <></>
        )}

        <TextField
          name="preferredStartDate"
          type="text"
          label="Preferred start date"
        />

        <TextField name="discountCode" type="text" label="Discount code" />
      </Internal>
    </FormDialog>
  );
}
