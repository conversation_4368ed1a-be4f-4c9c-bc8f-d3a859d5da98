import EditUserForm from "./EditUserForm";
import { Button, Dialog } from "@mui/material";
import { useState } from "react";
import { User } from "../../models/user/user";
import EditIcon from "@mui/icons-material/Edit";

const EditUserButton = ({ user }: { user: User }) => {
  const [open, setOpen] = useState(false);

  const onOpen = () => setOpen(true);
  const onClose = () => setOpen(false);
  return (
    <>
      <Dialog open={open} onClose={onClose}>
        <EditUserForm user={user} onClose={onClose} />
      </Dialog>
      <Button
        startIcon={<EditIcon />}
        size="small"
        variant="outlined"
        color="primary"
        onClick={onOpen}
      >
        Details
      </Button>
    </>
  );
};

export default EditUserButton;
