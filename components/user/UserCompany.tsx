import CompanyName from "../company/CompanyName";
import { DocumentReference } from "@firebase/firestore";
import { useDocumentData } from "react-firebase-hooks/firestore";

const UserCompany = ({ userRef }: { userRef: DocumentReference }) => {
  const [value, loading] = useDocumentData(userRef);

  if (loading) return null;
  if (!value) return <p>No user</p>;

  const companyRef = value.company;

  if (companyRef) return <CompanyName companyRef={companyRef} />;

  return null;
};

export default UserCompany;
