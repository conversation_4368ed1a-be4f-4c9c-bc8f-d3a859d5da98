import Link from "next/link";
import { FC } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ack,
  Typography,
  IconButton,
  Box,
} from "@mui/material";
import { useUser } from "../../hooks/user/use-user";
import { useAuthUser } from "../auth/AuthProvider";
import UserMissingDeductionInfoAlert from "./UserMissingDeductionInfoAlert";
import { FIREBASE_URL } from "../../config/constants";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { Call, CopyAll, Message } from "@mui/icons-material";
import { DocumentReference } from "firebase/firestore";
import { useCustomerJobsCount } from "../../hooks/job/jobHook";
import UserAvatar from "./UserAvatar";
import { userTitle } from "../../lib/user-to-user-title";
import { trackCall } from "../common/CallUserLink";
import { fullName } from "../../lib/full-name";

type Props = {
  userId: string;
  showExtras?: boolean;
  showDeductionWarning?: boolean;
};

const UserCard: FC<Props> = ({
  userId,
  showExtras = true,
  showDeductionWarning = true,
}) => {
  const authUser = useAuthUser();
  const [user, loading] = useUser(userId);

  // Only set title if user changed
  const title = userTitle(user);

  if (loading || !user) return null;

  return (
    <Stack spacing={1}>
      <Card sx={{ backgroundColor: "action.hover" }} variant="outlined">
        <Box sx={{ backgroundColor: "background.paper" }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Link passHref href={`/users/user?id=${user.reference.id}`}>
              <Stack
                alignItems="center"
                alignContent="space-between"
                spacing={1}
                direction="row"
                padding={1}
              >
                <UserAvatar size="small" userRef={user.reference} />
                <Stack direction="column">
                  <Typography fontWeight={700}>
                    {`${(user && user.firstName) || "N/A"} ${
                      (user && user.lastName) || ""
                    }`}
                  </Typography>
                  <UserJobsCount userReference={user.reference} />
                </Stack>
                <Box flex={1} />
              </Stack>
            </Link>
            <Box flex={1} />
            <IconButton
              sx={{ color: "text.disabled" }}
              onClick={() => {
                navigator.clipboard.writeText(fullName(user));
              }}
              size="small"
            >
              <CopyAll />
            </IconButton>
          </Stack>
        </Box>
        <Box height={2} />
        {title !== "Customer" && (
          <Typography padding={1} variant="caption">
            {title}
          </Typography>
        )}
      </Card>

      {showExtras &&
        showDeductionWarning &&
        !user.company &&
        !user.isSuperUser && <UserMissingDeductionInfoAlert user={user} />}

      {showExtras && !authUser.partner && (
        <Stack spacing={1} direction="row">
          <Link
            passHref
            target="_blank"
            rel="noopener noreferrer"
            href={`https://app.intercom.com/apps/gn8o0bfp/users/show?user_id=${user.reference.id}`}
          >
            <Button startIcon={<Message />} color="info" size="small">
              Intercom
            </Button>
          </Link>
          {user.phoneNumber && (
            <Link
              passHref
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => trackCall(authUser, user)}
              href={`tel:${user.phoneNumber}`}
            >
              <Button startIcon={<Call />} size="small">
                Call
              </Button>
            </Link>
          )}

          <Box flex={1} />

          <Link
            passHref
            target="_blank"
            rel="noopener noreferrer"
            href={`${FIREBASE_URL}/firestore/data/~2Fusers~2F${user.reference.id}`}
          >
            <IconButton color="warning" size="small">
              <WhatshotIcon />
            </IconButton>
          </Link>
        </Stack>
      )}
    </Stack>
  );
};

function UserJobsCount({
  userReference,
}: {
  userReference: DocumentReference;
}) {
  const count = useCustomerJobsCount(userReference);

  if (count === 0) return null;

  return (
    <Typography color="info.main" fontSize={12} variant="body2">
      User has {count} jobs
    </Typography>
  );
}

export default UserCard;
