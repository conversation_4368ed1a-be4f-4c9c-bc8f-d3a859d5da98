import formatTimeRelative from "../../lib/format-time-relative";
import Link from "next/link";
import Loading from "../common/Loading";
import { useState } from "react";
import { useCollection } from "react-firebase-hooks/firestore";
import {
  LinearProgress,
  Select,
  TableHead,
  TableCell,
  Tooltip,
  Table,
  TableRow,
  Typography,
} from "@mui/material";
import {
  getFirestore,
  orderBy,
  query,
  collection,
  where,
  limit,
  Timestamp,
} from "@firebase/firestore";

const PhoneUser = ({ phoneNumber }: { phoneNumber: string }) => {
  const collectionReference = query(
    query(
      collection(getFirestore(), "users"),
      where("phoneNumber", "==", phoneNumber),
    ),
    limit(1),
  );

  const [users, loading, error] = useCollection(collectionReference, {});

  if (loading) return null;
  if (error) return null;
  if (!users?.docs || users.docs.length !== 1) return null;
  const [user] = users.docs;
  return (
    <Link href={`/users/user/?id=${user.ref.id}`}>
      {user.data().firstName + " " + user.data().lastName}
    </Link>
  );
};

export const SmsItem = ({ sms }: { sms: any }) => {
  if (!sms) return <p>No sms</p>;

  const number = sms.direction === "outgoing" ? sms.to : sms.from;

  return (
    <TableRow>
      <TableCell>
        {sms.createTime &&
          formatTimeRelative((sms.createTime as Timestamp).toDate())}
      </TableCell>
      <TableCell>{number}</TableCell>
      <TableCell>
        <PhoneUser phoneNumber={number} />
      </TableCell>
      <TableCell>
        <Tooltip title={sms.message}>
          <Typography>{sms.message}</Typography>
        </Tooltip>
      </TableCell>
    </TableRow>
  );
};

const initialCollection = () =>
  query(collection(getFirestore(), "46elks"), orderBy("createTime", "desc"));

const SmsList = ({
  collection = initialCollection(),
  withDirection = true,
  initialDirection = "incoming",
}) => {
  const [directionFilter, setDirectionFilter] = useState(initialDirection);
  if (directionFilter && withDirection) {
    collection = query(collection, where("direction", "==", directionFilter));
  }

  const [value, loading, error] = useCollection(collection);
  if (error) return <p css={{ margin: 16 }}>Error: {`${error}`} </p>;
  if (loading) return <Loading />;

  return (
    <>
      {withDirection && (
        <Select
          style={{ margin: 16 }}
          size="small"
          native
          value={directionFilter}
          onChange={(event: any) => setDirectionFilter(event.target.value)}
        >
          <option value="incoming">Incoming</option>
          <option value="outgoing">Outgoing</option>
        </Select>
      )}
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Created</TableCell>
            <TableCell>
              {directionFilter === "incoming" ? "From" : "To"}
            </TableCell>
            <TableCell>User</TableCell>
            <TableCell>Body</TableCell>
          </TableRow>
        </TableHead>
        {loading ? (
          <LinearProgress />
        ) : (
          value &&
          value.docs.map((doc: any) => {
            const sms = doc.data();

            return <SmsItem key={doc.id} sms={sms} />;
          })
        )}
      </Table>
    </>
  );
};

export default SmsList;
