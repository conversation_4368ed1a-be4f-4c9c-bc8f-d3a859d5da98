import { updateDoc } from "firebase/firestore";
import { User } from "../../models/user/user";
import FormDialog from "../dialogs/FormDialog";
import { DialogProps } from "../dialogs/default-dialog-props";
import TextField from "../form-fields/TextField";

interface EditUserAddressDialogProps extends DialogProps {
  user: User;
}

export default function EditUserAddressDialog({
  user,
  isOpen,
  close,
}: EditUserAddressDialogProps) {
  return (
    <FormDialog
      isOpen={isOpen}
      close={close}
      title={"Edit location"}
      initialValues={user.address}
      onSubmit={async (changes) => {
        await updateDoc(user.reference, "address", changes);
      }}
    >
      <TextField name="streetAddress" label="Street address" />
      <TextField name="floor" label="Floor" />
      <TextField name="doorCode" label="Door code" />
      <TextField name="postalCode" label="Postal code" />
      <TextField name="zip" label="Zip" />
      <TextField name="city" label="City" />
      <TextField name="country" label="Country" />
      <TextField name="line1" label="Line 1" />
      <TextField name="line2" label="Line 2" />
    </FormDialog>
  );
}
