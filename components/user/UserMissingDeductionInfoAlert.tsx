import { Alert, AlertTitle } from "@mui/material";
import { User } from "../../models/user/user";

/**
 * Displays an alert if the user has not yet provided their deduction information.
 */
export default function UserMissingDeductionInfoAlert({
  user,
}: {
  user: Pick<User, "hasEnteredROTInformation">;
}) {
  if (user.hasEnteredROTInformation) return null;
  return (
    <Alert severity="warning">
      <AlertTitle>User has no deduction info</AlertTitle>
      This user cannot be sent invoices containing deductions.
    </Alert>
  );
}
