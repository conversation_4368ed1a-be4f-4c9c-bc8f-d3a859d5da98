import formatTimeRelative from "../../lib/format-time-relative";
import { collection, DocumentReference } from "@firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import { Stack, Divider } from "@mui/material";
import InfoItemRow from "../common/InfoItemRow";

const UserStatus = ({
  user,
  userRef,
}: {
  user: any;
  userRef: DocumentReference;
}) => {
  const [value, loading] = useCollection(collection(userRef, "devices"));

  if (loading) return null;
  if (!user) return null;

  return (
    <Stack spacing={1} divider={<Divider flexItem />}>
      <InfoItemRow
        noCopy
        required
        title="Last logged in"
        value={formatTimeRelative(user.lastLoggedIn?.toDate())}
      />

      <InfoItemRow
        noCopy
        title=" Push notification enabled"
        value={!value ? "" : value.docs.length > 0 ? "YES" : "NO"}
      />
    </Stack>
  );
};

export default UserStatus;
