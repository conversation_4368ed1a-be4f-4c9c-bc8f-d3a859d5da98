import UserCard from "./UserCard";
import { <PERSON>ack, ToggleButton, Typography } from "@mui/material";
import { useSnackbar } from "notistack";
import { DocumentReference } from "@firebase/firestore";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import removeUndefined from "../../lib/remove-undefined";
import parsePhoneNumber from "libphonenumber-js";
import FormDialog from "../dialogs/FormDialog";
import { DialogProps } from "../dialogs/default-dialog-props";
import CompanyName from "../company/CompanyName";
import TextField from "../form-fields/TextField";
import ToggleButtonsField from "../form-fields/ToggleButtonsField";
import Condition from "../form-fields/Condition";
import EmailField from "../form-fields/EmailField";
import PhoneNumberField from "../form-fields/PhoneNumberField";
import { DonePartnerId, isPlatformPartner } from "../../models/other/partner";
import { useAuthUser } from "../auth/AuthProvider";
import { isDefined } from "../../lib/filter-undefined";

interface CreateUserDialogProps extends DialogProps {
  companyRef?: DocumentReference;
}

export default function CreateUserDialog({
  companyRef,
  isOpen,
  close,
}: CreateUserDialogProps) {
  const { partnerDoc } = useAuthUser();
  const { enqueueSnackbar } = useSnackbar();

  return (
    <FormDialog
      isOpen={isOpen}
      close={close}
      title={"Create user"}
      initialValues={{
        type: companyRef ? "installer" : "customer",
        phoneNumber: "",
        firstName: "",
        lastName: "",
        email: "",
        address: {},
      }}
      onSubmit={async (newUser) => {
        if (!newUser.phoneNumber) return;

        if (!parsePhoneNumber(newUser.phoneNumber)) {
          enqueueSnackbar("Phone number has wrong format", {
            variant: "error",
          });

          return;
        }

        const { type, ...user } = newUser;
        const memberType = type === "installer" ? "memberOf" : "customerOf";
        const isPlatform = partnerDoc && isPlatformPartner(partnerDoc);

        const backofficeCreateUserPayload = {
          user: removeUndefined({
            ...user,
            [memberType]: isPlatform
              ? [partnerDoc.reference.id]
              : [partnerDoc?.reference?.id, DonePartnerId].filter(isDefined),
          }),
          companyId: companyRef?.id,
        };

        const result = await httpsCallable(
          getFunctions(getApp(), "europe-west1"),
          "createUserFromBackoffice",
        )(removeUndefined(backofficeCreateUserPayload));

        enqueueSnackbar(
          <Stack direction="column" spacing={1}>
            <Typography>User created</Typography>
            <UserCard
              showDeductionWarning={false}
              userId={(result.data as any).userId}
            />
          </Stack>,
          {
            variant: "default",
            anchorOrigin: { vertical: "bottom", horizontal: "right" },
          },
        );
      }}
    >
      <ToggleButtonsField name="type" fullWidth disabled={Boolean(companyRef)}>
        <ToggleButton value="customer">Customer</ToggleButton>
        <ToggleButton value="installer">Installer</ToggleButton>
      </ToggleButtonsField>
      <TextField name="firstName" type="text" required label="First name" />
      <TextField name="lastName" type="text" required label="Last name" />
      <EmailField name="email" required label="Email" />
      <PhoneNumberField name="phoneNumber" required label="Phone number" />

      {companyRef && <CompanyName companyRef={companyRef} />}

      <TextField name="internalComment" type="text" label="Internal Comment" />

      <Condition when="type" is="customer">
        <Typography> User location </Typography>

        <TextField
          name="address.line1"
          type="text"
          label="Street address line 1"
        />
        <TextField
          name="address.line2"
          type="text"
          label="Street address line 2"
        />
        <TextField name="address.zip" type="text" label="Zip code" />
        <TextField name="address.city" type="text" label="City" />
      </Condition>
    </FormDialog>
  );
}
