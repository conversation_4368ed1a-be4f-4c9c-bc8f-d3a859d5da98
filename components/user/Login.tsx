import dynamic from "next/dynamic";
import Phone from "@mui/icons-material/Phone";
import { getApp } from "firebase/app";
import { getFunctions, httpsCallable } from "@firebase/functions";
import { useAuthState } from "react-firebase-hooks/auth";
import { useEffect, useState } from "react";
import "react-phone-number-input/style.css";
import {
  TextField,
  Button,
  LinearProgress,
  InputAdornment,
  Typography,
  Stack,
} from "@mui/material";
import {
  getAuth,
  RecaptchaVerifier,
  ConfirmationResult,
  signInWithPhoneNumber,
  GoogleAuthProvider,
  signInWithPopup,
} from "firebase/auth";
import DoneLogo from "../common/DoneLogo";

const ReactCodeInput = dynamic(import("react-code-input"));

const Login = () => {
  const auth = getAuth();

  const [appVerifier, setAppVerifier] = useState<
    RecaptchaVerifier | undefined
  >();

  const [user] = useAuthState(auth);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [code, setCode] = useState("");
  const [loginStarted, setLoginStarted] = useState(false);
  const [confirmationResult, setConfirmationResult] =
    useState<ConfirmationResult | null>(null);
  const [error, setError] = useState<undefined | string>();

  const provider = new GoogleAuthProvider();

  provider.setCustomParameters({
    prompt: "select_account",
    hd: "doneservices.co",
  });

  useEffect(() => {
    if (appVerifier) return;
    setAppVerifier(new RecaptchaVerifier(auth, "recaptcha-container"));
  }, [appVerifier, auth]);

  if (user) {
    return <div />;
  }

  if (confirmationResult) {
    const confirmLogin = async (event: any) => {
      if (event) event.preventDefault();
      setLoginStarted(true);

      let result;

      try {
        result = await confirmationResult.confirm(code);
      } catch (error) {
        setLoginStarted(false);
        setCode("");
        setPhoneNumber("");
        setConfirmationResult(null);
        setError("Invalid code");
        return;
      }

      if (result != null) {
        await httpsCallable(
          getFunctions(getApp(), "europe-west1"),
          "updateUserLink",
        )();
        await auth.currentUser?.getIdToken(true);
      }
    };

    return (
      <form onSubmit={confirmLogin}>
        <Stack spacing={1}>
          <ReactCodeInput
            name="sms-code-input"
            type="text"
            fields={6}
            value={code}
            inputMode="numeric"
            onChange={(e) => {
              setCode(e);
            }}
          />
          <Typography color="GrayText">Enter sms code</Typography>

          <Button
            variant="contained"
            onClick={confirmLogin}
            disabled={loginStarted || !(code.length == 6)}
          >
            Login
          </Button>
        </Stack>
      </form>
    );
  }

  const loginWithGmail = async () => {
    await signInWithPopup(auth, provider);
  };

  const startLogin = async (event: any) => {
    if (!appVerifier) {
      console.log("appVerifier is not defined");
      return;
    }

    setError(undefined);
    if (event) event.preventDefault();
    setLoginStarted(true);

    signInWithPhoneNumber(auth, phoneNumber.trim(), appVerifier)
      .then((confirmationResult) => {
        setConfirmationResult(confirmationResult);
        setLoginStarted(false);
      })
      .catch((e) => {
        appVerifier.clear;
        console.warn("Error", e);
        setLoginStarted(false);
        setError(e);
      });
  };

  return (
    <form onSubmit={startLogin}>
      <Stack spacing={1}>
        {loginStarted && <LinearProgress />}
        {!loginStarted && (
          <TextField
            value={phoneNumber}
            placeholder={"Phone: +46xxxxxxxxx"}
            fullWidth
            type="tel"
            variant="standard"
            onChange={(e) => setPhoneNumber(e.target.value)}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="start">
                    <Phone />
                  </InputAdornment>
                ),
              },
            }}
          />
        )}
        {error && <Typography>Error, {error.toString()}!</Typography>}
        <div id="recaptcha-container" />
        <Button
          fullWidth
          disabled={loginStarted || phoneNumber.length < 3}
          variant="contained"
          onClick={startLogin}
          type="submit"
        >
          Login
        </Button>

        <Typography> or</Typography>
        <Button
          fullWidth
          color="primary"
          variant="outlined"
          onClick={loginWithGmail}
          type="submit"
        >
          Login with Gmail *
        </Button>
        <Typography color="text.secondary" variant="caption">
          * Done staff only
        </Typography>
      </Stack>
    </form>
  );
};

const LoginWithRecaptchaVerifier = () => {
  return (
    <Stack
      spacing={4}
      style={{
        position: "absolute",
        left: "50%",
        top: "30%",
        maxWidth: "500px",
        minWidth: "310px",
        padding: 20,
        transform: "translate(-50%, -50%)",
      }}
    >
      <Stack alignItems="baseline" spacing={2} direction="row">
        <DoneLogo size={35} />
        <Typography fontWeight={600} variant="h4">
          Backoffice
        </Typography>
      </Stack>
      <Login />
    </Stack>
  );
};

export default LoginWithRecaptchaVerifier;
