import JobsTableBase from "../job/table/JobsTable";
import { collection, getFirestore, query, where } from "@firebase/firestore";
import { DocumentReference } from "firebase/firestore";

export const UserJobs = ({ userRef }: { userRef: DocumentReference }) => {
  const userJobsQuery = query(
    collection(getFirestore(), "jobs"),
    where("customer", "==", userRef),
  );

  return <JobsTableBase initCollection={userJobsQuery} />;
};

export default UserJobs;
