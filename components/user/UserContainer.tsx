import CompanyJobs from "../company/CompanyJobs";
import Loading from "../common/Loading";
import UserJobs from "./UserJobs";
import { Grid2 } from "@mui/material";

import UserDetail from "./UserDetail";
import { useUser } from "../../hooks/user/use-user";
import Custom404Page from "../../pages/404";
import DialogManager from "../dialogs/DialogManager";
import ComponentContainer from "../common/ComponentContainer";
import SubjectInternalCommentsSection from "../job/internal-comments/SubjectInternalCommentsSection";
import FileQueue from "../upload/FileQueue";
import Internal from "../auth/Internal";

export const UserContainer = ({ userId }: { userId: string }) => {
  const [user, loading] = useUser(userId);

  if (loading) return <Loading />;

  if (!user) return <Custom404Page />;

  return (
    <FileQueue>
      <DialogManager>
        <Grid2 container spacing={0}>
          <Grid2 size={{ xs: 12, md: 5 }}>
            <div style={{ margin: 8 }}>
              <UserDetail userId={userId} />
            </div>
          </Grid2>

          <Grid2 size={{ xs: 12, md: 7 }}>
            <ComponentContainer title="Inbox">
              <SubjectInternalCommentsSection subject={user.reference} />
            </ComponentContainer>
          </Grid2>
        </Grid2>
        <Internal>
          <ComponentContainer
            padding={8}
            title={user.company ? "Company jobs" : "Customer jobs"}
          >
            {user.company ? (
              <CompanyJobs companyRef={user.company} />
            ) : (
              <UserJobs userRef={user.reference} />
            )}
          </ComponentContainer>
        </Internal>
      </DialogManager>
    </FileQueue>
  );
};

export default UserContainer;
