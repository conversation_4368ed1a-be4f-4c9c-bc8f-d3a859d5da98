import EditIcon from "@mui/icons-material/Edit";
import EditUserAddressDialog from "./EditUserAddressDialog";
import EditUserButton from "./EditUserButton";
import EditLocationDialog from "../dialogs/EditLocationDialog";
import Loading from "../common/Loading";
import ManagedDialog from "../dialogs/ManagedDialog";
import UploadImageButton from "../common/UploadImageButton";
import UserCompany from "./UserCompany";
import UserStatus from "./UserStatus";
import {
  Alert,
  Box,
  Button,
  Card,
  Divider,
  IconButton,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { FIREBASE_URL } from "../../config/constants";
import { startCase } from "lodash";
import { useDialog } from "../dialogs/DialogManager";
import { useUser } from "../../hooks/user/use-user";
import UserMissingDeductionInfoAlert from "./UserMissingDeductionInfoAlert";
import EditUserRotInfoDialog from "../job/dialogs/EditUserRotInfoDialog";
import { useUserSensitiveData } from "../../hooks/user/userUserSensitiveHook";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { Message } from "@mui/icons-material";
import { isDefined } from "../../lib/filter-undefined";
import UserAvatar from "./UserAvatar";
import { GoogleAuthProvider, getAuth, linkWithPopup } from "firebase/auth";
import { useAuthUser } from "../auth/AuthProvider";
import InfoItemRow from "../common/InfoItemRow";
import { userTitle } from "../../lib/user-to-user-title";
import PhoneNumberInfoItemRow from "../common/PhoneNumberInfoItemRow";
import Internal from "../auth/Internal";
import MapBox from "../map/MapBox";

export const UserDetail = ({ userId }: { userId: string }) => {
  const [user, userLoading] = useUser(userId);
  const { userSensitiveData, loading: sensitiveDataLoading } =
    useUserSensitiveData(userId);

  const { open: openEditUserLocation } = useDialog("edit-user-location");
  const { open: openEditUserAddress } = useDialog("edit-user-address");
  const { open: openEditUserRotInfo } = useDialog("edit-user-rot-info");

  const provider = new GoogleAuthProvider();

  provider.setCustomParameters({
    prompt: "select_account",
    hd: "doneservices.co",
  });

  const authContext = useAuthUser();

  const auth = getAuth();

  const userEmail = authContext.userDoc?.email;

  if (userLoading || sensitiveDataLoading) return <Loading />;

  if (!user) return <div style={{ margin: 20 }}> No user</div>;

  return (
    <Paper variant="outlined" style={{ padding: "12px" }}>
      <ManagedDialog
        id="edit-user-location"
        reference={user.reference}
        path={"location"}
        location={user.location}
        userName={[user.firstName, user.lastName].filter(isDefined).join(" ")}
        component={EditLocationDialog}
      />

      <ManagedDialog
        id="edit-user-address"
        user={user}
        component={EditUserAddressDialog}
      />

      <ManagedDialog
        userSensitiveData={userSensitiveData}
        id="edit-user-rot-info"
        component={EditUserRotInfoDialog}
      />

      <Stack spacing={2}>
        <Stack padding={1} spacing={2} alignItems="center" direction="row">
          <UserAvatar userRef={user.reference} />

          <Typography variant="h5">
            {user.firstName + " " + user.lastName}
          </Typography>

          <Box sx={{ flexGrow: 1 }} />

          <Internal>
            <Typography variant="h6" color="info.main">
              {userTitle(user)}
            </Typography>
          </Internal>
        </Stack>

        <Card variant="outlined" style={{ padding: 12 }}>
          <Stack spacing={1} divider={<Divider flexItem />}>
            <PhoneNumberInfoItemRow user={user} />

            <InfoItemRow
              title="E-mail"
              value={user.email ?? "No email registered"}
            />

            {user.address &&
              Object.entries(user.address).map(([key, value]) => {
                return (
                  <InfoItemRow key={key} title={startCase(key)} value={value} />
                );
              })}

            <UserStatus user={user} userRef={user.reference} />
          </Stack>
        </Card>
        {!user.company && !user.isSuperUser && (
          <UserMissingDeductionInfoAlert user={user} />
        )}
        {user.company && (
          <>
            <Typography variant="body2">Company</Typography>
            <UserCompany userRef={user.reference} />
          </>
        )}
        <Stack alignItems="center" spacing={1} direction="row">
          <EditUserButton user={user} />
          <Button
            startIcon={<EditIcon />}
            variant="outlined"
            size="small"
            onClick={() => {
              openEditUserRotInfo();
            }}
          >
            Tax
          </Button>
          <UploadImageButton
            documentRef={user.reference}
            dataKey="profileImage"
            fileKey={`user-profile-images-v1/${userId}/original.jpg`}
          />
          <Internal>
            <a
              target="_blank"
              rel="noopener noreferrer"
              href={`https://app.intercom.com/apps/gn8o0bfp/users/show?user_id=${user.reference.id}`}
            >
              <Button
                size="small"
                startIcon={<Message />}
                color="info"
                variant="outlined"
              >
                Intercom
              </Button>
            </a>

            <a
              target="_blank"
              rel="noopener noreferrer"
              href={`${FIREBASE_URL}/firestore/data/~2Fusers~2F${user.reference.id}`}
            >
              <IconButton color="warning" size="small">
                <WhatshotIcon />
              </IconButton>
            </a>
          </Internal>
        </Stack>

        {user.location?.coordinates ? (
          <MapBox
            sx={{ height: 224 }}
            center={{
              lat: user.location?.coordinates.latitude,
              lng: user.location?.coordinates.longitude,
            }}
          />
        ) : (
          <Alert severity="warning" variant="outlined">
            User location not set
          </Alert>
        )}
        <Stack direction="row" spacing={1}>
          <Button
            startIcon={<EditIcon />}
            variant="outlined"
            size="small"
            onClick={() => {
              openEditUserLocation();
            }}
          >
            Location
          </Button>
          <Internal>
            <Button
              startIcon={<EditIcon />}
              variant="outlined"
              size="small"
              onClick={() => {
                openEditUserAddress();
              }}
            >
              Billing address
            </Button>
          </Internal>
        </Stack>

        <Internal>
          {user.reference.id === authContext.userDoc?.reference.id && (
            <Button
              disabled={!userEmail || !userEmail.includes("@doneservices")}
              variant="contained"
              onClick={async () => {
                await linkWithPopup(auth.currentUser!, provider);
              }}
            >
              Link your account with your google account
            </Button>
          )}
        </Internal>
      </Stack>
    </Paper>
  );
};

export default UserDetail;
