import { updateDoc } from "@firebase/firestore";
import { Form } from "react-final-form";
import { TextField } from "mui-rff";

import { Button, DialogContent, DialogActions } from "@mui/material";
import { User } from "../../models/user/user";

const EditUserForm = ({
  user,
  onClose,
}: {
  user: User;
  onClose: () => void;
}) => {
  return (
    <Form
      onSubmit={async (updatedUser) => {
        await updateDoc(user.reference, { ...updatedUser });
        onClose();
      }}
      initialValues={{ ...user }}
      render={({ handleSubmit, pristine, invalid, submitting }) => (
        <form
          onSubmit={handleSubmit}
          style={{ display: "flex", flexDirection: "column" }}
        >
          <DialogContent>
            <TextField
              size="small"
              name="firstName"
              type="text"
              style={{ margin: 10 }}
              label="First name"
              variant="outlined"
            />
            <TextField
              size="small"
              name="lastName"
              type="text"
              style={{ margin: 10 }}
              label="Last name"
              variant="outlined"
            />
            <TextField
              size="small"
              name="email"
              style={{ margin: 10 }}
              type="text"
              label="Email"
              variant="outlined"
            />
            <TextField
              size="small"
              style={{ margin: 10 }}
              name="phoneNumber"
              required
              label="Phone number"
              variant="outlined"
              helperText="⚠️ Please make sure that the phone number is not already in use. User will be logged out if phone number is changed."
            />
          </DialogContent>
          <DialogActions>
            <Button
              disabled={submitting}
              onClick={onClose}
              style={{ color: "red" }}
            >
              Cancel
            </Button>
            <Button
              disabled={submitting || pristine}
              type="submit"
              color="primary"
            >
              Save
            </Button>
          </DialogActions>
        </form>
      )}
    />
  );
};

export default EditUserForm;
