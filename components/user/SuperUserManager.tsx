import { FC } from "react";

import {
  Button,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@mui/material";
import { useAdmin } from "../../hooks/other/adminConfigHook";
import Loading from "../common/Loading";
import {
  collection,
  deleteField,
  doc,
  getFirestore,
  serverTimestamp,
  updateDoc,
} from "firebase/firestore";
import { useUser } from "../../hooks/user/use-user";
import PageContainer from "../layout/PageContainer";
import { Fullscreen } from "@mui/icons-material";
import Link from "next/link";
import Header from "../layout/panels/Header";
import UserAvatar from "./UserAvatar";
import { useDialog } from "../dialogs/DialogManager";
import { SearchData } from "../../lib/services/global-search";
import removeUndefined from "../../lib/remove-undefined";

const SuperUserManagement: FC = () => {
  const { open: openGlobalSearchDialog } = useDialog("global-search");
  const { admin, loading } = useAdmin();

  if (loading) return <Loading />;

  return (
    <PageContainer tight>
      <Header title={"Staff management"}>
        <Button
          onClick={() =>
            openGlobalSearchDialog({
              title: "Select user to add as a staff",

              defaultOptions: ["users"],
              onItemSelected: async (selectedItem: SearchData) => {
                const selectedUserRef = doc(
                  collection(getFirestore(), "users"),
                  selectedItem.id,
                );

                await updateDoc(
                  doc(collection(getFirestore(), "admin"), "admin"),
                  {
                    [`superAdminUsers.${selectedUserRef.id}`]: removeUndefined({
                      role: "admin",
                      fullName: selectedItem.payload.fullName,
                      reference: selectedUserRef,
                      lastUpdatedAt: serverTimestamp(),
                    }),
                  },
                );
              },
            })
          }
          variant="text"
        >
          + Add
        </Button>
      </Header>

      <TableContainer>
        <Table size="small" aria-label="a table">
          <TableHead>
            <TableRow>
              <TableCell width={10}>Avatar</TableCell>
              <TableCell>Name</TableCell>
              <TableCell align="right">Action</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {admin?.superAdminUsers &&
              Object.keys(admin.superAdminUsers).map((id) => {
                return <StaffRow key={id} userId={id} />;
              })}
          </TableBody>
        </Table>
      </TableContainer>
    </PageContainer>
  );
};

function StaffRow({ userId }: { userId: string }) {
  const [user, loading] = useUser(userId);

  if (loading || !user) return null;

  return (
    <TableRow>
      <TableCell align="left">
        <UserAvatar size="small" userRef={user.reference} />
      </TableCell>
      <TableCell>
        {user.firstName} {user.lastName}
      </TableCell>

      <TableCell align="right">
        <Link
          passHref
          target="_blank"
          rel="noopener noreferrer"
          href={`/users/user/?id=${user.reference.id}`}
        >
          <Button startIcon={<Fullscreen />} size="small">
            Go to user
          </Button>
        </Link>
        <Button
          onClick={async () => {
            if (!window.confirm("Are you sure?")) return;
            await updateDoc(doc(collection(getFirestore(), "admin"), "admin"), {
              [`superAdminUsers.${userId}`]: deleteField(),
            });
          }}
          color="error"
        >
          Remove
        </Button>
      </TableCell>
    </TableRow>
  );
}

export default SuperUserManagement;
