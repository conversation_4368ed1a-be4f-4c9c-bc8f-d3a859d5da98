import { Chip } from "@mui/material";
import { useUser } from "../../hooks/user/use-user";
import { fullName } from "../../lib/full-name";
import { userTitle } from "../../lib/user-to-user-title";
import UserAvatar from "./UserAvatar";
import { isSystemUser, User } from "../../models/user/user";
import { PlatformTier } from "../auth/Tiered";
import { StandardTier } from "../auth/Tiered";

interface Props {
  userId: string;
  size?: "small" | "medium";
}

export default function UserChip({ userId, size = "medium" }: Props) {
  const [user, loading] = useUser(userId);

  if (loading || !user) return null;

  return (
    <Chip
      variant="outlined"
      color={
        user.isSuperUser || user.partner
          ? "primary"
          : user.company
            ? "info"
            : "warning"
      }
      size={size}
      avatar={<UserAvatar size="tiny" userRef={user.reference} />}
      label={
        user.company && (
          <>
            <PlatformTier>{label(user)}</PlatformTier>
            <StandardTier exact>Craftsman</StandardTier>
          </>
        )
      }
    />
  );
}

function label(user: User) {
  if (isSystemUser(user.reference)) {
    return "Done";
  }

  return `${fullName(user)} | ${userTitle(user)}`;
}
