import UserCard from "./UserCard";
import { <PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { DocumentReference, doc, getFirestore } from "@firebase/firestore";
import { useDialog } from "../dialogs/DialogManager";
import { SearchData } from "../../lib/services/global-search";

export default function SelectUserButton({
  userRef,
  onChange,
}: {
  userRef: DocumentReference;
  onChange: (doc?: DocumentReference) => void;
}) {
  const db = getFirestore();
  const { open: openGlobalSearchDialog } = useDialog("global-search");

  return (
    <>
      {userRef && <UserCard userId={userRef.id} />}

      {!userRef && (
        <Alert
          action={
            <Button
              size="small"
              color="inherit"
              onClick={() =>
                openGlobalSearchDialog({
                  title: "Select user",
                  defaultOptions: ["users"],
                  onItemSelected: (selectedItem: SearchData) => {
                    onChange(doc(db, "users", selectedItem.id));
                  },
                })
              }
            >
              Select user
            </Button>
          }
          severity="info"
        >
          Customer not set
        </Alert>
      )}

      {userRef && (
        <Button
          onClick={() => {
            onChange(undefined);
          }}
          size="small"
        >
          Remove user
        </Button>
      )}
    </>
  );
}
