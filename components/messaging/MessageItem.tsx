import { Stack, Tooltip, Typography } from "@mui/material";
import Reply<PERSON>llIcon from "@mui/icons-material/ReplyAll";
import MessageBubble, { MessageBubbleProps } from "./MessageBubble";
import { formatTime, formatTinyDistance } from "../../lib/format-time-relative";
import { DateOrTimestamp } from "../../lib/date-or-timestamp";
import { DocumentReference, DocumentData } from "firebase/firestore";
import Markdown from "../common/Markdown";
import TooltipToolbar from "../tooltip-toolbar/TooltipToolbar";
import MessageStack from "./MessageStack";

interface MessageItemProps extends Omit<MessageBubbleProps, "children"> {
  sender: DocumentReference<DocumentData>;
  text: string;
  isFromMe?: boolean;
  recipients?: string[];
  createTime?: DateOrTimestamp;
  tools?: React.ReactNode;
  toolsEnabled?: boolean;
  children?: React.ReactNode;
}

export default function MessageItem({
  text,
  sender,
  isFromMe,
  createTime,
  recipients,
  toolsEnabled = true,
  tools,
  children,
  ...bubbleProps
}: MessageItemProps) {
  return (
    <MessageStack isFromMe={isFromMe} sender={sender}>
      <TooltipToolbar
        enabled={toolsEnabled}
        placement={isFromMe ? "bottom-start" : "bottom-end"}
        tools={tools}
      >
        <MessageBubble {...bubbleProps}>
          <Stack>
            <Typography style={{ whiteSpace: "pre-line" }} variant="body2">
              <Markdown>{text}</Markdown>
            </Typography>
            {children}
            <Stack
              direction="row"
              spacing={0.5}
              justifyContent={isFromMe ? "flex-end" : "flex-start"}
              alignItems="center"
              divider={<Typography variant="caption">•</Typography>}
            >
              {recipients?.length ? (
                <RecipientsList recipients={recipients} />
              ) : null}

              <Tooltip title={formatTime(createTime)} arrow>
                <Typography variant="caption">
                  {formatTinyDistance(createTime)}
                </Typography>
              </Tooltip>
            </Stack>
          </Stack>
        </MessageBubble>
      </TooltipToolbar>
    </MessageStack>
  );
}

function RecipientsList({ recipients }: { recipients: string[] }) {
  if (!recipients?.length) return null;
  return (
    <Stack
      direction="row"
      spacing={0.5}
      alignItems="center"
      alignContent={"center"}
    >
      <ReplyAllIcon sx={{ fontSize: "1em" }} />
      <Stack
        direction="row"
        spacing={0}
        alignItems="center"
        divider={<Typography variant="caption">,&nbsp;</Typography>}
      >
        {recipients.map((recipient) => (
          <Typography variant="caption" key={recipient}>
            {recipient}
          </Typography>
        ))}
      </Stack>
    </Stack>
  );
}
