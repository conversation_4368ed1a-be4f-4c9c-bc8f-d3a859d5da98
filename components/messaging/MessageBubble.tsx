import { Box, styled } from "@mui/material";
import { Theme } from "@mui/system";
import { assertNever } from "../../lib/assert-never";

type MessageBubbleVariant = "external" | "own" | "internal" | "system";
export interface MessageBubbleProps {
  children: React.ReactNode;
  variant: MessageBubbleVariant;
}

const baseStyle = {
  borderRadius: 8,
  padding: 10,
  paddingTop: 8,
  paddingBottom: 8,
  minWidth: 100,
  maxWidth: "80%",
};

const paletteForVariant = (theme: Theme, variant: MessageBubbleVariant) => {
  switch (variant) {
    case "own":
      return theme.palette.ownMessage;
    case "internal":
      return theme.palette.internalMessage;
    case "external":
      return theme.palette.externalMessage;
    case "system":
      return theme.palette.systemMessage;
    default:
      assertNever(variant);
  }
};

const MessageBubble = styled(Box)<MessageBubbleProps>(({ theme, variant }) => {
  const palette = paletteForVariant(theme, variant);

  if (variant === "system") {
    return {
      borderWidth: 2,
      borderStyle: "dashed",
      borderColor: palette?.divider,
      ...baseStyle,
    };
  }

  return {
    backgroundColor: palette?.main,
    color: palette?.contrastText,
    ...baseStyle,
  };
});

export default MessageBubble;
