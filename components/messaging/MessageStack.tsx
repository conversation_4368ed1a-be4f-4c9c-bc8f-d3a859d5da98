import { Stack } from "@mui/material";
import UserAvatar from "../user/UserAvatar";
import { DocumentData, DocumentReference } from "firebase/firestore";

interface MessageStackProps {
  sender: DocumentReference<DocumentData>;
  isFromMe?: boolean;
  children: React.ReactNode;
}

export default function MessageStack({
  isFromMe,
  sender,
  children,
}: MessageStackProps) {
  return (
    <Stack
      direction={isFromMe ? "row-reverse" : "row"}
      alignItems="flex-end"
      spacing={1}
    >
      <UserAvatar size="small" userRef={sender} />
      {children}
    </Stack>
  );
}
