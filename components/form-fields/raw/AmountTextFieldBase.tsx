import {
  BaseTextFieldProps,
  Button,
  InputAdornment,
  TextField,
} from "@mui/material";
import { useCallback, useState } from "react";
import {
  AmountInfo,
  CentAmount,
  PercentageAmount,
  isCentAmount,
  isPercentageAmount,
} from "../../../models/article-groups/article";
import { toCents, toNumber } from "../../../models/finance/cent-value";
import lodash from "lodash";
import { Float } from "type-fest";
const { omit } = lodash;

const modes = ["percent", "currency"] as const;
export type Mode = (typeof modes)[number];

export interface AmountTextFieldBaseProps extends BaseTextFieldProps {
  value: AmountInfo<CentAmount | PercentageAmount>;
  onChange?: (
    value: AmountInfo<CentAmount | PercentageAmount>,
    mode: Mode,
  ) => void;
  initialMode?: Mode;
  allowedModes?: Mode[];
}

function rawValue(amount: AmountInfo<CentAmount | PercentageAmount>) {
  if (!amount) return "";
  if (isCentAmount(amount)) {
    return amount.amount?.value !== undefined
      ? String(toNumber(amount.amount.value))
      : "";
  }

  if (isPercentageAmount(amount)) {
    return amount.amount?.percentage !== undefined
      ? String(amount.amount.percentage * 100)
      : "";
  }

  return "";
}

function amountWithValue(
  amount: AmountInfo,
  value: number,
  mode: Mode,
): AmountInfo<CentAmount | PercentageAmount> {
  if (isNaN(value)) {
    return {
      ...(amount ?? {}),
      amount: {
        ...omit(amount?.amount ?? {}, "value", "percentage"),
      },
    };
  }

  if (mode === "currency") {
    return {
      ...(amount ?? {}),
      amount: {
        ...omit(amount?.amount ?? {}, "percentage"),
        value: toCents(value),
      },
    };
  } else {
    let percentageValue: Float<number> | undefined;

    if (value !== undefined) {
      percentageValue = (value / 100) as Float<number>;
    }

    return {
      ...(amount ?? {}),
      amount: {
        ...omit(amount?.amount ?? {}, "value"),
        percentage: percentageValue,
      },
    };
  }
}

/**
 * Base component for an amount text field.
 * If you want to use this in a form, use `AmountTextField` instead.
 */
export default function AmountTextFieldBase({
  value,
  onChange,
  initialMode,
  allowedModes = ["currency", "percent"],
  ...fieldProps
}: AmountTextFieldBaseProps) {
  const [mode, setModeState] = useState<Mode>(
    (initialMode ?? isPercentageAmount(value)) ? "percent" : "currency",
  );
  const fieldValue = rawValue(value);

  const onChangeCallback = useCallback(
    (newValue: string, mode: Mode) => {
      const numValue = newValue.trim() !== "" ? Number(newValue) : NaN;
      const newAmount = amountWithValue(value, numValue, mode);
      onChange?.(newAmount, mode);
    },
    [onChange, value],
  );

  return (
    <TextField
      value={fieldValue}
      onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
        onChangeCallback(event.target.value, mode);
      }}
      sx={{
        "& .MuiOutlinedInput-root": {
          "&.MuiInputBase-root fieldset": {
            borderColor: "transparent",
          },
        },
      }}
      slotProps={{
        input: {
          endAdornment: (
            <ToggleModeAdornment
              mode={mode}
              disabled={allowedModes.length === 1}
              setMode={(mode) => {
                if (!allowedModes.includes(mode)) return;
                setModeState(mode);
                onChangeCallback(fieldValue, mode);
              }}
            />
          ),
        },
      }}
      {...fieldProps}
    />
  );
}

function ToggleModeAdornment({
  mode,
  setMode,
  disabled,
}: {
  mode: Mode;
  setMode: (mode: Mode) => void;
  disabled?: boolean;
}) {
  return (
    <InputAdornment position="end">
      <Button
        sx={{ minWidth: 35 }}
        size="small"
        disabled={disabled}
        onClick={() => setMode(mode === "percent" ? "currency" : "percent")}
      >
        {mode === "percent" ? "%" : "kr"}
      </Button>
    </InputAdornment>
  );
}
