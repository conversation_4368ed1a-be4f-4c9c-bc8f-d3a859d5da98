import { MenuItem, TextField } from "@mui/material";
import { useMemo } from "react";
import { Field } from "react-final-form";
import { LabeledValue } from "../../config/constants";

interface LabeledValueFieldProps<T> {
  name: string;
  label: string;
  items: readonly LabeledValue<T>[];

  /**
   * If false, the value can be undefined and a "No value" item will be included.
   */
  required?: boolean;

  /**
   * The initial value of the field.
   */
  initialValue?: T;
}

function NoValueMenuItem() {
  return (
    <MenuItem value={undefined}>
      <i>No value</i>
    </MenuItem>
  );
}

/**
 * Displays a select field with labeled values.
 */
export default function LabeledValueField<T extends string | number | null>({
  name,
  label,
  items,
  required = false,
  initialValue,
}: LabeledValueFieldProps<T>) {
  const containsUndefined = useMemo(() => {
    return Boolean(items.find((item) => item.value === undefined));
  }, [items]);

  return (
    <Field
      allowUndefined={!required}
      name={name}
      initialValue={initialValue}
      render={({ input }) => (
        <TextField
          {...input}
          select
          fullWidth
          label={label}
          variant="outlined"
          required={required}
        >
          {items.map(({ value, label }) => (
            <MenuItem
              key={String(value) ?? ""}
              value={value === null ? undefined : value}
            >
              {label}
            </MenuItem>
          ))}
          {!required && !containsUndefined ? <NoValueMenuItem /> : null}
        </TextField>
      )}
    />
  );
}
