import TextField, { TextFieldProps } from "./TextField";

type EmailFieldProps = Omit<TextFieldProps<string>, "validator" | "type">;

/**
 * A field for e-mail addresses.
 */
export default function EmailField({ ...rest }: EmailFieldProps) {
  return (
    <TextField
      {...rest}
      type="email"
      validator={(value) => {
        if (
          value &&
          !value?.match(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)
        ) {
          return "Not a valid email format";
        }

        return undefined;
      }}
    />
  );
}
