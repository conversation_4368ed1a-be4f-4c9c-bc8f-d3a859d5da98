import { MenuItem, TextField } from "@mui/material";
import { getDoc } from "firebase/firestore";
import { Field } from "react-final-form";
import { useCollection } from "react-firebase-hooks/firestore";
import {
  Partner,
  partnerRef,
  partnersCollection,
} from "../../models/other/partner";
import { useAuthUser } from "../auth/AuthProvider";
import { useCallback } from "react";
import { usePromise } from "../../lib/hooks/use-promise";
import { isDefined } from "../../lib/filter-undefined";

interface PartnerSelectFieldProps {
  disableId?: string;
  required?: boolean;
  name?: string;
  title?: string;
  onChange?: (value: string) => void;
}

export default function PartnerSelectField({
  disableId,
  required = false,
  name,
  title,
  onChange,
}: PartnerSelectFieldProps) {
  const { partnerDoc } = useAuthUser();

  if (partnerDoc) {
    return (
      <PlatformPartnerSelectField
        partners={partnerDoc.networks}
        required={required}
        name={name}
        title={title}
        onChange={onChange}
      />
    );
  }

  return (
    <InternalPartnerSelectField
      disableId={disableId}
      required={required}
      name={name}
      title={title}
      onChange={onChange}
    />
  );
}

function PlatformPartnerSelectField({
  disableId,
  partners,
  required,
  name,
  title,
}: PartnerSelectFieldProps & {
  partners: string[];
}) {
  const loadPartners = useCallback(async () => {
    const refs = partners
      .map((partner) => partnerRef(partner))
      .filter(isDefined);
    const docs = await Promise.all(refs.map((ref) => getDoc(ref)));
    return docs.map((doc) => doc.data()).filter(isDefined);
  }, [partners]);

  const [items, loading] = usePromise(loadPartners);

  return (
    <BasePartnerSelectField
      items={items ?? []}
      loading={loading}
      disableId={disableId}
      required={required}
      name={name}
      title={title}
    />
  );
}

function InternalPartnerSelectField({
  disableId,
  required,
  name,
  title,
}: PartnerSelectFieldProps) {
  const [value, loading] = useCollection(partnersCollection());
  const docs = value?.docs?.map((doc) => doc.data()) ?? [];

  return (
    <BasePartnerSelectField
      items={docs}
      loading={loading}
      disableId={disableId}
      required={required}
      name={name}
      title={title}
    />
  );
}

function BasePartnerSelectField({
  items,
  loading,
  disableId,
  required = false,
  name = "partner",
  title = "Partner",
}: PartnerSelectFieldProps & {
  items: Partner[];
  loading: boolean;
}) {
  return (
    <Field
      name={name}
      required={required}
      render={({ input }) => (
        <TextField
          {...input}
          select
          fullWidth
          disabled={loading}
          required={required}
          label={title}
          variant="outlined"
        >
          {items.map((partner) => (
            <MenuItem
              key={partner.reference.id}
              value={partner.reference.id}
              disabled={partner.reference.id === disableId}
            >
              {partner.displayName ?? partner.name}
            </MenuItem>
          ))}
          {!required && (
            <MenuItem value={""}>
              <i>No partner</i>
            </MenuItem>
          )}
        </TextField>
      )}
    />
  );
}
