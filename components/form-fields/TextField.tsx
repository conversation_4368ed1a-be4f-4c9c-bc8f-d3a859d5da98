import { Field } from "react-final-form";
import {
  TextField as MaterialTextField,
  OutlinedTextFieldProps,
} from "@mui/material";
import { FieldValidator } from "final-form";

export interface TextFieldPropsBase
  extends Omit<OutlinedTextFieldProps, "variant"> {
  /**
   * The name of the field.
   * This is used to identify the field in the form state.
   */
  name: string;

  /**
   * The label of the field.
   */
  label: string;

  /**
   * Whether the field is required.
   * @default false
   */
  required?: boolean;

  /**
   * The helper text of the field.
   * This is displayed below the field.
   */
  helperText?: string;

  /**
   * Whether the field is multiline.
   * @default false
   */
  multiline?: boolean;

  /**
   * Whether the field should be focused on mount.
   * @default false
   */
  autoFocus?: boolean;
}

export interface TextFieldProps<T = any> extends TextFieldPropsBase {
  /**
   * The HTML type of the field.
   * @default "text"
   */
  type?: React.InputHTMLAttributes<unknown>["type"];

  /**
   * A function that parses the input of the field and outputs the value that should be stored in the form state.
   */
  parser?: (value: T) => T;

  /**
   * A function that formats the value from the form state to the value that should be displayed in the field.
   */
  formatter?: (value: T) => T;

  /**
   * Whether the field should be formatted on change.
   * @default false
   */
  formatOnBlur?: boolean;

  /**
   * A function that validates the value of the field.
   * @param value The value of the field.
   * @returns An error message if the value is invalid, or undefined if the value is valid.
   */
  validator?: FieldValidator<T>;

  /**
   * The maximum length of the field.
   */
  maxLength?: number;

  /**
   * The minimum length of the field.
   */
  minLength?: number;

  /**
   * The initial value of the field.
   */
  initialValue?: T;
}

/**
 * A text field.
 * Suitable to serves as a base for other form fields.
 */
export default function TextField({
  name,
  label,
  type = "text",
  required = false,
  helperText,
  multiline = false,
  parser,
  autoFocus = false,
  formatter,
  formatOnBlur = false,
  validator,
  maxLength,
  minLength,
  initialValue,
  ...fieldProps
}: TextFieldProps) {
  return (
    <Field
      name={name}
      parse={parser}
      format={formatter}
      formatOnBlur={formatOnBlur}
      initialValue={initialValue}
      validate={(value, allValues, meta) => {
        if (minLength !== undefined && (value ?? "").length < minLength) {
          return `Must be at least ${minLength} characters`;
        }

        if (maxLength !== undefined && (value ?? "").length > maxLength) {
          return `Must be at most ${maxLength} characters (currently ${
            (value ?? "").length
          })`;
        }

        return validator?.(value, allValues, meta) ?? undefined;
      }}
      render={({ input, meta }) => (
        <MaterialTextField
          {...input}
          {...fieldProps}
          autoFocus={autoFocus}
          error={Boolean(meta.error)}
          required={required}
          fullWidth
          multiline={multiline}
          rows={multiline ? 3 : undefined}
          type={type}
          label={label}
          variant="outlined"
          helperText={meta.error ? meta.error : helperText}
        />
      )}
    />
  );
}
