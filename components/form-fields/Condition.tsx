import { Field } from "react-final-form";

interface ConditionProps<T> {
  when: string;
  is?: T;
  children: React.ReactNode;
}

/**
 * Adds conditionaly rendering to a form field based on the value of another field.
 */
export default function Condition<T>({
  when,
  is,
  children,
}: ConditionProps<T>) {
  return (
    <Field name={when} subscription={{ value: true }}>
      {({ input: { value } }) => (value === is ? children : null)}
    </Field>
  );
}
