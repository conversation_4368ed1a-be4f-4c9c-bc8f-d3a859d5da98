import TextField, { TextFieldPropsBase } from "./TextField";

export interface NumberFieldProps extends TextFieldPropsBase {
  /**
   * The maximum value allowed.
   */
  max?: number;
  /**
   * The minimum value allowed.
   */
  min?: number;
}

/**
 * A text field that only accepts numbers.
 */
export default function NumberField({ max, min, ...rest }: NumberFieldProps) {
  return (
    <TextField
      {...rest}
      type={"number"}
      parser={(value) => {
        if (value === "") return undefined;
        const number = Number(value);
        if (isNaN(number)) return undefined;
        return number;
      }}
      validator={(value) => {
        if (value && isNaN(value)) {
          return "Value must be a number";
        }
        if (max !== undefined && value > max) {
          return `Value must be no more than ${max}`;
        }
        if (min !== undefined && value < min) {
          return `Value must be at least ${min}`;
        }
        return undefined;
      }}
    />
  );
}
