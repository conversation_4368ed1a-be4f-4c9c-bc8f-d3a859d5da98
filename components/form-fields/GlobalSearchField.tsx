import { Field } from "react-final-form";
import GlobalSearchAutocomplete, {
  GlobalSearchAutocompleteProps,
} from "../search/GlobalSearchAutocomplete";

interface GlobalSearchFieldProps<
  Multiple extends boolean | undefined = false,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = false,
> extends Omit<
    GlobalSearchAutocompleteProps<Multiple, DisableClearable, FreeSolo>,
    "value" | "onChange"
  > {
  name: string;
}

/**
 * A form field that allows the user to use global search to set a value.
 * @see {@link GlobalSearchAutocomplete}
 */
export default function GlobalSearchField<
  Multiple extends boolean | undefined = false,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = false,
>({
  name,
  ...props
}: GlobalSearchFieldProps<Multiple, DisableClearable, FreeSolo>) {
  return (
    <Field name={name}>
      {({ input }) => (
        <GlobalSearchAutocomplete
          {...props}
          value={input.value}
          onChange={(_, value) => input.onChange(value)}
        />
      )}
    </Field>
  );
}
