import {
  CountryCode,
  ParseError,
  parsePhoneNumberWithError,
} from "libphonenumber-js";
import TextField, { TextFieldProps } from "./TextField";

interface PhoneNumberFieldProps
  extends Omit<
    TextFieldProps<string>,
    "type" | "validator" | "parse" | "format"
  > {
  defaultCountry?: CountryCode;
}

/**
 * A phone number field that parses and formats phone numbers.
 * Numbers are displayed as "+46 70 123 45 67" and stored as "+46701234567".
 */
export default function PhoneNumberField({
  defaultCountry = "SE",
  ...rest
}: PhoneNumberFieldProps) {
  return (
    <TextField
      {...rest}
      type="tel"
      formatOnBlur
      parser={(value) => {
        const { plain } = parsePhoneNumber(value, defaultCountry);
        return plain ?? value;
      }}
      formatter={(value) => {
        const { formatted } = parsePhoneNumber(value, defaultCountry);
        return formatted;
      }}
      validator={(value) => {
        const { error } = parsePhoneNumber(value, defaultCountry);
        return error;
      }}
    />
  );
}

interface ParsedPhoneNumber {
  error?: string;
  plain?: string;
  formatted?: string;
}

function parsePhoneNumber(
  value: string | undefined,
  defaultCountry: CountryCode,
): ParsedPhoneNumber {
  if (!value) return {};
  try {
    const result = parsePhoneNumberWithError(value, defaultCountry);
    return {
      plain: result.format("E.164"),
      formatted: result.formatInternational(),
    };
  } catch (error: unknown) {
    if (error instanceof ParseError) {
      switch (error.message) {
        case "INVALID_COUNTRY":
          return { error: "Invalid country code" };
        case "NOT_A_NUMBER":
          return { error: "Not a valid phone number" };
        case "TOO_SHORT":
          return { error: "Phone number is too short" };
        case "TOO_LONG":
          return { error: "Phone number is too long" };
        case "INVALID_LENGTH":
          return {
            error: "Phone number is of invalid length",
          };
        default:
          return { error: "Invalid phone number" };
      }
    }
    return {};
  }
}
