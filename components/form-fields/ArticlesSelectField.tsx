import { getDoc, getDocs, Timestamp } from "firebase/firestore";
import { useEffect, useState } from "react";
import { LabeledValue, ServiceTypes } from "../../config/constants";
import {
  ArticleGroup,
  activeArticleGroupQuery,
  articleGroupRef,
} from "../../models/article-groups/article-group";
import AutocompleteLabeledValueField from "./AutocompleteField";
import { Alert, AlertTitle, Chip, Grid2, Typography } from "@mui/material";
import { formatDate } from "../../lib/format-time-relative";
import { Article, articlesQuery } from "../../models/article-groups/article";
import { FirestoreModel } from "../../models/firestore_model";
import { isDefined } from "../../lib/filter-undefined";

export interface PriceListFilter {
  partner?: string;
  services?: ServiceTypes[];
  activeAt?: Timestamp;

  /**
   * If specified, uses this exact article group.
   */
  groupId?: string;
}

interface ArticlesSelectFieldProps extends PriceListFilter {
  name?: string;
}

function ArticleGroupTitle({ group }: { group: ArticleGroup }) {
  return (
    <Grid2 container>
      <Grid2
        size={{ xs: 12 }}
        container
        direction={"row"}
        alignItems={"center"}
        spacing={1}
      >
        <Grid2>{group.title}</Grid2>
        <Grid2>
          {group.services.map((s) => (
            <Chip key={s} label={s} size="small" />
          ))}
        </Grid2>
      </Grid2>
      <Grid2 size={{ xs: 12 }}>
        <Typography variant="caption">
          Version {group.version} - {formatDate(group.startTime)}
        </Typography>
      </Grid2>
    </Grid2>
  );
}

/**
 * Field that shows articles from the active price list for a partner.
 */
export default function ArticlesSelectField({
  partner,
  activeAt,
  services,
  groupId,
  name = "article",
}: ArticlesSelectFieldProps) {
  const [loading, setLoading] = useState(true);
  const [noActivePriceList, setNoActivePriceList] = useState(false);
  const [items, setItems] = useState<LabeledValue<Article>[]>([]);
  const [priceList, setPriceList] = useState<ArticleGroup | null>(null);

  useEffect(() => {
    const fetch = async () => {
      let group: (ArticleGroup & FirestoreModel) | undefined;

      switch (true) {
        // Fetch group based on exact id
        case isDefined(groupId):
          const doc = await getDoc(articleGroupRef(groupId!));
          group = doc.data();
          break;
        // Lookup group based on filter
        case isDefined(partner) && isDefined(activeAt) && isDefined(services):
          const groups = await getDocs(
            activeArticleGroupQuery(partner!, activeAt!, services!),
          );
          const [groupDoc] = groups.docs;
          group = groupDoc?.data();
          break;
        // Neither a groupId nor lookup values are set
        default:
          return;
      }

      if (!group) {
        setNoActivePriceList(true);
        setLoading(false);
        return;
      }

      setPriceList(group);

      const articles = await getDocs(articlesQuery(group.reference.id));
      setItems(
        articles.docs.map((doc) => ({
          value: doc.data(),
          label: doc.data().title,
        })),
      );
      setLoading(false);
    };

    fetch();
  }, [partner, activeAt, services, groupId]);

  return (
    <>
      {priceList && <ArticleGroupTitle group={priceList} />}
      <AutocompleteLabeledValueField
        loading={loading}
        name={name}
        label="Article"
        items={items}
        required
      />
      {noActivePriceList && (
        <Alert severity="info">
          <AlertTitle>No active price list</AlertTitle>
          Could not find a price list for partner &quot;{partner}&quot; that was
          active when this job was created at {formatDate(activeAt)}.
        </Alert>
      )}
      {!items.length && !loading ? (
        <Alert severity="info">
          <AlertTitle>Empty price list</AlertTitle>
          The price list does not contain any articles.
        </Alert>
      ) : null}
    </>
  );
}
