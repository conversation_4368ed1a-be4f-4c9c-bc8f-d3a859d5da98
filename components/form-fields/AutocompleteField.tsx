import { Autocomplete, TextField } from "@mui/material";
import { Field, FieldInputProps, FieldMetaState } from "react-final-form";
import { LabeledValue } from "../../config/constants";
import { useMemo } from "react";

interface AutocompleteLabeledValueFieldProps<T> {
  name: string;
  label: string;
  loading?: boolean;
  items: LabeledValue<T>[];
  required?: boolean;
  multiple?: boolean;
}

export default function AutocompleteLabeledValueField<T>({
  name,
  ...props
}: AutocompleteLabeledValueFieldProps<T>) {
  return (
    <Field
      name={name}
      render={({ input, meta }) => (
        <AutocompleteFieldBase input={input} meta={meta} {...props} />
      )}
    />
  );
}

interface AutocompleteFieldBaseProps<T>
  extends Omit<AutocompleteLabeledValueFieldProps<T>, "name"> {
  input: FieldInputProps<any, HTMLElement>;
  meta: FieldMetaState<any>;
}

function AutocompleteFieldBase<T>({
  label,
  input,
  meta,
  items,
  required = false,
  loading = false,
  multiple = false,
}: AutocompleteFieldBaseProps<T>) {
  const selectedValues = useMemo(() => {
    if (multiple) {
      const values = input.value as Array<any>;
      return items.filter((item) => values.includes(item.value));
    } else {
      return items.find((item) => item.value === input.value);
    }
  }, [input.value, items, multiple]);

  return (
    <Autocomplete
      size="small"
      value={selectedValues}
      multiple={multiple}
      onChange={(_, item) => {
        if (multiple) {
          input.onChange((item as LabeledValue<T>[])?.map((i) => i.value));
        } else {
          input.onChange((item as LabeledValue<T>)?.value);
        }
      }}
      options={items}
      loading={loading}
      disabled={loading}
      renderInput={(params) => (
        <TextField
          {...params}
          fullWidth
          label={label}
          variant="outlined"
          required={required}
          error={Boolean(meta.error)}
        />
      )}
    />
  );
}
