import { Field } from "react-final-form";
import AmountTextFieldBase, {
  AmountTextFieldBaseProps,
} from "./raw/AmountTextFieldBase";
import {
  AmountInfo,
  CentAmount,
  PercentageAmount,
} from "../../models/article-groups/article";

interface AmountTextFieldProps
  extends Omit<AmountTextFieldBaseProps, "value" | "onChange"> {
  name: string;
  initialValue?: AmountInfo<CentAmount | PercentageAmount>;
}

export default function AmountTextField({
  name,
  initialValue,
  ...props
}: AmountTextFieldProps) {
  return (
    <Field
      name={name}
      initialValue={initialValue}
      render={({ input }) => <AmountTextFieldBase {...input} {...props} />}
    />
  );
}
