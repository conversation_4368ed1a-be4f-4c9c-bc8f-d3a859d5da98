import { Field } from "react-final-form";

import { ToggleButtonGroup, ToggleButtonGroupProps } from "@mui/material";

interface ToggleButtonsFieldProps
  extends Omit<ToggleButtonGroupProps, "onChange" | "value" | "exclusive"> {
  name: string;
  children: React.ReactNode;
}

/**
 * A toggle buttons field where the user can select one of the buttons.
 *
 * @example
 * <ToggleButtonsField name="type">
 *   <ToggleButton value="customer">Customer</ToggleButton>
 *   <ToggleButton value="installer">Installer</ToggleButton>
 * </ToggleButtonsField>
 */
export default function ToggleButtonsField({
  name,
  children,
  ...props
}: ToggleButtonsFieldProps) {
  return (
    <Field name={name}>
      {({ input }) => (
        <ToggleButtonGroup
          color="primary"
          exclusive
          size="small"
          {...props}
          {...input}
        >
          {children}
        </ToggleButtonGroup>
      )}
    </Field>
  );
}
