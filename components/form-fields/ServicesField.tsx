import { COMPANY_SERVICES } from "../../config/constants";
import AutocompleteLabeledValueField from "./AutocompleteField";

interface ServicesFieldProps {
  name?: string;
  required?: boolean;
}

export default function ServicesField({
  name = "services",
  required = false,
}: ServicesFieldProps) {
  return (
    <AutocompleteLabeledValueField
      name={name}
      multiple
      label={"Services"}
      items={COMPANY_SERVICES}
      required={required}
    />
  );
}
