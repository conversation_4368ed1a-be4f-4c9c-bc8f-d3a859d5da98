import {
  Autocomplete,
  AutocompleteProps,
  AutocompleteRenderOptionState,
  Box,
  TextField,
} from "@mui/material";
import { useGlobalSearch } from "./use-global-search";
import { SearchIndexes, SearchData } from "../../lib/services/global-search";
import { assertNever } from "../../lib/assert-never";

export interface GlobalSearchAutocompleteProps<
  Multiple extends boolean | undefined = false,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = false,
> extends Omit<
    AutocompleteProps<SearchData, Multiple, DisableClearable, FreeSolo>,
    | "options"
    | "filterOptions"
    | "getOptionLabel"
    | "renderOption"
    | "renderInput"
    | "onInputChange"
  > {
  indexes: SearchIndexes[];
  label?: string;
  optionsStyle?: OptionRenderStyle;
  partner?: string;
}

/**
 * Field that autocompletes search results.
 * @note See `GlobalSearchField` for a version compatible with forms.
 */
export default function GlobalSearchAutocomplete<
  Multiple extends boolean | undefined = false,
  DisableClea<PERSON> extends boolean | undefined = false,
  <PERSON><PERSON>olo extends boolean | undefined = false,
>({
  indexes,
  label = "Search",
  optionsStyle = "plain",
  partner,
  ...props
}: GlobalSearchAutocompleteProps<Multiple, DisableClearable, FreeSolo>) {
  const [search, options] = useGlobalSearch(indexes);
  const optionRenderer = createOptionsRenderer(optionsStyle);

  return (
    <Autocomplete
      {...props}
      filterOptions={(x) => x}
      options={options}
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.title
      }
      renderInput={(params) => <TextField {...params} label={label} />}
      onInputChange={(_, newInputValue) => {
        search(newInputValue, partner);
      }}
      renderOption={optionRenderer}
    />
  );
}

type OptionRenderer = (
  props: React.HTMLAttributes<HTMLLIElement>,
  option: SearchData,
  state: AutocompleteRenderOptionState,
) => React.ReactNode;

type OptionRenderStyle = "plain";

function createOptionsRenderer(style: OptionRenderStyle): OptionRenderer {
  switch (style) {
    case "plain":
      return (props, option) => (
        <Box component={"li"} {...props}>
          {option.title}
        </Box>
      );
    default:
      assertNever(style);
  }
}
