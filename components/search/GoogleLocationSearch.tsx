import * as React from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import parse from "autosuggest-highlight/parse";
import { debounce } from "@mui/material/utils";
import { Autocomplete, Grid2 } from "@mui/material";
import { useRef, useState } from "react";
import { useMapsLibrary } from "@vis.gl/react-google-maps";

interface MainTextMatchedSubstrings {
  offset: number;
  length: number;
}
interface StructuredFormatting {
  main_text: string;
  secondary_text: string;
  main_text_matched_substrings?: readonly MainTextMatchedSubstrings[];
}
export interface PlaceType {
  description: string;
  structured_formatting: StructuredFormatting;
  place_id: string; // Google places field
}

export default function GoogleLocationSearch({
  placeholder = "Location",
  onLocationChanged,
}: {
  placeholder?: string;
  onLocationChanged: (location: PlaceType) => void;
}) {
  const [value, setValue] = useState<PlaceType | null>(null);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState<readonly PlaceType[]>([]);
  const [open, setOpen] = React.useState(false);

  const places = useMapsLibrary("places");
  const autocompleteService =
    useRef<google.maps.places.AutocompleteService>(null);

  const fetch = React.useMemo(
    () =>
      debounce(
        (
          request: { input: string },
          callback: (
            results?:
              | readonly google.maps.places.AutocompletePrediction[]
              | null,
          ) => void,
        ) => {
          autocompleteService.current?.getPlacePredictions(request, callback);
        },
        400,
      ),
    [],
  );

  React.useEffect(() => {
    let active = true;

    if (!autocompleteService.current && places) {
      autocompleteService.current = new places.AutocompleteService();
    }
    if (!autocompleteService.current) {
      return undefined;
    }

    if (inputValue === "") {
      setOptions(value ? [value] : []);
      return undefined;
    }

    fetch(
      { input: inputValue },
      (
        results?: readonly google.maps.places.AutocompletePrediction[] | null,
      ) => {
        if (active) {
          let newOptions: readonly PlaceType[] = [];

          if (value) {
            newOptions = [value];
          }

          if (results) {
            // Convert AutocompletePrediction to PlaceType
            const convertedResults: PlaceType[] = results.map((prediction) => ({
              description: prediction.description,
              structured_formatting: {
                main_text: prediction.structured_formatting.main_text,
                secondary_text: prediction.structured_formatting.secondary_text,
                main_text_matched_substrings:
                  prediction.structured_formatting.main_text_matched_substrings,
              },
              place_id: prediction.place_id,
            }));
            newOptions = [...newOptions, ...convertedResults];
          }

          setOptions(newOptions);
        }
      },
    );

    return () => {
      active = false;
    };
  }, [value, inputValue, fetch, places]);

  return (
    <Autocomplete
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.description
      }
      filterOptions={(x) => x}
      options={options}
      open={open}
      onClose={() => setOpen(false)}
      noOptionsText={false}
      autoComplete
      includeInputInList
      filterSelectedOptions
      value={value}
      onChange={(_, newValue: PlaceType | null) => {
        setOptions(newValue ? [newValue, ...options] : options);
        setValue(newValue);
        if (newValue) {
          onLocationChanged(newValue);
        }
      }}
      onInputChange={(_, newInputValue) => {
        if (newInputValue.length === 0) {
          if (open) setOpen(false);
        } else {
          if (!open) setOpen(true);
        }
        setInputValue(newInputValue);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={placeholder}
          fullWidth
          disabled={!places}
          helperText={!places ? "Loading Google Maps..." : undefined}
        />
      )}
      renderOption={(props, option) => {
        const matches =
          option.structured_formatting.main_text_matched_substrings || [];

        const parts = parse(
          option.structured_formatting.main_text,
          matches.map((match: any) => [
            match.offset,
            match.offset + match.length,
          ]),
        );

        return (
          <li {...props}>
            <Grid2 container alignItems="center">
              <Grid2
                sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}
              >
                {parts.map((part, index) => (
                  <Box
                    key={index}
                    component="span"
                    sx={{ fontWeight: part.highlight ? "bold" : "regular" }}
                  >
                    {part.text}
                  </Box>
                ))}
                <Typography variant="body2" color="text.secondary">
                  {option.structured_formatting.secondary_text}
                </Typography>
              </Grid2>
            </Grid2>
          </li>
        );
      }}
    />
  );
}
