import {
  Tooltip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Stack,
  Divider,
  Box,
  useTheme,
  Typography,
} from "@mui/material";
import { useState, useEffect } from "react";
import { SEARCH_OPTIONS } from "../../config/constants";
import { useRouter } from "next/router";
import PersonIcon from "@mui/icons-material/Person";
import ApartmentIcon from "@mui/icons-material/Apartment";
import ConstructionIcon from "@mui/icons-material/Construction";
import TerminalIcon from "@mui/icons-material/Terminal";
import { useKey } from "../../lib/use-key";
import { SearchData } from "../../lib/services/global-search";
import KeyboardReturnIcon from "@mui/icons-material/KeyboardReturn";
import { useAuthUser } from "../auth/AuthProvider";

const GlobalSearchHitsList = ({
  filteredData,
  onClose,
  onItemSelected,
}: {
  filteredData: SearchData[];
  onClose: () => void;
  onItemSelected?: (selectedItem: SearchData) => void;
}) => {
  const theme = useTheme();
  const authContext = useAuthUser();
  const partner = authContext.partner;
  const router = useRouter();
  const [navIndex, setNavIndex] = useState<number>(0);

  useKey("ArrowDown", () => {
    setNavIndex((prev) => (prev === filteredData.length - 1 ? 0 : prev + 1));
  });

  useKey("ArrowUp", () => {
    setNavIndex((prev) => (prev === 0 ? 0 : prev - 1));
  });

  useKey("Enter", () => {
    if (filteredData[navIndex]) {
      onItemSelected
        ? onItemSelected(filteredData[navIndex])
        : router.push(filteredData[navIndex].href);
      onClose();
    }
  });

  useEffect(() => {
    const selectedItem = document.querySelector(`#search_results_${navIndex}`);

    if (selectedItem) {
      selectedItem.scrollIntoView({ behavior: "smooth" });
    }
  }, [navIndex]);

  return (
    <List style={{ maxHeight: 700, overflow: "scroll" }}>
      {SEARCH_OPTIONS.length == 0 ? (
        <span style={{ marginLeft: 12 }}> No search result </span>
      ) : (
        filteredData.map((option, index) => (
          <Box
            sx={{
              backgroundColor:
                index === navIndex ? theme.palette.action.selected : undefined,
            }}
            id={"search_results_" + index}
            key={"search_results_" + index}
          >
            <Stack direction="row">
              <ListItem
                style={{
                  width: "100%",
                  cursor: "pointer",
                }}
                onClick={() => {
                  if (option?.href) {
                    onItemSelected
                      ? onItemSelected(option)
                      : router.push(option.href);

                    onClose();
                  }
                }}
              >
                <ListItemAvatar>
                  {option.type === "commands" && <TerminalIcon />}
                  {option.type === "users" && <PersonIcon />}
                  {option.type === "companies" && <ApartmentIcon />}
                  {option.type === "jobs" && <ConstructionIcon />}
                </ListItemAvatar>
                <ListItemText
                  primary={
                    option.type === "jobs" ? (
                      <Tooltip title={option.payload.description}>
                        <Box style={{ marginBottom: 8 }}>{option.title} </Box>
                      </Tooltip>
                    ) : (
                      option.title
                    )
                  }
                  secondary={
                    <Box>
                      {option.type === "commands" && <strong>command</strong>}
                      {option.type === "users" && (
                        <Box>
                          <Typography>
                            Phone: <strong>{option.payload.phoneNumber}</strong>
                          </Typography>
                          <Typography>
                            Email: <strong>{option.payload.email}</strong>
                          </Typography>
                        </Box>
                      )}{" "}
                      {option.type === "companies" && (
                        <Typography>
                          Org no <strong>{option.payload.orgNo}</strong>
                        </Typography>
                      )}{" "}
                      {option.type === "jobs" && (
                        <>
                          Customer:{" "}
                          <strong>{option.payload.customerName}</strong>{" "}
                          {option.payload.externalReference && (
                            <>
                              External reference:{" "}
                              <strong>
                                {option.payload.externalReference}
                              </strong>
                            </>
                          )}{" "}
                          {option.payload.addressLine && (
                            <>
                              Location:{" "}
                              <strong>{option.payload.addressLine}</strong>
                            </>
                          )}{" "}
                          {!partner && (
                            <>
                              Company:{" "}
                              <strong>
                                {option.payload.companyName ?? "Not assigned"}
                              </strong>
                            </>
                          )}
                        </>
                      )}
                    </Box>
                  }
                />
              </ListItem>
              {index == navIndex && (
                <Box style={{ marginTop: 30, marginRight: 20 }}>
                  <KeyboardReturnIcon />
                </Box>
              )}
            </Stack>

            <Divider />
          </Box>
        ))
      )}
    </List>
  );
};

export default GlobalSearchHitsList;
