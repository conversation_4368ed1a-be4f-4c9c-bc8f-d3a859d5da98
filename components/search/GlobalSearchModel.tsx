import GlobalSearchHitsList from "./GlobalSearchHitsList";
import makeStyles from "@mui/styles/makeStyles";
import { SEARCH_OPTIONS } from "../../config/constants";
import { SearchBar } from "./GlobalSearchBar";
import { SearchData } from "../../lib/services/global-search";
import { useDebounce } from "use-debounce";
import { useState, useMemo, useCallback } from "react";
import {
  Dialog,
  Chip,
  DialogTitle,
  Stack,
  Divider,
  Typography,
  Box,
} from "@mui/material";
import { useAuthUser } from "../auth/AuthProvider";
import AddIcon from "@mui/icons-material/Add";
import AddBusinessIcon from "@mui/icons-material/AddBusiness";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import { useDialog } from "../dialogs/DialogManager";
import { DialogProps } from "../dialogs/default-dialog-props";

const useStyles = makeStyles({
  dialog: {
    position: "absolute",
    center: true,
    top: 40,
    borderRadius: 12,
  },
});

interface GlobalSearchProps extends DialogProps {
  defaultOptions?: string[];
  filteredIds?: string[];
  title?: string;
  onItemSelected?: (selectedItem: SearchData) => void;
}

export default function GlobalSearch({
  defaultOptions = SEARCH_OPTIONS,
  filteredIds,
  title,
  isOpen,
  close,
  onItemSelected,
}: GlobalSearchProps) {
  const authContext = useAuthUser();
  const partner = authContext.partner;
  const classes = useStyles();

  // Memoize initial options to prevent unnecessary recreations
  const initialOptions = useMemo(
    () => defaultOptions || SEARCH_OPTIONS,
    [defaultOptions],
  );
  const [options, setOptions] = useState<string[]>(initialOptions);
  const [searchResults, setSearchResults] = useState<SearchData[]>([]);
  const [searchResultDebounced] = useDebounce(searchResults, 200);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);

  // Memoize dialog hooks to prevent recreations
  const { open: openCreateJobDialog } = useDialog("create-job");
  const { open: openCreateUserDialog } = useDialog("create-user");
  const { open: openCreateCompanyDialog } = useDialog("create-company");

  // Memoize handlers
  const handleSearchQueryChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleSearchDataChange = useCallback((result: SearchData[]) => {
    setSearchResults(result);
  }, []);

  const handleLoadingChange = useCallback((isLoading: boolean) => {
    setLoading(isLoading);
  }, []);

  // Memoize filtered data calculation
  const filteredData = useMemo(
    () =>
      searchResultDebounced
        .filter((option) =>
          filteredIds ? !filteredIds?.includes(option.id) : true,
        )
        .filter((e) => options.includes(e.type) || e.type == "commands"),
    [searchResultDebounced, filteredIds, options],
  );

  // Memoize option toggle handler
  const handleOptionToggle = useCallback((option: string) => {
    setOptions((currentOptions) =>
      currentOptions.includes(option)
        ? currentOptions
            .filter((filter) => filter != option)
            .filter((filter) => filter != "all")
        : option == "all"
          ? SEARCH_OPTIONS
          : [option],
    );
  }, []);

  return (
    <Dialog
      classes={{
        paper: classes.dialog,
      }}
      fullWidth
      maxWidth="sm"
      open={isOpen}
      onClose={close}
    >
      {title && <DialogTitle>{title}</DialogTitle>}
      <Stack spacing={2}>
        <Stack padding={1} spacing={1}>
          <SearchBar
            options={options}
            onSearchQueryChange={handleSearchQueryChange}
            onSearchDataChange={handleSearchDataChange}
            onLoading={handleLoadingChange}
          />

          {defaultOptions.includes("all") && (
            <Stack style={{ marginRight: 12 }} direction="row" spacing={1}>
              {SEARCH_OPTIONS.map((option) => (
                <Chip
                  key={option}
                  color={options.includes(option) ? "primary" : undefined}
                  style={{ borderRadius: 6 }}
                  size="small"
                  label={option}
                  onClick={() => handleOptionToggle(option)}
                  variant="filled"
                />
              ))}
            </Stack>
          )}
        </Stack>

        {!loading && searchQuery.length > 0 && filteredData.length == 0 && (
          <Typography style={{ margin: 20 }}>
            {options.length == 0
              ? "No filters selected"
              : "No search result! Do you want to create instead?"}
          </Typography>
        )}

        {searchQuery.length > 0 && filteredData.length > 0 ? (
          <GlobalSearchHitsList
            filteredData={filteredData}
            onClose={close}
            onItemSelected={onItemSelected}
          />
        ) : (
          !partner && (
            <Stack divider={<Divider flexItem />} spacing={2}>
              {defaultOptions?.includes("users") && (
                <Box
                  onClick={() => openCreateUserDialog()}
                  style={{
                    cursor: "pointer",
                    marginRight: 20,
                    marginLeft: 20,
                  }}
                >
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={2}
                  >
                    <PersonAddIcon />

                    <Typography> Create user</Typography>
                  </Stack>
                </Box>
              )}

              {defaultOptions?.includes("companies") && (
                <Box
                  onClick={() => openCreateCompanyDialog()}
                  style={{
                    cursor: "pointer",
                    marginRight: 20,
                    marginLeft: 20,
                  }}
                >
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={2}
                  >
                    <AddBusinessIcon />

                    <Typography> Create company</Typography>
                  </Stack>
                </Box>
              )}

              {defaultOptions?.includes("jobs") && (
                <Box
                  onClick={() => openCreateJobDialog()}
                  style={{
                    cursor: "pointer",
                    marginRight: 20,
                    marginLeft: 20,
                  }}
                >
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={2}
                  >
                    <AddIcon />

                    <Typography> Create job</Typography>
                  </Stack>
                </Box>
              )}
              <Box />
            </Stack>
          )
        )}
      </Stack>
    </Dialog>
  );
}
