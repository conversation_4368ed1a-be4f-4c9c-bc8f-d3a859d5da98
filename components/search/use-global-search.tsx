import { debounce } from "@mui/material";
import { useMemo, useState } from "react";
import { useAuthUser } from "../auth/AuthProvider";
import {
  globalSearch,
  SearchData,
  SearchIndexes,
} from "../../lib/services/global-search";

/**
 * Hook for global search.
 * @param indexes The indexes to search.
 * @returns The search function and the results.
 */
export function useGlobalSearch(indexes: SearchIndexes[]) {
  const { partner: defaultPartner } = useAuthUser();
  const [results, setResults] = useState<SearchData[]>([]);

  const search = useMemo(
    () =>
      debounce((query: string, partner?: string) => {
        globalSearch(query, indexes, partner ?? defaultPartner).then(
          (results) => {
            setResults(results);
          },
        );
      }, 400),
    [defaultPartner, indexes],
  );

  return [search, results] as const;
}
