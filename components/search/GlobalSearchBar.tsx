import SearchIcon from "@mui/icons-material/Search";
import { useState } from "react";
import { TextField, InputAdornment } from "@mui/material";
import {
  globalSearch,
  SearchData,
  SearchIndexes,
} from "../../lib/services/global-search";
import { useAuthUser } from "../auth/AuthProvider";

export const SearchBar = ({
  onSearchQueryChange,
  onSearchDataChange,
  onLoading,
  options,
}: {
  onSearchQueryChange: (query: string) => void;
  onSearchDataChange: (results: SearchData[]) => void;
  onLoading: (loading: boolean) => void;
  options: string[];
}) => {
  const authContext = useAuthUser();
  const partner = authContext.partner;
  const [search, setSearch] = useState("");

  async function onSearch(query: string) {
    setSearch(query);
    onSearchQueryChange(query);

    if (query.length == 0) {
      onSearchDataChange([]);
      return;
    }

    onLoading(true);

    onSearchDataChange(
      await globalSearch(query, options as SearchIndexes[], partner),
    );

    onLoading(false);
  }

  return (
    <TextField
      autoFocus
      fullWidth
      style={{
        borderRadius: 4,
      }}
      placeholder={`Search...`}
      value={search}
      size="medium"
      onChange={async (event) => await onSearch(event.target.value)}
      slotProps={{
        input: {
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <kbd
                style={{
                  fontSize: 12,
                }}
              >
                esc
              </kbd>
            </InputAdornment>
          ),
        },
      }}
    />
  );
};
