import { Map, MapProps } from "@vis.gl/react-google-maps";
import { CENTRAL_SWEDEN_COORDINATES } from "../../config/constants";
import { ReactNode } from "react";
import { Box, BoxProps, useTheme } from "@mui/material";

interface MapBoxProps
  extends Omit<MapProps, "colorScheme" | "reuseMaps">,
    Pick<BoxProps, "sx"> {
  /** Child compnents like data layers, markers or controls */
  children?: ReactNode;
}

// FIXME: Add a dev map id and move to environment variables.
const defaultMapId = "7c22373521b2087b";

/**
 * Component that renders a specific map.
 */
export default function MapBox({
  mapId = defaultMapId,
  defaultZoom = 5,
  defaultCenter = CENTRAL_SWEDEN_COORDINATES,
  children,
  sx = { width: "100%", height: "100%" },
  ...props
}: MapBoxProps) {
  const theme = useTheme();
  return (
    <Box sx={sx}>
      <Map
        defaultZoom={defaultZoom}
        defaultCenter={defaultCenter}
        reuseMaps
        mapId={mapId}
        colorScheme={theme.palette.mode === "dark" ? "DARK" : "LIGHT"}
        {...props}
      >
        {children}
      </Map>
    </Box>
  );
}
