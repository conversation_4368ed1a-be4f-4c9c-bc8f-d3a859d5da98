import { Job } from "../../../models/job/job";
import JobMarker from "./JobMarker";

interface JobMarkersProps {
  jobs: Job[];
  selected?: string[];
  onClick?: (id: string, job: Job) => void;
}

/**
 * Component that displays markers for a list of jobs.
 */
export default function JobMarkers({
  jobs,
  onClick,
  selected,
}: JobMarkersProps) {
  return (
    <>
      {jobs?.map((job) => {
        return (
          <JobMarker
            key={job.reference.id}
            job={job}
            selected={selected?.includes(job.reference.id)}
            onClick={() => onClick?.(job.reference.id, job)}
          />
        );
      })}
    </>
  );
}
