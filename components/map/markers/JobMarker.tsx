import { AdvancedMarker, Pin } from "@vis.gl/react-google-maps";
import { Job } from "../../../models/job/job";

interface JobMarkerProps {
  job: Job;
  selected?: boolean;
  onClick?: () => void;
}

/**
 * Displays a marker for a job.
 */
export default function JobMarker({ job, onClick, selected }: JobMarkerProps) {
  const location = job.cache?.customerLocation?.coordinates;
  if (!location) return null;

  return (
    <AdvancedMarker
      onClick={onClick}
      position={{
        lat: location.latitude,
        lng: location.longitude,
      }}
    >
      <Pin background={selected ? "blue" : "white"} />
    </AdvancedMarker>
  );
}
