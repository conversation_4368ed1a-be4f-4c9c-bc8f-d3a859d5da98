import { AdvancedMarker, Pin } from "@vis.gl/react-google-maps";
import { Company, CompanyLocation } from "../../../models/company/company";

interface CompanyMarkerProps {
  company: Company;
  location: CompanyLocation;
  selected?: boolean;
  onClick?: () => void;
}

export default function CompanyMarker({
  location,
  selected,
  onClick,
}: CompanyMarkerProps) {
  const coordinates = location.coordinates;

  if (!coordinates) return null;

  return (
    <AdvancedMarker
      position={{
        lat: coordinates.latitude,
        lng: coordinates.longitude,
      }}
      onClick={onClick}
    >
      <Pin background={selected ? "blue" : "black"} />
    </AdvancedMarker>
  );
}
