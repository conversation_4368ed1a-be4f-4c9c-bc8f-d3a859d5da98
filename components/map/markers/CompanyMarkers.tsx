import { Company } from "../../../models/company/company";
import CompanyMarker from "./CompanyMarker";

interface CompanyMarkersProps {
  companies: Company[];
  selected?: string[];
  onClick?: (companyId: string) => void;
}

/**
 * Component that displays markers for a list of companies.
 */
export default function CompanyMarkers({
  companies,
  onClick,
  selected,
}: CompanyMarkersProps) {
  return (
    <>
      {companies?.map((company) => {
        const companyId = company.reference.id;
        return company.homeLocations?.map((location) => {
          return (
            <CompanyMarker
              key={companyId}
              company={company}
              location={location}
              onClick={() => onClick?.(companyId)}
              selected={selected?.includes(companyId)}
            />
          );
        });
      })}
    </>
  );
}
