import { useMap } from "@vis.gl/react-google-maps";
import { useEffect, useState } from "react";

/** Function that generates a style for a dataset feature. */
type StyleFunction = (
  feature: google.maps.DatasetFeature,
) => google.maps.FeatureStyleOptions | null;

/** Click event handler for a dataset feature. */
type EventCallback = (
  feature: google.maps.DatasetFeature,
  event: google.maps.FeatureMouseEvent,
) => void;

interface DatasetLayerProps {
  /** The ID of the dataset to render */
  datasetId: string;

  /** The style function to apply to the dataset */
  style: StyleFunction;

  /** Optional click event handler */
  onClick?: EventCallback;

  /** Optional render function for the info window */
  renderInfo?: (
    position: google.maps.LatLng,
    feature: google.maps.DatasetFeature,
  ) => React.ReactNode;

  children?: React.ReactNode;
}

function isDataset(
  dataset: google.maps.Feature,
): dataset is google.maps.DatasetFeature {
  return dataset.featureType === google.maps.FeatureType.DATASET;
}

function withDatasetFeature(func: StyleFunction) {
  return (params: google.maps.FeatureStyleFunctionOptions) => {
    // Get the dataset feature, so we can work with all of its attributes.
    const feature = params.feature;

    if (!isDataset(feature)) return null;

    return func(feature);
  };
}

function withDatasetEvent(datasetId: string, func: EventCallback) {
  return (event: google.maps.FeatureMouseEvent) => {
    // Find the current feature
    const feature = event.features
      .filter(isDataset)
      .find((feature) => feature.datasetId === datasetId);

    if (!feature) return;

    func(feature, event);
  };
}

/**
 * Component that renders a dataset on a map and optionally a info window.
 * Added as a child to a {@link MapBox} component.
 */
export function DatasetLayer({
  datasetId,
  style,
  onClick,
  renderInfo,
  children,
}: DatasetLayerProps) {
  const map = useMap();
  const [infoPosition, setInfoPosition] = useState<google.maps.LatLng | null>(
    null,
  );
  const [infoFeature, setInfoFeature] =
    useState<google.maps.DatasetFeature | null>(null);

  const onClickHandler = withDatasetEvent(datasetId, (dataset, event) => {
    setInfoPosition(event.latLng);
    setInfoFeature(dataset);
    onClick?.(dataset, event);
  });

  // Style effect
  useEffect(() => {
    const layer = map?.getDatasetFeatureLayer(datasetId);
    if (!layer) return;

    // Make layer visible
    layer.style = withDatasetFeature(style);

    // Remove the style on unmount, hiding the layer
    return () => {
      layer.style = null;
    };
  }, [map, datasetId, style]);

  // Callback effects
  useEffect(() => {
    const layer = map?.getDatasetFeatureLayer(datasetId);
    if (!layer) return;

    // Add an event listener
    const clickListener = layer.addListener("click", onClickHandler);

    // Cleanup
    return () => {
      clickListener?.remove();
    };
  }, [map, datasetId, style, onClickHandler]);

  return (
    <>
      {renderInfo && infoPosition && infoFeature
        ? renderInfo(infoPosition, infoFeature)
        : null}
      {children}
    </>
  );
}
