import { DatasetLayer } from "./DatasetLayer";
import { interpolateInferno } from "d3-scale-chromatic";
import { scaleSequentialQuantile } from "d3-scale";
import { Box, Divider, Stack, Typography } from "@mui/material";
import {
  ControlPosition,
  InfoWindow,
  MapControl,
} from "@vis.gl/react-google-maps";
import SumRow from "../../statistics/SumRow";

// FIXME: Move datasetId out to environment and add dev variant

/**
 * Map dataset layer showing population density in Sweden.
 */
export function PopulationDatasetLayer() {
  return (
    <DatasetLayer
      datasetId="f403d883-5599-4c81-a4e2-ce68bd5d9a91"
      style={setStyle}
      renderInfo={(position, feature) => (
        <PopulationInfoWindow position={position} feature={feature} />
      )}
    >
      <ScaleLegend />
    </DatasetLayer>
  );
}

// Create a stepper sequencer using the provided theme interpolator and value intervals
const intervals = [1, 5, 10, 100, 200, 300, 500, 2000];
const sequencer = scaleSequentialQuantile(interpolateInferno).domain(intervals);

function setStyle(
  feature: google.maps.DatasetFeature,
): google.maps.FeatureStyleOptions {
  const population = Number(feature.datasetAttributes["beftotalt"]);

  if (!population) return {};

  return {
    fillColor: sequencer(population),
    fillOpacity: population < 100 ? 0.6 : 0.9,
  };
}

function sum(feature: google.maps.DatasetFeature, ...attributes: string[]) {
  return attributes.reduce(
    (total, attribute) =>
      total + Number(feature.datasetAttributes[attribute] ?? 0),
    0,
  );
}

/**
 * Component that displays population info for a specific cell.
 */
function PopulationInfoWindow({
  position,
  feature,
}: {
  position: google.maps.LatLng;
  feature: google.maps.DatasetFeature;
}) {
  return (
    <InfoWindow position={position} headerContent="Population per km²">
      <Stack divider={<Divider />} spacing={1}>
        <Stack>
          <Typography variant="inherit">Age</Typography>
          <SumRow
            title="0 - 20"
            value={sum(feature, "ald0_5", "ald5_10", "ald10_15", "ald15_20")}
          />
          <SumRow
            title="20 - 40"
            value={sum(feature, "ald20_25", "ald25_30", "ald30_35", "ald35_40")}
          />
          <SumRow
            title="40 - 60"
            value={sum(feature, "ald40_45", "ald45_50", "ald50_55", "ald55_60")}
          />
          <SumRow
            title="60 - 80"
            value={sum(feature, "ald60_65", "ald65_70", "ald70_75", "ald75_85")}
          />
          <SumRow
            title="80+"
            value={sum(
              feature,
              "ald80_85",
              "ald85_90",
              "ald90_95",
              "ald95_100",
              "ald100w",
            )}
          />
        </Stack>
        <Stack>
          <SumRow title="Men" value={feature.datasetAttributes["man"]} />
          <SumRow title="Women" value={feature.datasetAttributes["kvinna"]} />
        </Stack>
        <SumRow
          variant="body2"
          title="Total"
          value={feature.datasetAttributes["beftotalt"]}
        />
      </Stack>
    </InfoWindow>
  );
}

/**
 * Component that displays a legend for the population density scale.
 */
function ScaleLegend() {
  return (
    <MapControl position={ControlPosition.LEFT_BOTTOM}>
      <Stack sx={{ marginLeft: 1 }}>
        {intervals.toReversed().map((value) => (
          <SumRow
            key={value}
            title={<ColorBox color={sequencer(value)} />}
            value={value}
            justifyContent="flex-start"
          />
        ))}
      </Stack>
    </MapControl>
  );
}

function ColorBox({ color }: { color: string }) {
  return <Box sx={{ width: 15, height: 15, backgroundColor: color }} />;
}
