import { ControlPosition, MapControl } from "@vis.gl/react-google-maps";
import { Company } from "../../models/company/company";
import { Job } from "../../models/job/job";
import CompanyMarkers from "./markers/CompanyMarkers";
import JobMarkers from "./markers/JobMarkers";
import MapBox from "./MapBox";
import { PopulationDatasetLayer } from "./layers/PopulationDatasetLayer";
import { ToggleButton, ToggleButtonGroup } from "@mui/material";
import {
  AssignmentOutlined,
  GroupsOutlined,
  HandymanOutlined,
} from "@mui/icons-material";
import { useState } from "react";
import Internal from "../auth/Internal";

interface OperationsMapProps {
  jobs?: Job[];
  selectedJobs?: string[];
  onJobSelect?: (id: string) => void;

  companies?: Company[];
  selectedCompanies?: string[];
  onCompanySelect?: (id: string) => void;
}

// FIXME: Move mapId out to environment and add dev variant

/**
 * Component that renders a map with jobs, companies and population density.
 */
export default function OperationsMap({
  jobs,
  selectedJobs,
  onJobSelect,
  companies,
  selectedCompanies,
  onCompanySelect,
}: OperationsMapProps) {
  const [layers, setLayers] = useState<Layer[]>(["jobs"]);
  return (
    <MapBox>
      {jobs && layers.includes("jobs") ? (
        <JobMarkers jobs={jobs} onClick={onJobSelect} selected={selectedJobs} />
      ) : null}
      <Internal>
        {companies && layers.includes("companies") ? (
          <CompanyMarkers
            companies={companies}
            onClick={onCompanySelect}
            selected={selectedCompanies}
          />
        ) : null}
        {layers.includes("population") && <PopulationDatasetLayer />}

        <MapControl position={ControlPosition.TOP_CENTER}>
          <LayerToggle value={layers} onChange={setLayers} />
        </MapControl>
      </Internal>
    </MapBox>
  );
}

type Layer = "jobs" | "companies" | "population";

function LayerToggle({
  value,
  onChange,
}: {
  value?: Layer[];
  onChange?: (mode: Layer[]) => void;
}) {
  return (
    <ToggleButtonGroup
      size="small"
      value={value}
      onChange={(_, value) => onChange?.(value)}
      sx={{ backgroundColor: "background.paper", marginTop: 1 }}
    >
      <ToggleButton value="jobs">
        <AssignmentOutlined />
      </ToggleButton>
      <ToggleButton value="companies">
        <HandymanOutlined />
      </ToggleButton>
      <ToggleButton value="population">
        <GroupsOutlined />
      </ToggleButton>
    </ToggleButtonGroup>
  );
}
