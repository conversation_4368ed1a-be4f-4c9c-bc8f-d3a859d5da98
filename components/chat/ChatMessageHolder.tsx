import UserChip from "../user/UserChip";
import { useUser } from "../../hooks/user/use-user";
import { IconButton, Stack, Typography } from "@mui/material";
import { ChatMessage as ChatMessageModel } from "../../models/other/message";
import ChatMessage from "./ChatMessage";
import MessageBubble from "../messaging/MessageBubble";
import formatTimeRelative from "../../lib/format-time-relative";
import { Delete } from "@mui/icons-material";
import { deleteDoc } from "firebase/firestore";

interface ChatMessageHolderProps {
  message: ChatMessageModel;
}

export default function ChatMessageHolder({ message }: ChatMessageHolderProps) {
  const [user, loading] = useUser(message.sender.id);

  if (loading) return null;

  const customer = !Boolean(user?.company) && !Boolean(user?.isSuperUser);

  const company = Boolean(user?.company);

  return (
    <Stack alignItems={customer ? "flex-start" : "flex-end"} spacing={0.5}>
      <Stack
        key={message.reference.id}
        alignItems={customer ? "flex-start" : "flex-end"}
        spacing={0.5}
      >
        {message.sender && <UserChip userId={message.sender.id} />}

        <Stack direction={customer ? "row" : "row-reverse"}>
          <MessageBubble
            variant={customer ? "internal" : company ? "own" : "external"}
          >
            <ChatMessage message={message} />
          </MessageBubble>
          <IconButton
            color="inherit"
            size="small"
            onClick={() => {
              if (window.confirm("Are you sure you want to delete this?")) {
                deleteDoc(message.reference);
              }
            }}
          >
            <Delete />
          </IconButton>
        </Stack>
      </Stack>
      <Typography variant="caption">
        {formatTimeRelative(message.createTime?.toDate())}
      </Typography>
    </Stack>
  );
}
