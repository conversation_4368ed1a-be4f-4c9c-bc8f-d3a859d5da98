import { Stack } from "@mui/material";
import ChatMessageHolder from "./ChatMessageHolder";
import { ChatMessage } from "../../models/other/message";

const ChatMessages = ({ messages }: { messages: ChatMessage[] }) => {
  return (
    <Stack maxHeight={500} overflow="scroll" spacing={3}>
      {messages.map((message) => (
        <ChatMessageHolder key={message.reference.id} message={message} />
      ))}
    </Stack>
  );
};

export default ChatMessages;
