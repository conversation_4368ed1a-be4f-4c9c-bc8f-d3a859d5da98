import ChatMessages from "./ChatMessages";
import MessageForm from "../common/MessageForm";
import { Alert, FormControlLabel, Stack, Switch } from "@mui/material";
import { DONE_BACKOFFICE_USER_REFERENCE } from "../../config/constants";
import { Job } from "../../models/job/job";
import { useAuthUser } from "../auth/AuthProvider";
import { useChatMessages } from "../../hooks/other/chatMessagesHook";
import { useState } from "react";
import {
  query,
  orderBy,
  CollectionReference,
  addDoc,
  serverTimestamp,
} from "firebase/firestore";
import Internal from "../auth/Internal";

interface ChatProps {
  job: Job;
  messagesRef: CollectionReference;
  readOnly?: boolean;
  children: React.ReactNode;
}

export default function Chat({
  job,
  readOnly,
  messagesRef,
  children,
}: ChatProps) {
  const authUser = useAuthUser();

  const { messages, loading } = useChatMessages(
    query(messagesRef, orderBy("createTime", "asc")),
  );

  const [sendFromCurrentUser, setSendFromCurrentUser] = useState(true);

  if (loading) return null;

  return (
    <Stack spacing={1}>
      {!messages || messages.length == 0 ? (
        <Alert severity="info">No messages at the moment</Alert>
      ) : (
        <ChatMessages messages={messages} />
      )}

      {!readOnly && (
        <MessageForm
          job={job}
          templateId={"Chat Message Templates"}
          placeholder={"Send chat message..."}
          onSubmit={async (message: string) => {
            await addDoc(messagesRef, {
              body: message,
              createTime: serverTimestamp(),
              timestamp: serverTimestamp(),
              sender: sendFromCurrentUser
                ? authUser.userDoc?.reference
                : DONE_BACKOFFICE_USER_REFERENCE(),
              type: "text",
            });
          }}
        />
      )}
      <Internal>
        {!readOnly && (
          <FormControlLabel
            control={
              <Switch
                checked={sendFromCurrentUser}
                onChange={(_) => setSendFromCurrentUser(!sendFromCurrentUser)}
                defaultChecked
              />
            }
            label={`Send as ${authUser.userDoc?.firstName}`}
          />
        )}
      </Internal>
      {children}
    </Stack>
  );
}
