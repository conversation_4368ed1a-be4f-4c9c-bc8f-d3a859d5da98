import formatTimeRelative from "../../lib/format-time-relative";
import { ChatMessage as ChatMessageModel } from "../../models/other/message";
import UserChip from "../user/UserChip";
import { Typo<PERSON>, <PERSON>ack, Button } from "@mui/material";
import {
  CallChatMessage,
  FileChatMessage,
  ImageChatMessage,
  InvoiceChatMessage,
  QuoteChatMessage,
  VideoChatMessage,
} from "../../models/other/message";

import ImageWithReference from "../common/ImageWithReference";
import { getFileUrl } from "../../lib/get-file-url";
import formatAmount from "../../lib/format-amount";
import { formatDistance } from "date-fns";
import Markdown from "../common/Markdown";

export const ChatMessage = ({ message }: { message: ChatMessageModel }) => {
  const type = message.type || "text";
  if (type === "text") {
    return (
      <Typography color="inherit" variant="body2">
        <Markdown>{message.body}</Markdown>
      </Typography>
    );
  }

  if (type === "info") {
    return (
      <Typography color="inherit" variant="body2">
        ℹ️ <Markdown>{message.body}</Markdown>
      </Typography>
    );
  }

  if (type === "image") {
    if (!(message as ImageChatMessage).imageRef)
      return (
        <Typography color="inherit" variant="body2">
          Image not available: <br /> Message ID: {message.reference.id}
        </Typography>
      );
    return (
      <ImageWithReference
        avatar={false}
        imageRef={(message as ImageChatMessage).imageRef!}
        thumbRef={(message as ImageChatMessage).thumbRef}
      />
    );
  }

  if (type === "file") {
    return (
      <Typography color="inherit" variant="body2">
        <strong>File:</strong>{" "}
        <Button
          variant="outlined"
          size="small"
          color="inherit"
          onClick={async () => {
            if (!(message as FileChatMessage).fileRef) return;

            const url = await getFileUrl((message as FileChatMessage).fileRef!);

            window.open(url, "_blank");
          }}
        >
          {(message as FileChatMessage)?.filename || "No file name"}
        </Button>
      </Typography>
    );
  }

  if (type === "offer" || type === "quote") {
    return (
      <div>
        <Stack spacing={1}>
          <Typography color="inherit" variant="body2">
            Quote
          </Typography>

          <Typography color="inherit" variant="body2">
            Amount (inc vat):{" "}
            <b>
              {formatAmount((message as QuoteChatMessage).amountIncVat) ||
                "Not set"}
            </b>
          </Typography>

          <Typography color="inherit" variant="body2">
            Amount (after tax deductions):{" "}
            <b>
              {formatAmount(
                (message as QuoteChatMessage).amountAfterTaxDeductions,
              ) || "Not set"}
            </b>
          </Typography>

          <Typography color="inherit" variant="body2">
            Status: <b>{(message as QuoteChatMessage).status || "Not set"}</b>
          </Typography>

          <Button
            variant="outlined"
            size="small"
            color="inherit"
            onClick={async () => {
              if (!(message as QuoteChatMessage).fileRef) return;

              const url = await getFileUrl(
                (message as QuoteChatMessage).fileRef!,
              );

              window.open(url, "_blank");
            }}
          >
            See file
          </Button>
        </Stack>
      </div>
    );
  }

  if (type === "invoice") {
    const invoice = (message as InvoiceChatMessage).invoice;

    return (
      <div>
        <Stack spacing={1}>
          <Typography variant="body2">
            <strong>Invoice</strong>
          </Typography>
          <Typography variant="body2">
            Amount to pay:{" "}
            <b>
              {formatAmount(
                (invoice?.calculatedValues?.totalNetAfterDeductions ||
                  invoice?.calculatedValues?.centAmountToPay ||
                  0) / 100,
              )}
            </b>
          </Typography>
          <Typography variant="body2">
            Due date:{" "}
            <b>
              {formatTimeRelative(
                (message as InvoiceChatMessage).invoice?.dueDate?.toDate(),
              )}
            </b>
          </Typography>
          <Button
            variant="outlined"
            size="small"
            color="inherit"
            onClick={async () => {
              if (!(message as InvoiceChatMessage).invoice?.pdfFilePath) return;

              const url = await getFileUrl(
                (message as InvoiceChatMessage).invoice?.pdfFilePath!,
              );

              window.open(url, "_blank");
            }}
          >
            Go to pdf
          </Button>
        </Stack>
      </div>
    );
  }

  if (type === "video") {
    if (!(message as VideoChatMessage).thumbRef)
      return (
        <h5 style={{ color: "grey" }}>
          Video preview not available: Message ID: {message.reference.id}
        </h5>
      );
    return (
      <ImageWithReference imageRef={(message as VideoChatMessage).thumbRef!} />
    );
  }

  if (type === "call") {
    return (
      <span
        style={{
          display: "block",
        }}
      >
        {"Call 📞"} @{" "}
        {formatTimeRelative((message as CallChatMessage).startTime?.toDate())} (
        {formatDistance(
          (message as CallChatMessage).startTime!.toDate(),
          (message as CallChatMessage).endTime!.toDate(),
        )}
        )
      </span>
    );
  }

  if (type === "missed-call") {
    <span>Missed call 📞</span>;
  }

  if (type === "cancelled-booked-call") {
    return (
      <div style={{ padding: 12 }}>
        <Typography
          variant="body2"
          style={{
            display: "block",
          }}
        >
          {`Booked call cancelled`}
        </Typography>
        <div style={{ height: 8 }} />
        <Typography variant="body2">
          From: <UserChip userId={message.sender.id} size="small" />
        </Typography>
      </div>
    );
  }

  if (type === "cancelled-scheduled-work-time") {
    return (
      <div style={{ padding: 12 }}>
        <span
          style={{
            display: "block",
          }}
        >
          {`Scheduled work cancelled`}
        </span>
        From: <UserChip userId={message.sender.id} />
      </div>
    );
  }

  return (
    <Typography variant="body2" style={{ padding: 12 }}>
      Unknown type: {type}
    </Typography>
  );
};

export default ChatMessage;
