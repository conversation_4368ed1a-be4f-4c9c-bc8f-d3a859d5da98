import { <PERSON>ton, Grid2 } from "@mui/material";
import { GridCellParams } from "@mui/x-data-grid";
import {
  getFirestore,
  collectionGroup,
  query,
  Query,
  orderBy,
} from "@firebase/firestore";
import { getFileUrl } from "../../lib/get-file-url";
import { QuerySnapshot, where } from "firebase/firestore";
import formatAmount from "../../lib/format-amount";
import PaginatedDataGrid from "../common/table/PaginatedDataGrid";
import { useMemo, useState } from "react";
import { FilterTabs } from "../company/CompaniesTable";
import { QUOTE_STATUSES } from "../../config/constants";
import router from "next/router";
import PageContainer from "../layout/PageContainer";
import PageGridContainer from "../layout/PageGridContainer";
import { ExtendedGridColDef } from "../common/table/columns";

const columns: ExtendedGridColDef[] = [
  {
    field: "createTime",
    type: "dateTime",
    headerName: "Created at",
    width: 160,
  },
  { field: "status", headerName: "Status", width: 100 },
  {
    field: "amountIncVat",
    headerName: "Amount inc vat",
    width: 120,
    renderCell: (params: GridCellParams) => {
      return <p>{formatAmount(params.row.amountIncVat)}</p>;
    },
  },
  {
    field: "amountAfterTaxDeduction",
    headerName: "Amount after tax",
    width: 120,
    renderCell: (params: GridCellParams) => {
      return <p>{formatAmount(params.row.amountAfterTaxDeduction)}</p>;
    },
  },
  { field: "companyName", headerName: "Company", width: 160 },
  { field: "customerName", headerName: "Customer", width: 160 },
  {
    field: "job",
    headerName: "Job",
    width: 120,
    renderCell: (params: GridCellParams) => {
      return (
        <Button
          variant="outlined"
          color="primary"
          size="small"
          onClick={() => {
            router.push(`/jobs/job/?id=${params.row.job.id}`);
          }}
        >
          Job
        </Button>
      );
    },
  },
  {
    field: "file",
    headerName: "Pdf",
    width: 120,
    renderCell: (params: GridCellParams) => {
      return (
        <Button
          variant="outlined"
          color="primary"
          size="small"
          onClick={() => getFileUrl(params.row.data.fileRef).then(window.open)}
        >
          Pdf
        </Button>
      );
    },
  },
];

export default function QuotesTable({
  queryReference,
}: {
  queryReference?: Query;
}) {
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  const quotesQuery = useMemo(() => {
    let queryRef = query(
      queryReference ?? collectionGroup(getFirestore(), "quotes"),
      orderBy("createTime", "desc"),
    );

    if (statusFilter) {
      queryRef = query(queryRef, where("status", "==", statusFilter));
    }

    return queryRef;
  }, [queryReference, statusFilter]);

  return (
    <PageContainer>
      <PageGridContainer>
        <Grid2>
          <FilterTabs
            label="Status"
            filter={statusFilter}
            onFilterChange={(filter) => setStatusFilter(filter)}
            filterConfig={QUOTE_STATUSES}
          />
        </Grid2>
        <Grid2 size="grow" container spacing={2}>
          <Grid2 size="grow">
            <PaginatedDataGrid
              rowGenerator={quoteRowGenerator}
              query={quotesQuery}
              columns={columns}
            />
          </Grid2>
        </Grid2>
      </PageGridContainer>
    </PageContainer>
  );
}

function quoteRowGenerator(value?: QuerySnapshot) {
  return (
    value?.docs.map((document, index) => ({
      id: document.ref.id,
      createTime: document.data().createTime?.toDate(),
      paginationCursor: document.data().createTime,
      status: document.data().status,
      declineReason:
        document.data().declineReason === "other"
          ? document.data().declineReasonOther
          : document.data().declineReason,
      companyName: document.data().cache?.companyName,
      customerName: document.data().cache?.customerName,
      amountIncVat: document.data().amountIncVat,
      amountAfterTaxDeduction: document.data().amountAfterTaxDeductions,
      data: document.data(),
      job: document.ref.parent.parent,
    })) ?? []
  );
}
