import { FirebaseError } from "firebase/app";
import { DevOnly } from "./DevOnly";
import { Typography } from "@mui/material";

/**
 * Display detailed error message when running in development environment.
 */
export function DetailedError({ error }: { error?: Error | unknown }) {
  if (!error) return null;

  if (error instanceof FirebaseError) {
    return (
      <DevOnly>
        <Typography variant="caption">
          {error.code}: {error.message}
        </Typography>
      </DevOnly>
    );
  }

  if (error instanceof Error) {
    return (
      <DevOnly>
        <Typography variant="caption">{error.message}</Typography>
      </DevOnly>
    );
  }

  return (
    <DevOnly>
      <Typography variant="caption">{JSON.stringify(error)}</Typography>
    </DevOnly>
  );
}
