import { ArrowUpward, ArrowDownward } from "@mui/icons-material";
import { GridActionsCellItem } from "@mui/x-data-grid";
import { DeleteOutline } from "mdi-material-ui";
import {
  dotPropColumn,
  stringColumn,
  numberColumn,
  unitTypeColumn,
  amountColumn,
  itemTypeColumn,
  deductionColumn,
  detailedDeductionColumn,
  actionsColumn,
  ExtendedGridColDef,
} from "../../common/table/columns";

/**
 * Base columns used for both quotes and invoices.
 */
const baseColumns: ExtendedGridColDef[] = [
  dotPropColumn({
    field: "article",
    headerName: "Article",
    path: "article.reference.id",
    type: "string",
    width: 200,
    visibility: false,
  }),
  stringColumn({
    field: "description",
    headerName: "Description",
    editable: true,
    sortable: false,
    minWidth: 300,
  }),
  numberColumn({
    field: "unitCount",
    headerName: "Count",
    editable: true,
    sortable: false,
  }),
  unitTypeColumn({ field: "unitType", editable: true }),
  amountColumn({ headerName: "Price", field: "unitPrice", editable: true }),
  itemTypeColumn({ field: "type", editable: true }),
  deductionColumn({ field: "deductionType", editable: true }),
];

/**
 * Additional computed columns.
 */
const summaryColumns: ExtendedGridColDef[] = [
  amountColumn({
    field: "_gross",
    headerName: "Total",
    editable: false,
    valueGetter: (_, row) => (row.unitPrice ?? 0) * (row.unitCount ?? 1),
  }),
];

interface RowActions {
  onDelete: (id: string) => void;
  onMove: (id: string, direction: "up" | "down") => void;
}

/**
 * Columns when displaying invoices.
 */
export function invoiceColumns(actions: RowActions): ExtendedGridColDef[] {
  return [
    ...baseColumns,
    detailedDeductionColumn({
      field: "detailedDeductionType",
      width: 150,
      editable: true,
    }),
    numberColumn({
      field: "workedHours",
      headerName: "Hours",
      editable: true,
    }),
    ...summaryColumns,
    actionsColumn({
      field: "action",
      headerName: "Tools",
      width: 200,
      renderCell: ({ row }) => {
        return [
          <GridActionsCellItem
            key={"action-move-up"}
            icon={<ArrowUpward />}
            label="Delete"
            onClick={() => {
              actions.onMove(row.uid, "up");
            }}
          />,
          <GridActionsCellItem
            key={"action-move-down"}
            icon={<ArrowDownward />}
            label="Delete"
            onClick={() => {
              actions.onMove(row.uid, "down");
            }}
          />,
          <GridActionsCellItem
            key={"action-delete"}
            icon={<DeleteOutline />}
            label="Delete"
            onClick={() => {
              actions.onDelete(row.uid);
            }}
          />,
        ];
      },
    }),
  ];
}
