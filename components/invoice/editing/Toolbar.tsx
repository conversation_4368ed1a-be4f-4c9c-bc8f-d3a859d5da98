import { Box, Button } from "@mui/material";
import { useDialog } from "../../dialogs/DialogManager";
import { AddAttachmentDialogProps } from "../../job/dialogs/AddAttachmentDialog";
import { JobArticle } from "../../../models/job/job-article";
import { Article } from "../../../models/article-groups/article";
import { Add } from "@mui/icons-material";

export interface ToolbarProps {
  articleGroupId?: string;
  onAddArticle: (line: JobArticle, article?: Article) => void;
}

export default function Toolbar({
  articleGroupId,
  onAddArticle,
}: ToolbarProps) {
  const { open } = useDialog<AddAttachmentDialogProps>("add-article");
  return (
    <Box>
      <Button
        color="primary"
        startIcon={<Add />}
        size="small"
        onClick={() =>
          open({
            settings: {
              groupId: articleGroupId,
              allowCustomArticle: true,
            },
            onSubmit: (line, article) => {
              onAddArticle(line, article);
            },
          })
        }
        sx={{
          marginBottom: "3px",
        }}
      >
        Add artice
      </Button>
    </Box>
  );
}
