import { CalculatedValues } from "../../../models/invoice/calculated-values";
import Summary from "./Summary";
import { Grid2 } from "@mui/material";

export interface FooterProps {
  calculatedValues: CalculatedValues;
}

export default function Footer({ calculatedValues }: FooterProps) {
  return (
    <Grid2
      container
      sx={{
        alignItems: "flex-end",
        justifyContent: "center",
        flexDirection: "column",
        padding: 1,
        width: "100%",
      }}
    >
      <Grid2>
        <Summary calculatedValues={calculatedValues} />
      </Grid2>
    </Grid2>
  );
}
