import { Invoice } from "../../../models/invoice/invoice";
import { DialogProps } from "../../dialogs/default-dialog-props";
import FormDialog from "../../dialogs/FormDialog";
import { updateDoc } from "firebase/firestore";
import { InvoiceFormFields } from "./FormFields";

interface EditInvoiceDialogProps extends DialogProps {
  invoice?: Invoice;
}

export default function EditInvoiceDialog({
  invoice,
  isOpen,
  close,
}: EditInvoiceDialogProps) {
  const isDraft = invoice?.status === "draft";
  return (
    <FormDialog
      title={isDraft ? "Edit invoice NEW" : "View invoice"}
      isOpen={isOpen}
      close={close}
      maxWidth="xl"
      fullWidth
      onSubmit={async ({ invoice }) => {
        if (!isDraft || !invoice) return;
        const {
          deductionTypes = [],
          lineItems = [],
          calculatedValues = {},
        } = invoice;
        await updateDoc(invoice.reference, {
          deductionTypes,
          lineItems,
          calculatedValues,
        });
      }}
      initialValues={{ invoice }}
    >
      <InvoiceFormFields name="invoice" isDraft={isDraft} />
    </FormDialog>
  );
}
