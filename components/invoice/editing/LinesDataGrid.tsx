import { v4 as uuid } from "uuid";
import {
  DataGrid,
  GridFooterContainer,
  GridToolbarContainer,
} from "@mui/x-data-grid";
import { InvoiceLineItem } from "../../../models/invoice/invoice-line-item";
import { toNumber } from "../../../models/finance/cent-value";
import { toEmbeddable } from "../../../models/article-groups/article";
import {
  isCustomArticle,
  isStandardArticle,
} from "../../../models/job/job-article";
import Toolbar, { ToolbarProps } from "./Toolbar";
import { invoiceColumns } from "./columns";
import Footer, { FooterProps } from "./Footer";
import { useCallback, useMemo } from "react";
import { calculatedValuesForLines } from "../../../models/quote/calculations";
import { CalculatedValues } from "../../../models/invoice/calculated-values";
import { getColumnVisibilityModel } from "../../common/table/columns";

interface LinesDataGridProps {
  mode: "customer" | "installer";
  articleGroupId?: string;
  editable?: boolean;
  lines: InvoiceLineItem[];
  onUpdate: (newLines: InvoiceLineItem[], summary: CalculatedValues) => void;
  hideFooter?: boolean;
}

export default function LinesDataGrid({
  mode,
  articleGroupId,
  editable = false,
  lines,
  onUpdate,
  hideFooter = false,
}: LinesDataGridProps) {
  const calculator = useCallback(
    (lines: InvoiceLineItem[]) =>
      calculatedValuesForLines(lines, {
        includeVat: mode === "customer",
        includeDeduction: mode === "installer",
      }),
    [mode],
  );

  const toolbarProps = useMemo<ToolbarProps>(
    () => ({
      articleGroupId,
      onAddArticle: (line, article) => {
        let lineItem: InvoiceLineItem | undefined;

        if (isStandardArticle(line) && article) {
          lineItem = {
            uid: uuid(),
            article: {
              ...toEmbeddable(article),
              reference: article.reference,
              type: "standard",
            },
            workedHours: null,
            detailedDeductionType: article.detailedDeductionType ?? null,
            unitPrice: toNumber(article.prices.customerPrice.amount.value ?? 0),
            unitCount: line.quantity,
            description: article.title,
            deductionType: article.deduction ?? null,
            type: article.lineItemType,
            unitType: article.unitType,
          };
        }

        if (isCustomArticle(line)) {
          const { quantity, ...article } = line;
          lineItem = {
            uid: uuid(),
            article: {
              ...article,
              type: "custom",
            },
            workedHours: null,
            detailedDeductionType: line.detailedDeductionType ?? null,
            unitPrice: toNumber(line.prices.customerPrice.amount.value ?? 0),
            unitCount: quantity,
            description: line.title,
            deductionType: line.deduction ?? null,
            type: line.lineItemType,
            unitType: line.unitType,
          };
        }

        if (!lineItem) {
          throw new Error("Trying to add an unknown line item to invoice");
        }

        const newLines = [...lines, lineItem];

        onUpdate(newLines, calculator(newLines));
      },
    }),
    [articleGroupId, lines, onUpdate],
  );

  const footerProps = useMemo<FooterProps>(
    () => ({
      calculatedValues: calculator(lines),
    }),
    [lines],
  );

  function CustomToolbar() {
    return (
      <GridToolbarContainer>
        <Toolbar {...toolbarProps} />
      </GridToolbarContainer>
    );
  }

  function CustomFooter() {
    return (
      <GridFooterContainer>
        <Footer {...footerProps} />
      </GridFooterContainer>
    );
  }

  const invoiceColumnsWithActions = invoiceColumns({
    onDelete: (uid) => {
      const newLines = lines.filter((line) => line.uid !== uid);
      onUpdate(newLines, calculator(newLines));
    },
    onMove: (uid, direction) => {
      const currentIndex = lines.findIndex((line) => line.uid === uid);
      if (currentIndex === -1) return;
      const newIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1;
      if (newIndex < 0 || newIndex >= lines.length) return;
      const newLines = [...lines];
      const [movedLine] = newLines.splice(currentIndex, 1);
      newLines.splice(newIndex, 0, movedLine);
      onUpdate(newLines, calculator(newLines));
    },
  });

  const visibilityColumns = useMemo(
    () => getColumnVisibilityModel(invoiceColumnsWithActions),
    [],
  );
  return (
    <>
      <DataGrid
        sx={{ backgroundColor: "background.paper" }}
        autoHeight
        showCellVerticalBorder
        density="compact"
        rows={lines}
        getRowId={(row) => row.uid}
        columns={invoiceColumnsWithActions}
        isCellEditable={(cell) =>
          Boolean(editable ? cell.colDef.editable : false)
        }
        disableRowSelectionOnClick
        editMode={"row"}
        processRowUpdate={(row) => {
          // Create a new list of lines but update the line that was changed.
          const newLines = lines.map((line) => {
            if (line.uid !== row.uid) return line;
            return {
              ...line,
              ...row,
            } as InvoiceLineItem;
          });
          onUpdate(newLines, calculator(newLines));
          return row;
        }}
        initialState={{
          columns: {
            columnVisibilityModel: visibilityColumns,
          },
        }}
        slots={{
          toolbar: CustomToolbar,
          footer: CustomFooter,
        }}
        hideFooter={hideFooter}
      />
    </>
  );
}
