import { Stack } from "@mui/material";
import LinesDataGrid from "./LinesDataGrid";
import { Field } from "react-final-form";
import { InvoiceLineItem } from "../../../models/invoice/invoice-line-item";
import { isDefined } from "../../../lib/filter-undefined";
import { calculatedValuesForLines } from "../../../models/quote/calculations";
import { Invoice } from "../../../models/invoice/invoice";
import TextField from "../../form-fields/TextField";

interface InvoiceFormFieldsProps {
  name: string;
  isDraft: boolean;
}

export function InvoiceFormFields({ name, isDraft }: InvoiceFormFieldsProps) {
  return (
    <>
      <LinesDataGridField name={name} isDraft={isDraft} />
      <Stack direction={"row"} spacing={1}>
        <TextField
          disabled={!isDraft}
          name={`${name}.workDescription`}
          label="Description"
          multiline
        />
        <TextField
          disabled={!isDraft}
          name={`${name}.otherInformation`}
          label="Other info"
          multiline
        />
      </Stack>
    </>
  );
}

function LinesDataGridField({ name, isDraft }: InvoiceFormFieldsProps) {
  return (
    <Field
      name={name}
      render={({ input }) => (
        <LinesDataGrid
          lines={input.value?.lineItems ?? []}
          editable={isDraft}
          articleGroupId={input.value?.lockedArticleGroup?.id}
          onUpdate={(newLines) => {
            input.onChange(updateLinesOnInvoice(input.value, newLines));
          }}
          mode={"customer"}
        />
      )}
    />
  );
}

function updateLinesOnInvoice(invoice: Invoice, newLines: InvoiceLineItem[]) {
  // Based on the new lines, get the deductions used.
  const deductionTypes = [
    ...new Set(
      newLines.map(({ deductionType }) => deductionType).filter(isDefined),
    ),
  ];

  // Create a new invoice.
  const newInvoice: Invoice = {
    ...invoice,
    deductionTypes,
    lineItems: newLines,
    calculatedValues: calculatedValuesForLines(newLines),
  };

  return newInvoice;
}
