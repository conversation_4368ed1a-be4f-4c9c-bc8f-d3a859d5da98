import { CentValue } from "../../../models/finance/cent-value";
import { Stack } from "@mui/material";
import formatAmount from "../../../lib/format-amount";
import { CalculatedValues } from "../../../models/invoice/calculated-values";
import BaseSumRow from "../../statistics/SumRow";

interface SummaryProps {
  calculatedValues: CalculatedValues;
}

export default function Summary({ calculatedValues }: SummaryProps) {
  const {
    totalGross,
    totalNetAfterDeductions,
    vat,
    fullTaxDeduction,
    centsRounding,
  } = calculatedValues;
  return (
    <Stack>
      <SumRow title="Sum" value={totalGross} showZero />
      <SumRow title="VAT" value={vat} showZero />
      <SumRow title="Deduction" value={fullTaxDeduction} negative />
      <SumRow title="Rounding" value={centsRounding} />
      <SumRow title="To pay" value={totalNetAfterDeductions} showZero />
    </Stack>
  );
}

function SumRow({
  title,
  value = 0,
  showZero = false,
  negative = false,
}: {
  title: string;
  value?: CentValue;
  showZero?: boolean;
  negative?: boolean;
}) {
  if (value === 0 && !showZero) return null;
  return (
    <BaseSumRow
      title={title}
      value={formatAmount((value * (negative ? -1 : 1)) / 100, 2)}
    />
  );
}
