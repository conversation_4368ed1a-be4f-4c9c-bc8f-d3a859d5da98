import CompanyContainer from "../company/details/CompanyDetails";
import JobContainer from "../job/Job";
import UserContainer from "../user/UserContainer";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { <PERSON><PERSON>, Chip, Dialog } from "@mui/material";
import { DataGrid, GridCellParams } from "@mui/x-data-grid";
import { fetchUser, getFullUserName } from "../../models/user/user";
import QuickSearchToolbar from "../tables/QuickSearchToolbar";
import TableLoadingOverlay from "../tables/TableLoadingOverlay";
import { FIREBASE_URL, TEST_USER_IDS } from "../../config/constants";
import { useCollection } from "react-firebase-hooks/firestore";
import { useEffect, useState } from "react";
import { getDoc, Query } from "@firebase/firestore";
import {
  Invoice,
  InvoiceAdminStatus,
  InvoiceStatus,
} from "../../models/invoice/invoice";
import { formatDateToExport } from "../../lib/format-date-to-export";
import { formatTime } from "../../lib/format-time-relative";
import { getFileUrl } from "../../lib/get-file-url";
import formatAmount from "../../lib/format-amount";
import { jobReference } from "../../models/job/job-provider";
import { ExtendedGridColDef } from "../common/table/columns";

function escapeRegExp(value: string): string {
  return value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
}

type Colors =
  | "default"
  | "primary"
  | "secondary"
  | "error"
  | "info"
  | "success"
  | "warning";

const getColorForStatus = (status: InvoiceStatus): Colors => {
  switch (status) {
    case "draft":
      return "default";
    case "sent":
      return "info";
    case "paid":
      return "success";
    case "refuted":
      return "error";
    case "credited":
      return "warning";
    case "creditLoss":
      return "warning";
    default:
      return "default";
  }
};

const getColorForAdminStatus = (status: InvoiceAdminStatus): Colors => {
  switch (status) {
    case "pending":
      return "default";
    case "ready":
      return "info";
    case "reported":
      return "success";
    case "partial":
      return "warning";
    case "denied":
      return "error";
    case "closed":
      return "success";
    default:
      return "default";
  }
};

const InvoicesTable = ({
  queryReference,
  companyTable = false,
}: {
  queryReference: Query;
  companyTable?: boolean;
}) => {
  const [value, loading, error] = useCollection(queryReference);
  const [jobId, setJobId] = useState<any>(null);
  const [companyId, setCompanyId] = useState<any>(null);
  const [customerId, setCustomerId] = useState<any>(null);
  const [initRows, setInitRows] = useState<any>([]);
  const [rows, setRows] = useState<any>(initRows);
  const [jobDataLoading, setJobDataLoading] = useState<boolean>(true);
  const [searchText, setSearchText] = useState("");

  useEffect(() => {
    async function fetchJobData(): Promise<any[]> {
      return await Promise.all(
        value?.docs.map(async (invoiceSnap) => {
          const invoice: Invoice = {
            ...invoiceSnap.data(),
            reference: invoiceSnap.ref,
          } as Invoice;

          const jobSnap = await getDoc(jobReference(invoice.job.id));

          const job = jobSnap?.data()
            ? {
                ...jobSnap.data(),
                reference: jobSnap.ref,
              }
            : null;

          const customer = await fetchUser(job?.customer);

          return {
            id: invoice.reference.id,
            createTime: invoice.createTime?.toDate(),
            status: invoice.status,
            adminStatus: invoice.adminStatus,
            companyName: invoice.company.name ?? "",
            customerName: getFullUserName(invoice.customer) ?? "",
            jobCreateTime: job?.createTime?.toDate(),
            fixedPriceJobs: job?.fixedPriceJobs,
            services: job?.services,
            dueDate: invoice?.dueDate?.toDate(),
            totalNet: invoice.calculatedValues?.totalNet
              ? invoice.calculatedValues.totalNet / 100
              : "",
            totalNetAfterDeduction: invoice.calculatedValues
              ?.totalNetAfterDeductions
              ? invoice.calculatedValues.totalNetAfterDeductions / 100
              : "",
            viewedByCustomer: invoice.events?.viewedByCustomer?.toDate(),
            fullyPaid: invoice.events?.fullyPaid?.toDate(),
            utm_source: job?.utm_source,
            tags: job?.tags,
            pdf: invoice.pdfFilePath,
            discountCode: job?.discountCode,
            description: job?.description,
            customerEmail: customer?.email,
            customerZip: customer?.address?.zip,
            number: invoice.number,
            data: invoice,
          };
        }) ?? [],
      );
    }

    setJobDataLoading(true);

    fetchJobData().then((data: any) => {
      const filteredRowData =
        data
          .filter((e: any) => Boolean(e))
          .filter(
            (e: any) => !TEST_USER_IDS.includes(e.data?.customer?.id ?? ""),
          ) ?? [];

      setInitRows(filteredRowData);
      setRows(filteredRowData);
      setJobDataLoading(false);
    });
  }, [value]);

  if (error) return <p>{`ERROR ${error}`}</p>;

  const columns: ExtendedGridColDef[] = [
    {
      field: "createTime",
      type: "date",
      headerName: "Created at",
      width: 160,
      renderCell: (params: GridCellParams) => {
        return <p>{formatTime(params.row.createTime)}</p>;
      },
    },
    {
      field: "dueDate",
      type: "date",
      headerName: "Due date",
      width: 160,
      renderCell: (params: GridCellParams) => {
        return (
          <p
            style={{
              color:
                params.row.status != "paid" && Date.now() > params.row.dueDate
                  ? "red"
                  : "black",
            }}
          >
            {formatTime(params.row.dueDate)}
          </p>
        );
      },
    },
    {
      field: "totalNet",
      headerName: "Total net (SEK) ",
      width: 160,
      renderCell: (params: GridCellParams) => {
        return <>{formatAmount(params.row.totalNet)} </>;
      },
    },
    {
      field: "status",
      headerName: "Status",
      width: 150,
      renderCell: (params: GridCellParams) => {
        const status: InvoiceStatus = params.row.status;

        return (
          <Chip
            color={getColorForStatus(status)}
            size="small"
            label={params.row.status ?? ""}
          />
        );
      },
    },
    {
      field: "adminStatus",
      headerName: "Admin Status",
      width: 150,
      renderCell: (params: GridCellParams) => {
        const status: InvoiceAdminStatus = params.row.adminStatus;

        if (!status) return null;

        return (
          <Chip
            color={getColorForAdminStatus(status)}
            size="small"
            label={params.row.adminStatus ?? ""}
          />
        );
      },
    },
    {
      field: "viewedByCustomer",
      type: "date",
      headerName: "Viewed by customer",
      width: 160,
    },
    {
      field: "fullyPaid",
      type: "date",
      headerName: "Fully paid",
      width: 160,
    },
    {
      field: "number",
      type: "string",
      headerName: "Number",
      width: 160,
    },
    {
      field: "companyName",
      headerName: "Company",
      width: 200,
      renderCell: (params: GridCellParams) => {
        return (
          <Button
            onClick={() => {
              setCompanyId(params.row.data.company?.reference?.id);
            }}
            color="primary"
            size="small"
          >
            {params.row.companyName ?? "-"}
          </Button>
        );
      },
    },
    {
      field: "customerName",
      headerName: "Customer",
      width: 200,
      renderCell: (params: GridCellParams) => {
        return (
          <Button
            onClick={() => {
              setCustomerId(params.row.data.customer?.reference?.id);
            }}
            color="primary"
            size="small"
          >
            {params.row.customerName ?? "-"}
          </Button>
        );
      },
    },
    { field: "description", headerName: "Job description", width: 200 },
    {
      field: "job",
      headerName: "Job",
      width: 120,
      renderCell: (params: GridCellParams) => {
        return (
          <Button
            onClick={() => {
              setJobId(params.row.data.job.id);
            }}
            size="small"
            color="primary"
          >
            Job
          </Button>
        );
      },
    },
    {
      field: "file",
      headerName: "File",
      width: 120,
      renderCell: (params: GridCellParams) => {
        return (
          <Button
            color="primary"
            size="small"
            onClick={() => getFileUrl(params.row.pdf).then(window.open)}
          >
            File
          </Button>
        );
      },
    },
    {
      field: "firestore",
      headerName: "Firestore",
      width: 120,
      renderCell: (params: GridCellParams) => {
        return (
          <a
            target="_blank"
            rel="noopener noreferrer"
            href={`${FIREBASE_URL}/firestore/data/~2Finvoices~2F${params.id}`}
          >
            <Button
              style={{
                marginRight: "4px",
                color: "#f59842",
                borderColor: "#f59842",
              }}
              color="primary"
              size="small"
              startIcon={<WhatshotIcon />}
            >
              Invoice
            </Button>
          </a>
        );
      },
    },
  ];

  const requestSearch = (searchValue: string) => {
    setSearchText(searchValue);
    const searchRegex = new RegExp(escapeRegExp(searchValue), "i");
    const filteredRows = initRows.filter((row: any) => {
      return Object.keys(row).some((field: any) => {
        return searchRegex.test(row[field]?.toString() ?? "");
      });
    });
    setRows(filteredRows);
  };

  return (
    <div
      style={{
        height: `calc(${companyTable ? "50vh" : "100vh"} - 50px)`,
        width: "100%",
      }}
    >
      <Dialog
        fullWidth
        maxWidth="lg"
        open={Boolean(jobId)}
        onClose={() => {
          setJobId(null);
        }}
      >
        <Button
          style={{ width: 100 }}
          onClick={() => {
            setJobId(null);
          }}
        >
          Close
        </Button>
        {jobId && <JobContainer jobId={jobId} />}
      </Dialog>
      <Dialog
        fullWidth
        maxWidth="lg"
        open={Boolean(customerId)}
        onClose={() => {
          setCustomerId(null);
        }}
      >
        <Button
          style={{ width: 100 }}
          onClick={() => {
            setCustomerId(null);
          }}
        >
          Close
        </Button>
        {customerId && <UserContainer userId={customerId} />}
      </Dialog>
      <Dialog
        fullWidth
        maxWidth="lg"
        open={Boolean(companyId)}
        onClose={() => {
          setCompanyId(null);
        }}
      >
        <Button
          style={{ width: 100 }}
          onClick={() => {
            setCompanyId(null);
          }}
        >
          Close
        </Button>
        {companyId && <CompanyContainer companyId={companyId} />}
      </Dialog>

      <>
        <QuickSearchToolbar
          exportFileName="reviews"
          rowsToExport={rows.map((e: any) => {
            return { ...e, createTime: formatDateToExport(e.createTime) };
          })}
          value={searchText}
          onChange={(event: any) => requestSearch(event.target.value)}
          clearSearch={() => requestSearch("")}
        />
        <DataGrid
          slots={{
            // toolbar: QuickSearchToolbar,
            loadingOverlay: TableLoadingOverlay,
          }}
          disableRowSelectionOnClick
          loading={loading || jobDataLoading}
          rows={rows}
          columns={columns}
        />
      </>
    </div>
  );
};

export default InvoicesTable;
