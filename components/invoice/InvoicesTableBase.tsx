import InvoicesTable from "./InvoicesTable";
import { Tab, Tabs } from "@mui/material";
import { useState } from "react";
import {
  getFirestore,
  collection,
  Timestamp,
  query,
  Query,
  where,
  orderBy,
  CollectionReference,
} from "@firebase/firestore";
import { INVOICE_STATUSES } from "../../config/constants";
import { subDays } from "date-fns";

const StatusTabs = ({ status, setStatus }: { status: any; setStatus: any }) => (
  <Tabs
    value={INVOICE_STATUSES.findIndex((s) => s.filter === status)}
    onChange={(event, index) => setStatus(INVOICE_STATUSES[index].filter)}
  >
    {INVOICE_STATUSES.map((s) => (
      <Tab key={s.filter} label={s.label} />
    ))}
  </Tabs>
);

const InvoicesTableBase = ({
  initCollection,
  companyTable = false,
}: {
  initCollection?: Query;
  companyTable?: boolean;
}) => {
  const [status, setStatus] = useState("last30days");

  const db = getFirestore();
  let queryReference: CollectionReference | Query = collection(db, "invoices");

  if (initCollection) {
    queryReference = initCollection;
  }

  queryReference = query(
    queryReference,
    where("status", "in", [
      "sent",
      "paid",
      "refuted",
      "credited",
      "creditLoss",
    ]),
  );

  if (status == "last30days") {
    queryReference = query(
      query(
        queryReference,
        where("createTime", ">", Timestamp.fromDate(subDays(new Date(), 30))),
      ),
    );
  } else if (status.length > 0) {
    queryReference = query(queryReference, where("status", "==", status));
  }

  queryReference = query(queryReference, orderBy("createTime", "desc"));

  return (
    <>
      <StatusTabs status={status} setStatus={setStatus} />
      <InvoicesTable
        companyTable={companyTable}
        queryReference={queryReference}
      />
    </>
  );
};

export default InvoicesTableBase;
