import Link from "next/link";
import { useStorageURL } from "../../lib/hooks/use-storage-url";
import PreviewCard, { Variant } from "./PreviewCard";
import { Tooltip } from "@mui/material";

interface StorageFilePreviewProps {
  name: string;
  path: string;
  variant?: Variant;
  width?: number | string;
  height?: number | string;
}

export function StorageFilePreview({
  name,
  path,
  variant = "portrait",
  width,
  height,
}: StorageFilePreviewProps) {
  const [url] = useStorageURL(path);
  return (
    <Tooltip title={name} arrow>
      <Link href={url ?? ""} download={name}>
        <PreviewCard
          src={url}
          title={name}
          variant={variant}
          width={width}
          height={height}
        />
      </Link>
    </Tooltip>
  );
}
