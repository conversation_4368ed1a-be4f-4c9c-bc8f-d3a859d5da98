import { CardMedia, Card, Box, Typography } from "@mui/material";
import { assertNever } from "../../lib/assert-never";
import { BrokenImage } from "@mui/icons-material";
import { useEffect, useState } from "react";
export interface PreviewCardProps {
  src: string | undefined;
  title?: string;
  variant?: Variant;
  onClick?: () => void;
  width?: number | string;
  height?: number | string;
}

export type Variant = "box" | "portrait" | "full";

/**
 * Displays a preview of an image.
 */
export default function PreviewCard({
  src,
  title,
  variant = "box",
  onClick,
  width,
  height,
}: PreviewCardProps) {
  const [fallback, setFallback] = useState(src === undefined);

  useEffect(() => {
    setFallback(src === undefined);
  }, [src]);

  return (
    <Card
      variant="outlined"
      sx={{ width: width ?? widthForVariant(variant), height }}
      onClick={onClick}
    >
      {fallback ? (
        <IconCardMedia variant={variant} title={title} height={height} />
      ) : (
        <CardMedia
          sx={{
            objectFit: "contain",
          }}
          image={src}
          title={title}
          component={"img"}
          onError={() => {
            setFallback(true);
          }}
        />
      )}
    </Card>
  );
}

function IconCardMedia({
  title,
  variant,
  height: customHeight,
}: {
  title?: string;
  variant: Variant;
  height?: number | string;
}) {
  return (
    <Box
      sx={{
        height: customHeight ?? heightForVariant(variant),
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <BrokenImage fontSize="large" color="disabled" />
      <Typography variant="caption">{title}</Typography>
    </Box>
  );
}

const widthForVariant = (variant: Variant): number | undefined => {
  switch (variant) {
    case "box":
      return 80;
    case "portrait":
      return 150;
    case "full":
      return undefined;
    default:
      assertNever(variant);
  }
};

const heightForVariant = (variant: Variant): number | string | undefined => {
  switch (variant) {
    case "box":
      return 80;
    case "portrait":
      return 200;
    case "full":
      return "100vh";
    default:
      assertNever(variant);
  }
};
