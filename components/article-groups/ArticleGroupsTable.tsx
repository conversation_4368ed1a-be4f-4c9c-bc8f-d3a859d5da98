import {
  DataGrid,
  GridActionsCellItem,
  GridActionsColDef,
} from "@mui/x-data-grid";
import {
  collection,
  getFirestore,
  orderBy,
  query,
  Timestamp,
} from "firebase/firestore";
import ListAltIcon from "@mui/icons-material/ListAlt";
import CopyAllIcon from "@mui/icons-material/CopyAll";
import LibraryAddIcon from "@mui/icons-material/LibraryAdd";
import { useRouter } from "next/router";
import { useCollection } from "react-firebase-hooks/firestore";
import { Button, Grid2, Tooltip } from "@mui/material";
import {
  getEditable,
  useDocumentUpdate,
} from "../../lib/hooks/use-document-update";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import { formatDate } from "../../lib/format-time-relative";
import CreateArticleGroupDialog from "./dialogs/CreateArticleGroupDialog";
import CreateArticleGroupRevisionDialog from "./dialogs/CreateArticleGroupRevisionDialog";
import DuplicateArticleGroupDialog from "./dialogs/DuplicateArticleGroupDialog";
import PageContainer from "../layout/PageContainer";
import PageGridContainer from "../layout/PageGridContainer";
import { PlusOne } from "@mui/icons-material";
import { ExtendedGridColDef } from "../common/table/columns";

export default function ArticleGroupsTable() {
  const collectionRef = collection(getFirestore(), "articleGroups");
  const collectionQuery = query(collectionRef, orderBy("createTime", "desc"));

  const [commitUpdate, updating] = useDocumentUpdate(
    collectionRef,
    editableProps,
  );

  const [value, loading] = useCollection(collectionQuery);
  const docs = value?.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

  return (
    <DialogManager>
      <PageContainer>
        <ManagedDialog
          id={"create-group-dialog"}
          component={CreateArticleGroupDialog}
        />
        <ManagedDialog
          id={"create-revision-dialog"}
          component={CreateArticleGroupRevisionDialog}
        />
        <ManagedDialog
          id={"copy-dialog"}
          component={DuplicateArticleGroupDialog}
        />
        <PageGridContainer>
          <Grid2 size={{ xs: 1 }}>
            <NewGroupButton />
          </Grid2>
          <Grid2 container size="grow">
            <Grid2 size="grow">
              <DataGrid
                showCellVerticalBorder
                rows={docs ?? []}
                columns={columns}
                slotProps={{
                  loadingOverlay: {
                    variant: "linear-progress",
                  },
                }}
                loading={loading || updating}
                disableRowSelectionOnClick
                editMode={"row"}
                processRowUpdate={(row) => {
                  commitUpdate({ ...row });
                  return row;
                }}
              />
            </Grid2>
          </Grid2>
        </PageGridContainer>
      </PageContainer>
    </DialogManager>
  );
}

const columns: ExtendedGridColDef[] = [
  {
    field: "enabled",
    headerName: "Enabled",
    type: "boolean",
    editable: true,
  },
  {
    field: "addToAttachments",
    headerName: "Add to Attachments",
    type: "boolean",
    editable: true,
  },
  {
    field: "partner",
    headerName: "Partner",
    width: 150,
    valueFormatter: (value) => value && (value as { id: string })?.id,
  },
  {
    field: "services",
    headerName: "Services",
    width: 150,
    type: "string",
  },
  {
    field: "title",
    headerName: "Title",
    width: 200,
    type: "string",
    editable: true,
  },
  {
    field: "version",
    headerName: "Version",
    type: "number",
  },
  {
    field: "startTime",
    headerName: "Activation",
    width: 200,
    type: "date",
    editable: true,
    valueFormatter: (value: Timestamp) => formatDate(value),
  },
  {
    field: "id",
    headerName: "Actions",
    type: "actions",
    getActions: ({ row }) => [
      <EditArticlesButton key={`edit-${row.id}`} groupId={row.id} />,
      <NewVersionButton
        key={`new-${row.id}`}
        from={row.id}
        partner={row.partner?.id}
        title={row.title}
      />,
      <CopyGroupButton
        key={`copy-${row.id}`}
        from={row.id}
        title={row.title}
        partner={row.partner?.id}
      />,
    ],
  } as GridActionsColDef,
];

const editableProps = getEditable(columns);

function EditArticlesButton({ groupId }: { groupId: string }) {
  const router = useRouter();
  return (
    <GridActionsCellItem
      icon={
        <Tooltip title="Show articles" arrow>
          <ListAltIcon />
        </Tooltip>
      }
      onClick={() => {
        router.push(
          `/price-lists/legacy/articles?group=${encodeURIComponent(groupId)}`,
        );
      }}
      label="Edit Articles"
    />
  );
}

function CopyGroupButton(props: {
  from: string;
  title: string;
  partner?: string;
}) {
  const { open } = useDialog("copy-dialog");
  return (
    <GridActionsCellItem
      icon={
        <Tooltip title="Duplicate" arrow>
          <CopyAllIcon />
        </Tooltip>
      }
      onClick={() => {
        open(props);
      }}
      label="Copy Group"
    />
  );
}

function NewVersionButton(props: {
  from: string;
  partner: string;
  title: string;
}) {
  const { open } = useDialog("create-revision-dialog");
  return (
    <GridActionsCellItem
      icon={
        <Tooltip title="New version" arrow>
          <LibraryAddIcon />
        </Tooltip>
      }
      onClick={() => {
        open(props);
      }}
      label="Create new version"
    />
  );
}

function NewGroupButton() {
  const { open } = useDialog("create-group-dialog");
  return (
    <Button
      size="small"
      startIcon={<PlusOne />}
      variant="outlined"
      color="primary"
      onClick={() => open()}
      style={{ textTransform: "none", padding: "3px 8px" }} //button Size change in React Material Ui
    >
      New price list
    </Button>
  );
}
