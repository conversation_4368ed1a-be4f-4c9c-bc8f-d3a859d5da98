import { DataGrid } from "@mui/x-data-grid";
import { deleteField } from "firebase/firestore";
import { useCollectionData } from "react-firebase-hooks/firestore";
import {
  getEditable,
  useDocumentUpdate,
} from "../../lib/hooks/use-document-update";
import { flattenInput } from "../../lib/flatten-input";
import { ArticleGroupType } from "../../models/article-groups/article-group";
import { useMemo } from "react";
import { columnsForType, ColumnVisibility } from "./article-columns";
import {
  articlesCollectionRef,
  articlesQuery,
} from "../../models/article-groups/article";
import { CalculationOptions } from "../../models/quote/calculation-options";
import { getColumnVisibilityModel } from "../common/table/columns";

interface Props {
  groupId: string;
  type: ArticleGroupType;
  visibility?: ColumnVisibility;
  calculationOptions?: CalculationOptions;
  onSelectionChange?: (ids: string[]) => void;
}

export default function ArticlesTable({
  groupId,
  type,
  visibility,
  calculationOptions,
  onSelectionChange,
}: Props) {
  const [articles, loading] = useCollectionData(articlesQuery(groupId));
  const docs = articles?.map((doc) => ({ id: doc.reference.id, ...doc }));

  const columns = useMemo(
    () => columnsForType(type, calculationOptions, visibility),
    [type, calculationOptions, visibility],
  );

  const editableProps = useMemo(() => {
    const baseFields = getEditable(columns);
    return [
      ...baseFields,
      ...priceFields(baseFields.find((p) => p === "prices.craftsmanProceeds")),
      ...priceFields(baseFields.find((p) => p === "prices.customerPrice")),
      ...priceFields(baseFields.find((p) => p === "prices.partnerPrice")),
    ];
  }, [columns]);

  const [commitUpdate, updating] = useDocumentUpdate(
    articlesCollectionRef(groupId),
    editableProps,
  );

  const visibilityColumns = useMemo(
    () => getColumnVisibilityModel(columns),
    [type, visibility],
  );

  return (
    <DataGrid
      sx={{
        border: "none",
        "&.MuiDataGrid-root .MuiDataGrid-cell:focus-within": {
          outline: "none !important",
        },
        "&.MuiDataGrid-root .MuiDataGrid-row:not(:hover)": {
          backgroundColor: "background.paper",
        },
        "&.MuiDataGrid-root .MuiDataGrid-footerContainer": {
          backgroundColor: "background.paper",
        },
      }}
      rows={docs ?? []}
      density="compact"
      columns={columns}
      slotProps={{
        loadingOverlay: {
          variant: "linear-progress",
        },
      }}
      loading={loading || updating}
      editMode={"row"}
      onRowSelectionModelChange={(newSelection) => {
        onSelectionChange?.(newSelection as string[]);
      }}
      processRowUpdate={(row) => {
        const data: { [key: string]: unknown } = flattenInput(row);
        // Prices are sensitive so we need to remove any undefined values from the database.
        const edited = Object.entries(data).reduce((acc, [key, value]) => {
          if (!key.startsWith("prices") || value !== undefined) {
            return { ...acc, [key]: value };
          }
          return { ...acc, [key]: deleteField() };
        }, {});

        commitUpdate(edited);
        return edited;
      }}
      initialState={{
        columns: {
          columnVisibilityModel: visibilityColumns,
        },
      }}
    />
  );
}

/** Returns price fields for a path if it's set. Used for whitelisting edits. */
function priceFields(path: string | undefined) {
  if (!path) return [];
  return [`${path}.amount.percentage`, `${path}.amount.value`];
}
