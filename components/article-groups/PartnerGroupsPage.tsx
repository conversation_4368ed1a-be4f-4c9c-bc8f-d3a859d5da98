import { useState, useRef } from "react";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import { Content, Header, MenuPanel, MainPanel } from "../layout/panels";
import { articleGroupsQuery } from "../../models/article-groups/repository";
import { orderBy, where } from "firebase/firestore";
import { useCollectionData } from "react-firebase-hooks/firestore";
import { ArticleGroup } from "../../models/article-groups/article-group";
import {
  Button,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Tooltip,
} from "@mui/material";
import {
  Fullscreen,
  FullscreenExit,
  Schedule,
  EditNote,
  Public,
  Settings,
} from "@mui/icons-material";
import { FirestoreModel } from "../../models/firestore_model";
import ArticlesTable from "./ArticlesTable";
import { useSelectedModel } from "../../lib/hooks/use-selected-model";
import EmptyState from "../empty-state/EmptyState";
import { formatDate, formatTime } from "../../lib/format-time-relative";
import { useAuthUser } from "../auth/AuthProvider";
import ManagedDialog from "../dialogs/ManagedDialog";
import EditArticleGroupDialog from "./dialogs/EditArticleGroupDialog";
import { PartnerTier } from "../../models/other/partner";
import {
  PanelGroup,
  PanelResizeHandle,
  ImperativePanelHandle,
} from "react-resizable-panels";

export default function PartnerGroupsPage() {
  const { partnerDoc } = useAuthUser();
  const [fullscreen, setFullscreen] = useState(false);
  const menuPanelRef = useRef<ImperativePanelHandle>(null);

  const [groups, _, error] = useCollectionData(
    articleGroupsQuery(
      where("partner", "==", partnerDoc?.reference),
      where("type", "==", "branch"),
      where("enabled", "==", true),
      orderBy("createTime", "desc"),
    ),
  );

  const [selectedGroup, setSelectedGroup] = useSelectedModel(groups, "group");

  const toggleFullscreen = () => {
    if (fullscreen) {
      menuPanelRef.current?.expand();
    } else {
      menuPanelRef.current?.collapse();
    }
    setFullscreen(!fullscreen);
  };

  return (
    <DialogManager>
      <ManagedDialog
        id={"edit-group-dialog"}
        component={EditArticleGroupDialog}
      />
      <PanelGroup autoSaveId="partner-groups" direction="horizontal">
        <MenuPanel ref={menuPanelRef} collapsible>
          <Header title={"Price lists"} />
          <Content>
            {groups?.length ? null : <EmptyState error={Boolean(error)} />}
            <ArticleGroupsList
              groups={groups}
              onSelected={(group) => setSelectedGroup(group)}
              selected={selectedGroup}
            />
          </Content>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          <MainPanelContent
            group={selectedGroup}
            fullscreen={fullscreen}
            setFullscreen={toggleFullscreen}
          />
        </MainPanel>
      </PanelGroup>
    </DialogManager>
  );
}

function ArticleGroupsList({
  groups,
  selected,
  onSelected,
}: {
  groups?: (ArticleGroup & FirestoreModel)[];
  selected?: ArticleGroup & FirestoreModel;
  onSelected?: (group: ArticleGroup & FirestoreModel) => void;
}) {
  if (!groups) {
    return null;
  }

  return (
    <List dense disablePadding>
      {groups.map((group) => (
        <ListItem disablePadding divider key={group.reference.id}>
          <ListItemButton
            onClick={() => {
              onSelected?.(group);
            }}
            selected={group.reference.id === selected?.reference.id}
          >
            <ListItemText
              primary={group.title}
              secondary={formatDate(group.startTime?.toDate())}
            />
          </ListItemButton>
        </ListItem>
      ))}
    </List>
  );
}

function MainPanelContent({
  group,
  fullscreen,
  setFullscreen,
}: {
  group?: ArticleGroup & FirestoreModel;
  fullscreen?: boolean;
  setFullscreen?: (fullscreen: boolean) => void;
}) {
  const { partnerDoc } = useAuthUser();
  if (!group) {
    return null;
  }
  return (
    <>
      <Header
        title={group.title}
        titleSecondary={
          <>
            <Chip
              label={`v${group.version}`}
              size="small"
              variant="outlined"
              color="info"
            />
            <StatusBadge group={group} />
          </>
        }
      >
        <EditGroupButton groupId={group.reference.id} group={group} />
        <Tooltip title={fullscreen ? "Minimize" : "Maximize"} arrow>
          <IconButton onClick={() => setFullscreen?.(!fullscreen)}>
            {fullscreen ? <FullscreenExit /> : <Fullscreen />}
          </IconButton>
        </Tooltip>
      </Header>
      <Content>
        <ArticlesTable
          visibility={{
            internal: false,
            tier: partnerDoc?.tier ?? PartnerTier.None,
            customerPrices: true,
            installerPrices: false,
          }}
          groupId={group.reference.id}
          type={group.type}
          calculationOptions={group.calculationOptions}
        />
      </Content>
    </>
  );
}

function StatusBadge({ group }: { group: ArticleGroup }) {
  if (!group.enabled) {
    return <Chip icon={<EditNote />} label="Draft" size="small" />;
  }

  if (group.startTime && group.startTime.toDate() > new Date()) {
    return (
      <Tooltip
        title={`Scheduled for release at ${formatTime(
          group.startTime.toDate(),
        )}`}
        arrow
      >
        <Chip
          icon={<Schedule />}
          label="Scheduled"
          color="warning"
          size="small"
        />
      </Tooltip>
    );
  }

  return (
    <>
      <Tooltip
        title={
          group.startTime
            ? `Live since ${formatTime(group.startTime.toDate())}`
            : "One or more branches are live"
        }
        arrow
      >
        <Chip icon={<Public />} label="Live" color="error" size="small" />
      </Tooltip>
      <Chip
        label="Changes will not affect existing quotes"
        color="default"
        size="small"
        variant="outlined"
      />
    </>
  );
}

function EditGroupButton({
  groupId,
  group,
  disabled,
}: {
  groupId: string;
  group: ArticleGroup;
  disabled?: boolean;
}) {
  const { open } = useDialog("edit-group-dialog");
  return (
    <Button
      startIcon={<Settings />}
      onClick={() => open({ group, groupId })}
      disabled={disabled}
    >
      Configure
    </Button>
  );
}
