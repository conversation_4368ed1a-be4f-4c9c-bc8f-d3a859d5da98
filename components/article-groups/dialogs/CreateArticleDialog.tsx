import { collection, doc, serverTimestamp, setDoc } from "firebase/firestore";
import removeUndefined from "../../../lib/remove-undefined";
import { Article, ArticleInput } from "../../../models/article-groups/article";
import { articleGroupRef } from "../../../models/article-groups/article-group";
import FormDialog from "../../dialogs/FormDialog";
import TextField from "../../form-fields/TextField";
import ArticlesFormFields from "../forms/ArticleFormFields";

interface CreateArticleDialogProps {
  isOpen: boolean;
  close: () => void;
  groupId?: string;
}

const initialValues: Partial<Article & { id: string }> = {
  deduction: "greenTechnology",
  lineItemType: "material",
  unitType: "pcs",
};

function parseId(value?: string) {
  return value
    ?.replace(/[ _]/g, "-")
    .replace(/[^\w-]/g, "")
    .toLowerCase();
}

export default function CreateArticleDialog({
  isOpen,
  close,
  groupId,
}: CreateArticleDialogProps) {
  return (
    <FormDialog
      title={"New article"}
      isOpen={isOpen}
      close={close}
      initialValues={initialValues}
      allowKeepOpen
      keepOpenText="Create more"
      onSubmit={async (values, form) => {
        if (!groupId) throw new Error("GroupId is required");
        const {
          id,
          title,
          description,
          deduction,
          unitType,
          lineItemType,
          prices,
        } = values;

        if (!id) throw new Error("Id is required");

        const article: ArticleInput = {
          createTime: serverTimestamp(),
          enabled: false,
          title,
          description,
          deduction,
          unitType,
          lineItemType,
          order: null,
          prices: {
            customerPrice: prices.customerPrice ?? { amount: {} },
            craftsmanProceeds: prices.craftsmanProceeds ?? {
              amount: {},
            },
            partnerPrice: prices.partnerPrice ?? { amount: {} },
          },
          fieldsConfig: {
            allowUnitCountEditing: true,
          },
          attachment: {
            enabled: false,
          },
        };

        await setDoc(
          doc(collection(articleGroupRef(groupId), "articles"), id),
          removeUndefined(article),
        );

        form.initialize({ deduction, lineItemType, unitType });
      }}
    >
      <TextField
        name="id"
        parser={parseId}
        required
        maxLength={50}
        type="text"
        label="Id/SKU"
        helperText="Used to reference this article and cannot be changed later."
      />
      <ArticlesFormFields />
    </FormDialog>
  );
}
