import { TextField } from "@mui/material";
import { DocumentReference } from "firebase/firestore";
import { Field } from "react-final-form";
import {
  ArticleGroup,
  articleGroupRef,
} from "../../../models/article-groups/article-group";
import FormDialog from "../../dialogs/FormDialog";
import { createNewArticleGroupRevision } from "./article-group-operations";

interface CreateArticleGroupRevisionDialogProps {
  isOpen: boolean;
  close: () => void;
  from?: string;
  title?: string;
  partner?: string;
}

export default function CreateArticleGroupRevisionDialog({
  isOpen,
  close,
  from,
  title,
  partner,
}: CreateArticleGroupRevisionDialogProps) {
  return (
    <FormDialog
      title={"New version"}
      description={
        "Create a new version of the price list. Articles will be copied from the previous version."
      }
      isOpen={isOpen}
      close={close}
      initialValues={{ title, partner }}
      onSubmit={async (values) => {
        const { title } = values;
        if (!from) throw new Error("Missing reference to previous version");
        await createNewArticleGroupRevision({
          title,
          from: articleGroupRef(from) as DocumentReference<ArticleGroup>,
        });
      }}
    >
      <Field
        name="partner"
        render={({ input }) => (
          <TextField
            {...input}
            disabled
            fullWidth
            type="text"
            label="Partner"
            variant="outlined"
          />
        )}
      />
      <Field
        name="title"
        render={({ input }) => (
          <TextField
            {...input}
            required
            fullWidth
            type="text"
            label="Title"
            variant="outlined"
          />
        )}
      />
    </FormDialog>
  );
}
