import { TextField } from "@mui/material";
import { Field } from "react-final-form";
import FormDialog from "../../dialogs/FormDialog";
import { duplicateArticleGroup } from "../../../models/article-groups/duplicate-article-group";

interface DuplicateArticleGroupDialogProps {
  isOpen: boolean;
  close: () => void;
  title?: string;
  from?: string;
  partner?: string;
}

export default function DuplicateArticleGroupDialog({
  isOpen,
  close,
  title,
  from,
  partner,
}: DuplicateArticleGroupDialogProps) {
  return (
    <FormDialog
      title={"Duplicate price list"}
      description={
        "Create a new price list with the same articles as the selected price list."
      }
      isOpen={isOpen}
      close={close}
      initialValues={{ title, partner: "" }}
      onSubmit={async (values) => {
        const { title } = values;
        if (!from) throw new Error("Missing reference to previous version");
        await duplicateArticleGroup(from, {
          title,
        });
      }}
    >
      <Field
        name="title"
        render={({ input }) => (
          <TextField
            {...input}
            required
            fullWidth
            type="text"
            label="Title"
            variant="outlined"
          />
        )}
      />
    </FormDialog>
  );
}
