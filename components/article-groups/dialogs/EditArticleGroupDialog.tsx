import {
  ArticleGroup,
  articleGroupRef,
} from "../../../models/article-groups/article-group";
import FormDialog from "../../dialogs/FormDialog";
import ArticleGroupFormFields from "../forms/ArticleGroupFormFields";
import removeUndefined from "../../../lib/remove-undefined";
import { updateDoc } from "firebase/firestore";
import { useAuthUser } from "../../auth/AuthProvider";
import { partnerRef, PartnerTier } from "../../../models/other/partner";
import PartnerSelectField from "../../form-fields/PartnerSelectField";
import { Alert, AlertTitle, Divider } from "@mui/material";
import { FormSpy } from "react-final-form";
import GlobalSearchField from "../../form-fields/GlobalSearchField";
import Internal from "../../auth/Internal";
import { useEffect, useState } from "react";
import {
  Company,
  companyRef,
  fetchCompaniesById,
} from "../../../models/company/company";

interface EditArticleGroupDialogProps {
  groupId?: string;
  group?: ArticleGroup;
  isOpen: boolean;
  close: () => void;
}

function useCompanies(ids: string[] | undefined, isOpen: boolean) {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Reset companies when dialog closes
    if (!isOpen) {
      setCompanies([]);
      return;
    }

    // Create a stable reference to the ids array
    const currentIds = ids ? [...ids] : [];

    // Only fetch if dialog is open and we have ids
    if (currentIds.length === 0) return;

    let isMounted = true;
    setLoading(true);

    fetchCompaniesById(currentIds)
      .then((fetchedCompanies) => {
        if (isMounted) {
          setCompanies(fetchedCompanies);
          setLoading(false);
        }
      })
      .catch((error) => {
        console.error("Error fetching companies:", error);
        if (isMounted) {
          setLoading(false);
        }
      });

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [isOpen, ids ? ids.join(",") : ""]); // Use a string join to create a stable dependency

  return [companies, loading] as const;
}

export default function EditArticleGroupDialog({
  groupId,
  group,
  isOpen,
  close,
}: EditArticleGroupDialogProps) {
  const { partnerDoc } = useAuthUser();
  const [companies] = useCompanies(
    group?.companies?.map((c) => c.id),
    isOpen,
  );

  return (
    <FormDialog
      title={"Edit price list"}
      isOpen={isOpen}
      close={close}
      initialValues={{
        ...group,
        startTime: group?.startTime?.toDate(),
        network: group?.network?.id,
        companies:
          companies.length > 0
            ? companies.map((c) => ({ id: c.reference.id, title: c.name }))
            : [],
      }}
      onSubmit={async (values) => {
        const {
          title,
          partner,
          name,
          network,
          companies,
          enabled,
          startTime,
          addToAttachments,
          calculationOptions,
        } = values;
        if (!groupId || !group)
          throw new Error("Missing reference to previous version");

        const ref = articleGroupRef(groupId);

        if ((partnerDoc?.tier ?? PartnerTier.None) < PartnerTier.Platform) {
          await updateDoc(
            ref,
            "calculationOptions.useSwedishGreenTechnologyStandardDeduction",
            calculationOptions?.useSwedishGreenTechnologyStandardDeduction ??
              false,
          );
          return;
        }

        await updateDoc(
          ref,
          removeUndefined({
            title,
            name,
            partner,
            network: network ? partnerRef(network) : null,
            companies: companies?.length
              ? companies?.map((company) => companyRef(company.id))
              : null,
            enabled,
            startTime,
            addToAttachments,
            calculationOptions,
          }),
        );
      }}
    >
      {group?.enabled && (
        <Alert severity="warning">
          <AlertTitle>Active price list</AlertTitle>
          This price list is currently active and may already be applied to
          orders.
        </Alert>
      )}
      <Internal>
        <PartnerSelectField required title="Partner" name="partner" />
      </Internal>
      <PartnerSelectField title="Network" name="network" />
      <FormSpy>
        {({ values }) => (
          <GlobalSearchField
            indexes={["companies"]}
            partner={values.network}
            label="Installers"
            multiple
            name="companies"
            disabled={!values.network}
          />
        )}
      </FormSpy>

      <Divider />
      <ArticleGroupFormFields />
    </FormDialog>
  );
}
