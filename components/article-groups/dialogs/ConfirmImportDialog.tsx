import { DialogContentText } from "@mui/material";
import ConfirmDialog, { ConfirmActions } from "../../dialogs/ConfirmDialog";
import RevisionIsActiveWarning from "../alerts/RevisionIsActiveWarning";

interface ConfirmImportDialog {
  isGroupActive: boolean;
  isOpen: boolean;
  close: () => void;
  onImport: () => void;
}

export default function ConfirmImportDialog({
  isGroupActive,
  onImport,
  isOpen,
  close,
}: ConfirmImportDialog) {
  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={close}
      title={"Confirm import"}
      actions={[ConfirmActions.cancel, { title: "Import", onClick: onImport }]}
    >
      <DialogContentText>
        Are you sure you want to import articles?
        <br />
        Existing articles will be updated.
      </DialogContentText>
      <RevisionIsActiveWarning show={isGroupActive} />
    </ConfirmDialog>
  );
}
