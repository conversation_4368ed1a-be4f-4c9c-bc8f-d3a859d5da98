import { updateDoc } from "firebase/firestore";
import { Attachment, Lock, Add } from "@mui/icons-material";
import {
  ArticleJobAttachment,
  articleRef,
  AttachmentConfig,
  sanitizeAttachment,
} from "../../../models/article-groups/article";
import { DialogProps } from "../../dialogs/default-dialog-props";
import FormDialog from "../../dialogs/FormDialog";
import removeUndefined from "../../../lib/remove-undefined";
import { Button, Grid2, List, Tooltip, Typography } from "@mui/material";
import AttachmentListItem from "../../job/attachments/AttachmentListItem";
import EmptyState from "../../empty-state/EmptyState";
import { Field } from "react-final-form";
import { useState } from "react";
import { v4 as uuid } from "uuid";
import AttachmentDetailsForm from "../forms/AttachmentDetailsForm";

interface ArticleAttachmentSettingsDialogProps extends DialogProps {
  groupId?: string;
  articleId?: string;
  articleName?: string;
  defaultConfig?: AttachmentConfig;
  defaultAttachments?: ArticleJobAttachment[];
}

export default function ArticleAttachmentSettingsDialog({
  groupId,
  articleId,
  articleName,
  defaultConfig,
  defaultAttachments,
  isOpen,
  close,
}: ArticleAttachmentSettingsDialogProps) {
  const [selectedIndex, setSelectedIndex] = useState<number>();

  return (
    <FormDialog
      maxWidth="md"
      fullWidth
      title={`${articleName ?? "Article"} field settings`}
      isOpen={isOpen}
      close={() => {
        close();
        setSelectedIndex(undefined);
      }}
      initialValues={{
        quantityToAttach: defaultConfig?.quantityToAttach,
        matchRegExpPattern: defaultConfig?.matchRegExp?.pattern,
        jobAttachments: defaultAttachments,
      }}
      onSubmit={async (values) => {
        if (!groupId || !articleId) return;
        const ref = articleRef(groupId, articleId);
        await updateDoc(ref, {
          attachment: removeUndefined({
            enabled: Boolean(
              values.quantityToAttach || values.matchRegExpPattern,
            ),
            quantityToAttach: values.quantityToAttach,
            matchRegExp: values.matchRegExpPattern
              ? {
                  pattern: values.matchRegExpPattern,
                }
              : undefined,
          }),
          jobAttachments: values.jobAttachments.map(sanitizeAttachment),
        });
      }}
    >
      <Grid2 container spacing={2} sx={{ minHeight: 300 }}>
        <Grid2 size={{ xs: 12, md: 6 }}>
          <Typography variant="h6">Attachments</Typography>
          <AttachmentList
            selected={selectedIndex}
            onSelect={setSelectedIndex}
          />
          <AddAttachmentButton
            onAddAttachment={(index) => setSelectedIndex(index)}
          />
        </Grid2>
        <Grid2 size={{ xs: 12, md: 6 }}>
          <Typography variant="h6">Details</Typography>
          <AttachmentDetailsForm
            name="jobAttachments"
            index={selectedIndex}
            onDelete={() => setSelectedIndex(undefined)}
          />
        </Grid2>
      </Grid2>
    </FormDialog>
  );
}

const defaultAttachments: ArticleJobAttachment[] = [];

function LockedIcon() {
  return (
    <Tooltip title="Inherited from root article" arrow>
      <Lock color="disabled" />
    </Tooltip>
  );
}

function AttachmentList({
  onSelect,
  selected,
}: {
  selected?: number;
  onSelect?: (index: number) => void;
}) {
  return (
    <Field name="jobAttachments" defaultValue={defaultAttachments}>
      {({ input }) => {
        if (!input.value?.length)
          return (
            <EmptyState
              title={"No attachments added"}
              icon={<Attachment />}
              sx={{ height: 100 }}
            />
          );
        return (
          <List dense>
            {input.value?.map((attachment, index) => (
              <AttachmentListItem
                key={attachment.id}
                title={attachment.title}
                type={attachment.type}
                description={attachment.description}
                onClick={() => onSelect?.(index)}
                selected={index === selected}
                secondaryAction={
                  attachment.source === "root" ? <LockedIcon /> : null
                }
              />
            ))}
          </List>
        );
      }}
    </Field>
  );
}

function AddAttachmentButton({
  onAddAttachment,
}: {
  onAddAttachment?: (index: number) => void;
}) {
  return (
    <Field name="jobAttachments" defaultValue={defaultAttachments}>
      {({ input }) => {
        return (
          <Button
            startIcon={<Add />}
            onClick={() => {
              const attachment: ArticleJobAttachment = {
                id: uuid(),
                source: "self",
                type: "note",
                title: "New attachment",
                description: "",
              };
              const newList = [...input.value, attachment];
              input.onChange(newList);
              onAddAttachment?.(newList.length - 1);
            }}
          >
            New attachment
          </Button>
        );
      }}
    </Field>
  );
}
