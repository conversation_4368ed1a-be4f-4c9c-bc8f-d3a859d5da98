import {
  getFirestore,
  doc,
  serverTimestamp,
  writeBatch,
  collection,
  addDoc,
  getDoc,
  getDocs,
  DocumentReference,
  updateDoc,
  DocumentData,
} from "firebase/firestore";
import removeUndefined from "../../../lib/remove-undefined";
import {
  ArticleGroup,
  ArticleGroupInput,
} from "../../../models/article-groups/article-group";

/**
 * Copy all articles from one group to another.
 */
export async function copyArticles({
  from,
  to,
}: {
  from: DocumentReference;
  to: DocumentReference;
}) {
  const db = getFirestore();
  const articles = await getDocs(collection(from, "articles"));

  if (articles.empty) return;
  if (articles.docs.length > 500)
    throw Error(
      "Copying price lists with more than 500 articles is not supported yet",
    );

  const batch = writeBatch(db);
  articles.forEach((article) => {
    batch.set(doc(collection(to, "articles"), article.id), {
      ...article.data(),
      createTime: serverTimestamp(),
    });
  });

  await batch.commit();
}

/**
 * Creates a new article group revision.
 */
export async function createNewArticleGroupRevision({
  from,
  title,
}: {
  from: DocumentReference<ArticleGroup>;
  title: string;
}) {
  const db = getFirestore();
  const originGroup = await getDoc<ArticleGroup, DocumentData>(from);

  if (!originGroup.exists()) {
    throw new Error(`Article group does not exist`);
  }

  const originData = originGroup.data();

  if (originData.replacedBy) {
    throw new Error(
      `Article group have already been replaced by another group`,
    );
  }

  const groupInput: ArticleGroupInput = {
    ...originData,
    createTime: serverTimestamp(),
    title,
    enabled: false,
    version: originData.version + 1,
    replaces: originGroup.ref,
  };

  const newGroup = await addDoc(
    collection(db, "articleGroups"),
    removeUndefined(groupInput),
  );

  await updateDoc(originGroup.ref, {
    replacedBy: newGroup,
  });

  await copyArticles({ from: originGroup.ref, to: newGroup });
}
