import { ArticleGroup } from "../../../models/article-groups/article-group";
import FormDialog from "../../dialogs/FormDialog";
import PartnerSelectField from "../../form-fields/PartnerSelectField";
import ArticleGroupFormFields from "../forms/ArticleGroupFormFields";
import { FirestoreModel } from "../../../models/firestore_model";
import Internal from "../../auth/Internal";
import { Divider } from "@mui/material";
import { FormSpy } from "react-final-form";
import GlobalSearchField from "../../form-fields/GlobalSearchField";
import { duplicateArticleGroup } from "../../../models/article-groups/duplicate-article-group";

interface BranchArticleGroupDialogProps {
  isOpen: boolean;
  close: () => void;
  from?: ArticleGroup & FirestoreModel;
}

export default function BranchArticleGroupDialog({
  isOpen,
  close,
  from,
}: BranchArticleGroupDialogProps) {
  return (
    <FormDialog
      title={"Branch price list"}
      description={
        "Create a new branch of the price list with separate settings."
      }
      isOpen={isOpen}
      close={close}
      initialValues={{
        title: from?.title,
        name: from?.name,
        partner: from?.partner?.id,
        network: from?.network?.id,
        companies: [] as { id: string; title: string }[],
        enabled: false,
        startTime: undefined,
        addToAttachments: true,
        calculationOptions: {
          useSwedishGreenTechnologyStandardDeduction:
            from?.calculationOptions
              ?.useSwedishGreenTechnologyStandardDeduction ?? false,
        },
      }}
      onSubmit={async (values) => {
        if (!from) throw new Error("Missing reference to previous version");

        const input = {
          ...values,
          type: "branch",
          companies: values.companies.map((company) => company.id),
        };

        await duplicateArticleGroup(from.reference.id, input);
      }}
    >
      <Internal>
        <PartnerSelectField required title="Partner" name="partner" />
      </Internal>
      <PartnerSelectField title="Network" name="network" />
      <FormSpy>
        {({ values }) => (
          <GlobalSearchField
            indexes={["companies"]}
            partner={values.network}
            label="Installers"
            multiple
            name="companies"
            disabled={!values.network}
          />
        )}
      </FormSpy>

      <Divider />
      <ArticleGroupFormFields />
    </FormDialog>
  );
}
