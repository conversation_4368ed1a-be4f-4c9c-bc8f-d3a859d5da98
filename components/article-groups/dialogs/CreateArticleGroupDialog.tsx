import {
  addDoc,
  collection,
  getFirestore,
  serverTimestamp,
} from "firebase/firestore";
import removeUndefined from "../../../lib/remove-undefined";
import {
  ArticleGroupInput,
  ArticleGroupType,
} from "../../../models/article-groups/article-group";
import FormDialog from "../../dialogs/FormDialog";
import PartnerSelectField from "../../form-fields/PartnerSelectField";
import ServicesField from "../../form-fields/ServicesField";
import Internal from "../../auth/Internal";
import { partnerRef } from "../../../models/other/partner";
import { useAuthUser } from "../../auth/AuthProvider";
import { ArticleGroupBasicFields } from "../forms/ArticleGroupFormFields";

interface CreateArticleGroupDialogProps {
  isOpen: boolean;
  close: () => void;
  type?: ArticleGroupType;
}

export default function CreateArticleGroupDialog({
  isOpen,
  close,
  type = "standalone",
}: CreateArticleGroupDialogProps) {
  const { partner: authPartner } = useAuthUser();

  return (
    <FormDialog
      title={"New price list"}
      description={"Create a new empty price list."}
      isOpen={isOpen}
      close={close}
      onSubmit={async (values) => {
        const { title, name, partner: formPartner, services } = values;

        // If the current user is a partner, this group belongs to that partner.
        const partner = authPartner || formPartner;

        const group: ArticleGroupInput = {
          createTime: serverTimestamp(),
          title,
          name,
          type,
          version: 1,
          enabled: false,
          addToAttachments: true,
          partner: partner && partnerRef(partner),
          services,
          calculationOptions: {
            useSwedishGreenTechnologyStandardDeduction: false,
          },
        };

        await addDoc(
          collection(getFirestore(), "articleGroups"),
          removeUndefined(group),
        );
      }}
    >
      <Internal>{type !== "root" && <PartnerSelectField />}</Internal>
      <ServicesField />
      <ArticleGroupBasicFields />
    </FormDialog>
  );
}
