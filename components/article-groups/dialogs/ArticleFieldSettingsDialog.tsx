import { updateDoc } from "firebase/firestore";
import { Checkboxes } from "mui-rff";
import {
  articleRef,
  FieldsConfig,
} from "../../../models/article-groups/article";
import { DialogProps } from "../../dialogs/default-dialog-props";
import FormDialog from "../../dialogs/FormDialog";

interface ArticleFieldSettingsDialogProps extends DialogProps {
  groupId?: string;
  articleId?: string;
  articleName?: string;
  defaultConfig?: FieldsConfig;
}

export default function ArticleFieldSettingsDialog({
  groupId,
  articleId,
  articleName,
  defaultConfig,
  isOpen,
  close,
}: ArticleFieldSettingsDialogProps) {
  return (
    <FormDialog
      title={`${articleName ?? "Article"} editable fields`}
      description={"Set which fields can be edited by installer"}
      isOpen={isOpen}
      close={close}
      initialValues={defaultConfig}
      onSubmit={async (values) => {
        if (!groupId || !articleId) return;
        const ref = articleRef(groupId, articleId);
        await updateDoc(ref, {
          fieldsConfig: values,
        });
      }}
    >
      <Checkboxes
        name="allowDeductionSelection"
        data={{ label: "Allow editing deduction", value: false }}
      />
      <Checkboxes
        name="allowDescriptionEditing"
        data={{ label: "Allow editing description", value: false }}
      />
      <Checkboxes
        name="allowUnitTypeEditing"
        data={{ label: "Allow editing unit type", value: false }}
      />
      <Checkboxes
        name="allowUnitPriceEditing"
        data={{ label: "Allow editing price", value: false }}
      />
      <Checkboxes
        name="allowUnitCountEditing"
        data={{ label: "Allow editing unit count", value: false }}
      />
    </FormDialog>
  );
}
