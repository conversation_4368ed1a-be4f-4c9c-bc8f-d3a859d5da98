import { <PERSON><PERSON>, <PERSON>, Stack, Tooltip, Typography } from "@mui/material";
import { EditNote, Attachment, Lock } from "@mui/icons-material";
import {
  GridActionsColDef,
  GridActionsCellItem,
  GridCellParams,
  GridColumnHeaderParams,
} from "@mui/x-data-grid";
import {
  DEDUCTION_TYPES,
  DETAILED_DEDUCTION_TYPES,
  LINE_ITEM_TYPES,
  LINE_ITEM_UNIT_TYPE,
} from "../../config/constants";
import { formatPercentage } from "../../lib/format-percentage";
import {
  isPercentageAmount,
  isCentAmount,
  AmountBreakdown,
  AttachmentConfig,
  FieldsConfig,
  Article,
  ArticleJobAttachment,
} from "../../models/article-groups/article";
import { havePrices, doneProceeds } from "../../models/article-groups/proceeds";
import {
  ColumnCreator,
  dotPropColumn,
  booleanColumn,
  numberColumn,
  stringColumn,
  labeledV<PERSON><PERSON><PERSON><PERSON>umn,
  ExtendedGridColDef,
} from "../common/table/columns";
import AmountEditCell from "../common/table/custom/AmountEditCell";
import { formatCentAmount } from "../common/table/utils";
import { useDialog } from "../dialogs/DialogManager";
import { ArticleGroupType } from "../../models/article-groups/article-group";
import { isDefined } from "../../lib/filter-undefined";
import { Mode } from "../form-fields/raw/AmountTextFieldBase";
import { toPayAmount } from "../../models/quote/calculations";
import { toCents, toNumber } from "../../models/finance/cent-value";
import { CalculationOptions } from "../../models/quote/calculation-options";
import { PartnerTier } from "../../models/other/partner";
import uniqolor from "uniqolor";

const adaptivePriceColumn: ColumnCreator<{
  key: string;
  allowedModes?: Mode[];
}> = ({ key, allowedModes, ...options }) =>
  dotPropColumn({
    field: `prices.${key}`,
    align: "right",
    headerAlign: "right",
    width: 150,
    renderCell: ({ value }) => {
      if (!value) return null;

      if (isPercentageAmount(value) && value.amount.percentage !== undefined) {
        return formatPercentage(value.amount.percentage);
      }

      if (isCentAmount(value) && value.amount.value !== undefined) {
        return formatCentAmount(value.amount.value);
      }

      return "";
    },
    renderEditCell: (params) => (
      <AmountEditCell allowedModes={allowedModes} {...params} />
    ),
    ...options,
  });

function colorForMargin(margin: number) {
  if (margin > 0.09) return "green";

  if (margin > 0) return "yellow";

  return "red";
}

function optional<T = ExtendedGridColDef>(hide: boolean, value: T) {
  return hide ? undefined : value;
}

function lockableHeader({ hint }: { hint?: string } = {}) {
  return (params: GridColumnHeaderParams) => {
    const { headerName, editable } = params.colDef;
    if (editable)
      return <Typography variant="subtitle2">{headerName}</Typography>;
    return (
      <Stack
        direction="row"
        alignItems="center"
        alignContent={"center"}
        spacing={0.5}
      >
        <Typography variant="subtitle2">{headerName}</Typography>
        <Tooltip title={hint ?? "Inherited from root article"} arrow>
          <Lock fontSize="inherit" color="disabled" />
        </Tooltip>
      </Stack>
    );
  };
}

export interface ColumnVisibility {
  internal: boolean;
  tier: PartnerTier;
  customerPrices: boolean;
  installerPrices: boolean;
}

const defaultColumnVisibility: ColumnVisibility = {
  internal: false,
  tier: PartnerTier.None,
  customerPrices: false,
  installerPrices: false,
};

export const columnsForType = (
  type: ArticleGroupType,
  calculationOptions?: CalculationOptions,
  show: ColumnVisibility = defaultColumnVisibility,
): ExtendedGridColDef[] => {
  const isPlatform = show.tier >= PartnerTier.Platform;
  const onlyPartnerColumns = !show.internal && !isPlatform;
  const isRoot = ["root", "standalone"].includes(type);
  return [
    optional(
      onlyPartnerColumns,
      booleanColumn({
        field: "enabled",
        headerName: "Visible",
        editable: true,
        sortable: false,
      }),
    ),
    optional<GridActionsColDef>(onlyPartnerColumns, {
      field: "_",
      headerName: "Settings",
      type: "actions",
      getActions: ({ row }) => {
        const article: Article = row;
        return [
          <EditArticleFieldsButton
            key={`edit-fields-${row.id}`}
            groupId={article.reference.parent.parent?.id ?? ""}
            articleId={row.id}
            articleName={row.title}
            defaultConfig={row.fieldsConfig}
          />,
          <EditArticleAttachmenButton
            key={`edit-attachments-${row.id}`}
            groupId={article.reference.parent.parent?.id ?? ""}
            articleId={row.id}
            articleName={row.title}
            defaultConfig={row.attachment}
            defaultAttachments={row.jobAttachments}
          />,
        ];
      },
    }),
    optional(
      !onlyPartnerColumns,
      numberColumn({
        field: "order",
        headerName: "Order",
        editable: isRoot,
        sortable: false,
        visibility: false,
      }),
    ),
    stringColumn({
      field: "id",
      headerName: "Id/SKU",
      renderHeader: lockableHeader({ hint: "Permanent value" }),
      width: 200,
      editable: false,
      sortable: false,
    }),
    stringColumn({
      field: "group",
      headerName: "Group",
      renderHeader: lockableHeader(),
      editable: isRoot && !onlyPartnerColumns,
      sortable: false,
      renderCell: (params: GridCellParams) => {
        if (!params.row.group) return null;
        const color = uniqolor(params.row.group).color;
        return (
          <Chip
            label={params.row.group}
            size="small"
            sx={{ color, borderColor: color }}
            variant="outlined"
          />
        );
      },
    }),
    dotPropColumn({
      field: "prices.partnerPrice.titlePrefix",
      headerName: onlyPartnerColumns ? "Art. no" : "Partner prefix",
      editable: true,
      sortable: false,
      width: 200,
      visibility: false,
    }),
    stringColumn({
      field: "title",
      headerName: "Title",
      renderHeader: lockableHeader(),
      width: 400,
      editable: isRoot && !onlyPartnerColumns,
      sortable: false,
    }),
    optional(
      !show.customerPrices,
      adaptivePriceColumn({
        key: "customerPrice",
        allowedModes: ["currency"],
        headerName: "Customer ex VAT",
        width: 180,
        editable: true,
      }),
    ),
    optional(
      !show.customerPrices,
      dotPropColumn({
        field: "customerToPay",
        headerName: "After deduction",
        width: 180,
        align: "right",
        headerAlign: "right",
        editable: false,
        valueGetter: (_, row) => {
          const article: Article = row;
          const { deduction, prices } = article;

          if (!isCentAmount(prices.customerPrice)) {
            return null;
          }
          const toPay = toPayAmount(
            toNumber(prices.customerPrice.amount.value),
            {
              deduction:
                calculationOptions?.useSwedishGreenTechnologyStandardDeduction &&
                deduction === "greenTechnology"
                  ? "greenTechnologyStandard"
                  : deduction,
            },
          );
          return formatCentAmount(toCents(toPay));
        },
      }),
    ),
    // For non-platform partners, partner price is the same as installer price.
    optional(
      !show.internal && isPlatform,
      adaptivePriceColumn({
        key: "partnerPrice",
        renderHeader: lockableHeader(),
        headerName: show.internal ? "Partner" : "Installer",
        editable: show.internal,
      }),
    ),
    optional(
      !show.internal && !show.installerPrices,
      adaptivePriceColumn({
        key: "craftsmanProceeds",
        headerName: "Installer",
        editable: true,
      }),
    ),
    optional(!show.internal, {
      field: "doneProceeds",
      headerName: "Margin",
      renderCell: ({ row }) => {
        const prices = row.prices as AmountBreakdown;
        if (!havePrices(prices)) {
          return null;
        }

        const margin = doneProceeds(prices);
        return (
          <Typography variant="inherit" color={colorForMargin(margin)}>
            {(margin * 100).toPrecision(2)}%
          </Typography>
        );
      },
    }),
    labeledValueColumn({
      values: DEDUCTION_TYPES,
      allowNull: true,
      field: "deduction",
      headerName: "Deduction",
      renderHeader: lockableHeader(),
      editable: isRoot && !onlyPartnerColumns,
      sortable: false,
      width: 150,
    }),
    labeledValueColumn({
      values: DETAILED_DEDUCTION_TYPES,
      allowNull: true,
      field: "detailedDeductionType",
      headerName: "Detailed Deduction",
      renderHeader: lockableHeader(),
      editable: isRoot && !onlyPartnerColumns,
      sortable: false,
      width: 160,
    }),
    labeledValueColumn({
      field: "lineItemType",
      headerName: "Type",
      renderHeader: lockableHeader(),
      editable: isRoot && !onlyPartnerColumns,
      values: LINE_ITEM_TYPES,
      sortable: false,
    }),
    labeledValueColumn({
      field: "unitType",
      headerName: "Unit",
      renderHeader: lockableHeader(),
      editable: isRoot && !onlyPartnerColumns,
      values: LINE_ITEM_UNIT_TYPE,
      sortable: false,
    }),
  ].filter(isDefined);
};

function EditArticleFieldsButton(props: {
  groupId: string;
  articleId: string;
  articleName: string;
  defaultConfig?: FieldsConfig;
}) {
  const { open } = useDialog("article-field-settings-dialog");
  return (
    <GridActionsCellItem
      icon={
        <Tooltip title="Installer editable fields" arrow>
          <EditNote />
        </Tooltip>
      }
      onClick={() => {
        open(props);
      }}
      label="Installer editable fields"
    />
  );
}

function EditArticleAttachmenButton(props: {
  groupId: string;
  articleId: string;
  articleName: string;
  defaultConfig?: AttachmentConfig;
  defaultAttachments?: ArticleJobAttachment[];
}) {
  const { open } = useDialog("article-attachment-settings-dialog");
  return (
    <GridActionsCellItem
      icon={
        <Badge
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          badgeContent=""
          invisible={!props.defaultAttachments?.length}
          variant="dot"
          color="success"
        >
          <Tooltip title="Attachments" arrow>
            <Attachment />
          </Tooltip>
        </Badge>
      }
      onClick={() => {
        open(props);
      }}
      label="Attachments"
    />
  );
}
