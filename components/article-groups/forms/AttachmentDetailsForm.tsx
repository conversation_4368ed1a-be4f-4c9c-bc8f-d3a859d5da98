import { Edit } from "@mui/icons-material";
import { ArticleJobAttachment } from "../../../models/article-groups/article";
import { LabeledValue } from "../../../models/job/job-note";
import { RichMetadata } from "../../../models/other/rich-metadata";
import TextField from "../../form-fields/TextField";
import {
  Button,
  Divider,
  IconButton,
  Stack,
  ToggleButton,
  Typography,
} from "@mui/material";
import { DeleteOutlined } from "@mui/icons-material";
import EmptyState from "../../empty-state/EmptyState";
import { Field, useField } from "react-final-form";
import { AttachmentIcon } from "../../job/attachments/AttachmentImage";
import Condition from "../../form-fields/Condition";
import ToggleButtonsField from "../../form-fields/ToggleButtonsField";

const defaultAttachments: ArticleJobAttachment[] = [];

export default function AttachmentDetailsForm({
  name,
  index,
  onDelete,
}: {
  name: string;
  index?: number;
  onDelete?: () => void;
}) {
  if (index === undefined)
    return (
      <EmptyState
        icon={<Edit />}
        title="Select an attachment to edit it"
        sx={{ height: 100 }}
      />
    );

  return (
    <Field name={`${name}.${index}.source`}>
      {({ input }) => {
        const disabled = input.value === "root";
        return (
          <Stack spacing={1}>
            <ToggleButtonsField
              name={`${name}.${index}.type`}
              fullWidth
              disabled={disabled}
            >
              <ToggleButton value="note">
                <AttachmentIcon type={"note"} sx={{ marginRight: 1 }} />
                Note
              </ToggleButton>
              <ToggleButton value="installation">
                <AttachmentIcon type={"installation"} sx={{ marginRight: 1 }} />
                Login
              </ToggleButton>
            </ToggleButtonsField>
            <Divider />
            <TextField
              name={`${name}.${index}.title`}
              required
              disabled={disabled}
              label="Title"
            />
            <TextField
              name={`${name}.${index}.description`}
              disabled={disabled}
              label="Description"
            />
            <Divider />
            <Condition when={`${name}.${index}.type`} is="note">
              <NoteFields name={name} index={index} disabled={disabled} />
            </Condition>
            <Condition when={`${name}.${index}.type`} is="installation">
              <LoginFields name={name} index={index} disabled={disabled} />
            </Condition>
            <Divider />
            <Field name="jobAttachments" defaultValue={defaultAttachments}>
              {({ input }) => (
                <Button
                  color="error"
                  disabled={disabled}
                  onClick={() => {
                    input.onChange(input.value.filter((_, i) => i !== index));
                    onDelete?.();
                  }}
                >
                  Delete attachment
                </Button>
              )}
            </Field>
          </Stack>
        );
      }}
    </Field>
  );
}

interface FieldProps {
  name: string;
  index: number;
  disabled: boolean;
}

function NoteFields({ name, index, disabled }: FieldProps) {
  return (
    <>
      <TextField
        name={`${name}.${index}.url`}
        required
        disabled={disabled}
        label="Url"
      />
    </>
  );
}

function LoginFields({ name, index, disabled }: FieldProps) {
  const fieldsName = `${name}.${index}.fields`;
  const fields = useField<LabeledValue[]>(fieldsName, { defaultValue: [] });
  const actionsName = `${name}.${index}.actions`;
  const actions = useField<RichMetadata[]>(actionsName, { defaultValue: [] });
  return (
    <Stack spacing={1}>
      <Typography variant="subtitle1">Fields</Typography>
      {fields?.input?.value?.map((_, index) => (
        <Stack direction="row" spacing={1} key={`${fieldsName}.${index}`}>
          <TextField
            name={`${fieldsName}.${index}.label`}
            required
            disabled={disabled}
            label="Label"
          />
          <TextField
            name={`${fieldsName}.${index}.value`}
            required
            disabled={disabled}
            label="Value"
          />
          <IconButton
            disabled={disabled}
            onClick={() =>
              fields.input.onChange(
                fields.input.value.filter((_, i) => i !== index),
              )
            }
          >
            <DeleteOutlined />
          </IconButton>
        </Stack>
      ))}
      <Button
        disabled={disabled}
        onClick={() => {
          const newField: LabeledValue = {
            label: "New field",
            value: "",
          };
          fields.input.onChange([...fields.input.value, newField]);
        }}
      >
        Add field
      </Button>
      <Typography variant="subtitle1">Actions</Typography>
      {actions?.input?.value?.map((_, index) => (
        <Stack direction="row" spacing={1} key={`${actionsName}.${index}`}>
          <TextField
            name={`${actionsName}.${index}.title`}
            required
            disabled={disabled}
            label="Title"
          />
          <TextField
            name={`${actionsName}.${index}.url`}
            required
            disabled={disabled}
            label="Url"
          />
          <IconButton
            disabled={disabled}
            onClick={() =>
              actions.input.onChange(
                actions.input.value.filter((_, i) => i !== index),
              )
            }
          >
            <DeleteOutlined />
          </IconButton>
        </Stack>
      ))}
      <Button
        disabled={disabled}
        onClick={() => {
          const newAction: RichMetadata = {
            title: "New action",
            url: "",
          };
          actions.input.onChange([...actions.input.value, newAction]);
        }}
      >
        Add action
      </Button>
    </Stack>
  );
}
