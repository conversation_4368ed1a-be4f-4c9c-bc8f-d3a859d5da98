import { Divider, Stack } from "@mui/material";
import {
  DEDUCTION_TYPES,
  LINE_ITEM_TYPES,
  LINE_ITEM_UNIT_TYPE,
} from "../../../config/constants";
import { Article } from "../../../models/article-groups/article";
import AmountTextField from "../../form-fields/AmountTextField";
import LabeledValueField from "../../form-fields/LabeledValueField";
import TextField from "../../form-fields/TextField";
import Internal from "../../auth/Internal";

export default function ArticlesFormFields({
  initialArticle,
}: {
  initialArticle?: Article;
}) {
  return (
    <>
      <TextField
        name="title"
        required
        type="text"
        label="Title"
        initialValue={initialArticle?.title}
      />
      <TextField
        name="description"
        type="text"
        label="Description"
        initialValue={initialArticle?.description}
      />
      <Stack direction={"row"} spacing={2}>
        <LabeledValueField
          name="deduction"
          label="Deduction"
          items={DEDUCTION_TYPES}
          initialValue={initialArticle?.deduction}
        />
        <LabeledValueField
          name="lineItemType"
          label="Type"
          items={LINE_ITEM_TYPES}
          required
          initialValue={initialArticle?.lineItemType}
        />
        <LabeledValueField
          name="unitType"
          label="Unit"
          items={LINE_ITEM_UNIT_TYPE}
          required
          initialValue={initialArticle?.unitType}
        />
      </Stack>
      <Divider />
      <AmountTextField
        name="prices.customerPrice"
        label="Customer Price"
        allowedModes={["currency"]}
        initialValue={initialArticle?.prices?.customerPrice}
        helperText="Excl. VAT and before deductions."
      />
      <Internal>
        <AmountTextField
          name="prices.craftsmanProceeds"
          label="Craftsman Proceeds"
          initialValue={initialArticle?.prices?.craftsmanProceeds}
        />
        <AmountTextField
          name="prices.partnerPrice"
          label="Partner Price"
          initialValue={initialArticle?.prices?.partnerPrice}
        />
      </Internal>
    </>
  );
}
