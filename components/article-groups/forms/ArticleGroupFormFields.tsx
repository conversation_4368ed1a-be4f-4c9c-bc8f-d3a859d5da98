import { DateTimePicker, Switches } from "mui-rff";
import TextField from "../../form-fields/TextField";
import { Field } from "react-final-form";
import { PlatformTier } from "../../auth/Tiered";

export default function ArticleGroupFormFields() {
  return (
    <>
      <PlatformTier>
        <ArticleGroupBasicFields />
        <Switches
          name="enabled"
          data={{
            label: "Enabled",
            value: true,
          }}
        />
        <Field name="enabled" subscription={{ value: true }}>
          {({ input: { value } }) => (
            <DateTimePicker
              name="startTime"
              label="Start time"
              required={value}
              fieldProps={{
                helperText: "When the article group should be activated",
              }}
            />
          )}
        </Field>
        <Switches
          name="addToAttachments"
          data={{
            label: "Show on job for customer & installer",
            value: true,
          }}
        />
      </PlatformTier>
      <Switches
        name="calculationOptions.useSwedishGreenTechnologyStandardDeduction"
        data={{
          label: "Use 3% admin method for green technology",
          value: true,
        }}
      />
    </>
  );
}

export function ArticleGroupBasicFields() {
  return (
    <>
      <TextField
        name="title"
        label="Title"
        required
        helperText="The title shown for end users"
      />
      <TextField
        name="name"
        label="Name"
        helperText="Internal name to use (if other than title)"
      />
    </>
  );
}
