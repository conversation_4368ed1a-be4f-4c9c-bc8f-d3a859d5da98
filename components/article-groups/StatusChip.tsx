import { EditNote, Schedule, Public } from "@mui/icons-material";
import { Chip, Tooltip } from "@mui/material";
import { formatTime } from "../../lib/format-time-relative";
import { ArticleGroup } from "../../models/article-groups/article-group";

export default function StatusChip({ group }: { group: ArticleGroup }) {
  if (!group.enabled) {
    return <Chip icon={<EditNote />} label="Draft" size="small" />;
  }

  if (group.startTime && group.startTime.toDate() > new Date()) {
    return (
      <Tooltip
        title={`Scheduled for release at ${formatTime(
          group.startTime.toDate(),
        )}`}
        arrow
      >
        <Chip
          icon={<Schedule />}
          label="Scheduled"
          color="warning"
          size="small"
        />
      </Tooltip>
    );
  }

  const startTimeHint = group.startTime
    ? `Live since ${formatTime(group.startTime.toDate())}`
    : "One or more branches are live";

  if (group.replacedBy) {
    return (
      <Tooltip
        title={`${startTimeHint} - Have been replaced by newer price list`}
        arrow
      >
        <Chip
          icon={<Public />}
          label="Live (Archived)"
          color="info"
          size="small"
        />
      </Tooltip>
    );
  }

  return (
    <Tooltip title={startTimeHint} arrow>
      <Chip icon={<Public />} label="Live" color="error" size="small" />
    </Tooltip>
  );
}
