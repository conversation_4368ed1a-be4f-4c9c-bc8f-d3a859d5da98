import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/router";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import { Content, Header, MenuPanel, MainPanel } from "../layout/panels";
import {
  articleGroupsQuery,
  rootArticleGroupsQuery,
} from "../../models/article-groups/repository";
import { DocumentReference, where } from "firebase/firestore";
import { useCollectionData } from "react-firebase-hooks/firestore";
import {
  ArticleGroup,
  canBeBranchedByPartner,
} from "../../models/article-groups/article-group";
import {
  Button,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Tooltip,
} from "@mui/material";
import {
  Fullscreen,
  FullscreenExit,
  Add,
  ForkRight,
  ShoppingCartOutlined,
  Settings,
  SubdirectoryArrowRight,
} from "@mui/icons-material";
import { FirestoreModel } from "../../models/firestore_model";
import ArticlesTable from "./ArticlesTable";
import { useSelectedModel } from "../../lib/hooks/use-selected-model";
import ManagedDialog from "../dialogs/ManagedDialog";
import CreateArticleDialog from "./dialogs/CreateArticleDialog";
import CreateArticleGroupDialog from "./dialogs/CreateArticleGroupDialog";
import EmptyState from "../empty-state/EmptyState";
import BranchArticleGroupDialog from "./dialogs/BranchArticleGroupDialog";
import ExpandableItem from "../common/lists/ExpandableItem";
import { titleForService } from "../../config/constants";
import EditArticleGroupDialog from "./dialogs/EditArticleGroupDialog";
import ArticleFieldSettingsDialog from "./dialogs/ArticleFieldSettingsDialog";
import ArticleAttachmentSettingsDialog from "./dialogs/ArticleAttachmentSettingsDialog";
import {
  DonePartnerId,
  isPlatformPartner,
  PartnerTier,
} from "../../models/other/partner";
import { useAuthUser } from "../auth/AuthProvider";
import StatusChip from "./StatusChip";
import VersionChip from "./VersionChip";
import { PlatformTier } from "../auth/Tiered";
import { Subheader } from "../common/lists/Subheader";
import {
  PanelGroup,
  PanelResizeHandle,
  ImperativePanelHandle,
} from "react-resizable-panels";
import { useRef } from "react";

function query(partnerId?: string) {
  return rootArticleGroupsQuery(partnerId ?? DonePartnerId);
}

export default function BranchedGroupsPage() {
  const router = useRouter();
  const { partner } = useAuthUser();
  const [fullscreen, setFullscreen] = useState(false);
  const menuPanelRef = useRef<ImperativePanelHandle>(null);

  // Fetch all article groups
  const [groups, _, error] = useCollectionData(query(partner));

  // Filter root groups
  const [activeRoots, archivedRoots] = useMemo(() => {
    const rootGroups = groups?.filter((group) => group.type === "root") ?? [];
    return [
      rootGroups.filter((group) => !group.replacedBy),
      rootGroups.filter((group) => group.replacedBy),
    ];
  }, [groups, partner]);

  const [selectedGroup, setSelectedGroup] = useSelectedModel(groups, "id");
  const [expandedGroup, setExpandedGroup] = useState<DocumentReference | null>(
    null,
  );

  // Initialize selected group and expanded group from query params
  useEffect(() => {
    if (selectedGroup?.type === "root") {
      setExpandedGroup(selectedGroup.parent ?? selectedGroup.reference);
    }
  }, [selectedGroup]);

  const handleGroupClick = (group: ArticleGroup & FirestoreModel) => {
    router.push(`/price-lists/?id=${group.reference.id}`);
    setSelectedGroup(group);
  };

  const toggleFullscreen = () => {
    if (fullscreen) {
      menuPanelRef.current?.expand();
    } else {
      menuPanelRef.current?.collapse();
    }
    setFullscreen(!fullscreen);
  };

  return (
    <DialogManager>
      <ManagedDialog
        id={"create-article-dialog"}
        component={CreateArticleDialog}
      />
      <ManagedDialog
        id={"create-group-dialog"}
        component={CreateArticleGroupDialog}
      />
      <ManagedDialog
        id={"create-branch-dialog"}
        component={BranchArticleGroupDialog}
      />
      <ManagedDialog
        id={"edit-group-dialog"}
        component={EditArticleGroupDialog}
      />
      <ManagedDialog
        id={"article-field-settings-dialog"}
        component={ArticleFieldSettingsDialog}
      />
      <ManagedDialog
        id={"article-attachment-settings-dialog"}
        component={ArticleAttachmentSettingsDialog}
      />
      <PanelGroup autoSaveId="branched-groups" direction="horizontal">
        <MenuPanel ref={menuPanelRef} collapsible>
          <Header title={"Price lists"}>
            <PlatformTier>
              <CreateArticleGroupButton />
            </PlatformTier>
          </Header>
          <Content>
            {groups?.length ? (
              <List dense disablePadding>
                <Subheader noTopBorder>Active</Subheader>
                <RootArticleGroupsList
                  groups={activeRoots}
                  onSelected={handleGroupClick}
                  selected={selectedGroup}
                  expandedGroup={expandedGroup}
                  onSetExpanded={setExpandedGroup}
                />
                <Subheader noTopBorder>Archived</Subheader>
                <RootArticleGroupsList
                  groups={archivedRoots}
                  onSelected={handleGroupClick}
                  selected={selectedGroup}
                  expandedGroup={expandedGroup}
                  onSetExpanded={setExpandedGroup}
                />
              </List>
            ) : (
              <EmptyState error={error} />
            )}
          </Content>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          <MainPanelContent
            group={selectedGroup}
            fullscreen={fullscreen}
            setFullscreen={toggleFullscreen}
          />
        </MainPanel>
      </PanelGroup>
    </DialogManager>
  );
}

function RootArticleGroupsList({
  groups,
  selected,
  onSelected,
  expandedGroup,
  onSetExpanded,
}: {
  groups: (ArticleGroup & FirestoreModel)[];
  selected?: ArticleGroup & FirestoreModel;
  onSelected?: (group: ArticleGroup & FirestoreModel) => void;
  expandedGroup: DocumentReference | null;
  onSetExpanded: (group: DocumentReference) => void;
}) {
  return (
    <>
      {groups.map((group) => (
        <ExpandableItem
          key={group.reference.id}
          onClick={() => {
            onSelected?.(group);
            onSetExpanded(group.reference);
          }}
          selected={group.reference.id === selected?.reference.id}
          expanded={expandedGroup?.id === group.reference.id}
          title={group.name ?? group.title}
          subtitle={group.services?.map(titleForService).join(", ")}
          icon={<ShoppingCartOutlined />}
          accessory={<VersionChip version={group.version} />}
        >
          <BranchArticleGroupsList
            parent={group.reference}
            selected={selected}
            onSelected={onSelected}
          />
        </ExpandableItem>
      ))}
    </>
  );
}

function BranchArticleGroupsList({
  parent,
  selected,
  onSelected,
}: {
  parent: DocumentReference;
  selected?: ArticleGroup & FirestoreModel;
  onSelected?: (group: ArticleGroup & FirestoreModel) => void;
}) {
  const [branches] = useCollectionData(
    articleGroupsQuery(where("parent", "==", parent)),
  );

  const [expandedBranch, setExpandedBranch] =
    useState<DocumentReference | null>(null);

  return (
    <List dense disablePadding>
      {branches?.map((branch) => (
        <ExpandableItem
          key={branch.reference.id}
          selected={branch.reference.id === selected?.reference.id}
          expanded={expandedBranch?.id === branch.reference.id}
          title={branch.name ?? branch.title}
          subtitle={branch.network?.id || branch.merchant?.id}
          onClick={() => {
            onSelected?.(branch);
            setExpandedBranch(branch.reference);
          }}
          level={1}
        >
          <CompanyBranchArticleGroupsList
            parent={branch.reference}
            selected={selected}
            onSelected={onSelected}
          />
        </ExpandableItem>
      ))}
    </List>
  );
}

function CompanyBranchArticleGroupsList({
  parent,
  selected,
  onSelected,
}: {
  parent: DocumentReference;
  selected?: ArticleGroup & FirestoreModel;
  onSelected?: (group: ArticleGroup & FirestoreModel) => void;
}) {
  const [companyBranches] = useCollectionData(
    articleGroupsQuery(where("parent", "==", parent)),
  );

  return (
    <List dense disablePadding>
      {companyBranches?.map((branch) => (
        <CompanyBranchItem
          key={branch.reference.id}
          branch={branch}
          selected={selected}
          onSelected={onSelected}
        />
      ))}
    </List>
  );
}

function CompanyBranchItem({
  branch,
  selected,
  onSelected,
}: {
  branch: ArticleGroup & FirestoreModel;
  selected?: ArticleGroup & FirestoreModel;
  onSelected?: (group: ArticleGroup & FirestoreModel) => void;
}) {
  return (
    <ListItem
      disablePadding
      divider
      sx={{ backgroundColor: "background.paper" }}
    >
      <ListItemButton
        onClick={() => onSelected?.(branch)}
        selected={branch.reference.id === selected?.reference.id}
        sx={{ pl: 5 }}
      >
        <ListItemIcon>
          <SubdirectoryArrowRight />
        </ListItemIcon>
        <ListItemText
          primary={branch.name ?? branch.title}
          secondary={`${branch.companies?.length ?? 0} installers`}
        />
      </ListItemButton>
    </ListItem>
  );
}

function MainPanelContent({
  group,
  fullscreen,
  setFullscreen,
}: {
  group?: ArticleGroup & FirestoreModel;
  fullscreen?: boolean;
  setFullscreen?: (fullscreen: boolean) => void;
}) {
  const { partnerDoc, isSuperAdmin = false } = useAuthUser();
  if (!group) {
    return null;
  }

  const isBranchable = canBeBranchedByPartner(group, partnerDoc);

  return (
    <>
      <Header
        title={group.name || group.title}
        titleSecondary={
          <>
            <VersionChip version={group.version} />
            <StatusChip group={group} />
          </>
        }
      >
        <CreateArticleButton
          groupId={group.reference.id}
          disabled={!["root", "standalone"].includes(group.type)}
        />
        <PlatformTier>
          <CreateBranchButton group={group} disabled={!isBranchable} />
        </PlatformTier>
        <EditGroupButton groupId={group.reference.id} group={group} />
        <Tooltip title={fullscreen ? "Minimize" : "Maximize"} arrow>
          <IconButton onClick={() => setFullscreen?.(!fullscreen)}>
            {fullscreen ? <FullscreenExit /> : <Fullscreen />}
          </IconButton>
        </Tooltip>
      </Header>
      <Content>
        <ArticlesTable
          groupId={group.reference.id}
          type={group.type}
          calculationOptions={group.calculationOptions}
          visibility={{
            internal: isSuperAdmin,
            tier: partnerDoc?.tier ?? PartnerTier.None,
            customerPrices: true,
            installerPrices: isPlatformPartner(partnerDoc),
          }}
        />
      </Content>
    </>
  );
}

function CreateArticleGroupButton() {
  const { open } = useDialog("create-group-dialog");
  return (
    <Button startIcon={<Add />} onClick={() => open({ type: "root" })}>
      New price list
    </Button>
  );
}

function CreateArticleButton({
  groupId,
  disabled,
}: {
  groupId: string;
  disabled?: boolean;
}) {
  const { open } = useDialog("create-article-dialog");
  return (
    <Button
      startIcon={<Add />}
      onClick={() => open({ groupId })}
      disabled={disabled}
    >
      Add article
    </Button>
  );
}

function CreateBranchButton({
  group,
  disabled,
}: {
  group: ArticleGroup & FirestoreModel;
  disabled?: boolean;
}) {
  const { open } = useDialog("create-branch-dialog");
  return (
    <Button
      startIcon={<ForkRight />}
      onClick={() => open({ from: group })}
      disabled={disabled}
    >
      Create branch
    </Button>
  );
}

function EditGroupButton({
  groupId,
  group,
  disabled,
}: {
  groupId: string;
  group: ArticleGroup;
  disabled?: boolean;
}) {
  const { open } = useDialog("edit-group-dialog");
  return (
    <Button
      startIcon={<Settings />}
      onClick={() => open({ group, groupId })}
      disabled={disabled}
    >
      Configure
    </Button>
  );
}
