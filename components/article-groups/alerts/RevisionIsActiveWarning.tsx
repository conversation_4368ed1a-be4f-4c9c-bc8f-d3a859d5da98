import { Alert, AlertTitle } from "@mui/material";

export default function RevisionIsActiveWarning({ show }: { show: boolean }) {
  if (!show) return null;
  return (
    <Alert severity="warning">
      <AlertTitle>This price list version is currently active</AlertTitle>
      Updating articles will affect all new quotes for existing jobs using this
      version and will be visible for users immediately.
    </Alert>
  );
}
