import { useState } from "react";
import { Select, MenuItem, FormControl, InputLabel } from "@mui/material";
import Airtable from "airtable";
import { AIRTABLE_TOKEN } from "../../config/constants";
import { User } from "../../models/user/user";
import { Job } from "../../models/job/job";
import { getTemplateValues } from "../../lib/template-values";
import { MessageTemplate } from "../../models/message/message-template";

/**
 * Template selector component for message templates
 * This component should only be rendered inside the Internal component
 * to ensure unauthorized users don't trigger API calls
 */
export interface TemplateSelectProps {
  templateId: string;
  user?: User;
  job?: Job;
  onTemplateSelect: (
    template: string,
    fields: string[],
    values: Map<string, string>,
  ) => void;
}

/**
 * A select component for choosing message templates
 * Only loads templates when the dropdown is opened
 */
export function TemplateSelect({
  templateId,
  user,
  job,
  onTemplateSelect,
}: TemplateSelectProps) {
  const [messageTemplates, setMessageTemplates] = useState<MessageTemplate[]>(
    [],
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const fetchMessageTemplates = async () => {
    if (messageTemplates.length > 0 || isLoading) return;

    setIsLoading(true);

    try {
      Airtable.configure({
        endpointUrl: "https://api.airtable.com",
        apiKey: AIRTABLE_TOKEN,
      });

      const base = Airtable.base("appOrWilMq3o5LSD1");
      const records = await base(templateId).select().all();

      const templates: MessageTemplate[] = records?.map((record: any) => {
        return {
          title: record.get("title") as string,
          content: record.get("content") as string,
          to: record.get("to") as string,
          fields: record.get("fields") as string[],
        };
      });

      const filteredTemplates = user?.company
        ? templates.filter(
            (item: any) =>
              item.to === (user.company ? "craftsman" : "customer"),
          )
        : templates;

      setMessageTemplates(filteredTemplates);
    } catch (error) {
      console.error("Error fetching message templates:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpen = () => {
    setIsOpen(true);
    fetchMessageTemplates();
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleTemplateChange = async (entry: any) => {
    const templateValues = await getTemplateValues(
      entry.target.value.fields,
      job,
    );

    onTemplateSelect(
      entry.target.value.content,
      entry.target.value.fields,
      templateValues,
    );
  };

  return (
    <FormControl>
      <InputLabel id="message-template-select">Select template</InputLabel>
      <Select
        style={{ marginTop: 8 }}
        size="small"
        labelId="message-template-select"
        fullWidth
        label="Select template"
        variant="outlined"
        onChange={handleTemplateChange}
        onOpen={handleOpen}
        onClose={handleClose}
        open={isOpen}
      >
        {isLoading ? (
          <MenuItem disabled>Loading templates...</MenuItem>
        ) : (
          messageTemplates.map((item: any, index: any) => (
            <MenuItem value={item} key={index}>
              {item.title}
            </MenuItem>
          ))
        )}
      </Select>
    </FormControl>
  );
}
