import {
  Grid2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>utton,
  ListItemText,
  Typography,
  TypographyProps,
  styled,
} from "@mui/material";
import { formatTinyDistance } from "../../../lib/format-time-relative";
import { DocumentReference, Timestamp } from "firebase/firestore";
import SubjectTypeIcon from "./SubjectTypeIcon";
import TaskListItemAvatar from "./TaskListItemAvatar";

/**
 * Grid that will responsively trim text to fit in the grid.
 */
const TrimmedTextGrid = styled(Grid2)(() => ({
  textOverflow: "ellipsis",
  overflow: "hidden",
  whiteSpace: "nowrap",
}));

export interface TaskListItemProps {
  title: React.ReactNode;
  onClick: () => void;

  secondaryTitle?: React.ReactNode;
  text?: React.ReactNode;
  timestamp?: Timestamp | null;
  userRef?: DocumentReference;

  type?: string;

  selected?: boolean;
  dimmed?: boolean;

  titleTypographyProps?: Omit<TypographyProps, "ref">;
  secondaryTitleTypographyProps?: Omit<TypographyProps, "ref">;
  textTypographyProps?: Omit<TypographyProps, "ref">;
  timestampTypographyProps?: Omit<TypographyProps, "ref">;
}

/**
 * A list item used in task lists.
 */
export default function TaskListItem({
  title,
  secondaryTitle,
  text,
  timestamp,
  onClick,
  userRef,
  selected = false,
  dimmed = false,
  type,
  titleTypographyProps = {},
  secondaryTitleTypographyProps = {},
  textTypographyProps = {},
  timestampTypographyProps = {},
}: TaskListItemProps) {
  return (
    <ListItem
      disablePadding
      divider
      sx={{ backgroundColor: "background.paper" }}
    >
      <ListItemButton onClick={() => onClick()} selected={selected}>
        <ListItemText
          slotProps={{
            primary: {
              color: dimmed ? "textSecondary" : "textPrimary",
              ...titleTypographyProps,
            },
            secondary: {
              color: dimmed ? "textSecondary" : "textPrimary",
              ...textTypographyProps,
            },
          }}
          primary={
            <Grid2 container>
              <TrimmedTextGrid
                size="grow"
                sx={{ fontWeight: 600, paddingRight: 1 }}
              >
                {title}
              </TrimmedTextGrid>
              <Grid2 size={"auto"}>
                <Typography variant="body2" {...secondaryTitleTypographyProps}>
                  {secondaryTitle}
                  <SubjectTypeIcon type={type} />
                </Typography>
              </Grid2>
            </Grid2>
          }
          secondary={
            <Grid2
              container
              alignItems={"center"}
              justifyContent={"center"}
              spacing={1}
              mt={1}
            >
              {userRef ? (
                <Grid2>
                  <TaskListItemAvatar dimmed={dimmed} userRef={userRef} />
                </Grid2>
              ) : null}
              <TrimmedTextGrid size="grow">{text}</TrimmedTextGrid>
              <Grid2>
                <Typography
                  variant="caption"
                  color="textSecondary"
                  {...timestampTypographyProps}
                >
                  {timestamp ? formatTinyDistance(timestamp) : null}
                </Typography>
              </Grid2>
            </Grid2>
          }
        />
      </ListItemButton>
    </ListItem>
  );
}
