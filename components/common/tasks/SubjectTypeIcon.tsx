import { AssignmentOutlined, Person, StoreRounded } from "@mui/icons-material";
import { Tooltip } from "@mui/material";

const subjectTypeIconSx = {
  verticalAlign: "middle",
  marginTop: -0.4,
  marginLeft: 0.5,
};

export default function SubjectTypeIcon({ type }: { type?: string }) {
  switch (type) {
    case "jobs":
      return (
        <Tooltip title="Order" arrow>
          <AssignmentOutlined fontSize="inherit" sx={subjectTypeIconSx} />
        </Tooltip>
      );
    case "companies":
      return (
        <Tooltip title="Company" arrow>
          <StoreRounded fontSize="inherit" sx={subjectTypeIconSx} />
        </Tooltip>
      );
    case "users":
      return (
        <Tooltip title="User" arrow>
          <Person fontSize="inherit" sx={subjectTypeIconSx} />
        </Tooltip>
      );
    default:
      return null;
  }
}
