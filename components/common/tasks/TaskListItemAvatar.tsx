import { Grid2 } from "@mui/material";
import UserAvatar, { UserAvatarProps } from "../../user/UserAvatar";

/**
 * Avatar for a task item.
 */
export default function TaskListItemAvatar({
  dimmed,
  size = "tiny",
  userRef,
  ...avatarProps
}: {
  dimmed: boolean;
} & UserAvatarProps) {
  if (!userRef) return null;
  return (
    <Grid2 size={"auto"} sx={{ opacity: dimmed ? 0.5 : 1.0 }}>
      <UserAvatar userRef={userRef} size={size} {...avatarProps} />
    </Grid2>
  );
}
