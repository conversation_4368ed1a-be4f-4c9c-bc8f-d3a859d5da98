import { useState } from "react";
import { Avatar, Card, Grid2, Skeleton, Typography } from "@mui/material";
import { ref, getStorage, getDownloadURL } from "firebase/storage";
import { CustomWidthTooltip } from "./CustomWidthToolTip";
import { JobImage } from "../../models/job/job-image";

const TooltipImagePreview = ({
  image,
  size,
  marginTop = 20,
}: {
  image: JobImage;
  size?: number;
  marginTop?: number;
}) => {
  const [thumbUrl, setThumbUrl] = useState<any>(null);
  const [originalUrl, setOriginalUrl] = useState<any>(null);

  if (!image) return null;
  const storage = getStorage();
  const description = image.description;

  if (image.thumb) {
    const thumbPathReference = ref(storage, image.thumb);
    getDownloadURL(thumbPathReference).then(setThumbUrl);
  }

  const originalPathReference = ref(storage, image.original);
  getDownloadURL(originalPathReference).then(setOriginalUrl);

  if (thumbUrl || originalUrl) {
    return (
      <CustomWidthTooltip
        arrow
        title={
          <Card style={{ width: 500 }}>
            <Avatar
              style={{
                width: 500,
                height: 500,
              }}
              variant="rounded"
              src={originalUrl}
              onClick={() => window.open(originalUrl, "_blank")}
            />
            {description && (
              <Typography style={{ margin: 8 }} variant="body2">
                {description}
              </Typography>
            )}
          </Card>
        }
      >
        <Grid2
          style={{ width: size || 72, marginRight: 20, marginTop: marginTop }}
          direction="column"
          alignItems="stretch"
        >
          <Avatar
            style={{
              width: size || 72,
              height: size || 72,
              cursor: "pointer",
            }}
            variant="rounded"
            src={thumbUrl ?? originalUrl}
            onClick={() => window.open(originalUrl, "_blank")}
          />
          {description && (
            <Typography variant="body2" noWrap>
              {description}
            </Typography>
          )}
        </Grid2>
      </CustomWidthTooltip>
    );
  } else {
    return (
      <Skeleton
        style={{ marginRight: 20, marginTop: marginTop }}
        variant="rectangular"
        width={size || 72}
        height={size || 72}
      />
    );
  }
};

export default TooltipImagePreview;
