interface ConditionalProps {
  if: boolean;
  children: React.ReactNode;
}

/**
 * Show children if the condition is true.
 */
export function Visible({ if: condition, children }: ConditionalProps) {
  return condition ? <>{children}</> : null;
}

/**
 * Hide children if the condition is true.
 */
export function Hidden({ if: condition, children }: ConditionalProps) {
  return condition ? null : <>{children}</>;
}
