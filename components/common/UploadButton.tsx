import { FileDrop } from "react-file-drop";
import { useState } from "react";
import {
  ButtonGroup,
  Button,
  Stack,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  TextField,
  LinearProgress,
} from "@mui/material";
import {
  getStorage,
  ref,
  StorageReference,
  uploadBytes,
} from "firebase/storage";
import {
  collection,
  getFirestore,
  doc,
  setDoc,
  serverTimestamp,
  updateDoc,
  CollectionReference,
} from "@firebase/firestore";
import { useAuthUser } from "../auth/AuthProvider";

const IMAGE_TYPES = ["image/jpeg", "image/png"];
const FILE_TYPES = ["application/pdf"];

export function getFileRef(
  file: File,
  jobId: string,
  messageId: string,
): StorageReference {
  const storageRef = getStorage();
  if (IMAGE_TYPES.includes(file.type)) {
    return ref(storageRef, `chat-image-v1/${jobId}/${messageId}/original.jpg`);
  } else if (FILE_TYPES.includes(file.type)) {
    return ref(storageRef, `chat-files-v1/${jobId}/${messageId}/${file.name}`);
  }

  throw new Error("Invalid file type " + file.type);
}

const QuoteAmountInputs = ({
  amountIncVat,
  setAmountIncVat,
  amountAfterTaxDeductions,
  setAmountAfterTaxDeductions,
}: {
  amountIncVat: any;
  setAmountIncVat: any;
  amountAfterTaxDeductions: any;
  setAmountAfterTaxDeductions: any;
}) => (
  <Stack>
    <div style={{ height: 24 }} />
    <TextField
      label="Amount (inc vat)"
      value={amountIncVat}
      type="number"
      style={{ float: "right" }}
      onChange={(event) => setAmountIncVat(event.target.value)}
      required
    />
    <div style={{ height: 24 }} />
    <TextField
      style={{ float: "right" }}
      label="Amount (after tax)"
      value={amountAfterTaxDeductions}
      type="number"
      onChange={(event) => setAmountAfterTaxDeductions(event.target.value)}
      required
    />
  </Stack>
);

export const UploadFile = ({
  type,
  messagesRef,
  jobId,
  onClose,
  file: initialFile,
}: {
  type: any;
  messagesRef: CollectionReference;
  jobId: string;
  onClose: () => void;
  file?: any;
}) => {
  const authUser = useAuthUser();
  const [file, setFile] = useState(initialFile);
  const [loading, setLoading] = useState(false);
  const [deductionType, setDeductionType] = useState<any>(null);
  const [amountIncVat, setAmountIncVat] = useState<any>(null);
  const [amountAfterTaxDeductions, setAmountAfterTaxDeductions] =
    useState<any>(null);

  const messageRef = doc(messagesRef);

  const handleDrop = (files: any) => {
    setFile(files[0]);
  };
  const uploadFile = async () => {
    setLoading(true);
    const fileRef = getFileRef(file, jobId, messageRef.id);
    await uploadBytes(fileRef, file);

    if (type === "offer") {
      setDoc(messageRef, {
        type: "offer",
        fileRef: fileRef.fullPath,
        sender: authUser.userDoc?.reference,
        deductionType: [deductionType],
        filename: file.name,
        createTime: serverTimestamp(),
        timestamp: serverTimestamp(),
        status: "pending",
        amountIncVat: parseInt(amountIncVat),
        amountAfterTaxDeductions: parseInt(amountAfterTaxDeductions),
      });

      await updateDoc(doc(collection(getFirestore(), "jobs"), jobId), {
        "events.offerSent": serverTimestamp(),
      });
    } else if (IMAGE_TYPES.includes(file.type)) {
      setDoc(messageRef, {
        type: "image",
        imageRef: fileRef.fullPath,
        sender: authUser?.userDoc?.reference,
        filename: file.name,
        createTime: serverTimestamp(),
        timestamp: serverTimestamp(),
      });
    } else {
      setDoc(messageRef, {
        type: "file",
        fileRef: fileRef.fullPath,
        sender: authUser?.userDoc?.reference,
        filename: file.name,
        createTime: serverTimestamp(),
        timestamp: serverTimestamp(),
      });
    }

    setLoading(false);
    onClose();
  };
  return (
    <>
      <DialogTitle>Choose file{type === "offer" && " (offer)"}</DialogTitle>
      <DialogContent>
        {!initialFile && (
          <FileDrop onDrop={handleDrop}>
            <div
              style={{
                borderRadius: "8px",
                border: "2px solid gray",
                borderStyle: "dotted",
                padding: "20px",
              }}
            >
              Drop files here!
            </div>
          </FileDrop>
        )}
        {file && (
          <>
            <div style={{ height: 12 }} />
            <b>{file.name}</b>
            <br />
            {file.type}
            <br />
            <div style={{ height: 12 }} />
          </>
        )}

        {type === "offer" && (
          <>
            <div style={{ height: 24 }} />
            <ButtonGroup
              fullWidth
              color="primary"
              aria-label="primary button group"
            >
              <Button
                size="small"
                variant={deductionType === "rut" ? "contained" : "outlined"}
                onClick={() => {
                  setDeductionType("rut");
                }}
              >
                RUT
              </Button>
              <Button
                size="small"
                variant={deductionType === "rot" ? "contained" : "outlined"}
                onClick={() => {
                  setDeductionType("rot");
                }}
              >
                ROT
              </Button>
              <Button
                size="small"
                variant={
                  deductionType === "greenTechnology" ? "contained" : "outlined"
                }
                onClick={() => {
                  setDeductionType(null);
                }}
              >
                GREEN TECH
              </Button>
              <Button
                size="small"
                variant={deductionType === null ? "contained" : "outlined"}
                onClick={() => {
                  setDeductionType(null);
                }}
              >
                NONE
              </Button>
            </ButtonGroup>

            <QuoteAmountInputs
              amountIncVat={amountIncVat}
              setAmountIncVat={setAmountIncVat}
              amountAfterTaxDeductions={amountAfterTaxDeductions}
              setAmountAfterTaxDeductions={setAmountAfterTaxDeductions}
            />
          </>
        )}
        {loading && (
          <>
            <div style={{ height: 12 }} /> <LinearProgress />
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button
          onClick={uploadFile}
          color="primary"
          disabled={
            !file ||
            loading ||
            (type === "offer" && (!amountAfterTaxDeductions || !amountIncVat))
          }
        >
          Upload
        </Button>
      </DialogActions>
    </>
  );
};

const UploadButton = ({
  messagesRef,
  jobId,
}: {
  messagesRef: CollectionReference;
  jobId: string;
}) => {
  const [open, setOpen] = useState(false);

  const onOpen = (type: any) => setOpen(type);
  const onClose = () => setOpen(false);

  return (
    <>
      <Stack direction="row">
        <Button onClick={() => onOpen("file")}>File</Button>
        <Button onClick={() => onOpen("offer")}>Quote</Button>
      </Stack>
      <Dialog maxWidth="xs" fullWidth open={open} onClose={onClose}>
        <UploadFile
          type={open}
          messagesRef={messagesRef}
          jobId={jobId}
          onClose={onClose}
        />
      </Dialog>
    </>
  );
};

export default UploadButton;
