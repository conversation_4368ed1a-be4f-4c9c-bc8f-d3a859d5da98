import WriteCompanyDialog from "../company/WriteCompanyDialog";
import CreateUserDialog from "../user/CreateUserDialog";
import CreateJobDialog from "../job/CreateJobDialog";
import ManagedDialog from "../dialogs/ManagedDialog";

export default function CreateDialogGroup() {
  return (
    <>
      <ManagedDialog id="create-user" component={CreateUserDialog} />

      <ManagedDialog id="create-company" component={WriteCompanyDialog} />

      <ManagedDialog id="create-job" component={CreateJobDialog} />
    </>
  );
}
