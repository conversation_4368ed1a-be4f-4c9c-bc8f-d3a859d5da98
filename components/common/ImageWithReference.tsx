import { Avatar } from "@mui/material";
import { getStorage, ref, getDownloadURL } from "firebase/storage";
import { useEffect, useState } from "react";

const ImageWithReference = ({
  avatar = true,
  imageRef,
  thumbRef,
}: {
  avatar?: boolean;
  imageRef: string;
  thumbRef?: string;
}) => {
  const [imageUrl, setImageUrl] = useState<null | string>(null);
  const [thumbUrl, setThumbUrl] = useState<null | string>(null);

  useEffect(() => {
    if (!imageRef) return;
    const storage = getStorage();

    const pathReference = ref(storage, imageRef);
    const thumbReference = thumbRef ? ref(storage, thumbRef) : null;
    getDownloadURL(pathReference).then(setImageUrl);
    thumbReference && getDownloadURL(thumbReference).then(setThumbUrl);
  }, [imageRef, thumbRef]);

  if (imageUrl) {
    return avatar ? (
      <Avatar
        style={{ cursor: "pointer" }}
        variant="rounded"
        src={thumbUrl ?? imageUrl ?? ""}
        onClick={() => window.open(imageUrl ?? "", "_blank")}
      />
    ) : (
      <img
        onClick={() => window.open(imageUrl ?? "", "_blank")}
        style={{ borderRadius: 6, cursor: "pointer", maxHeight: 200 }}
        src={thumbUrl ?? imageUrl}
      />
    );
  } else {
    return <p>No image</p>;
  }
};

export default ImageWithReference;
