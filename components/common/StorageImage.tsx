import Image, { ImageProps } from "next/image";
import { useStorageURL } from "../../lib/hooks/use-storage-url";

interface StorageImageProps extends Omit<Partial<ImageProps>, "src"> {
  path: string;
  bucket?: string;
}

export default function StorageImage({
  path,
  bucket,
  ...other
}: StorageImageProps) {
  const [url] = useStorageURL(path, bucket);
  if (!url) {
    return null;
  }

  return (
    <Image {...other} alt={other.alt ?? "image"} src={url} loading="lazy" />
  );
}
