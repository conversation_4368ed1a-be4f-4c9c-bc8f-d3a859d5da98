import Box from "@mui/material/Box";
import CheckIcon from "@mui/icons-material/Check";
import Chip from "@mui/material/Chip";
import MenuItem from "@mui/material/MenuItem";
import OutlinedInput from "@mui/material/OutlinedInput";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import { FormControl, InputLabel } from "@mui/material";
import { LabeledValue } from "../../config/constants";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      display: "inherit",
      zIndex: 9999999,
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
    },
  },
};

interface MultipleSelectProps<T extends string> {
  handleChange: (event: SelectChangeEvent<string[]>) => void;
  selected: string[];
  selections: LabeledValue<T>[];
  title: string;
}

function MultipleSelect<T extends string>({
  handleChange,
  selected,
  selections,
  title,
}: MultipleSelectProps<T>) {
  return (
    <FormControl>
      <InputLabel style={{ width: 460 }}>
        {selected.length !== 0 && (
          <Chip
            style={{ marginRight: 12 }}
            size="small"
            label={selected.length}
          />
        )}
        {title}
      </InputLabel>
      <Select
        style={{ width: 460, marginTop: 8 }}
        variant="outlined"
        multiple
        label={title}
        size="small"
        value={selected}
        onChange={handleChange}
        input={<OutlinedInput label="Chip" />}
        renderValue={(items: string[]) => (
          <Box
            sx={{
              overflow: "scroll",
            }}
          >
            {items.map((value) => (
              <Chip
                style={{ marginRight: 8, borderRadius: 4 }}
                size="small"
                key={value}
                label={value}
              />
            ))}
          </Box>
        )}
        MenuProps={MenuProps}
      >
        {selections.map((item, index) => (
          <MenuItem key={index} value={item.value as unknown as string}>
            {item.label}
            <Box sx={{ flexGrow: 1 }} />
            {selected.includes(item.value) && <CheckIcon />}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}

export default MultipleSelect;
