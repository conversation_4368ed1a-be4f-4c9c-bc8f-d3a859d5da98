import AuthLayer from "./AuthLayer";
import DoneHead from "./Head";
import Loading from "./Loading";
import { sv as svLocale } from "date-fns/locale";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { createContext, FC, ReactNode, useMemo, useState } from "react";
import { APIProvider } from "@vis.gl/react-google-maps";
import { GOOGLE_MAP_API_KEY } from "../../config/constants";
import { SnackbarProvider } from "notistack";
import { GlobalStyles, ThemeProvider } from "@mui/material";
import { createDoneTheme } from "../../theme/theme";
import {
  backgroundDefaultDark,
  backgroundDefaultLight,
} from "../../theme/colors";
import DialogManager from "../dialogs/DialogManager";
import AuthProvider from "../auth/AuthProvider";

export const ColorModeContext = createContext({ toggleColorMode: () => {} });

interface DoneProps {
  children: ReactNode;
}

const Done: FC<DoneProps> = ({ children }) => {
  const [mode, setMode] = useState<"light" | "dark">(
    window.matchMedia &&
      window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light",
  );
  const colorMode = useMemo(
    () => ({
      toggleColorMode: () => {
        setMode((prevMode) => (prevMode === "light" ? "dark" : "light"));
      },
    }),
    [],
  );

  const theme = useMemo(() => createDoneTheme(mode), [mode]);

  return (
    <AuthProvider loadingComponent={<Loading />}>
      <APIProvider apiKey={GOOGLE_MAP_API_KEY}>
        <ColorModeContext.Provider value={colorMode}>
          <GlobalStyles
            styles={{
              body: {
                backgroundColor:
                  mode === "dark"
                    ? backgroundDefaultDark
                    : backgroundDefaultLight,
              },
            }}
          />
          <DoneHead />
          <LocalizationProvider
            adapterLocale={svLocale}
            dateAdapter={AdapterDateFns}
          >
            <SnackbarProvider
              maxSnack={3}
              anchorOrigin={{
                vertical: "top",
                horizontal: "center",
              }}
            >
              <DialogManager>
                <ThemeProvider theme={theme}>
                  <AuthLayer>{children}</AuthLayer>
                </ThemeProvider>
              </DialogManager>
            </SnackbarProvider>
          </LocalizationProvider>
        </ColorModeContext.Provider>
      </APIProvider>
    </AuthProvider>
  );
};

export default Done;
