import Head from "next/head";
import { doneColor } from "../../theme/colors";

const DoneHead = () => {
  return (
    <Head>
      <link
        rel="apple-touch-icon"
        sizes="180x180"
        href="/apple-touch-icon.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="32x32"
        href="/favicon-32x32.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="16x16"
        href="/favicon-16x16.png"
      />
      <link rel="manifest" href="/site.webmanifest" />
      <link
        href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"
        rel="stylesheet"
        integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN"
        crossOrigin="anonymous"
      />

      <link rel="mask-icon" href="/safari-pinned-tab.svg" color={doneColor} />
      <meta name="msapplication-TileColor" content={doneColor} />
      <meta name="theme-color" content="#ffffff"></meta>
      <title> Done | Backoffice </title>
    </Head>
  );
};

export default DoneHead;
