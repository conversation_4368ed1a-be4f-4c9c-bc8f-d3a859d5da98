import { CopyAll } from "@mui/icons-material";
import { Typography, IconButton, Box, Stack } from "@mui/material";
import { User } from "../../models/user/user";
import CallUserLink from "./CallUserLink";

/**
 * @param user  A raw, unformatted international phone number (e.g. +46701234567)
 */
export default function PhoneNumberInfoItemRow({ user }: { user: User }) {
  return (
    <Stack spacing={2} alignItems="center" direction="row">
      <Typography color="text.secondary" variant="body2">
        {"Phone Number:"}
        <Typography
          sx={{
            whiteSpace: "pre-line",
          }}
          fontWeight={600}
          variant="body2"
          color="text.primary"
        >
          <CallUserLink user={user} />
        </Typography>
      </Typography>
      <Box flex={1} />

      {user.phoneNumber && (
        <IconButton
          sx={{ color: "text.disabled" }}
          onClick={() => {
            navigator.clipboard.writeText(user.phoneNumber!);
          }}
          size="small"
        >
          <CopyAll />
        </IconButton>
      )}
    </Stack>
  );
}
