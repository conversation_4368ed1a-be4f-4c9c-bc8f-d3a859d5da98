import {
  doc,
  getFirestore,
  arrayUnion,
  Timestamp,
  setDoc,
} from "firebase/firestore";
import { parsePhoneNumber } from "libphonenumber-js";
import Link from "next/link";
import { AuthUser, User } from "../../models/user/user";
import { useAuthUser } from "../auth/AuthProvider";

/**
 * A link that can be clicked to call a user. Creates a tel: link
 * and tracks the click.
 * @param user The user to enable calling
 */
export default function CallUserLink({ user }: { user: User }) {
  const auth = useAuthUser();
  return user.phoneNumber ? (
    <Link
      href={`tel:${user.phoneNumber}`}
      onClick={() => trackCall(auth, user)}
    >
      {parsePhoneNumber(user.phoneNumber)?.formatInternational()}
    </Link>
  ) : (
    <p> – </p>
  );
}

/**
 * Tracks a call to a user from Backoffice.
 * Sends an increment to the backofficeCalls document.
 * @param user The user that was called
 */
export async function trackCall(caller: Auth<PERSON>ser, callee: User) {
  if (caller.isSuperAdmin !== true) return; // No-op on tracking if user is not super admin

  const ref = doc(
    getFirestore(),
    `admin/backofficeCalls/callers/${caller.userDoc!.reference.id}`,
  );
  await setDoc(
    ref,
    {
      [callee.reference.id]: arrayUnion(Timestamp.now()),
    },
    { merge: true },
  );
}
