import Footer from "./Footer";
import Layout from "../layout/Layout";
import Login from "../user/Login";
import PartnerLayout from "../layout/PartnerLayout";
import { FC, ReactNode } from "react";
import { INTERCOM_APP_ID } from "../../config/constants";
import { IntercomProvider } from "react-use-intercom";
import { useAuthUser } from "../auth/AuthProvider";
import { Button, Paper } from "@mui/material";
import WarningPage from "./WarningPage";
import { Block, Logout } from "@mui/icons-material";
import { getAuth, signOut } from "firebase/auth";

interface AuthLayerProps {
  children: ReactNode;
}

const AuthLayer: FC<AuthLayerProps> = ({ children }) => {
  const authContext = useAuthUser();
  const { partner, partnerDoc } = authContext;

  if (!authContext.firebaseUser || !authContext.firebaseUser.uid)
    return (
      <Paper>
        <Login />
        <Footer />
      </Paper>
    );

  return (
    <>
      {partner ? (
        <IntercomProvider
          appId={INTERCOM_APP_ID!}
          autoBoot
          autoBootProps={{
            hideDefaultLauncher: true,
            alignment: "left",
            userId: authContext.userDoc?.reference?.id,
            email: authContext.userDoc?.email,
            name: authContext.userDoc?.firstName,
            company: partnerDoc
              ? {
                  companyId: partnerDoc.reference.id,
                  name: partnerDoc.name,
                }
              : undefined,
          }}
        >
          <PartnerLayout>{children}</PartnerLayout>
        </IntercomProvider>
      ) : authContext.isSuperAdmin ? (
        <Layout>{children}</Layout>
      ) : (
        <WarningPage
          icon={<Block />}
          severity="error"
          title="Permission denied"
          message="We’re sorry, but it appears you do not have the necessary permissions
      to access this resource or perform the requested action. For the
      security of your account and our system. Contact done dev or support team for assistance and please click the log out
      button."
        >
          <Button
            style={{ maxWidth: 100 }}
            startIcon={<Logout />}
            color="warning"
            onClick={() => {
              signOut(getAuth());
            }}
          >
            Log out
          </Button>
        </WarningPage>
      )}
    </>
  );
};

export default AuthLayer;
