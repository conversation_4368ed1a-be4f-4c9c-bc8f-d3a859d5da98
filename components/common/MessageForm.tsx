import { Job } from "../../models/job/job";
import { useState } from "react";
import { TextField as Mui<PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, Button } from "@mui/material";
import { Send } from "@mui/icons-material";
import Form from "./Form";
import { User } from "../../models/user/user";
import Handlebars from "handlebars";
import TextField from "../form-fields/TextField";
import Internal from "../auth/Internal";
import { TemplateSelect } from "../common/TemplateSelect";

interface MessageFormProps {
  job?: Job;
  user?: User;
  templateId: string;
  placeholder: string;
  onSubmit: (message: string) => void;
}

export default function MessageForm({
  job,
  user,
  templateId,
  placeholder,
  onSubmit,
}: MessageFormProps) {
  const [sending, setSending] = useState(false);
  const [templateFields, setTemplateFields] = useState<string[]>([]);
  const [templateValues, setTemplateValues] = useState<Map<string, string>>(
    new Map(),
  );
  const [message, setMessage] = useState({ message: "" });
  const [template, setTemplate] = useState("");

  const handleTemplateSelect = (
    content: string,
    fields: string[],
    values: Map<string, string>,
  ) => {
    const messageTemplate = Handlebars.compile(content);

    setMessage({
      message: messageTemplate(Object.fromEntries(values)),
    });
    setTemplate(content);
    setTemplateFields(fields);
    setTemplateValues(values);
  };

  const handleTemplateFieldChange = (change: any, field: string) => {
    const newValues = new Map(templateValues);
    newValues.set(field, change.target.value);

    setTemplateValues(newValues);

    if (template) {
      const smsTemplate = Handlebars.compile(template);
      setMessage({
        message: smsTemplate(Object.fromEntries(newValues)),
      });
    }
  };

  return (
    <Stack spacing={1.5}>
      <Internal>
        <TemplateSelect
          templateId={templateId}
          user={user}
          job={job}
          onTemplateSelect={handleTemplateSelect}
        />
      </Internal>

      {templateFields.map((field) => (
        <MuiTextField
          key={field}
          error={templateValues.get(field)?.length == 0}
          value={templateValues.get(field)}
          onChange={(change) => handleTemplateFieldChange(change, field)}
          size="small"
          label={field}
          variant="outlined"
        />
      ))}

      <Form
        initialValues={message}
        onSubmit={async (values, form) => {
          setSending(true);
          onSubmit(values.message);
          setTemplate("");
          setMessage({ message: "" });
          setTemplateFields([]);
          setTemplateValues(new Map());
          setSending(false);
          form.reset();
        }}
      >
        <Stack spacing={1} direction="row">
          <TextField
            size="small"
            name="message"
            type="text"
            label={placeholder}
            multiline
          />

          <Button
            endIcon={sending ? null : <Send />}
            size="small"
            type="submit"
            disabled={sending}
            variant="contained"
            color="primary"
          >
            {sending ? "Sending..." : "Send"}
          </Button>
        </Stack>
      </Form>
    </Stack>
  );
}
