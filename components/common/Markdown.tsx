import { Link, Typography } from "@mui/material";
import MuiMarkdown, { getOverrides } from "mui-markdown";

export default function Markdown({ children }: { children?: string | null }) {
  return (
    <MuiMarkdown
      overrides={{
        ...getOverrides(),
        p: {
          component: Typography,
          props: {
            variant: "body2",
          },
        },
        a: {
          component: Link,
          props: {
            style: {
              textDecoration: "underline",
              wordBreak: "break-all",
            },
            variant: "body2",
          },
        },
        span: {
          component: Typography,
          props: {
            variant: "body2",
            component: "span",
          },
        },
        img: {
          component: "img",
          props: {
            style: {
              maxWidth: "300px",
            },
          },
        },
      }}
    >
      {children}
    </MuiMarkdown>
  );
}
