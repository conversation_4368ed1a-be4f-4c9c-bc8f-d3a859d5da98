import { Button, ButtonProps } from "@mui/material";

interface FileButtonProps extends Omit<ButtonProps<"label">, "onChange"> {
  multiple?: boolean;
  accept?: string;
  onChange: (files: FileList | null) => void;
}

export default function FileButton({
  multiple = false,
  accept = "*",
  onChange,
  children = "Upload files",
  ...props
}: FileButtonProps) {
  return (
    <Button {...props} component="label" tabIndex={-1}>
      {children}
      <VisuallyHiddenInput
        accept={accept}
        type="file"
        onChange={(event) => onChange(event.target.files)}
        multiple={multiple}
      />
    </Button>
  );
}

function VisuallyHiddenInput(
  props: React.InputHTMLAttributes<HTMLInputElement>,
) {
  return <input {...props} style={{ display: "none" }} />;
}
