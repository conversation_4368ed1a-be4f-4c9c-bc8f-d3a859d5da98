import { Box, Button, Dialog } from "@mui/material";
import { DocumentReference, updateDoc } from "@firebase/firestore";
import { FileDrop } from "react-file-drop";
import { useState } from "react";
import {
  getStorage,
  ref,
  StorageReference,
  uploadBytes,
} from "firebase/storage";
import { Photo } from "@mui/icons-material";
import { LoadingButton } from "@mui/lab";

const getFileRef = (fileKey: any) => {
  const storageRef = getStorage();

  return ref(storageRef, fileKey);
};

const UploadForm = ({
  documentRef,
  dataKey,
  fileRef,
  onClose,
}: {
  documentRef: DocumentReference;
  dataKey: string;
  fileRef: StorageReference;
  onClose: () => void;
}) => {
  const [file, setFile] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const handleDrop = (files: any, event: any) => {
    setFile(files[0]);
  };

  const uploadFile = async () => {
    setLoading(true);
    if (
      !file ||
      !file?.type ||
      !["image/jpeg", "image/png"].includes(file?.type)
    ) {
      setLoading(false);
      return window.alert("File not found or not valid type");
    }

    await uploadBytes(fileRef, file);

    await updateDoc(documentRef, { [dataKey]: fileRef.fullPath });

    setLoading(false);
    if (onClose) onClose();
  };

  return (
    <>
      <FileDrop onDrop={handleDrop}>
        <Box
          style={{
            backgroundColor: "green",
            color: "white",
            padding: 20,
          }}
        >
          <p> Drop some files here!</p>
        </Box>
      </FileDrop>
      <LoadingButton
        fullWidth
        variant="contained"
        color="primary"
        loading={loading}
        disabled={!file}
        onClick={uploadFile}
      >
        Upload
      </LoadingButton>
    </>
  );
};

const UploadImageButton = ({
  documentRef,
  dataKey,
  fileKey,
}: {
  documentRef: DocumentReference;
  dataKey: string;
  fileKey: string;
}) => {
  const [open, setOpen] = useState(false);

  const onOpen = () => setOpen(true);
  const onClose = () => setOpen(false);

  const fileRef = getFileRef(fileKey);

  return (
    <>
      <Button
        startIcon={<Photo />}
        size="small"
        variant="outlined"
        color="primary"
        onClick={onOpen}
      >
        Image
      </Button>
      <Dialog open={open} onClose={onClose}>
        <UploadForm
          documentRef={documentRef}
          dataKey={dataKey}
          fileRef={fileRef}
          onClose={onClose}
        />
      </Dialog>
    </>
  );
};

export default UploadImageButton;
