import { ReactElement } from "react";
import Stack from "@mui/material/Stack";
import Alert from "@mui/material/Alert";
import AlertTitle from "@mui/material/AlertTitle";
import Typography from "@mui/material/Typography";
import DoneLogo from "./DoneLogo";

interface WarningPageProps {
  title: string;
  message: string;
  children?: ReactElement;
  icon?: ReactElement;
  severity?: "error" | "warning" | "info" | "success";
}

export default function WarningPage({
  title,
  message,
  children,
  icon,
  severity = "info",
}: WarningPageProps) {
  return (
    <Stack
      maxWidth={800}
      spacing={2}
      sx={{ margin: 10, color: "text.primary" }}
    >
      <Stack alignItems="center" direction="row" spacing={2}>
        <DoneLogo size={22} />
        <Typography style={{ fontWeight: 600 }} variant="h6">
          Backoffice
        </Typography>
      </Stack>

      <Alert severity={severity} icon={icon ? icon : null}>
        <Stack spacing={1.5}>
          <AlertTitle>{title}</AlertTitle>
          <Typography variant="body2">{message}</Typography>
        </Stack>
      </Alert>

      {children && <div style={{ maxWidth: 150 }}>{children}</div>}
    </Stack>
  );
}
