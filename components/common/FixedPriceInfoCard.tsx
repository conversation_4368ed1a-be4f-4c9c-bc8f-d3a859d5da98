import { Paper, List, ListItem, ListItemText } from "@mui/material";
import { doc, getFirestore } from "firebase/firestore";
import { FixedPriceProduct } from "../../models/job/job";
import { useDocument } from "react-firebase-hooks/firestore";
import { FixedPriceJob } from "../../models/job/fixed_price_job";
import formatAmount from "../../lib/format-amount";

const FixedPriceInfoCard = ({
  fixedPriceId,
  quantity,
  products,
}: {
  fixedPriceId: string;
  quantity?: number;
  products?: Array<FixedPriceProduct>;
}) => {
  const db = getFirestore();

  const [fixedPriceJobSnap, loading] = useDocument(
    doc(db, `fixed-price-jobs/${fixedPriceId}`),
  );

  if (loading) return null;

  if (!fixedPriceJobSnap?.exists())
    return (
      <h4 style={{ color: "red" }}>
        Fixed Price Job is not in current list! {fixedPriceId}
      </h4>
    );

  const fixedPriceJob = {
    ...fixedPriceJobSnap.data(),
    reference: fixedPriceJobSnap.ref,
  } as FixedPriceJob;

  return (
    <Paper variant="outlined">
      <List dense component="nav" aria-label="mailbox folders">
        <ListItem dense divider>
          <ListItemText
            primary="Fixed price title:"
            secondary={`${quantity ?? "1"} x ${fixedPriceJob.title}`}
          />
        </ListItem>
        <ListItem dense>
          <ListItemText
            primary="Price"
            secondary={`${
              fixedPriceJob.priceBreakdown.laborBeforeDeduction
                ? `${formatAmount(
                    fixedPriceJob.priceBreakdown.laborBeforeDeduction,
                  )} arb.`
                : ""
            }  + ${formatAmount(
              fixedPriceJob.priceBreakdown.materialBeforeDeduction,
            )}mat. ${
              fixedPriceJob.deductionType
                ? `- ${formatAmount(
                    fixedPriceJob.priceBreakdown.totalDeduction,
                  )} ${fixedPriceJob.deductionType}`
                : ""
            } = ${formatAmount(
              fixedPriceJob.priceBreakdown.totalCustomerPriceAfterDeduction,
            )}`}
          />
        </ListItem>

        {products &&
          products.map((e) => {
            return (
              <ListItem key={e.articleId} dense>
                <a
                  style={{ color: "purple" }}
                  href={e.webURL}
                >{`${e.units}x ${e.title}`}</a>
              </ListItem>
            );
          })}
      </List>
    </Paper>
  );
};

export default FixedPriceInfoCard;
