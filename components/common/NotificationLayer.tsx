import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { useAuthUser } from "../auth/AuthProvider";
import { ReactNode, useEffect, useState } from "react";
import { SnackbarContent, useSnackbar } from "notistack";
import { DialogContentText, Stack, Typography } from "@mui/material";
import {
  addDoc,
  collection,
  getFirestore,
  serverTimestamp,
} from "firebase/firestore";
import { VAPID_KEY } from "../../config/constants";
import ConfirmDialog, { ConfirmActions } from "../dialogs/ConfirmDialog";
import { Inbox } from "@mui/icons-material";
import Router from "next/router";

interface NotificationLayerProps {
  children: ReactNode;
}

export default function NotificationLayer({
  children,
}: NotificationLayerProps) {
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    if (
      isNotificationSupported() &&
      typeof window !== "undefined" &&
      "serviceWorker" in navigator
    ) {
      const messaging = getMessaging();
      const unsubscribe = onMessage(messaging, (payload) => {
        enqueueSnackbar(
          <SnackbarContent
            onClick={() => {
              if (!payload.data?.url) return;

              Router.push(payload.data.url);
            }}
          >
            <Stack
              maxWidth={300}
              spacing={2}
              alignItems="inline"
              direction="row"
            >
              <Inbox style={{ marginTop: 4 }} fontSize="small" />
              <Stack>
                {payload.data?.title && (
                  <Typography fontWeight={600} variant="body2">
                    {payload.data?.title}
                  </Typography>
                )}

                <Typography variant="body2" noWrap>
                  {payload.data?.body}
                </Typography>
              </Stack>
            </Stack>
          </SnackbarContent>,
          {
            style: { cursor: "pointer" },
            autoHideDuration: 5000, // 5 second
            anchorOrigin: {
              vertical: "top",
              horizontal: "right",
            },
          },
        );
      });

      return () => {
        unsubscribe();
      };
    }

    return;
  }, [enqueueSnackbar]);

  return (
    <>
      {isNotificationSupported() && <NotificationPermissionDialog />}
      {children}
    </>
  );
}

function NotificationPermissionDialog() {
  const authUser = useAuthUser();
  const [open, setOpen] = useState(Notification.permission !== "granted");
  const { enqueueSnackbar } = useSnackbar();

  return (
    <ConfirmDialog
      isOpen={open}
      onClose={() => {
        setOpen(false);
      }}
      title={"Notification permission"}
      actions={[
        ConfirmActions.cancel,
        {
          title: "Allow",
          onClick: async () => {
            Notification.requestPermission();

            /*
            That was the issue with safari and mobile and 10/6 on chrome.

            `Notification.requestPermission();`

            This function is super unpredictable. On safari it sometimes return promise sometimes not. And firebase also check
            `Notification.permission` in order to retrieve token. So That 5 second delay is dirt hack to solve the issue.
            */
            await new Promise((resolve) => setTimeout(resolve, 4000));

            if (Notification.permission === "granted") {
              const token = await getToken(getMessaging(), {
                vapidKey: VAPID_KEY,
              });

              await addDoc(
                collection(
                  getFirestore(),
                  `users/${authUser.userDoc?.reference.id}/devices`,
                ),
                {
                  createTime: serverTimestamp(),
                  token: token,
                  client: "co.doneservices.backoffice",
                },
              );

              enqueueSnackbar("Notification permission granted", {
                anchorOrigin: {
                  vertical: "bottom",
                  horizontal: "right",
                },
                variant: "success",
              });
            }
          },
        },
      ]}
    >
      <DialogContentText>
        In order to get notification for inbox updates accept notification from
        browser prompt as well. permissions.
      </DialogContentText>
    </ConfirmDialog>
  );
}

const isNotificationSupported = () => "Notification" in window;
