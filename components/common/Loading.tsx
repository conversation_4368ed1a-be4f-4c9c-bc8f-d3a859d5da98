import { Avatar, Box, CircularProgress, Fade, Grid2 } from "@mui/material";

const Loading = () => {
  return (
    <Grid2
      container
      spacing={0}
      direction="column"
      alignItems="center"
      justifyContent="center"
      sx={{ minHeight: "80vh", minWidth: "100%" }} // Use sx prop for styling
    >
      <Grid2>
        <Box sx={{ m: 1, position: "relative" }}>
          <Fade
            in
            style={{
              transitionDelay: "200ms",
            }}
            unmountOnExit
          >
            <Avatar
              sx={{
                // Use sx prop for styling
                position: "absolute",
                width: 52,
                height: 52,
                marginTop: "-26px",
                marginLeft: "-26px",
              }}
              src="https://uploads-ssl.webflow.com/5ccd614f15eeacce24eec18e/5ff757e1b0c536be282ffeef_done%20app%20logo.png"
            />
          </Fade>
          <Fade
            in
            style={{
              transitionDelay: "200ms",
            }}
            unmountOnExit
          >
            <CircularProgress
              size={62}
              sx={{
                // Use sx prop for styling
                color: "#9d58ed",
                position: "absolute",
                top: "50%",
                left: "50%",
                marginTop: "-31px",
                marginLeft: "-31px",
              }}
            />
          </Fade>
        </Box>
      </Grid2>
    </Grid2>
  );
};

export default Loading;
