import { CopyAll, Warning } from "@mui/icons-material";
import { Typography, IconButton, Box, Stack } from "@mui/material";
import React from "react";

/**
 * @param required  If true, show warning icon when value is null
 */
export default function InfoItemRow({
  icon = null,
  title,
  value,
  noCopy,
  required = false,
}: {
  icon?: React.ReactNode;
  title: string;
  value?: string | null;
  noCopy?: boolean;
  required?: boolean;
}) {
  if (!required && !value) return null;

  return (
    <Stack spacing={2} alignItems="center" direction="row">
      {icon}
      <Typography color="text.secondary" variant="body2">
        {title}:
        <Typography
          sx={{
            whiteSpace: "pre-line",
            fontStyle: value ? "normal" : "italic",
          }}
          fontWeight={value ? 600 : undefined}
          variant="body2"
          color="text.primary"
        >
          {value ?? "No data"}
        </Typography>
      </Typography>
      <Box flex={1} />
      {!value && required && <Warning sx={{ color: "warning.main" }} />}

      {!noCopy && value && (
        <IconButton
          sx={{ color: "text.disabled" }}
          onClick={() => {
            navigator.clipboard.writeText(value);
          }}
          size="small"
        >
          <CopyAll />
        </IconButton>
      )}
    </Stack>
  );
}
