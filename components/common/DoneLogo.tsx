import { useTheme } from "@mui/material";
import { CSSProperties } from "@mui/styles";
import Image from "next/image";

export default function DoneLogo({
  style,
  size = 16,
}: {
  style?: CSSProperties;
  size?: number;
}) {
  const theme = useTheme();

  return theme.palette.mode == "dark" ? (
    <Image
      src="/done-logo-dark.png"
      width={size}
      style={style}
      height={size}
      alt="done logo"
    />
  ) : (
    <Image
      src="/done-logo-light.png"
      width={size}
      style={style}
      height={size}
      alt="done logo"
    />
  );
}
