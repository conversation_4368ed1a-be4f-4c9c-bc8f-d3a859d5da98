import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";

export interface AlertMessage {
  severity: "error" | "warning" | "info" | "success";
  title?: string;
  message: string;
  action?: {
    label: string;
    url: string;
  };
}

/**
 * Display a message banner with a title, content and an optional action button.
 */
export default function MessageBanner({
  message,
}: {
  message: AlertMessage | undefined;
}) {
  if (!message) return null;
  const { severity, title, message: content, action } = message;
  return (
    <Alert
      severity={severity}
      action={
        action ? (
          <Button
            color="inherit"
            size="small"
            onClick={() => {
              window.open(action.url, "_blank");
            }}
          >
            {action.label}
          </Button>
        ) : null
      }
    >
      {title ? <AlertTitle>{title}</AlertTitle> : null}
      {content}
    </Alert>
  );
}
