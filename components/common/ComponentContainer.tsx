import { Grid2, Paper, Typography } from "@mui/material";
import { ReactNode } from "react";

const ComponentContainer = ({
  children,
  title,
  actions,
  padding = 4,
  hidden = false,
}: {
  children: ReactNode;
  title?: string;
  actions?: ReactNode;
  padding?: number;
  hidden?: boolean;
}) => {
  if (hidden) return null;
  return (
    <div style={{ padding }}>
      <Paper variant="outlined" style={{ width: "100%", padding: 12 }}>
        <Grid2 container spacing={1} alignItems="center">
          {title && (
            <Grid2 size={"grow"}>
              <Typography style={{ fontWeight: 700 }}>{title}</Typography>
            </Grid2>
          )}
          <Grid2>{actions}</Grid2>
          <Grid2 size={12}>{children}</Grid2>
        </Grid2>
      </Paper>
    </div>
  );
};

export default ComponentContainer;
