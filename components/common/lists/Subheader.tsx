import { ListSubheader, Typography } from "@mui/material";

interface SubheaderProps {
  children: React.ReactNode;

  /**
   * If true, the subheader will not have a top border.
   * Useful for the first subheader in a list.
   */
  noTopBorder?: boolean;
}

/**
 * A subheader for a list.
 * Used for sectioning items in a list.
 *
 * @example
 * ```tsx
 * <List>
 *   <Subheader>New</Subheader>
 *   <ListItem>New item 1</ListItem>
 *   <ListItem>New item 2</ListItem>
 *   <Subheader>Old</Subheader>
 *   <ListItem>Old item 1</ListItem>
 *   <ListItem>Old item 2</ListItem>
 * </List>
 * ```
 */
export function Subheader({ children, noTopBorder = false }: SubheaderProps) {
  return (
    <ListSubheader
      sx={{
        borderTop: noTopBorder ? undefined : "1px solid",
        borderBottom: "1px solid",
        borderColor: "divider",
        lineHeight: "2",
      }}
    >
      <Typography variant="caption">{children}</Typography>
    </ListSubheader>
  );
}
