import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>utton,
  <PERSON>I<PERSON>,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from "@mui/material";

import {
  FolderOutlined,
  KeyboardArrowDown,
  KeyboardArrowRight,
  SubdirectoryArrowRight,
} from "@mui/icons-material";
import { useState } from "react";

export type ExpandableItemProps =
  | UncontrolledExpandableItemProps
  | ControlledExpandableItemProps;

interface BaseExpandableItemProps {
  title: string;
  subtitle?: string;
  accessory?: React.ReactNode;
  onClick?: () => void;
  selected?: boolean;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  level?: number;
}

/**
 * An uncontrolled expandable item that is managed by the component itself.
 */
interface UncontrolledExpandableItemProps extends BaseExpandableItemProps {
  expanded?: never;
  onToggleExpand?: never;
}

/**
 * A controlled expandable item that is managed by the parent component.
 */
interface ControlledExpandableItemProps extends BaseExpandableItemProps {
  expanded: boolean;
  onToggleExpand?: () => void;
}

export default function ExpandableItem({
  title,
  subtitle,
  accessory,
  selected,
  expanded,
  onClick,
  onToggleExpand,
  children,
  icon,
  level = 0,
}: ExpandableItemProps) {
  const [internalExpanded, setInternalExpanded] = useState(false);
  const isControlled = expanded !== undefined && onToggleExpand !== undefined;
  const isExpanded = isControlled ? expanded : internalExpanded;

  return (
    <>
      <ListItem
        disablePadding
        divider
        sx={{ backgroundColor: "background.paper" }}
      >
        <ListItemButton
          onClick={() => onClick?.()}
          onDoubleClick={() => {
            setInternalExpanded(!isExpanded);
            onToggleExpand?.();
          }}
          selected={selected}
          sx={{ pl: 1 * level }}
        >
          <IconButton
            size="small"
            sx={{ width: 24, height: 24 }}
            onClick={(event) => {
              event.stopPropagation();
              onToggleExpand?.();
              setInternalExpanded(!isExpanded);
            }}
          >
            {isExpanded ? (
              <KeyboardArrowDown fontSize="small" color="disabled" />
            ) : (
              <KeyboardArrowRight fontSize="small" color="disabled" />
            )}
          </IconButton>
          <ListItemIcon>
            {icon ? (
              icon
            ) : level === 0 ? (
              <FolderOutlined />
            ) : (
              <SubdirectoryArrowRight />
            )}
          </ListItemIcon>
          <ListItemText primary={title} secondary={subtitle} />
          {accessory}
        </ListItemButton>
      </ListItem>
      <Collapse in={isExpanded} timeout="auto">
        {children}
      </Collapse>
    </>
  );
}
