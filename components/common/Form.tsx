import { useEffect } from "react";
import {
  FormProps as FinalFormProps,
  Form as FinalForm,
  useForm,
} from "react-final-form";

interface FormProps extends FinalFormProps {
  children: React.ReactNode;
  render?: never; // Block usage of render prop as Omit causes parsing errors.
  component?: never;
  enableShortcuts?: boolean;
}

/**
 * Convenience form wrapper allowing children.
 * @param props Properties for the form. See `FormProps` for full specification.
 */
export default function Form({
  enableShortcuts = false,
  children,
  ...props
}: FormProps) {
  return (
    <FinalForm
      {...props}
      render={({ handleSubmit }) => (
        <form onSubmit={handleSubmit}>
          {enableShortcuts && <FormKeyboardShortcutSetup />}
          {children}
        </form>
      )}
    />
  );
}

function FormKeyboardShortcutSetup() {
  const form = useForm();

  useEffect(() => {
    document.addEventListener("keydown", (e) => {
      if (
        (e.ctrlKey && e.key === "Enter") ||
        (e.meta<PERSON>ey && e.key === "Enter")
      ) {
        form.submit();
      }
    });
  }, [form]);

  return null;
}
