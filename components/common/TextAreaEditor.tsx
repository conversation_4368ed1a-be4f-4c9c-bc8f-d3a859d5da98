import { useEffect, useState } from "react";
import { Button, Stack, TextField } from "@mui/material";
import { DocumentReference, updateDoc } from "@firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";
import { getProperty } from "dot-prop";

type TextAreaProps = {
  save: any;
  initialText: string;
  minRows: number;
};

function TextArea({ initialText, save, minRows }: TextAreaProps) {
  const [value, setValue] = useState(initialText);
  useEffect(() => {
    setValue(initialText);
  }, [initialText]);
  return (
    <Stack spacing={1}>
      <TextField
        fullWidth
        multiline
        maxRows={10}
        value={value}
        minRows={minRows}
        variant="outlined"
        onChange={(event) => setValue(event.target.value)}
      />

      <Button
        fullWidth
        color="primary"
        variant="contained"
        onClick={() => save(value)}
        disabled={initialText === value}
      >
        Save
      </Button>
    </Stack>
  );
}

type TextAreaEditorProps = {
  docRef: DocumentReference;
  dataKey: string;
  minRows?: number;
};

export default function TextAreaEditor({
  docRef,
  dataKey,
  minRows = 8,
}: TextAreaEditorProps) {
  const [value, loading] = useDocument(docRef);
  if (loading) return null;

  const save = (value: any) => {
    updateDoc(docRef, { [dataKey]: value });
  };

  const job = value?.data();

  const text = job ? getProperty(job, dataKey) || "" : "";
  return <TextArea minRows={minRows} save={save} initialText={text} />;
}
