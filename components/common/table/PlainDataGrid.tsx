import { DataGrid, DataGridProps } from "@mui/x-data-grid";
import {
  query,
  Query,
  QuerySnapshot,
  CollectionReference,
} from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import { SxProps, Theme, useTheme } from "@mui/material";
import { ExtendedGridColDef } from "./columns";

export interface TableRow {
  id: string;
}

export interface PlainDataGridProps extends Pick<DataGridProps, "density"> {
  rowGenerator: (value?: QuerySnapshot) => TableRow[];
  queryReference: Query | CollectionReference;
  columns: ExtendedGridColDef[];
  onRowSelected?: (id: string) => void;
  sx?: SxProps<Theme>;
}

/**
 * This component provide table for firestore collections.
 *
 * @param rowGenerator it should be function which generate from `snapshot` to `TableRow`
 * @param queryReference query or collection reference
 * @param columns column definitions for `DataGrip` see example from `JobsTable`
 */
export default function PlainDataGrid({
  rowGenerator,
  queryReference,
  columns,
  onRowSelected,
  sx,
  density = "standard",
}: PlainDataGridProps) {
  const dataQuery = query(queryReference);
  const [value, loading, error] = useCollection(dataQuery);

  if (error) return <p style={{ margin: 20 }}>{`ERROR ${error}`}</p>;

  return (
    <PlainDataGridBase
      loading={loading}
      onRowSelected={onRowSelected}
      rows={rowGenerator(value)}
      columns={columns}
      sx={sx}
      density={density}
    />
  );
}

interface PlainDataGridBaseProps extends Pick<DataGridProps, "density"> {
  rows: TableRow[];
  onRowSelected?: (id: string) => void;
  columns: ExtendedGridColDef[];
  loading: boolean;
  sx?: SxProps<Theme>;
}

export function PlainDataGridBase({
  rows,
  columns,
  onRowSelected,
  loading,
  sx = {},
  density = "standard",
}: PlainDataGridBaseProps) {
  const theme = useTheme();

  return (
    <DataGrid
      sx={{
        "&.MuiDataGrid-root .MuiDataGrid-cell:focus-within": {
          outline: "none !important",
        },
        ...sx,
      }}
      onRowClick={(row) => {
        onRowSelected?.(row.id as string);
      }}
      density={density}
      style={{ backgroundColor: theme.palette.background.paper }}
      disableColumnFilter
      showCellVerticalBorder
      loading={loading}
      rows={rows}
      columns={columns}
    />
  );
}
