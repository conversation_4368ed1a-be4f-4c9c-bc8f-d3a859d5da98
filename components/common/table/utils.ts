import {
  GridCellParams,
  GridRenderCellParams,
  GridValueGetter,
  GridValueSetter,
} from "@mui/x-data-grid";
import { get, set } from "lodash";
import formatAmount from "../../../lib/format-amount";
import { asNumber, isNumber } from "../../../lib/numbers";
import { toNumber } from "../../../models/finance/cent-value";
import { formatPercentage as intlFormatPercentage } from "../../../lib/format-percentage";

/**
 * Returns a value getter function that can be used in a data grid column.
 * @param path The path to the value in the row object.
 * @returns A value getter function.
 */
export const valueGetter =
  (path: string): GridValueGetter<any> =>
  (_, row) =>
    get(row, path);
/**
 * Returns a value setter function that can be used in a data grid column.
 * @param path The path to the value in the row object.
 * @returns A value setter function.
 */
export const valueSetter =
  (path: string): GridValueSetter<any> =>
  (value, row) => {
    return set({ ...row }, path, value);
  };

/**
 * Returns a value formatter function that can be used in a data grid column.
 * @returns A value formatter function.
 */
export const amountValueFormatter = (value: GridRenderCellParams) => {
  return isNumber(value) ? formatAmount(value, 2) : value;
};

/**
 * Returns a value formatter function that can be used in a data grid column.
 * @returns A value formatter function.
 */

export const centAmountValueFormatter = (value: number) => {
  return formatCentAmount(value);
};
/**
 * Formats the given value as a cent amount.
 * @param value Value to format.
 * @returns Returns the formatted value.
 */
export const formatCentAmount = (value: number) =>
  isNumber(value) ? formatAmount(toNumber(value)) : value;

/**
 * Returns a value parser function that can be used in a data grid column.
 * @returns A value parser function.
 */
export const centAmountValueParser =
  (): ((value: any | undefined, params?: GridCellParams) => any) => (value) => {
    const numberValue = value !== null ? asNumber(value) : null;
    return numberValue;
  };

/**
 * Returns a value formatter function that can be used in a data grid column.
 * @returns A value formatter function.
 */
export const percentValueFormatter = (value: number) => {
  return formatPercentage(value);
};
/**
 * Formats the given value as a percentage.
 * @param value Value to format.
 * @returns Returns the formatted value.
 */
export const formatPercentage = (value: number) =>
  isNumber(value) ? intlFormatPercentage(value, 2) : value;
