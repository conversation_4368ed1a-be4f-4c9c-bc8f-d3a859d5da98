import { GridColDef, GridSingleSelectColDef } from "@mui/x-data-grid";
import {
  DEDUCTION_TYPES,
  DETAILED_DEDUCTION_TYPES,
  LINE_ITEM_TYPES,
  LINE_ITEM_UNIT_TYPE,
  LabeledValue,
} from "../../../config/constants";
import {
  amountValueFormatter,
  centAmountValueFormatter,
  centAmountValueParser,
  percentValueFormatter,
  valueGetter,
  valueSetter,
} from "./utils";
import formatTimeRelative, {
  formatDate,
  formatTime,
} from "../../../lib/format-time-relative";
import { Tooltip, Typography, TypographyProps } from "@mui/material";
import _ from "lodash";

/**
 * Function that can return column definitions as `GridColDef` to be used in a `DataGrid`.
 * Can be combined with other `*ColumnCreator` functions.
 * Requires that all required properties are provided to it.
 */
export type ExtendedGridColDef = GridColDef & {
  visibility?: boolean;
};

type StrictColumnCreator<
  O = {},
  C extends ExtendedGridColDef = ExtendedGridColDef,
> = (options: O & C) => C;

type StrictColumnCreatorLabel<O = {}, C extends GridColDef = GridColDef> = (
  options: O & C,
) => GridSingleSelectColDef;

/**
 * Function that can return column definitions as `GridColDef` to be used in a `DataGrid`.
 * Can be combined with other `*ColumnCreator` functions.
 */
export type ColumnCreator<
  O = {},
  C extends ExtendedGridColDef = ExtendedGridColDef,
> = (options: O & Partial<C>) => C;

/**
 * Creates a DataGrid column for strings.
 */
export const stringColumn: StrictColumnCreator = (options) => ({
  type: "string",
  renderCell: ({ value }) => (
    <Tooltip arrow title={value}>
      <Typography variant="inherit" noWrap>
        {value}
      </Typography>
    </Tooltip>
  ),
  ...options,
});

/**
 * Creates a DataGrid column for numbers.
 */
export const numberColumn: StrictColumnCreator = (options) => ({
  type: "number",
  ...options,
});

/**
 * Creates a DataGrid column for booleans.
 */
export const booleanColumn: StrictColumnCreator = (options) => ({
  type: "boolean",
  ...options,
});

/**
 * Creates a DataGrid column for dates.
 */
export const dateColumn: StrictColumnCreator = (options) => ({
  headerName: "Date",
  type: "date",
  valueFormatter: (value) => formatDate(value),
  ...options,
});

/**
 * Creates a DataGrid column for timestamps.
 */
export const dateTimeColumn: StrictColumnCreator = (options) => ({
  headerName: "Date",
  type: "dateTime",
  valueFormatter: (value) => formatTime(value),
  ...options,
});

export const relativeDateTimeColumn: StrictColumnCreator<{
  color?: (value: any) => TypographyProps["color"];
}> = ({ color, ...options }) => ({
  headerName: "Date",
  type: "dateTime",
  renderCell: ({ value }) => (
    <Tooltip arrow title={formatTime(value)}>
      <Typography
        variant="inherit"
        noWrap
        sx={color ? { color: `${color(value)}.main` } : undefined}
      >
        {formatTimeRelative(value)}
      </Typography>
    </Tooltip>
  ),
  ...options,
});

/**
 * Creates a DataGrid column pointing to a path.
 * @param option.path Dot-prop path to the property to display. Defaults to value of `field`.
 */
export const dotPropColumn: StrictColumnCreator<{ path?: string }> = ({
  path,
  field,
  ...options
}): GridColDef => ({
  field,
  valueGetter: valueGetter(path ?? field),
  valueSetter: valueSetter(path ?? field),
  ...options,
});

/**
 * Creates a DataGrid column for LabeledValue.
 * @param option.values Possible values that can be choosen.
 * @param option.allowNull Allow value to be set to `null`. Default: `false`.
 */

export const labeledValueColumn: StrictColumnCreatorLabel<{
  values: LabeledValue<unknown>[];
  allowNull?: boolean;
}> = ({ values, allowNull = true, ...options }) =>
  ({
    type: "singleSelect",
    valueOptions: allowNull
      ? [{ value: null, label: "None" }, ...values]
      : values,
    valueFormatter: (value) =>
      values.find((v) => v.value === value)?.label ?? value,
    ...options,
  }) as GridSingleSelectColDef;

/**
 * Creates a DataGrid column containing `RawQuoteLineItemType` values.
 */
export const itemTypeColumn: StrictColumnCreator = (options) => ({
  headerName: "Type",
  ...labeledValueColumn({ values: LINE_ITEM_TYPES, ...options }),
});

/**
 * Creates a DataGrid column containing `LineItemUnitType` values.
 */
export const unitTypeColumn: StrictColumnCreator = (options) => ({
  headerName: "Unit",
  ...labeledValueColumn({ values: LINE_ITEM_UNIT_TYPE, ...options }),
});

/**
 * Creates a DataGrid column containing `DeductionType` values.
 */
export const deductionColumn: StrictColumnCreator = (options) => ({
  headerName: "Deduction",
  ...labeledValueColumn({ values: DEDUCTION_TYPES, ...options }),
});

/**
 * Creates a DataGrid column containing `DetailedDeductionType` values.
 */
export const detailedDeductionColumn: StrictColumnCreator = (options) => ({
  headerName: "Deduction type",
  ...labeledValueColumn({ values: DETAILED_DEDUCTION_TYPES, ...options }),
});

/**
 * Creates a DataGrid column containing amount values.
 */
export const amountColumn: StrictColumnCreator = (options) => ({
  headerName: "Amount",
  type: "number",
  valueFormatter: amountValueFormatter,
  ...options,
});

/**
 * Creates a DataGrid column containing cent amount values.
 */
export const centAmountColumn: StrictColumnCreator = (options) => ({
  headerName: "Amount",
  type: "number",
  valueParser: centAmountValueParser(),
  valueFormatter: centAmountValueFormatter,
  ...options,
});

/**
 * Creates a DataGrid column containing amount values.
 */
export const percentColumn: StrictColumnCreator = (options) => ({
  headerName: "Percent",
  type: "number",
  valueFormatter: percentValueFormatter,
  ...options,
});

/**
 * Creates a DataGrid column containing row actions.
 */
export const actionsColumn = (
  options: Omit<GridColDef, "type">,
): GridColDef => ({
  type: "actions",
  ...options,
});

export function optional<T = GridColDef>(hide: boolean, value: T) {
  return hide ? undefined : value;
}

export function getColumnVisibilityModel(columns: ExtendedGridColDef[]) {
  return _.mapValues(
    _.keyBy(columns, "field"),
    (column) => column.visibility ?? true,
  );
}
