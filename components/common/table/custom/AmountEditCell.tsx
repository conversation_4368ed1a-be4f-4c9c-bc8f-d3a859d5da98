import { GridRenderEditCellParams, useGridApiContext } from "@mui/x-data-grid";
import AmountTextFieldBase, {
  AmountTextFieldBaseProps,
} from "../../../form-fields/raw/AmountTextFieldBase";

type AmountEditCellProps = GridRenderEditCellParams &
  Pick<AmountTextFieldBaseProps, "initialMode" | "allowedModes">;

export default function AmountEditCell({
  id,
  value,
  field,
  initialMode,
  allowedModes,
}: AmountEditCellProps) {
  const apiRef = useGridApiContext();

  return (
    <AmountTextFieldBase
      initialMode={initialMode}
      allowedModes={allowedModes}
      value={value}
      onChange={async (value, mode) => {
        await apiRef.current.setEditCellValue({
          id,
          field,
          value: {
            // We need to reset the fields to make sure that the old value is not kept.
            amount: {
              percentage: undefined,
              value: undefined,
              ...value.amount,
            },
          },
        });
      }}
    />
  );
}
