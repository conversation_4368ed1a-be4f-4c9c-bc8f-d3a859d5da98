import { DataGrid, DataGridProps } from "@mui/x-data-grid";
import {
  Query,
  QuerySnapshot,
  CollectionReference,
  DocumentData,
} from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCount } from "../../../lib/hooks/use-firestore-count";
import { SxProps, Theme, useTheme } from "@mui/material";
import { usePaginatedQuery } from "../../../lib/hooks/use-paginated-query";
import { GridInitialStateCommunity } from "@mui/x-data-grid/models/gridStateCommunity";
import { ExtendedGridColDef } from "./columns";
import {
  GetCursorFunction,
  lastKeyCursor,
} from "../../../lib/hooks/firestore-cursors";

const DEFAULT_PAGE_SIZE = 30;

export interface TableRow {
  /** A unique identifier for the row */
  id: string;

  /** Additional data to be displayed in the table */
  [key: string]: unknown;
}

export interface PaginatedDataGridProps<
  AppModel extends DocumentData = DocumentData,
  DBModel extends DocumentData = DocumentData,
> extends Pick<DataGridProps, "density"> {
  /** Create rows from a query snapshot */
  rowGenerator: (value?: QuerySnapshot<AppModel, DBModel>) => TableRow[];

  /**
   * Find cursor from query snapshot. Must match the orderBy constraints on the query.
   *
   * The `createTime` of the last document will be used by default if not provided.
   */
  getCursor?: GetCursorFunction<AppModel, DBModel>;

  /** Query or collection reference */
  query: Query<AppModel, DBModel> | CollectionReference<AppModel, DBModel>;

  /** Column definitions */
  columns: ExtendedGridColDef[];

  /** Callback when a row is selected */
  onRowSelected?: (id: string) => void;

  /** Custom styles */
  sx?: SxProps<Theme>;

  /**
   * Number of rows per page.
   * @default 30
   */
  pageSize?: number;

  initialState?: GridInitialStateCommunity;
}

export default function PaginatedDataGrid<
  AppModel extends DocumentData,
  DBModel extends DocumentData = DocumentData,
>({
  rowGenerator,
  query: baseQuery,
  getCursor,
  columns,
  onRowSelected,
  sx,
  density = "standard",
  pageSize = DEFAULT_PAGE_SIZE,
  initialState,
}: PaginatedDataGridProps<AppModel, DBModel>) {
  const cursorFinder = useMemo(
    () => getCursor ?? lastKeyCursor<AppModel, DBModel>("createTime"),
    [getCursor],
  );

  // Get total number of rows, -1 indicates the total is unknown
  const [totalRows = -1] = useFirestoreCount(baseQuery);
  const [[currentSnapshot], { goToPage, page }, loading] = usePaginatedQuery(
    baseQuery,
    cursorFinder,
    pageSize,
  );

  const rows = useMemo(
    () => rowGenerator(currentSnapshot),
    [currentSnapshot, rowGenerator],
  );

  const theme = useTheme();

  return (
    <DataGrid
      sx={{
        "&.MuiDataGrid-root .MuiDataGrid-cell:focus-within": {
          outline: "none !important",
        },
        "&.MuiDataGrid-root .MuiDataGrid-row:not(:hover)": {
          backgroundColor: "background.paper",
        },
        "&.MuiDataGrid-root .MuiDataGrid-footerContainer": {
          backgroundColor: "background.paper",
        },
        ...sx,
      }}
      onRowClick={(row) => {
        onRowSelected?.(row.id as string);
      }}
      style={{ backgroundColor: theme.palette.background.paper }}
      density={density}
      disableColumnFilter
      showCellVerticalBorder
      pageSizeOptions={[pageSize]}
      paginationModel={{ page, pageSize }}
      rowCount={totalRows}
      paginationMode="server"
      paginationMeta={{
        hasNextPage: true,
      }}
      onPaginationModelChange={async ({ page }) => {
        goToPage(page);
      }}
      loading={loading}
      pagination
      rows={rows}
      columns={columns}
      initialState={initialState}
    />
  );
}
