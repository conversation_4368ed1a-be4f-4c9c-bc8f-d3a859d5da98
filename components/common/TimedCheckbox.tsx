import { getProperty } from "dot-prop";
import formatTimeRelative from "../../lib/format-time-relative";
import React, { useState } from "react";
import { DocumentReference, Timestamp, updateDoc } from "firebase/firestore";
import { useDocument } from "react-firebase-hooks/firestore";
import {
  Checkbox,
  Tooltip,
  Popover,
  CardContent,
  CardHeader,
  TextField,
  CardActions,
  Button,
} from "@mui/material";

const defaultValue = () => new Date();

type TimePopoverProps = {
  dataKey: string;
  onClose: any;
  documentRef: DocumentReference;
};

const TimePopover: React.FC<TimePopoverProps> = ({
  onClose,
  documentRef,
  dataKey,
}) => {
  const [value, setValue] = useState(defaultValue());
  const onSave = () => {
    updateDoc(documentRef, dataKey, Timestamp.fromDate(value));
    onClose();
  };
  return (
    <>
      <CardHeader title="Set time" />
      <CardContent>
        <TextField
          id="datetime-local"
          label="Set date for event:"
          type="datetime-local"
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
          onChange={(e) => setValue(new Date(e.target.value))}
        />
      </CardContent>
      <CardActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button color="primary" onClick={onSave}>
          Save
        </Button>
      </CardActions>
    </>
  );
};

type WithDocumentProps = {
  dataKey: string;
  document: any;
  documentRef: DocumentReference;
};

const WithDocument: React.FC<WithDocumentProps> = ({
  dataKey,
  document,
  documentRef,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const time = getProperty(document, dataKey) as Timestamp;

  const onCheckboxClick = (event: any) => {
    if (time) {
      if (
        window.confirm("Are you sure you want to remove this time? " + dataKey)
      ) {
        updateDoc(documentRef, dataKey, null);
      }
      return;
    }

    if (event.shiftKey) {
      updateDoc(documentRef, dataKey, Timestamp.fromDate(defaultValue()));
      return;
    }
    setAnchorEl(event.target);
  };

  const onClose = () => setAnchorEl(null);

  return (
    <>
      <Tooltip
        title={time ? (formatTimeRelative(time.toDate()) ?? "") : "Not set"}
      >
        <Checkbox
          checked={Boolean(time)}
          onClick={onCheckboxClick}
          style={{ cursor: "pointer" }}
        />
      </Tooltip>
      {anchorEl && (
        <Popover
          disableEnforceFocus
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={onClose}
        >
          <TimePopover
            dataKey={dataKey}
            onClose={onClose}
            documentRef={documentRef}
          />
        </Popover>
      )}
    </>
  );
};

type WithDocumentRefProps = {
  dataKey: string;
  documentRef: DocumentReference;
};

const WithDocumentRef: React.FC<WithDocumentRefProps> = ({
  documentRef,
  dataKey,
  ...props
}) => {
  const [value, loading] = useDocument(documentRef);
  if (loading) return <Checkbox disabled />;
  return (
    <WithDocument
      dataKey={dataKey}
      documentRef={documentRef}
      document={value?.data()}
      {...props}
    />
  );
};

type TimedCheckboxProps = {
  documentRef: DocumentReference;
  dataKey: string;
};

const TimedCheckbox: React.FC<TimedCheckboxProps> = ({
  documentRef,
  dataKey,
  ...props
}) => {
  return (
    <WithDocumentRef documentRef={documentRef} dataKey={dataKey} {...props} />
  );
};

export default TimedCheckbox;
