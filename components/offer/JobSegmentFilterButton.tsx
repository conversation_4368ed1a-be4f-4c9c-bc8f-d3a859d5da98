import { Divider, ListItemIcon, ListItemText, capitalize } from "@mui/material";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import {
  ElectricCar,
  ShoppingCart,
  Handyman,
  HeatPump,
} from "@mui/icons-material";

interface JobSegmentFilterButtonProps {
  onChange: (status: string) => void;
  title: string;
  count: number;
  initialSegment?: string;
}

export default function JobSegmentFilterButton({
  onChange,
  title,
  count,
  initialSegment,
}: JobSegmentFilterButtonProps) {
  return (
    <SelectButton
      persistent
      initialSelection={initialSegment}
      title={`${count ?? "-"} ${capitalize(title)}`}
      onSelection={(value: string) => {
        onChange(value);
      }}
    >
      <SelectButtonItem value={"ev-chargers"}>
        <ListItemIcon>
          <ElectricCar fontSize="small" />
        </ListItemIcon>
        <ListItemText>EV</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"heat-pump"}>
        <ListItemIcon>
          <HeatPump fontSize="small" />
        </ListItemIcon>
        <ListItemText>Heat pump</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"marketplace"}>
        <ListItemIcon>
          <ShoppingCart fontSize="small" />
        </ListItemIcon>
        <ListItemText>Marketplace</ListItemText>
      </SelectButtonItem>
      <Divider />
      <SelectButtonItem value={undefined}>
        <ListItemIcon>
          <Handyman fontSize="small" />
        </ListItemIcon>
        <ListItemText>All</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}

export function titleForSegmentFilter(filter?: string | string[]) {
  switch (filter) {
    case "ev-chargers":
      return "EV";
    case "heat-pump":
      return "Heat pump";
    case "marketplace":
      return "Marketplace";
  }
  return "All";
}
