import { ListItemText, capitalize } from "@mui/material";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";

interface OffersTagFilterButtonProps {
  onChange: (status: string) => void;
  title: string;
}

export default function OffersQueryFilterButton({
  onChange,
  title,
}: OffersTagFilterButtonProps) {
  return (
    <SelectButton
      persistent
      title={capitalize(title)}
      onSelection={(value: string) => {
        onChange(value);
      }}
    >
      <SelectButtonItem value={undefined}>
        <ListItemText>All</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"notSent"}>
        <ListItemText>Not sent</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"notMatched"}>
        <ListItemText>Not matched</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}
