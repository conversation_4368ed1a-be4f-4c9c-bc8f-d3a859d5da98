import { Button, ButtonProps } from "@mui/material";
import { useEffect, useRef } from "react";
import { getFeedback } from "@sentry/react";
import { Feedback } from "@mui/icons-material";

interface FeedbackButtonProps extends Omit<ButtonProps, "ref"> {
  title?: string;
}

/**
 * A button that opens the Sentry feedback dialog.
 */
export default function FeedbackButton({
  title = "Feedback",
  ...props
}: FeedbackButtonProps) {
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (buttonRef.current) {
      return getFeedback()?.attachTo(buttonRef.current);
    }
    return () => {};
  }, []);

  return (
    <Button {...props} ref={buttonRef} startIcon={<Feedback />}>
      {title}
    </Button>
  );
}
