import { Button, ButtonProps } from "@mui/material";
import { Support } from "@mui/icons-material";
import { useIntercom } from "react-use-intercom";

interface SupportButtonProps extends Omit<ButtonProps, "ref"> {
  title?: string;
}

/**
 * A button that opens the Intercom dialog.
 */
export default function SupportButton({
  title = "Support",
  ...props
}: SupportButtonProps) {
  const intercom = useIntercom();

  return (
    <Button {...props} onClick={() => intercom.show()} startIcon={<Support />}>
      {title}
    </Button>
  );
}
