import { DocumentData, doc, setDoc } from "firebase/firestore";
import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import LabeledValueField from "../../form-fields/LabeledValueField";
import TextField from "../../form-fields/TextField";
import { sessionsCollection } from "../../../models/assistants/provider";
import {
  AssistantSession,
  availableAssistants,
} from "../../../models/assistants/assistant-session";
import removeUndefined from "../../../lib/remove-undefined";

interface NewSessionDialogProps extends DialogProps {}

export default function NewSessionDialog({
  isOpen,
  close,
}: NewSessionDialogProps) {
  return (
    <FormDialog
      title={"New session"}
      isOpen={isOpen}
      close={close}
      onSubmit={async (values) => {
        const { assistant, subject, input } = values;
        await setDoc<Partial<AssistantSession>, DocumentData>(
          doc(sessionsCollection()),
          removeUndefined({
            assistant,
            subject: subject ? doc(subject) : undefined,
            input,
          }),
        );
      }}
    >
      <LabeledValueField
        name="assistant"
        label="Assistant"
        items={availableAssistants}
      />
      <TextField name="subject" label="Subject reference" />
      <TextField name="input" label="Message" />
    </FormDialog>
  );
}
