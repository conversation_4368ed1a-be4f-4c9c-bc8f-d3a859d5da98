import UserAvatar from "../user/UserAvatar";
import { useMemoDocument } from "../../lib/hooks/use-memo-document";
import { User } from "../../models/user/user";
import { DocumentReference } from "firebase/firestore";
import { SelectButtonItem, SelectButtonItemProps } from "./SelectButton";
import { ListItemAvatar, ListItemText } from "@mui/material";

interface UserMenuItemProps extends Omit<SelectButtonItemProps, "children"> {
  userRef: DocumentReference;
  label?: string;
}

/**
 * A menu item representing a user.
 */
export default function UserSelectButtonItem({
  userRef,
  label,
  ...menuItemProps
}: UserMenuItemProps) {
  const [user] = useMemoDocument<User>(userRef);
  return (
    <SelectButtonItem {...menuItemProps}>
      <ListItemAvatar sx={{ minWidth: 0, marginRight: 2 }}>
        <UserAvatar userRef={userRef} size="small" />
      </ListItemAvatar>
      <ListItemText
        primary={`${user?.firstName} ${user?.lastName}`}
        secondary={label}
      />
    </SelectButtonItem>
  );
}
