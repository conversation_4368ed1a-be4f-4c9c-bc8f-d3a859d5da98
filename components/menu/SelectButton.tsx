import { Button, ButtonProps, Menu, MenuItem } from "@mui/material";
import { createContext, useCallback, useContext, useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

export interface SelectButtonProps<T>
  extends Omit<ButtonProps, "onClick" | "title"> {
  title: React.ReactNode;
  onSelection: (item: T) => void;
  persistent?: boolean;
  initialSelection?: T;
}

/**
 * A button that opens an anchored menu.
 * @example
 * <SelectButton title="Select an item" onSelection={(item) => console.log(item)}>
 *  <SelectButtonItem value="item1">Item 1</SelectButtonItem>
 *  <SelectButtonItem value="item2">Item 2</SelectButtonItem>
 * </SelectButton>
 */
export default function SelectButton<T>(props: SelectButtonProps<T>) {
  const {
    title,
    children,
    persistent = false,
    initialSelection,
    onSelection,
    ...buttonProps
  } = props;
  const [anchor, setAnchor] = useState<null | HTMLElement>(null);
  const [currentValue, setCurrentValue] = useState<unknown>(initialSelection);
  const open = Boolean(anchor);

  const setSelected = useCallback(
    (value: unknown) => {
      if (persistent) setCurrentValue(value);
      onSelection(value as T);
      setAnchor(null);
    },
    [onSelection, persistent],
  );

  return (
    <>
      <Button
        onClick={(event) => {
          setAnchor(event.currentTarget);
        }}
        endIcon={<ExpandMoreIcon />}
        {...buttonProps}
      >
        {title}
      </Button>
      <Menu
        anchorEl={anchor}
        open={open}
        onClose={() => {
          setAnchor(null);
        }}
      >
        <SelectButtonContext.Provider value={{ currentValue, setSelected }}>
          {children}
        </SelectButtonContext.Provider>
      </Menu>
    </>
  );
}

const SelectButtonContext = createContext<{
  currentValue?: unknown;
  setSelected: (value: unknown) => void;
}>({ setSelected: () => {} });

export interface SelectButtonItemProps {
  value: unknown;
  children: React.ReactNode;
}

/**
 * An item in a SelectButton menu.
 */
export function SelectButtonItem(props: SelectButtonItemProps) {
  const { value, children } = props;
  const { currentValue, setSelected } = useContext(SelectButtonContext);
  return (
    <MenuItem
      dense
      selected={value === currentValue}
      onClick={() => setSelected(value)}
    >
      {children}
    </MenuItem>
  );
}
