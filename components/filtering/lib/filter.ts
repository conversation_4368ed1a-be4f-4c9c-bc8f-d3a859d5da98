import { build } from "./builder";
import { PredicateFunction, PredicateType } from "./predicates";
import { v4 as uuid } from "uuid";

interface FilterPredicate {
  func: PredicateType;
  args: (unknown | FilterConfig | FilterPredicate)[];
}

/**
 * A serializable blueprint for a filter.
 */
export interface FilterConfig {
  id: string;
  title: string;
  predicate: FilterPredicate;
}

/**
 * A runnable filter.
 */
export interface RunnableFilter<T = unknown> extends Readonly<FilterConfig> {
  filter: PredicateFunction<T>;
}

// TODO: This should use a schema validator instead of this montrosity
export function isFilterDefinition(
  item: unknown | FilterConfig,
): item is FilterConfig {
  return (
    item !== null &&
    typeof item === "object" &&
    "predicate" in item &&
    isFilterPredicate(item.predicate)
  );
}

// TODO: This should use a schema validator instead of this montrosity
export function isFilterPredicate(
  item: unknown | FilterPredicate,
): item is FilterPredicate {
  return (
    item !== null &&
    typeof item === "object" &&
    "func" in item &&
    "args" in item
  );
}

/**
 * Compiles a {@link FilterConfig} into a {@link RunnableFilter}
 * @param config The configuration to compile
 * @returns The compiled filter
 */
export function compile(
  config: FilterConfig | FilterPredicate,
): RunnableFilter {
  let id: string;
  let title: string;
  let predicate: FilterPredicate;

  if (isFilterDefinition(config)) {
    id = config.id;
    title = config.title;
    predicate = config.predicate;
  } else {
    id = "";
    title = "";
    predicate = config;
  }

  if (!predicate || !isFilterPredicate(predicate)) {
    throw new Error(
      "Trying to compile an invalid configuration - missing predicate",
    );
  }

  return {
    id,
    title,
    predicate,
    filter: build(
      predicate.func,
      predicate.args.map((arg) =>
        isFilterDefinition(arg) || isFilterPredicate(arg)
          ? compile(arg).filter
          : arg,
      ),
    ),
  };
}

/**
 * Compiles an array of {@link FilterConfig} into an array of {@link RunnableFilter}
 * @param configs An array of configurations to compile
 * @returns An array of compiled filters
 */
export function compileAll(configs: FilterConfig[]): RunnableFilter[] {
  return configs.map(compile);
}

interface JoinFiltersOptions {
  id?: string;
  title?: string;
  func?: "all" | "none" | "any";
}

/**
 * Creates a new filter from other filters.
 */
export function joinedFilter(
  filters: FilterConfig[],
  options: JoinFiltersOptions = {},
): FilterConfig {
  if (!filters.every(isFilterDefinition)) {
    throw new Error("Only FilterConfig can be joined");
  }

  return asConfig({
    id: options.id || uuid(),
    title: options.title || filters.map((filter) => filter.title).join(", "),
    predicate: {
      func: options.func || "all",
      args: filters,
    },
  });
}

/**
 * Creates a {@link FilterConfig} from anything {@link FilterConfig}-like (such as {@link RunnableFilter}),
 * basically the reverse of {@link compile}
 */
function asConfig(item: FilterConfig | RunnableFilter): FilterConfig {
  return {
    id: item.id,
    title: item.title,
    predicate: {
      func: item.predicate.func,
      args: item.predicate.args.map((arg) =>
        isFilterDefinition(arg) ? asConfig(arg) : arg,
      ),
    },
  };
}
