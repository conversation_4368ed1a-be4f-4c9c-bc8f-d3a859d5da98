import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import {
  compile,
  joinedFilter,
  isFilterDefinition,
  isFilterPredicate,
} from "./filter";
import * as builder from "./builder";
import { PredicateType } from "./predicates";
import * as uuid from "uuid";

// Mock uuid to return predictable values
jest.mock("uuid", () => ({
  v4: jest.fn().mockReturnValue("mocked-uuid"),
}));

describe("compile", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(builder, "build").mockImplementation(() => () => true);
  });

  it("should compile a FilterConfig into a RunnableFilter", () => {
    // Arrange
    const config = {
      id: "test-id",
      title: "Test Filter",
      predicate: {
        func: "isEqual" as PredicateType,
        args: ["path", "value"],
      },
    };

    // Act
    const result = compile(config);

    // Assert
    expect(result).toEqual({
      id: "test-id",
      title: "Test Filter",
      predicate: config.predicate,
      filter: expect.any(Function),
    });
    expect(builder.build).toHaveBeenCalledWith("isEqual", ["path", "value"]);
  });

  it("should compile a FilterPredicate into a RunnableFilter", () => {
    // Arrange
    const predicate = {
      func: "isEqual" as PredicateType,
      args: ["path", "value"],
    };

    // Act
    const result = compile(predicate);

    // Assert
    expect(result).toEqual({
      id: "",
      title: "",
      predicate,
      filter: expect.any(Function),
    });
    expect(builder.build).toHaveBeenCalledWith("isEqual", ["path", "value"]);
  });

  it("should recursively compile nested predicates", () => {
    // Arrange
    const nestedPredicate = {
      func: "isEqual" as PredicateType,
      args: ["nestedPath", "nestedValue"],
    };

    const config = {
      id: "test-id",
      title: "Test Filter",
      predicate: {
        func: "all" as PredicateType,
        args: ["someArg", nestedPredicate],
      },
    };

    // Act
    const result = compile(config);

    // Assert
    expect(result).toEqual({
      id: "test-id",
      title: "Test Filter",
      predicate: config.predicate,
      filter: expect.any(Function),
    });

    // Verify the nested predicate was compiled
    expect(builder.build).toHaveBeenCalledWith("all", [
      "someArg",
      expect.any(Function),
    ]);
  });

  it("should throw an error for invalid configuration", () => {
    // Arrange
    const invalidConfig = "not a valid config" as any;

    // Act & Assert
    expect(() => compile(invalidConfig)).toThrow();
  });

  it("should throw an error with specific message for invalid configuration", () => {
    // Arrange
    const invalidConfig = "not a valid config" as any;

    // Act & Assert
    expect(() => compile(invalidConfig)).toThrow(/invalid configuration/i);
  });
});

describe("isFilterDefinition", () => {
  it("should return true for valid FilterConfig objects", () => {
    // Arrange
    const validConfig = {
      id: "test-id",
      title: "Test Filter",
      predicate: {
        func: "isEqual" as PredicateType,
        args: ["path", "value"],
      },
    };

    // Act & Assert
    expect(isFilterDefinition(validConfig)).toBe(true);
  });

  it("should return false for objects missing required properties", () => {
    // Arrange
    const invalidConfig = {
      id: "test-id",
      title: "Test Filter",
      // Missing predicate property
    };

    // Act & Assert
    expect(isFilterDefinition(invalidConfig)).toBe(false);
  });

  it("should return false for null or undefined", () => {
    // Act & Assert
    expect(isFilterDefinition(null)).toBe(false);
    expect(isFilterDefinition(undefined)).toBe(false);
  });

  it("should return false for non-objects", () => {
    // Act & Assert
    expect(isFilterDefinition("string")).toBe(false);
    expect(isFilterDefinition(123)).toBe(false);
    expect(isFilterDefinition(true)).toBe(false);
  });

  it("should return false when predicate is invalid", () => {
    // Arrange
    const configWithInvalidPredicate = {
      id: "test-id",
      title: "Test Filter",
      predicate: "not a valid predicate",
    };

    // Act & Assert
    expect(isFilterDefinition(configWithInvalidPredicate)).toBe(false);
  });
});

describe("isFilterPredicate", () => {
  it("should return true for valid FilterPredicate objects", () => {
    // Arrange
    const validPredicate = {
      func: "isEqual" as PredicateType,
      args: ["path", "value"],
    };

    // Act & Assert
    expect(isFilterPredicate(validPredicate)).toBe(true);
  });

  it("should return false for objects missing required properties", () => {
    // Arrange
    const missingArgs = { func: "isEqual" };
    const missingFunction = { args: ["path", "value"] };

    // Act & Assert
    expect(isFilterPredicate(missingArgs)).toBe(false);
    expect(isFilterPredicate(missingFunction)).toBe(false);
  });

  it("should return false for null or undefined", () => {
    // Act & Assert
    expect(isFilterPredicate(null)).toBe(false);
    expect(isFilterPredicate(undefined)).toBe(false);
  });

  it("should return false for non-objects", () => {
    // Act & Assert
    expect(isFilterPredicate("string")).toBe(false);
    expect(isFilterPredicate(123)).toBe(false);
    expect(isFilterPredicate(true)).toBe(false);
  });
});

describe("joinedFilter", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should create a filter that joins other filters with default options", () => {
    // Arrange
    const filters = [
      {
        id: "filter1",
        title: "Filter 1",
        predicate: {
          func: "isEqual" as PredicateType,
          args: ["path1", "value1"],
        },
      },
      {
        id: "filter2",
        title: "Filter 2",
        predicate: {
          func: "isEqual" as PredicateType,
          args: ["path2", "value2"],
        },
      },
    ];

    // Act
    const result = joinedFilter(filters);

    // Assert
    expect(result).toEqual({
      id: "mocked-uuid",
      title: "Filter 1, Filter 2",
      predicate: {
        func: "all",
        args: filters,
      },
    });
  });

  it("should use provided options when specified", () => {
    // Arrange
    const filters = [
      {
        id: "filter1",
        title: "Filter 1",
        predicate: {
          func: "isEqual" as PredicateType,
          args: ["path1", "value1"],
        },
      },
    ];

    const options = {
      id: "custom-id",
      title: "Custom Title",
      func: "any" as const,
    };

    // Act
    const result = joinedFilter(filters, options);

    // Assert
    expect(result).toEqual({
      id: "custom-id",
      title: "Custom Title",
      predicate: {
        func: "any",
        args: filters,
      },
    });
  });

  it("should handle empty filters array", () => {
    // Arrange
    const filters: any[] = [];

    // Act
    const result = joinedFilter(filters);

    // Assert
    expect(result).toEqual({
      id: "mocked-uuid",
      title: "",
      predicate: {
        func: "all",
        args: [],
      },
    });
  });

  it("should throw an error for invalid filters input", () => {
    // Arrange
    const invalidFilters = "not an array" as any;

    // Act & Assert
    expect(() => joinedFilter(invalidFilters)).toThrow();
  });

  it("should throw an error when filters contain invalid items", () => {
    // Arrange
    const invalidFilters = [
      {
        id: "filter1",
        title: "Filter 1",
        predicate: {
          func: "isEqual" as PredicateType,
          args: ["path1", "value1"],
        },
      },
      "not a valid filter", // Invalid item
    ] as any;

    // Act & Assert
    expect(() => joinedFilter(invalidFilters)).toThrow();
  });
});
