import { capitalize } from "@mui/material";
import { FilterGroup } from "./group";

const CoreGroup: FilterGroup = {
  title: "Overview",
  filters: [
    {
      id: "all",
      title: "All",
      predicate: {
        func: "isSet",
        args: ["referenceNumber"],
      },
    },
    {
      id: "not-matched",
      title: "Not matched",
      predicate: {
        func: "isNotSet",
        args: ["company"],
      },
    },
  ],
};

const PriorityGroup: FilterGroup = {
  title: "Priority",
  filters: [
    {
      id: "high-prio",
      title: "High prio",
      predicate: {
        func: "arrayContains",
        args: ["operationTags", "High Prio"],
      },
    },
  ],
};

const TypeGroup: FilterGroup = {
  title: "Type",
  filters: [
    {
      id: "type-installation",
      title: "Installation",
      predicate: {
        func: "arrayContains",
        args: ["tags", "type:installation"],
      },
    },
    {
      id: "type-warranty",
      title: "Warranty",
      predicate: {
        func: "arrayContains",
        args: ["tags", "type:warranty"],
      },
    },
    {
      id: "type-ev-charger",
      title: "EV charger",
      predicate: {
        func: "arrayContains",
        args: ["tags", "category:ev-charger"],
      },
    },
    {
      id: "type-batteries",
      title: "Battery",
      predicate: {
        func: "arrayContains",
        args: ["tags", "category:battery"],
      },
    },
  ],
};

const CustomersGroup: FilterGroup = {
  title: "Customers",
  filters: [
    {
      id: "not-signed-in",
      title: "Not signed in",
      predicate: {
        func: "isNotSet",
        args: ["events.customerLoggedIn"],
      },
    },
    {
      id: "no-prechecks",
      title: "No prechecks",
      predicate: {
        func: "isNotSet",
        args: ["events.partnershipPrechecksReported"],
      },
    },
  ],
};

const PrerequisitesGroup: FilterGroup = {
  title: "Prerequisites",
  filters: [
    {
      id: "missing-prerequisites",
      title: "Prerequisites not started",
      predicate: {
        func: "all",
        args: [
          {
            func: "isEqual",
            args: ["requiresPrerequisitesFulfilled", true],
          },
          {
            func: "isNotSet",
            args: ["events.prerequisitesPending"],
          },
          {
            func: "isNotSet",
            args: ["events.prerequisitesFulfilled"],
          },
        ],
      },
    },
    {
      id: "pending-prerequisites",
      title: "Prerequisites pending",
      predicate: {
        func: "all",
        args: [
          {
            func: "isSet",
            args: ["events.prerequisitesPending"],
          },
          {
            func: "isNotSet",
            args: ["events.prerequisitesFulfilled"],
          },
        ],
      },
    },
    {
      id: "fuilfilled-prerequisites",
      title: "Prerequisites fuilfilled",
      predicate: {
        func: "isSet",
        args: ["events.prerequisitesFulfilled"],
      },
    },
  ],
};

const SchedulingGroup: FilterGroup = {
  title: "Scheduling",
  filters: [
    {
      id: "not-scheduled",
      title: "Not scheduled",
      predicate: {
        func: "isNotSet",
        args: ["scheduledWorkStartTime"],
      },
    },
    {
      id: "scheduled",
      title: "Scheduled",
      predicate: {
        func: "isSet",
        args: ["scheduledWorkStartTime"],
      },
    },
    {
      id: "scheduled-today",
      title: "Today",
      predicate: {
        func: "isToday",
        args: ["scheduledWorkStartTime"],
      },
    },
    {
      id: "overdue",
      title: "Overdue",
      predicate: {
        func: "isBefore",
        args: ["scheduledWorkStartTime"],
      },
    },
  ],
};

const MerchantsGroup = (partnerIds: string[]): FilterGroup => ({
  title: "Merchants",
  filters: partnerIds.map((id) => ({
    id: `merchant-${id}`,
    title: capitalize(id ?? "Unkown"),
    predicate: {
      func: "isEqual",
      args: ["merchant.id", id],
    },
  })),
});

const NetworksGroup = (partnerIds: string[]): FilterGroup => ({
  title: "Networks",
  filters: partnerIds.map((id) => ({
    id: `network-${id}`,
    title: capitalize(id),
    predicate: {
      func: "isEqual",
      args: ["network.id", id],
    },
  })),
});

const InstallersGroup = (
  installers: { id: string; name: string }[],
): FilterGroup => ({
  title: "Installers",
  filters: installers.map(({ id, name }) => ({
    id: `installer-${id}`,
    title: name,
    predicate: {
      func: "isEqual",
      args: ["company.id", id],
    },
  })),
});

export const JobFilters = {
  CoreGroup,
  PriorityGroup,
  TypeGroup,
  CustomersGroup,
  PrerequisitesGroup,
  SchedulingGroup,
  MerchantsGroup,
  NetworksGroup,
  InstallersGroup,
} as const;
