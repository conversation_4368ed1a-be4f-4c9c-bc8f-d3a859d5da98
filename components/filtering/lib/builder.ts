import { PredicateContructor, PredicateType, registry } from "./predicates";

/**
 * Builds a predicate function.
 * @param type Type of predicate
 * @param args Arguments to send to the constructor.
 * @returns Returns a function that tests a model.
 */
export function build<T = unknown>(type: PredicateType, args: unknown[]) {
  if (!registry[type]) throw new Error(`${type} is not a valid predicate type`);
  return (registry[type] as PredicateContructor<T>)(...args);
}
