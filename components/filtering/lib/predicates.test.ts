import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import { registry, PredicateFunction } from "./predicates";

interface MockModel {
  foo: string;
  baz?: string;
}

describe("Predicates", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("isSet", () => {
    it("should return true when the path is defined", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "foo";

      // Act
      const result = registry.isSet<MockModel>(path)(model);

      // Assert
      expect(result).toBe(true);
    });

    it("should return false when the path is not defined", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "baz";

      // Act
      const result = registry.isSet<MockModel>(path)(model);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe("isNotSet", () => {
    it("should return true when the path is not defined", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "baz";

      // Act
      const result = registry.isNotSet<MockModel>(path)(model);

      // Assert
      expect(result).toBe(true);
    });

    it("should return false when the path is defined", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "foo";

      // Act
      const result = registry.isNotSet<MockModel>(path)(model);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe("isEqual", () => {
    it("should return true when the path value equals the provided value", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "foo";
      const value = "bar";

      // Act
      const result = registry.isEqual<MockModel>(path, value)(model);

      // Assert
      expect(result).toBe(true);
    });

    it("should return false when the path value does not equal the provided value", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "foo";
      const value = "baz";

      // Act
      const result = registry.isEqual<MockModel>(path, value)(model);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe("isNotEqual", () => {
    it("should return true when the path value does not equal the provided value", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "foo";
      const value = "baz";

      // Act
      const result = registry.isNotEqual<MockModel>(path, value)(model);

      // Assert
      expect(result).toBe(true);
    });

    it("should return false when the path value equals the provided value", () => {
      // Arrange
      const model = { foo: "bar" };
      const path = "foo";
      const value = "bar";

      // Act
      const result = registry.isNotEqual<MockModel>(path, value)(model);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe("all", () => {
    it("should return true when all predicates return true", () => {
      // Arrange
      const model = { foo: "bar", baz: "qux" };
      const predicate1 = jest
        .fn()
        .mockReturnValue(true) as unknown as PredicateFunction<typeof model>;
      const predicate2 = jest
        .fn()
        .mockReturnValue(true) as unknown as PredicateFunction<typeof model>;

      // Act
      const result = registry.all(predicate1, predicate2)(model);

      // Assert
      expect(result).toBe(true);
      expect(predicate1).toHaveBeenCalledWith(model);
      expect(predicate2).toHaveBeenCalledWith(model);
    });

    it("should return false when any predicate returns false", () => {
      // Arrange
      const model = { foo: "bar", baz: "qux" };
      const predicate1 = jest
        .fn()
        .mockReturnValue(true) as unknown as PredicateFunction<typeof model>;
      const predicate2 = jest
        .fn()
        .mockReturnValue(false) as unknown as PredicateFunction<typeof model>;

      // Act
      const result = registry.all(predicate1, predicate2)(model);

      // Assert
      expect(result).toBe(false);
      expect(predicate1).toHaveBeenCalledWith(model);
      expect(predicate2).toHaveBeenCalledWith(model);
    });

    it("should handle non-array predicates", () => {
      // Arrange
      const model = { foo: "bar" };
      const predicates = "not an array" as any;

      // We need to call the function with a model to trigger the error
      // Since the error check happens when the returned function is called
      const predicate = registry.all(predicates);

      // Act & Assert
      expect(() => predicate(model)).toThrow("predicate is not a function");
    });
  });

  describe("none", () => {
    it("should return true when all predicates return false", () => {
      // Arrange
      const model = { foo: "bar", baz: "qux" };
      const predicate1 = jest
        .fn()
        .mockReturnValue(false) as unknown as PredicateFunction<typeof model>;
      const predicate2 = jest
        .fn()
        .mockReturnValue(false) as unknown as PredicateFunction<typeof model>;

      // Act
      const result = registry.none(predicate1, predicate2)(model);

      // Assert
      expect(result).toBe(true);
      expect(predicate1).toHaveBeenCalledWith(model);
      expect(predicate2).toHaveBeenCalledWith(model);
    });

    it("should return false when any predicate returns true", () => {
      // Arrange
      const model = { foo: "bar", baz: "qux" };
      const predicate1 = jest
        .fn()
        .mockReturnValue(false) as unknown as PredicateFunction<typeof model>;
      const predicate2 = jest
        .fn()
        .mockReturnValue(true) as unknown as PredicateFunction<typeof model>;

      // Act
      const result = registry.none(predicate1, predicate2)(model);

      // Assert
      expect(result).toBe(false);
      expect(predicate1).toHaveBeenCalledWith(model);
      expect(predicate2).toHaveBeenCalledWith(model);
    });

    it("should handle non-array predicates", () => {
      // Arrange
      const model = { foo: "bar" };
      const predicates = "not an array" as any;

      // We need to call the function with a model to trigger the error
      // Since the error check happens when the returned function is called
      const predicate = registry.none(predicates);

      // Act & Assert
      expect(() => predicate(model)).toThrow("predicate is not a function");
    });
  });

  describe("any", () => {
    it("should return true when any predicate returns true", () => {
      // Arrange
      const model = { foo: "bar", baz: "qux" };
      const predicate1 = jest
        .fn()
        .mockReturnValue(false) as unknown as PredicateFunction<typeof model>;
      const predicate2 = jest
        .fn()
        .mockReturnValue(true) as unknown as PredicateFunction<typeof model>;

      // Act
      const result = registry.any(predicate1, predicate2)(model);

      // Assert
      expect(result).toBe(true);
      expect(predicate1).toHaveBeenCalledWith(model);
      expect(predicate2).toHaveBeenCalledWith(model);
    });

    it("should return false when all predicates return false", () => {
      // Arrange
      const model = { foo: "bar", baz: "qux" };
      const predicate1 = jest
        .fn()
        .mockReturnValue(false) as unknown as PredicateFunction<typeof model>;
      const predicate2 = jest
        .fn()
        .mockReturnValue(false) as unknown as PredicateFunction<typeof model>;

      // Act
      const result = registry.any(predicate1, predicate2)(model);

      // Assert
      expect(result).toBe(false);
      expect(predicate1).toHaveBeenCalledWith(model);
      expect(predicate2).toHaveBeenCalledWith(model);
    });

    it("should handle non-array predicates", () => {
      // Arrange
      const model = { foo: "bar" };
      const predicates = "not an array" as any;

      // We need to call the function with a model to trigger the error
      // Since the error check happens when the returned function is called
      const predicate = registry.any(predicates);

      // Act & Assert
      expect(() => predicate(model)).toThrow("predicate is not a function");
    });
  });
});
