import { compile, FilterConfig, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./filter";

export interface FilterGroup {
  title: string;
  filters: FilterConfig[];
}

export interface RunnableFilterGroup extends Omit<FilterGroup, "filters"> {
  filters: RunnableFilter[];
}

export function compileGroup(group: FilterGroup): RunnableFilterGroup {
  const { title, filters } = group;
  return {
    title,
    filters: filters.map(compile),
  };
}
