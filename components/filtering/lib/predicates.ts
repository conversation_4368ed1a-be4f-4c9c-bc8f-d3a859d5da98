import { ModelPaths } from "../../../lib/model-key-paths";
import { isDefined } from "../../../lib/filter-undefined";
import _ from "lodash";
import {
  isToday as isTodayFns,
  isAfter as isAfterFns,
  isBefore as isBeforeFns,
  add,
  endOfDay,
  startOfDay,
} from "date-fns";
import { getDate } from "../../../lib/date-or-timestamp";

export type PredicateContructor<T> = (
  ...args: unknown[]
) => PredicateFunction<T>;
export type PredicateFunction<T> = (model: T) => boolean;

function isSet<T>(path: ModelPaths<T>): PredicateFunction<T> {
  return (model: T) => isDefined(_.get(model, path));
}

function isNotSet<T>(path: ModelPaths<T>): PredicateFunction<T> {
  return (model: T) => !isDefined(_.get(model, path));
}

function isEqual<T, P extends ModelPaths<T> = ModelPaths<T>, V = unknown>(
  path: P,
  value: V,
): PredicateFunction<T> {
  return (model: T) => _.get(model, path) === value;
}

function isNotEqual<T, P extends ModelPaths<T> = ModelPaths<T>, V = unknown>(
  path: P,
  value: V,
): PredicateFunction<T> {
  return (model: T) => _.get(model, path) !== value;
}

function isAfter<T>(
  path: ModelPaths<T>,
  unit = "days",
  value = 0,
): PredicateFunction<T> {
  return (model: T) => {
    const date = getDate(_.get(model, path));
    if (!date) return false;
    const check = endOfDay(add(new Date(), { [unit]: value }));
    return isAfterFns(date, check);
  };
}

function isBefore<T>(
  path: ModelPaths<T>,
  unit = "days",
  value = 0,
): PredicateFunction<T> {
  return (model: T) => {
    const date = getDate(_.get(model, path));
    if (!date) return false;
    const check = startOfDay(add(new Date(), { [unit]: value }));
    return isBeforeFns(date, check);
  };
}

function isToday<T>(path: ModelPaths<T>): PredicateFunction<T> {
  return (model: T) => {
    const date = getDate(_.get(model, path));
    if (!date) return false;
    return isTodayFns(date);
  };
}

function arrayContains<T, P extends ModelPaths<T> = ModelPaths<T>, V = unknown>(
  path: P,
  value: V,
): PredicateFunction<T> {
  return (model: T) => {
    const array = _.get(model, path);
    if (!Array.isArray(array)) return false;
    return array.includes(value);
  };
}

function all<T>(...predicates: PredicateFunction<T>[]): PredicateFunction<T> {
  if (!Array.isArray(predicates)) {
    console.error("Not an array!", { predicates });
    throw new Error("Not an array!");
  }
  return (model: T) => predicates.every((predicate) => predicate(model));
}

function none<T>(...predicates: PredicateFunction<T>[]): PredicateFunction<T> {
  if (!Array.isArray(predicates)) {
    console.error("Not an array!", { predicates });
    throw new Error("Not an array!");
  }
  return (model: T) => predicates.every((predicate) => !predicate(model));
}

function any<T>(...predicates: PredicateFunction<T>[]): PredicateFunction<T> {
  if (!Array.isArray(predicates)) {
    console.error("Not an array!", { predicates });
    throw new Error("Not an array!");
  }
  return (model: T) =>
    Boolean(predicates.find((predicate) => predicate(model)));
}

export const registry = {
  isSet,
  isNotSet,
  isEqual,
  isNotEqual,
  all,
  none,
  any,
  isToday,
  isAfter,
  isBefore,
  arrayContains,
} as const;

export type PredicateType = keyof typeof registry;
