import { describe, expect, it, jest } from "@jest/globals";
import { build } from "./builder";
import { registry, PredicateType } from "./predicates";

describe("build", () => {
  it("should build a predicate function from a valid type", () => {
    // Arrange
    const mockPredicate = jest.fn(() => true);
    const mockConstructor = jest.fn(() => mockPredicate);
    const originalRegistry = { ...registry };

    // Mock the registry
    (registry as any).testPredicate = mockConstructor;

    // Act
    const result = build("testPredicate" as PredicateType, ["arg1", "arg2"]);

    // Assert
    expect(mockConstructor).toHaveBeenCalledWith("arg1", "arg2");
    expect(result).toBe(mockPredicate);

    // Restore registry
    Object.assign(registry, originalRegistry);
  });

  it("should throw an error for invalid predicate type", () => {
    // Act & Assert
    expect(() => {
      build("invalidType" as PredicateType, []);
    }).toThrow("invalidType is not a valid predicate type");
  });
});
