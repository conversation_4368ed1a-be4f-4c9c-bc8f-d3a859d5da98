import { Add, Remove } from "@mui/icons-material";
import {
  ListItemButtonProps,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  IconButton,
  Grow,
  Tooltip,
} from "@mui/material";
import { MouseEventHandler, useState } from "react";

interface FilterItemProps
  extends Pick<ListItemButtonProps, "selected" | "onClick"> {
  title: string;
  count?: number;
  additive?: boolean;
  onAccessoryClick?: MouseEventHandler<HTMLButtonElement>;
}

/**
 * A {@link ListItem} for a filter in a {@link FilterCollection}.
 */
export default function FilterItem({
  title,
  count = 0,
  selected,
  additive = false,
  onClick,
  onAccessoryClick,
}: FilterItemProps) {
  const [showAccessory, setShowAccessory] = useState(false);
  return (
    <ListItem disablePadding sx={{ backgroundColor: "background.paper" }}>
      <ListItemButton
        dense
        selected={selected}
        onClick={onClick}
        onPointerOver={() => setShowAccessory(true)}
        onPointerLeave={() => setShowAccessory(false)}
        sx={{ paddingLeft: 0.5 }}
      >
        <Grow in={showAccessory || (selected && additive)}>
          <Tooltip title={selected ? "Exclude filter" : "Include filter"} arrow>
            <IconButton
              size="small"
              sx={{ width: 24, height: 24, marginRight: 0.5 }}
              onClick={(e) => {
                e.stopPropagation();
                onAccessoryClick?.(e);
              }}
            >
              {selected ? (
                <Remove fontSize="small" />
              ) : (
                <Add fontSize="small" />
              )}
            </IconButton>
          </Tooltip>
        </Grow>
        <ListItemText
          primary={title}
          slotProps={{ primary: { noWrap: true, marginRight: 2 } }}
        />
        <Typography variant="body2" textAlign="right" color={"text.secondary"}>
          {count}
        </Typography>
      </ListItemButton>
    </ListItem>
  );
}
