import { ExpandLess, ExpandMore, Folder } from "@mui/icons-material";
import {
  ListItemButtonProps,
  ListItemButton,
  ListItemText,
  ListItemIcon,
} from "@mui/material";
import { ReactNode } from "react";

interface CollectionHeaderProps extends Pick<ListItemButtonProps, "onClick"> {
  title: string;
  isOpen?: boolean;
  icon?: ReactNode;
}

/**
 * A header for a collapsible collection of {@link FilterItem}s within a {@link FilterCollection}.
 */
export default function CollectionHeader({
  title,
  onClick,
  isOpen,
  icon,
}: CollectionHeaderProps) {
  return (
    <ListItemButton onClick={onClick}>
      <ListItemIcon>{icon || <Folder />}</ListItemIcon>
      <ListItemText primary={title} />
      {isOpen ? <ExpandLess /> : <ExpandMore />}
    </ListItemButton>
  );
}
