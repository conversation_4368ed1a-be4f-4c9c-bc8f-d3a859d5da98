import { Collapse, Divider } from "@mui/material";
import {
  useState,
  Fragment,
  MouseEvent as ReactMouseEvent,
  ReactNode,
} from "react";
import { Subheader } from "../../common/lists/Subheader";
import { RunnableFilter, RunnableFilterGroup } from "../lib";
import CollectionHeader from "./CollectionHeader";
import FilterItem from "./FilterItem";

export type FilterOnClickHandler = (
  filter: RunnableFilter,
  additive: boolean,
  event: ReactMouseEvent<HTMLElement, MouseEvent>,
) => void;

interface FilterCollectionProps<T> {
  title: string;
  additive?: boolean;
  icon?: ReactNode;
  groups?: RunnableFilterGroup[];
  filters?: RunnableFilter[];
  isFilterSelected?: (filter: RunnableFilter) => boolean;
  onFilterClick?: FilterOnClickHandler;
  models: T[];
}

/**
 * Displays a collapsible list of {@link RunnableFilterGroup} or a list of {@link RunnableFilter}s.
 */
export default function FilterCollection<T>({
  title,
  icon,
  groups,
  filters,
  models,
  additive = false,
  isFilterSelected,
  onFilterClick,
}: FilterCollectionProps<T>) {
  const [isOpen, setIsOpen] = useState(true);
  return (
    <>
      <CollectionHeader
        title={title}
        icon={icon}
        isOpen={isOpen}
        onClick={() => setIsOpen(!isOpen)}
      />
      <Collapse in={isOpen} timeout="auto" unmountOnExit>
        {filters?.length ? <Divider /> : null}
        {filters?.map((filter) => (
          <FilterItem
            key={filter.id}
            title={filter.title}
            count={models.filter(filter.filter).length}
            additive={additive}
            selected={isFilterSelected?.(filter) ?? false}
            onClick={(e) => {
              onFilterClick?.(filter, e.shiftKey, e);
            }}
            onAccessoryClick={(e) => {
              onFilterClick?.(filter, true, e);
            }}
          />
        ))}
        {groups?.map((group) => (
          <Fragment key={group.title}>
            <Subheader>{group.title}</Subheader>
            {group.filters.map((filter) => (
              <FilterItem
                key={filter.id}
                title={filter.title}
                count={models.filter(filter.filter).length}
                additive={additive}
                selected={isFilterSelected?.(filter) ?? false}
                onClick={(e) => {
                  onFilterClick?.(filter, e.shiftKey, e);
                }}
                onAccessoryClick={(e) => {
                  onFilterClick?.(filter, true, e);
                }}
              />
            ))}
          </Fragment>
        ))}
      </Collapse>
    </>
  );
}
