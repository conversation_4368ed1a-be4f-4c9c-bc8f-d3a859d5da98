import { useAdmin } from "../../hooks/other/adminConfigHook";
import IssueAssignButton, {
  CommonIssueAssignButtonProps,
} from "./IssueAssignButton";

interface DoneIssueAssignButtonProps extends CommonIssueAssignButtonProps {}

export function DoneIssueAssignButton({
  selected,
  onSelection,
  disabled = false,
}: DoneIssueAssignButtonProps) {
  const { admin } = useAdmin();

  return (
    <IssueAssignButton
      assigneeList={admin?.superAdminUsers ?? {}}
      selected={selected}
      onSelection={onSelection}
      disabled={disabled}
    />
  );
}
