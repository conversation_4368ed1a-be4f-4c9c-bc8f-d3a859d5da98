import { DateTimePicker } from "mui-rff";
import { Issue, IssueParticipant } from "../../../models/inbox/issue";
import { snoozeIssue } from "../../../models/inbox/provider";
import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { add } from "date-fns";

interface SnoozeDateDialogProps extends DialogProps {
  issue: Issue | undefined;
  onIssueSnoozed: () => void;
  participant?: IssueParticipant;
}

export default function SnoozeDateDialog({
  issue,
  participant,
  onIssueSnoozed,
  isOpen,
  close,
}: SnoozeDateDialogProps) {
  return (
    <FormDialog
      title="Snooze until date"
      isOpen={isOpen}
      close={close}
      onSubmit={async ({ snoozeUntilDate }) => {
        if (!issue || !participant) return;
        const now = new Date();
        const max = add(new Date(), { days: 30 });

        if (snoozeUntilDate < now) {
          throw new Error("Snooze date cannot be in the past");
        }
        if (snoozeUntilDate > max) {
          throw new Error(
            `Snooze date cannot be more than 30 days in the future`,
          );
        }

        await snoozeIssue(issue.reference, participant, snoozeUntilDate);
        onIssueSnoozed();
      }}
    >
      <DateTimePicker
        name="snoozeUntilDate"
        required
        disablePast
        autoFocus
        maxDateTime={add(new Date(), { days: 30 })}
      />
    </FormDialog>
  );
}
