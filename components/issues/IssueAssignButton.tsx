import { Divider, ListItemIcon, ListItemText } from "@mui/material";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import { useAuthUser } from "../auth/AuthProvider";
import UserAvatar from "../user/UserAvatar";
import { userCollection } from "../../models/user/user";
import { DocumentReference, doc } from "firebase/firestore";
import { useMemoUser } from "../../hooks/user/use-user";
import { DONE_BACKOFFICE_USER_REFERENCE } from "../../config/constants";
import { UserRoleMap } from "../../models/user/user-role";

export interface CommonIssueAssignButtonProps {
  selected: DocumentReference | null;
  disabled?: boolean;
  onSelection: (user: DocumentReference | null) => void;
}

interface IssueAssignButtonProps extends CommonIssueAssignButtonProps {
  assigneeList: UserRoleMap;
}

export default function IssueAssignButton({
  selected,
  onSelection,
  assigneeList,
  disabled = false,
}: IssueAssignButtonProps) {
  const auth = useAuthUser();

  return (
    <AssigneeSelectButton
      selected={selected}
      onSelection={onSelection}
      disabled={disabled}
    >
      <SelectButtonItem value={auth.userDoc?.reference}>
        <ListItemIcon>
          {auth.userDoc?.reference && (
            <UserAvatar size="small" userRef={auth.userDoc.reference} />
          )}
        </ListItemIcon>
        <ListItemText>Assign to me</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={null}>
        <ListItemIcon>
          <PersonOutlineIcon />
        </ListItemIcon>
        <ListItemText>Unassign</ListItemText>
      </SelectButtonItem>
      <Divider />
      {Object.keys(assigneeList)
        .filter(
          (id) =>
            id !== auth.userDoc?.reference.id &&
            id !== DONE_BACKOFFICE_USER_REFERENCE().id,
        )
        .sort()
        .map((id) => {
          return <UserItem id={id} key={id} />;
        })}
    </AssigneeSelectButton>
  );
}

function UserItem({ id }: { id: string }) {
  const reference = doc(userCollection(), id);
  const [user] = useMemoUser(id);
  return (
    <SelectButtonItem value={reference}>
      <ListItemIcon>
        <UserAvatar size="small" userRef={reference} />
      </ListItemIcon>
      <ListItemText>Assign to {user?.firstName}</ListItemText>
    </SelectButtonItem>
  );
}

function AssigneeSelectButton({
  selected,
  onSelection,
  children,
  disabled = false,
}: {
  selected: DocumentReference | null;
  onSelection: (user: DocumentReference | null) => void;
  children: React.ReactNode;
  disabled?: boolean;
}) {
  const [user] = useMemoUser(selected?.id);

  if (!selected)
    return (
      <SelectButton
        startIcon={<PersonOutlineIcon />}
        endIcon={undefined}
        title="Assign"
        onSelection={onSelection}
        disabled={disabled}
      >
        {children}
      </SelectButton>
    );

  return (
    <SelectButton
      startIcon={<UserAvatar size="small" userRef={selected} />}
      endIcon={undefined}
      title={user?.firstName ?? "-"}
      onSelection={onSelection}
      disabled={disabled}
    >
      {children}
    </SelectButton>
  );
}
