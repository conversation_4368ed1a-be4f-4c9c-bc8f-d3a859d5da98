import { Divider, ListItemIcon, ListItemText, capitalize } from "@mui/material";
import BedtimeIcon from "@mui/icons-material/Bedtime";
import ArchiveIcon from "@mui/icons-material/Archive";
import UnarchiveIcon from "@mui/icons-material/Unarchive";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import { useFirestoreCount } from "../../lib/hooks/use-firestore-count";
import { IssueStatus } from "../../models/inbox/issue";
import { Query } from "firebase/firestore";

interface IssuesStatusFilterButtonProps {
  onChange: (status: IssueStatus) => void;
  title: string;
  countQuery: Query;
}

export default function IssuesStatusFilterButton({
  onChange,
  title,
  countQuery,
}: IssuesStatusFilterButtonProps) {
  const [count] = useFirestoreCount(countQuery);
  return (
    <SelectButton
      persistent
      title={`${count ?? "-"} ${capitalize(title)}`}
      onSelection={(value: IssueStatus) => {
        onChange(value);
      }}
    >
      <SelectButtonItem value={"open"}>
        <ListItemIcon>
          <UnarchiveIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>Open</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"closed"}>
        <ListItemIcon>
          <ArchiveIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>Closed</ListItemText>
      </SelectButtonItem>
      <Divider />
      <SelectButtonItem value={"snoozed"}>
        <ListItemIcon>
          <BedtimeIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>Snoozed</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}
