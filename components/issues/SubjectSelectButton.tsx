import {
  Divider,
  <PERSON><PERSON>temAvatar,
  ListItemIcon,
  ListItemText,
  Stack,
} from "@mui/material";
import { doc, DocumentReference, getFirestore } from "firebase/firestore";
import { Company } from "../../models/company/company";
import SelectButton, {
  SelectButtonItem,
  SelectButtonItemProps,
} from "../menu/SelectButton";
import UserSelectButtonItem from "../menu/UserSelectButtonItem";
import { isModel } from "../../lib/filter-is-model";
import { Job } from "../../models/job/job";
import { User } from "../../models/user/user";
import { useMemoDocument } from "../../lib/hooks/use-memo-document";
import { FirestoreModel } from "../../models/firestore_model";
import { isDefined } from "../../lib/filter-undefined";
import { AssignmentOutlined } from "@mui/icons-material";
import { CompanyAvatarWithImage } from "../company/CompanyName";
import Internal from "../auth/Internal";

interface SubjectSelectButtonProps {
  subject?: DocumentReference;
  selected?: DocumentReference;
  onSelection?: (subject: DocumentReference) => void;
}

export default function SubjectSelectButton({
  subject,
  selected,
  onSelection,
}: SubjectSelectButtonProps) {
  const [model] = useMemoDocument(subject);
  const [selectedModel] = useMemoDocument(selected);
  const active = selectedModel ?? model;

  const title = titleForSubject(active);

  return (
    <SelectButton
      title={title}
      value={selected?.path}
      persistent
      onSelection={(item: string) => {
        if (!item) return;
        onSelection?.(doc(getFirestore(), item));
      }}
    >
      <Stack divider={<Divider />}>
        <RelatedJobItem subject={model} />
        <RelatedCustomerItem subject={model} />
        <RelatedCompanyItem subject={model} />
      </Stack>
    </SelectButton>
  );
}

function titleForSubject(subject?: FirestoreModel) {
  if (isModel<Job>("jobs", subject)) {
    return "Order";
  }

  if (isModel<User>("users", subject)) {
    return `${subject.firstName ?? ""} ${subject.lastName ?? ""}`;
  }

  if (isModel<Company>("companies", subject)) {
    return subject.name;
  }

  return "Subject";
}

function RelatedJobItem({ subject }: { subject?: FirestoreModel }) {
  if (isModel<Job>("jobs", subject)) {
    return <JobSelectButtonItem job={subject} value={subject.reference.path} />;
  }
  return null;
}

function RelatedCustomerItem({ subject }: { subject?: FirestoreModel }) {
  if (isModel<Job>("jobs", subject) && subject.customer) {
    return (
      <UserSelectButtonItem
        userRef={subject.customer}
        value={subject.customer.path}
        label="Customer"
      />
    );
  }

  if (isModel<User>("users", subject)) {
    return (
      <UserSelectButtonItem
        userRef={subject.reference}
        value={subject.reference.path}
      />
    );
  }

  return null;
}

function RelatedCompanyItem({ subject }: { subject?: FirestoreModel }) {
  if (isModel<Job>("jobs", subject) && subject.company) {
    return <CompanyMenuItems companyRef={subject.company} />;
  }
  if (isModel<User>("users", subject) && subject.company) {
    return (
      <CompanyMenuItems
        companyRef={subject.company}
        exclude={subject.reference}
      />
    );
  }
  if (isModel<Company>("companies", subject)) {
    return <CompanyMenuItems companyRef={subject.reference} />;
  }
  return null;
}

const roleOrder = ["owner", "admin", "user"] as const;

function CompanyMenuItems({
  companyRef,
  exclude,
}: {
  companyRef: DocumentReference;
  exclude?: DocumentReference;
}) {
  const [company] = useMemoDocument<Company>(companyRef);
  if (!company) return null;
  return (
    <>
      <Internal>
        <SelectButtonItem value={companyRef.path}>
          <ListItemAvatar sx={{ minWidth: 0, marginRight: 2 }}>
            <CompanyAvatarWithImage company={company} />
          </ListItemAvatar>
          <ListItemText
            primary={company.name}
            secondary={`${company.status} installer`}
          />
        </SelectButtonItem>
      </Internal>
      {Object.values(company.usersV2)
        .filter((user) => user.reference.path !== exclude?.path)
        .sort((a, b) => roleOrder.indexOf(a.role) - roleOrder.indexOf(b.role))
        .map(({ reference, role }) => (
          <UserSelectButtonItem
            key={reference.path}
            userRef={reference}
            value={reference.path}
            label={"Installatör"}
          />
        ))}
    </>
  );
}

interface JobSelectButtonItemProps
  extends Omit<SelectButtonItemProps, "children"> {
  job: Job;
}

function JobSelectButtonItem({
  job,
  ...buttonProps
}: JobSelectButtonItemProps) {
  return (
    <SelectButtonItem {...buttonProps}>
      <ListItemIcon>
        <AssignmentOutlined fontSize="inherit" />
      </ListItemIcon>
      <ListItemText
        primary={`Order ${[job.referenceNumber, job.externalReference]
          .filter(isDefined)
          .join(" - ")}`}
        secondary={job.partner}
      />
    </SelectButtonItem>
  );
}
