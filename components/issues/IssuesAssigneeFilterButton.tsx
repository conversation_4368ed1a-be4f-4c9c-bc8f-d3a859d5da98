import { Divider, ListItemIcon, ListItemText, capitalize } from "@mui/material";
import GroupIcon from "@mui/icons-material/Group";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import { useState } from "react";
import { useAuthUser } from "../auth/AuthProvider";
import UserAvatar from "../user/UserAvatar";
import { DocumentReference } from "firebase/firestore";

export type Assignee = DocumentReference | null | undefined;
type AssigneeMode = "my-inbox" | "unassigned" | "all";

interface IssuesAssigneeFilterButtonProps {
  onChange: (assignee: Assignee) => void;
}

export default function IssuesAssigneeFilterButton({
  onChange,
}: IssuesAssigneeFilterButtonProps) {
  const auth = useAuthUser();
  const [assignee, setAssignee] = useState<AssigneeMode>("my-inbox");
  return (
    <SelectButton
      persistent
      title={capitalize(assignee.replace("-", " "))}
      onSelection={(value: AssigneeMode) => {
        setAssignee(value);
        switch (value) {
          case "my-inbox":
            onChange(auth.userDoc?.reference);
            break;
          case "unassigned":
            onChange(null);
            break;
          case "all":
            onChange(undefined);
            break;
        }
      }}
    >
      <SelectButtonItem value={"my-inbox"}>
        <ListItemIcon>
          {auth.userDoc?.reference && (
            <UserAvatar size="small" userRef={auth.userDoc.reference} />
          )}
        </ListItemIcon>
        <ListItemText>My inbox</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"unassigned"}>
        <ListItemIcon>
          <PersonOutlineIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>Unassigned</ListItemText>
      </SelectButtonItem>
      <Divider />
      <SelectButtonItem value={"all"}>
        <ListItemIcon>
          <GroupIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>All</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}
