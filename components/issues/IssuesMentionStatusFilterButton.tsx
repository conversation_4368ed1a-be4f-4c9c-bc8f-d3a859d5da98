import { Badge, ListItemIcon, ListItemText, capitalize } from "@mui/material";
import AlternateEmailIcon from "@mui/icons-material/AlternateEmail";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import { useFirestoreCount } from "../../lib/hooks/use-firestore-count";
import { IssueMentionStatus } from "../../models/inbox/issue";
import { Query } from "firebase/firestore";

interface IssuesMentionStatusFilterButtonProps {
  onChange: (status: IssueMentionStatus) => void;
  title: string;
  countQuery: Query;
}

export default function IssuesMentionStatusFilterButton({
  onChange,
  title,
  countQuery,
}: IssuesMentionStatusFilterButtonProps) {
  const [count] = useFirestoreCount(countQuery);
  return (
    <SelectButton
      persistent
      title={`${count ?? "-"} ${capitalize(title)}`}
      onSelection={(value: IssueMentionStatus) => {
        onChange(value);
      }}
    >
      <SelectButtonItem value={"unread"}>
        <ListItemIcon>
          <Badge color="primary" variant="dot">
            <AlternateEmailIcon fontSize="small" />
          </Badge>
        </ListItemIcon>
        <ListItemText>Unread</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"all"}>
        <ListItemIcon>
          <AlternateEmailIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>All</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}
