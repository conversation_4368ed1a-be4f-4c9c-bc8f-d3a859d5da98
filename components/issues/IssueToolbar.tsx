import { <PERSON><PERSON>, Stack } from "@mui/material";
import ArchiveIcon from "@mui/icons-material/Archive";
import UnarchiveIcon from "@mui/icons-material/Unarchive";
import BedtimeOffIcon from "@mui/icons-material/BedtimeOff";
import { DocumentReference, updateDoc } from "firebase/firestore";
import { Issue } from "../../models/inbox/issue";
import { snoozeIssue, unSnoozeIssue } from "../../models/inbox/provider";
import { InternalCommentParticipant } from "../../models/inbox/internal-comment";
import IssueSnoozeButton from "./IssueSnoozeButton";
import {} from "./IssueAssignButton";
import { useDialog } from "../dialogs/DialogManager";
import { useAuthUser } from "../auth/AuthProvider";
import { DoneIssueAssignButton } from "./DoneIssueAssignButton";
import { PartnerIssueAssignButton } from "./PartnerIssueAssignButton";
import SnoozeDateDialog from "./dialogs/SnoozeDateDialog";
import ManagedDialog from "../dialogs/ManagedDialog";

interface IssueToolbarProps {
  issue: Issue;
  participant: InternalCommentParticipant;
  onClose?: () => void;
  disabled?: boolean;
}

export default function IssueToolbar({
  issue,
  participant,
  onClose,
  disabled = false,
}: IssueToolbarProps) {
  return (
    <>
      <ManagedDialog
        id="custom-snooze"
        component={SnoozeDateDialog}
        issue={issue}
        onIssueSnoozed={() => {
          onClose?.();
        }}
      />
      <Stack direction={"row"} spacing={1} padding={0}>
        <AssignButton
          issue={issue}
          participant={participant}
          disabled={disabled}
        />
        <SnoozeButton
          issue={issue}
          participant={participant}
          onClose={onClose}
          disabled={disabled}
        />
        <StatusButton
          issue={issue}
          participant={participant}
          onClose={onClose}
          disabled={disabled}
        />
      </Stack>
    </>
  );
}

function SnoozeButton({
  issue,
  participant,
  onClose,
  disabled = false,
}: IssueToolbarProps) {
  const { open } = useDialog("custom-snooze");
  const { status } = issue?.participants[participant] ?? {};

  if (status === "closed") return null;

  if (status === "snoozed") {
    return (
      <Button
        startIcon={<BedtimeOffIcon />}
        onClick={async () => {
          await unSnoozeIssue(issue.reference, participant);
          onClose?.();
        }}
        disabled={disabled}
      >
        Un-snooze
      </Button>
    );
  }

  return (
    <IssueSnoozeButton
      disabled={disabled}
      onSelection={async (snoozeUntilDate) => {
        if (snoozeUntilDate === "custom") {
          open({ participant });
          return;
        }
        await snoozeIssue(issue.reference, participant, snoozeUntilDate);
        onClose?.();
      }}
    />
  );
}

function StatusButton({
  issue,
  participant,
  onClose,
  disabled = false,
}: IssueToolbarProps) {
  const { status } = issue?.participants[participant] ?? {};

  if (status === "closed")
    return (
      <Button
        startIcon={<UnarchiveIcon />}
        variant="contained"
        onClick={async () => {
          await updateDoc(issue.reference, {
            [`participants.${participant}.status`]: "open",
          });
          onClose?.();
        }}
        disabled={disabled}
      >
        Re-open
      </Button>
    );

  return (
    <Button
      startIcon={<ArchiveIcon />}
      variant="contained"
      onClick={async () => {
        await updateDoc(issue.reference, {
          [`participants.${participant}.status`]: "closed",
        });
        onClose?.();
      }}
      disabled={disabled}
    >
      Close
    </Button>
  );
}

function AssignButton({ issue, participant, disabled }: IssueToolbarProps) {
  const authUser = useAuthUser();
  const { assignee = null } = issue?.participants[participant] ?? {};

  const assign = async (assignee: DocumentReference | null) => {
    await updateDoc(issue.reference, {
      [`participants.${participant}.assignee`]: assignee,
    });
  };

  return authUser.partner ? (
    <PartnerIssueAssignButton
      partner={authUser.partnerDoc!}
      selected={assignee}
      onSelection={assign}
      disabled={disabled}
    />
  ) : (
    <DoneIssueAssignButton
      selected={assignee}
      onSelection={assign}
      disabled={disabled}
    />
  );
}
