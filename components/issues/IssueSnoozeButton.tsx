import { Divider, ListItemText, Typography } from "@mui/material";
import BedtimeIcon from "@mui/icons-material/Bedtime";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import {
  addDays,
  addHours,
  addMonths,
  addWeeks,
  format,
  nextMonday,
  setHours,
  startOfHour,
} from "date-fns";

const modes = [
  {
    title: "Later",
    modifier: (date: Date) => addHours(date, 2),
    formatter: (date: Date) => format(date, "HH:mm"),
  },
  {
    title: "Tomorrow",
    modifier: (date: Date) => startOfHour(setHours(addDays(date, 1), 8)),
    formatter: (date: Date) => format(date, "EEE HH:mm"),
  },
  {
    title: "Monday",
    modifier: (date: Date) => startOfHour(setHours(nextMonday(date), 8)),
    formatter: (date: Date) => format(date, "EEE HH:mm"),
  },
  {
    title: "One week",
    modifier: (date: Date) => addWeeks(date, 1),
    formatter: (date: Date) => format(date, "EEE do MMM HH:mm"),
  },
  {
    title: "One month",
    modifier: (date: Date) => addMonths(date, 1),
    formatter: (date: Date) => format(date, "EEE do MMM HH:mm"),
  },
];

interface IssueSnoozeButtonProps {
  onSelection: (date: Date | "custom") => void;
  disabled?: boolean;
}

export default function IssueSnoozeButton({
  onSelection,
  disabled = false,
}: IssueSnoozeButtonProps) {
  const now = new Date();
  return (
    <SelectButton
      startIcon={<BedtimeIcon />}
      endIcon={undefined}
      title="Snooze"
      onSelection={onSelection}
      disabled={disabled}
    >
      {modes.map(({ title, modifier, formatter }) => {
        const date = modifier(now);
        return (
          <SelectButtonItem value={date} key={title}>
            <ListItemText>{title}</ListItemText>
            <Typography paddingLeft={2} variant="body2" color="text.secondary">
              {formatter(date)}
            </Typography>
          </SelectButtonItem>
        );
      })}
      <Divider />
      <SelectButtonItem value={"custom"}>
        <ListItemText>Custom date</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}
