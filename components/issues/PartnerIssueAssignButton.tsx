import IssueAssignButton, {
  CommonIssueAssignButtonProps,
} from "./IssueAssignButton";
import { Partner } from "../../models/other/partner";

interface PartnerIssueAssignButtonProps extends CommonIssueAssignButtonProps {
  partner: Partner;
}

export function PartnerIssueAssignButton({
  partner,
  selected,
  onSelection,
  disabled = false,
}: PartnerIssueAssignButtonProps) {
  return (
    <IssueAssignButton
      assigneeList={partner.users}
      selected={selected}
      onSelection={onSelection}
      disabled={disabled}
    />
  );
}
