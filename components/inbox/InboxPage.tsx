import InternalCommentsList from "../job/internal-comments/InternalCommentsList";
import React, { useEffect, useState, useMemo, useRef } from "react";
import JobDetails from "../job/Job";
import SubjectInternalCommentsList from "../job/internal-comments/SubjectInternalCommentsList";
import SubjectInternalCommentForm from "../job/internal-comments/SubjectInternalCommentForm";
import { Issue } from "../../models/inbox/issue";
import {
  arrayUnion,
  DocumentReference,
  FieldPath,
  updateDoc,
} from "firebase/firestore";
import IssueToolbar from "../issues/IssueToolbar";
import { useAuthUser } from "../auth/AuthProvider";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import {
  Content,
  DetailsPanel,
  Footer,
  Header,
  MainPanel,
  MenuPanel,
} from "../layout/panels";
import { InternalCommentParticipant } from "../../models/inbox/internal-comment";
import PartnersJob from "../job/PartnersJob";
import CompanyDetails from "../company/details/CompanyDetails";
import UserDetail from "../user/UserDetail";
import ManagedDialog from "../dialogs/ManagedDialog";
import ConfirmDeleteCommentDialog from "../../models/inbox/dialogs/ConfirmDeleteComment";
import FileDropArea from "../upload/FileDropArea";
import FileQueue from "../upload/FileQueue";
import SubjectSelectButton from "../issues/SubjectSelectButton";
import { issueForSubjectQuery } from "../../models/inbox/provider";
import { useCollectionData } from "react-firebase-hooks/firestore";
import { PlatformTier } from "../auth/Tiered";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { lastKeyCursor } from "../../lib/hooks/firestore-cursors";
import { useInfiniteScroll } from "../../lib/hooks/use-infinite-scroll";
import { useInfiniteScrollQuery } from "../../lib/hooks/use-infinite-scroll-query";
import { Query } from "firebase/firestore";
import Loading from "../common/Loading";

interface InboxPageProps {
  title: string;
  query: Query<Issue>;
  filterButton: React.ReactNode;
  selected?: Issue;
  onSelected?: (issue: Issue | undefined) => void;
}

/**
 * InboxPage displays a three-panel layout for managing internal communications:
 * - Left panel: List of issues/conversations
 * - Middle panel: Selected conversation thread with comment input
 * - Right panel: Details about the subject being discussed (job/company/user)
 *
 * Features:
 * - Pagination with infinite scroll
 * - File attachments via drag & drop
 * - Automatic marking of issues as read
 * - Subject switching within conversations
 * - Platform-tier restricted features
 *
 * @param props {@link InboxPageProps}
 * @returns React component
 */
export default function InboxPage({
  title,
  query,
  filterButton,
  selected,
  onSelected,
}: InboxPageProps) {
  const auth = useAuthUser();
  const participant = auth.partner ? "partner" : "done";
  const pageSize = 20;

  const contentRef = useRef<HTMLDivElement>(null);

  const [documents, loading, loadMore, hasMore] = useInfiniteScrollQuery(
    query,
    lastKeyCursor(new FieldPath("participants", participant, "lastUpdateTime")),
    pageSize,
  );

  // Scroll to top when query changes.
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = 0;
    }
  }, [query]);

  const issues = useMemo(() => documents.map((doc) => doc.data()), [documents]);

  const handleScroll = useInfiniteScroll({
    hasMorePages: hasMore,
    loading,
    onLoadMore: loadMore,
  });

  return (
    <DialogManager>
      <ManagedDialog
        id="confirm-delete-comment"
        component={ConfirmDeleteCommentDialog}
      />
      <FileQueue>
        <FileDropArea>
          <PanelGroup autoSaveId="inbox" direction="horizontal">
            <MenuPanel>
              <Header title={title}>{filterButton}</Header>
              <Content onScroll={handleScroll} ref={contentRef}>
                <InternalCommentsList
                  participant={participant}
                  issues={issues}
                  selected={selected}
                  onSelected={async (issue) => {
                    onSelected?.(issue);
                    // Mark this issue as read by the user.
                    if (
                      auth.userDoc?.reference &&
                      !issue.viewedBy.some(
                        (viewer) => viewer.id === auth.userDoc?.reference.id,
                      )
                    ) {
                      await updateDoc(issue.reference, {
                        viewedBy: arrayUnion(auth.userDoc.reference),
                      });
                    }
                  }}
                />
                {loading && <Loading />}
              </Content>
            </MenuPanel>
            <PanelResizeHandle />
            <MainPanel>
              <MainPanelContent
                issue={selected}
                participant={participant}
                onClose={() => {
                  onSelected?.(undefined);
                }}
              />
            </MainPanel>
            <PanelResizeHandle />
            <DetailsPanel>
              <DetailsPanelContents subject={selected?.subject} />
            </DetailsPanel>
          </PanelGroup>
        </FileDropArea>
      </FileQueue>
    </DialogManager>
  );
}

interface IssueToolbarProps {
  issue?: Issue;
  participant: InternalCommentParticipant;
  onClose?: () => void;
}

function MainPanelContent({ issue, participant, onClose }: IssueToolbarProps) {
  const { open } = useDialog("confirm-delete-comment");
  const type = issue?.subject.parent.id;

  const [switchSubject, setSwitchSubject] = useState<DocumentReference>();
  const [switchIssues, loadingSwitch] = useCollectionData(
    switchSubject ? issueForSubjectQuery(switchSubject) : undefined,
  );
  const [switchIssue] = switchIssues ?? [];

  useEffect(() => {
    if (issue) {
      setSwitchSubject(undefined);
    }
  }, [issue]);

  if (!issue || !type) return null;

  return (
    <>
      <Header
        title={"Discussion"}
        titleSecondary={
          <PlatformTier>
            <SubjectSelectButton
              subject={issue.subject}
              selected={switchSubject}
              onSelection={(jumpSubject) => {
                setSwitchSubject(jumpSubject);
              }}
            />
          </PlatformTier>
        }
      >
        <IssueToolbar
          issue={switchSubject ? switchIssue : issue}
          participant={participant}
          onClose={onClose}
          disabled={switchSubject ? loadingSwitch || !switchIssue : !issue}
        />
      </Header>
      <Content sx={{ backgroundColor: "background.paper" }}>
        <SubjectInternalCommentsList
          subject={switchSubject ?? issue.subject}
          onDelete={(comment) => {
            open({ comment });
          }}
        />
      </Content>
      <Footer paddingBottom={2}>
        <SubjectInternalCommentForm
          subject={switchSubject ?? issue.subject}
          elevated
        />
      </Footer>
    </>
  );
}

interface DetailsPanelContentsProps {
  subject?: DocumentReference;
}

function DetailsPanelContents({ subject }: DetailsPanelContentsProps) {
  const [switchSubject, setSwitchSubject] = useState<DocumentReference>();
  useEffect(() => {
    setSwitchSubject(subject);
  }, [subject]);

  if (!subject) return null;

  return (
    <>
      <Header
        title={"Details"}
        titleSecondary={
          <PlatformTier>
            <SubjectSelectButton
              subject={subject}
              selected={switchSubject}
              onSelection={(jumpSubject) => {
                setSwitchSubject(jumpSubject);
              }}
            />
          </PlatformTier>
        }
      />
      <SubjectDetails subject={switchSubject} />
    </>
  );
}

function SubjectDetails({ subject }: DetailsPanelContentsProps) {
  const authUser = useAuthUser();
  if (!subject) return null;

  const subjectType = subject.parent.id;

  switch (subjectType) {
    case "jobs":
      if (authUser.partner)
        return (
          <PartnersJob hideToolbar jobId={subject.id} hideInternalComments />
        );
      return <JobDetails slim hideInternalComments jobId={subject.id} />;
    case "companies":
      return (
        <PlatformTier>
          <Content>
            <CompanyDetails companyId={subject.id} hideInternalComments />
          </Content>
        </PlatformTier>
      );
    case "users":
      return (
        <PlatformTier>
          <Content>
            <UserDetail userId={subject.id} />
          </Content>
        </PlatformTier>
      );
    default:
      return null;
  }
}
