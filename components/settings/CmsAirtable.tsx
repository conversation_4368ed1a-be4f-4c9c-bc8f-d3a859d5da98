import { But<PERSON>, Stack } from "@mui/material";
import { getApp } from "firebase/app";
import { getFunctions, httpsCallable } from "@firebase/functions";
import { useSnackbar } from "notistack";
import { useState } from "react";

const CmsAirtable = () => {
  return (
    <Stack spacing={2}>
      <FetchAirtableButton
        buttonTitle="Update fixed price jobs"
        functionName="fetchFixedPriceJobsFromAirtable"
      />
      <FetchAirtableButton
        buttonTitle="Update booking flow configs"
        functionName="fetchBookingFlowConfigFromAirtable"
      />
      <FetchAirtableButton
        buttonTitle="Update services"
        functionName="fetchServicesFromAirtable"
      />
      <FetchAirtableButton
        buttonTitle="Update discount codes"
        functionName="fetchDiscountCodesFromAirtable"
      />
    </Stack>
  );
};

export const FetchAirtableButton = ({
  functionName,
  buttonTitle,
}: {
  functionName: string;
  buttonTitle: string;
}) => {
  const { enqueueSnackbar } = useSnackbar();
  const [fetchButtonLoading, setButtonLoading] = useState(false);

  return (
    <Button
      fullWidth
      style={{ marginRight: 8, marginBottom: 8 }}
      color="primary"
      disabled={fetchButtonLoading}
      variant="contained"
      onClick={async () => {
        setButtonLoading(true);
        httpsCallable(getFunctions(getApp(), "europe-west1"), functionName)()
          .then((_) => {
            enqueueSnackbar(`Completed - ${buttonTitle}`, {
              variant: "success",
            });
            setButtonLoading(false);
          })
          .catch((error) => {
            enqueueSnackbar(`Error occurred ${error}`, {
              variant: "warning",
            });
            setButtonLoading(false);
          });
      }}
    >
      {buttonTitle}
    </Button>
  );
};

export default CmsAirtable;
