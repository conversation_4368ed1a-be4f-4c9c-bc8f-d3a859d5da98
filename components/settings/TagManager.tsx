import { collection, doc, getFirestore, updateDoc } from "@firebase/firestore";
import {
  Button,
  Checkbox,
  CircularProgress,
  IconButton,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { deleteField } from "firebase/firestore";
import { Delete } from "mdi-material-ui";
import { useSnackbar } from "notistack";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";
import removeUndefined from "../../lib/remove-undefined";
import { Tag } from "../../models/other/admin-config";
import Form from "../common/Form";
import TextField from "../form-fields/TextField";
import { Checkboxes } from "mui-rff";
import _ from "lodash";

const TagForm = () => {
  const { enqueueSnackbar } = useSnackbar();

  return (
    <Form
      initialValues={{ show: ["companies", "jobs"] }}
      onSubmit={async (data) => {
        await updateDoc(
          doc(collection(getFirestore(), "admin"), "configuration"),
          {
            [`tags.${data.name}`]: removeUndefined({
              name: data.name,
              show: data.show,
              intercomId: data.intercomId,
            }),
          },
        );
        enqueueSnackbar("New tag created", { variant: "success" });
      }}
    >
      <Stack spacing={2}>
        <TextField name="name" type="text" label="Name" required />

        <TextField name="intercomId" type="text" label="Intercom id" />

        <Typography variant="caption">
          Use intercom id for only already added tags to intercom. Intercom id
          is generated automatically if you do not specify it.
        </Typography>

        <Checkboxes
          label="Show for"
          name="show"
          data={[
            { label: "Jobs", value: "jobs" },
            { label: "Companies", value: "companies" },
          ]}
        />
        <Button size="small" color="primary" variant="outlined" type="submit">
          Add new tag
        </Button>
      </Stack>
    </Form>
  );
};

const TagList = () => {
  const { config, loading } = useAdminConfig();

  if (!config) return null;

  if (loading) return <CircularProgress />;

  return (
    <TableContainer style={{ height: 500, overflow: "scroll" }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell colSpan={2} />
            <TableCell align="left" colSpan={3}>
              Visibility
            </TableCell>
            <TableCell align="left" colSpan={config.segments?.length}>
              Required for auto offer
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Name</TableCell>
            <TableCell>Intercom Id</TableCell>
            <TableCell align="center">Jobs</TableCell>
            <TableCell align="center">Companies</TableCell>
            <TableCell align="center"></TableCell>
            {config.segments.map((segment) => (
              <TableCell key={segment} align="center">
                {segment}
              </TableCell>
            ))}
            <TableCell align="center"></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {config.tags &&
            _.sortBy(Object.values(config.tags), ["name"]).map((tag) => (
              <TagListRow key={tag.name} tag={tag} segments={config.segments} />
            ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

const TagListRow = ({ tag, segments }: { tag: Tag; segments: string[] }) => {
  return (
    <TableRow>
      <TableCell component="th" scope="row">
        {tag.name}
      </TableCell>
      <TableCell component="th" scope="row">
        {tag.intercomId}
      </TableCell>
      <TableCell align="center" component="th" scope="row">
        <TagManagerCheckBoxVisibility
          tag={tag}
          type="jobs"
          checked={tag.show?.includes("jobs")}
        />
      </TableCell>
      <TableCell align="center" component="th" scope="row">
        <TagManagerCheckBoxVisibility
          tag={tag}
          type="companies"
          checked={tag.show?.includes("companies")}
        />
      </TableCell>
      <TableCell
        sx={{ borderRightStyle: "solid" }}
        component="th"
        scope="row"
      />
      {segments.map((segment) => (
        <TableCell key={segment} align="center" component="th" scope="row">
          <TagManagerCheckBoxWhiteListAutoOffers
            tag={tag}
            type={segment}
            checked={tag.requiredForAutoOffer?.includes(segment)}
          />
        </TableCell>
      ))}

      <TableCell align="center" padding="checkbox">
        <IconButton
          onClick={async () => {
            await updateDoc(
              doc(collection(getFirestore(), "admin"), "configuration"),
              {
                [`tags.${tag.name}`]: deleteField(),
              },
            );
          }}
        >
          <Delete />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

const TagManager = () => {
  return (
    <>
      <TagForm />
      <h4 style={{ marginTop: 50 }}>Tags </h4>
      <TagList />
    </>
  );
};

export default TagManager;

function TagManagerCheckBoxVisibility({
  tag,
  checked = false,
  type,
}: {
  tag: Tag;
  checked?: boolean;
  type: string;
}) {
  return (
    <Checkbox
      checked={checked}
      onChange={async (event) => {
        await updateDoc(doc(getFirestore(), "admin/configuration"), {
          [`tags.${tag.name}.show`]: event.target.checked
            ? [...(tag.show ?? []), type]
            : tag.show?.filter((show) => show !== type),
        });
      }}
    />
  );
}

function TagManagerCheckBoxWhiteListAutoOffers({
  tag,
  checked = false,
  type,
}: {
  tag: Tag;
  checked?: boolean;
  type: string;
}) {
  return (
    <Checkbox
      checked={checked}
      onChange={async (event) => {
        await updateDoc(doc(getFirestore(), "admin/configuration"), {
          [`tags.${tag.name}.requiredForAutoOffer`]: event.target.checked
            ? [...(tag.requiredForAutoOffer ?? []), type]
            : tag.requiredForAutoOffer?.filter((req) => req !== type),
        });
      }}
    />
  );
}
