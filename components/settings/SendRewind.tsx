import { LoadingButton } from "@mui/lab";
import { getApp } from "firebase/app";
import { getFunctions, httpsCallable } from "firebase/functions";
import { useSnackbar } from "notistack";
import { useState } from "react";
import { fetchCompany } from "../../models/company/company";
import { User, fetchUser } from "../../models/user/user";
import { doc, getFirestore } from "firebase/firestore";

const SendRewind = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);

  return (
    <LoadingButton
      disabled
      loading={loading}
      variant="contained"
      onClick={async () => {
        setLoading(true);

        for (const companyId of companyIdsTest /* companyIds */) {
          const company = await fetchCompany(companyId);

          const webPageLink = `https://done-rewind.web.app/?id=${companyId}`;

          const companyUsers = company.usersV2 && Object.keys(company.usersV2);

          if (!companyUsers) {
            console.log(
              "DONE",
              `No users found for company ${company.name} - ${company.reference.id}`,
            );

            enqueueSnackbar(
              `No users found for company ${company.name} - ${company.reference.id}`,
              {
                variant: "error",
              },
            );
            continue;
          }

          for (const userId of companyUsers) {
            const userReference = doc(getFirestore(), `users/${userId}`);

            const user = await fetchUser(userReference);

            if (!user) continue;

            if (!user.email) {
              console.log(
                `No email for user ${user.reference.id} - ${company.name}`,
              );

              enqueueSnackbar(`No email for user ${user.reference.id}`, {
                variant: "error",
              });
              continue;
            }

            await sendEmail(user, webPageLink);
          }
        }

        enqueueSnackbar(
          "Rewind send to companies. Check console for more detail!",
          {
            variant: "success",
          },
        );

        setLoading(false);
      }}
    >
      Send rewind
    </LoadingButton>
  );
};

export default SendRewind;

async function sendEmail(user: User, url: string) {
  console.log("sending email to", user.email);

  const data = {
    payload: {
      title: "DONE: Tack för året som varit! 🎉",
      receiverName: user.firstName,
      buttonTitle: "Ladda ned video",
      buttonUrl: url,
      body: `Vi hoppas att du har haft en lika bra start på året som vi har! Vi är jättetacksamma för att du är med och hjälper oss att förverkliga våra kunders drömhem och hoppas att du är lika taggad på 2024 som vi är. <br><br> Vi har sammanställt ditt år med oss och allt du har åstadkommit, småjobb som stordåd! Klicka på länken nedan för att se hur ditt år med Done var. <br><br> Nu ser vi till att göra det här året minst lika fantastiskt!`,
    },

    email: "<EMAIL>", //user.email,
  };

  try {
    await httpsCallable(
      getFunctions(getApp(), "europe-west1"),
      "sendEmail",
    )(data);
  } catch {
    console.log("FAILED to send email", user.email);
  }
}

// Add companies here!
const companyIdsTest = ["2qFgFnsI7sL7lxG5G5Hg"];
