import { Delete } from "mdi-material-ui";
import { Form } from "react-final-form";
import { TextField } from "mui-rff";
import { useSnackbar } from "notistack";
import {
  Button,
  CircularProgress,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  TableCell,
} from "@mui/material";
import {
  getFirestore,
  collection,
  doc,
  updateDoc,
  arrayUnion,
  arrayRemove,
} from "@firebase/firestore";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";

const Areas = () => {
  const { enqueueSnackbar } = useSnackbar();

  return (
    <Form
      onSubmit={async (data) => {
        await updateDoc(
          doc(collection(getFirestore(), "admin"), "configuration"),
          {
            areas: arrayUnion(data.value),
          },
        );
        enqueueSnackbar("Updated value", { variant: "success" });
      }}
      render={({ handleSubmit }) => (
        <form onSubmit={handleSubmit}>
          <TextField
            size="small"
            name="value"
            type="text"
            label="Add new area"
            variant="outlined"
            required
          />
          <br></br>
          <br></br>

          <Button
            style={{
              marginRight: "12px",
            }}
            color="primary"
            variant="outlined"
            type="submit"
          >
            Add new area
          </Button>
        </form>
      )}
    />
  );
};

const AreaList = () => {
  const { config, loading } = useAdminConfig();

  if (!config) return null;

  if (loading) return <CircularProgress />;

  return (
    <TableContainer style={{ height: 802, overflow: "scroll" }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Areas</TableCell>
            <TableCell>Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {config.areas.map((row: any) => (
            <TableRow key={row}>
              <TableCell scope="row">{row}</TableCell>
              <TableCell padding="checkbox">
                <IconButton
                  onClick={async () => {
                    await updateDoc(
                      doc(collection(getFirestore(), "admin"), "configuration"),
                      {
                        areas: arrayRemove(row),
                      },
                    );
                  }}
                >
                  <Delete />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

const AreaManager = () => {
  return (
    <>
      <Areas />
      <AreaList />
    </>
  );
};

export default AreaManager;
