import { Button, Typography } from "@mui/material";
import {
  collection,
  getFirestore,
  getDocs,
  orderBy,
  query,
  Timestamp,
  where,
  DocumentReference,
} from "firebase/firestore";
import { downloadCsvFile } from "../../lib/download-csv-file";
import { formatSwedishZipCode } from "../../lib/format-zip-code";
import { fetchUser } from "../../models/user/user";
import { useSnackbar } from "notistack";

import { useState } from "react";
import { DesktopDatePicker } from "@mui/x-date-pickers";

const CustomerListAfterDate = () => {
  const { enqueueSnackbar } = useSnackbar();

  const [dateFrom, setDateFrom] = useState<Date | null>(new Date());

  const handleChange = (newDate: Date | null) => {
    setDateFrom(newDate);
  };

  return (
    <>
      <DesktopDatePicker
        disableFuture
        label="From date"
        value={dateFrom}
        onChange={handleChange}
        slotProps={{ textField: {} }}
        // renderInput={(params: any) => <TextField {...params} />}
      />

      <Typography>
        *Customers who have at least one job which is labeled done.
      </Typography>

      <Button
        variant="contained"
        onClick={async () => {
          const db = getFirestore();
          var jobsDocsAfter = await getDocs(
            query(
              collection(db, "jobs"),
              where(
                "events.jobDone",
                ">",
                Timestamp.fromDate(dateFrom ?? new Date()),
              ),
              where("events.jobDone", "!=", null),
              orderBy("events.jobDone", "desc"),
            ),
          );

          const customerData = await Promise.all(
            jobsDocsAfter.docs.map(async (e) => {
              if (!e.data().customer) return null;

              const user = await fetchUser(
                e.data().customer as DocumentReference,
              );

              if (!user) return null;

              const { firstName, lastName, address } = user;

              if (!address) return null;

              const { city, streetAddress, zip } = address;

              if (!zip || !city || !streetAddress) return null;

              return {
                name: `${firstName} ${lastName}`,
                addressLine1: streetAddress,
                adressLine2: `${formatSwedishZipCode(zip)} ${city}`,
              };
            }),
          );

          const uniqueUserEntries = customerData
            // Remove null users
            .filter(Boolean)
            .filter(
              (value, index, self) =>
                index === self.findIndex((data) => data?.name === value?.name),
            );

          downloadCsvFile(
            uniqueUserEntries,
            `done_customer_list_from_${dateFrom?.toDateString()}_issued_at`,
          );
          enqueueSnackbar("Users list was downloaded", {
            variant: "success",
          });
        }}
      >
        Download customer data from {dateFrom?.toDateString()}
      </Button>
    </>
  );
};

export default CustomerListAfterDate;
