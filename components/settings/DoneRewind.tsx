import { getDocs } from "@firebase/firestore";
import { Button } from "@mui/material";
import {
  collection,
  collectionGroup,
  getFirestore,
  orderBy,
  query,
  Timestamp,
  where,
} from "firebase/firestore";
import { TEST_USER_IDS } from "../../config/constants";
import {
  Company,
  companyOwner,
  fetchCompany,
} from "../../models/company/company";
import { Job } from "../../models/job/job";
import { fetchParsedInvoiceLinesForJob } from "../../models/invoice/parsed_invoice_line";

const DoneRewind = () => {
  return (
    <Button variant="contained" onClick={generateRewindData}>
      Generate rewind as JSON
    </Button>
  );
};

export default DoneRewind;

async function generateRewindData(): Promise<void> {
  const db = getFirestore();
  // Fetch all eligible companies
  const companies = await getDocs(
    query(collection(db, "companies"), where("status", "==", "active")),
  );

  var rewind: any = {};
  for (const company of companies.docs) {
    const companyRewind = await generateRewind(company.id);
    if (companyRewind) {
      rewind[company.id] = companyRewind;
    }
  }

  const json = JSON.stringify(rewind);

  var pom = document.createElement("a");
  var blob = new Blob([json], {
    type: "application/json;charset=utf-8;",
  });
  var url = URL.createObjectURL(blob);
  pom.href = url;
  pom.setAttribute("download", `doneRewind.json`);
  pom.click();
}

async function generateRewind(companyId: string): Promise<any> {
  console.log("Generating rewind data for company", companyId);
  const db = getFirestore();

  const company = await fetchCompany(companyId);

  const jobsInclDemo = await getDocs(
    query(
      collection(db, "jobs"),
      where("company", "==", company.reference),
      where("events.jobDone", ">", Timestamp.fromDate(new Date(2023, 0, 1))),
    ),
  );

  const convertedJobs = jobsInclDemo.docs.map((doc) => {
    return { ...doc.data(), reference: doc.ref } as Job;
  });

  // Remove demo jobs
  const completedJobsIn2023 = convertedJobs.filter(
    (job) => TEST_USER_IDS.indexOf(job.customer?.id ?? "") === -1,
  );

  if (completedJobsIn2023.length === 0) return null;

  // Earned sum of quotes
  let totalEarning = 0;
  for (const job of completedJobsIn2023) {
    let earningOnJob = 0;

    const invoiceLines = await fetchParsedInvoiceLinesForJob(job.reference);

    earningOnJob = invoiceLines.reduce((sum, current) => {
      return sum + (current.totalAmount ?? 0);
    }, 0);

    totalEarning = earningOnJob + totalEarning;
  }

  // Locations
  const jobLocations: any[] = completedJobsIn2023
    .map((job) => job.cache?.customerLocation?.coordinates)
    .filter((x) => x);

  // Ratings
  const allReviews = await getDocs(
    query(
      collection(db, company.reference.path + "/companyReviews"),
      where("status", "==", "complete"),
    ),
  );
  const reviewsWithPublicFeedback = allReviews.docs
    .filter(
      (review) =>
        review.get("publicFeedback")?.length > 0 && review.get("rating") >= 0.8,
    )
    .map((snapshot) => {
      return {
        publicFeedback: snapshot.get("publicFeedback"),
        rating: snapshot.get("rating") * 5,
      };
    });

  if (totalEarning === 0) return null;

  if (completedJobsIn2023.length < 3) return null;

  const chatAnalysis = (await generateRewindChatAnalysis(company)) ?? {};

  if (reviewsWithPublicFeedback.length < 3) return null;

  const data = {
    company: company.reference.path,
    companyName: company.name,
    completedJobs: completedJobsIn2023.length,
    acceptedQuotesSum: totalEarning / 100,
    jobLocations,
    customerReviews: reviewsWithPublicFeedback,
    chatAnalysis,
    services: company.services,
  };

  const json = JSON.stringify(data);
  console.log(json);

  return data;
}

async function generateRewindChatAnalysis(
  company: Company,
): Promise<any | undefined> {
  const emojis = require("emojis-list");
  const db = getFirestore();

  const owner = companyOwner(company);

  if (!owner) return;

  const chatMessages = await getDocs(
    query(
      collectionGroup(db, "messages"),
      where("sender", "==", owner),
      where("createTime", ">", Timestamp.fromDate(new Date(2021, 0, 1))),
      orderBy("createTime", "desc"),
    ),
  );

  const emojiCount: { [userId: string]: number } = {};

  for (const message of chatMessages.docs) {
    if (message.get("type") !== "text") continue;

    for (const emoji of emojis) {
      if ((message.get("body") as string).includes(emoji)) {
        if (emoji !== "🏻") {
          if (emojiCount[emoji] === undefined) {
            emojiCount[emoji] = 1;
          } else {
            emojiCount[emoji] += 1;
          }
        }
      }
    }
  }

  return {
    emojiCount,
    numberOfMessages: chatMessages.size,
  };
}
