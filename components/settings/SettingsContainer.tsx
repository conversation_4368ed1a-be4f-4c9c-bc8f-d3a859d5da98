import { Grid2, Stack } from "@mui/material";
import PageContainer from "../layout/PageContainer";
import AreaManager from "./AreaManager";
import CmsAirtable from "./CmsAirtable";
import CraftsmanEmailList from "./CraftsmanEmailList";
import CustomerListAfterDate from "./CustomerListAfterDate";
import DoneRewind from "./DoneRewind";
import SendRewind from "./SendRewind";
import TagManager from "./TagManager";
import UpdateMultilineDocument from "./UpdateMultilineDocument";
import ComponentContainer from "../common/ComponentContainer";
import AutoOffersSettings from "./AutoOfferSettings";

export default function SettingsContainer() {
  return (
    <PageContainer>
      <Stack spacing={2}>
        <Grid2 container spacing={1}>
          <Grid2 size={{ md: "grow" }}>
            <ComponentContainer padding={8} title="Auto offers settings">
              <AutoOffersSettings />
            </ComponentContainer>
          </Grid2>

          <Grid2 size="grow">
            <ComponentContainer padding={8} title="Airtable CMS">
              <CmsAirtable />
            </ComponentContainer>
          </Grid2>
        </Grid2>

        <Grid2 container spacing={1}>
          <Grid2 size="grow">
            <ComponentContainer padding={0} title="Tag settings">
              <TagManager />
            </ComponentContainer>
          </Grid2>

          <Grid2 size="grow">
            <ComponentContainer padding={0} title="Area settings">
              <AreaManager />
            </ComponentContainer>
          </Grid2>
        </Grid2>

        <Grid2 container spacing={1}>
          <Grid2 size="grow">
            <ComponentContainer
              padding={0}
              title="Update document multi-line field"
            >
              <UpdateMultilineDocument />
            </ComponentContainer>
          </Grid2>

          <Grid2 size="grow">
            <ComponentContainer padding={0} title="Data export">
              <Stack spacing={1} padding={2}>
                <CustomerListAfterDate />

                <CraftsmanEmailList />

                <DoneRewind />

                <SendRewind />
              </Stack>
            </ComponentContainer>
          </Grid2>
        </Grid2>
      </Stack>
    </PageContainer>
  );
}
