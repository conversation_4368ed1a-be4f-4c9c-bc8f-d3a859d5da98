import { <PERSON><PERSON>, <PERSON><PERSON>, Stack } from "@mui/material";
import { doc, getFirestore, updateDoc } from "@firebase/firestore";
import { Form } from "react-final-form";
import { TextField } from "mui-rff";
import { useSnackbar } from "notistack";

const UpdateMultilineDocument = () => {
  const db = getFirestore();
  const { enqueueSnackbar } = useSnackbar();

  return (
    <>
      <Stack spacing={1}>
        <Alert variant="filled" severity="warning">
          Do not use it alone! Ask @devs
        </Alert>

        <Form
          onSubmit={async (data) => {
            const ref = doc(db, data.documentPath);
            await updateDoc(ref, { [data.fieldPath]: data.value });
            enqueueSnackbar("Tag added", { variant: "success" });
          }}
          render={({ handleSubmit }) => (
            <form onSubmit={handleSubmit}>
              <TextField
                size="small"
                fullWidth
                name="documentPath"
                type="text"
                label="Document path"
                variant="outlined"
                required
              />
              <br />
              <br />
              <TextField
                size="small"
                fullWidth
                name="fieldPath"
                type="text"
                label="Field path"
                variant="outlined"
                required
              />
              <br />
              <br />
              <TextField
                size="small"
                name="value"
                type="text"
                multiline
                label="Content"
                variant="outlined"
                required
              />
              <br />
              <br />
              <Button color="primary" variant="outlined" type="submit">
                Add tag
              </Button>
            </form>
          )}
        />
      </Stack>
    </>
  );
};

export default UpdateMultilineDocument;
