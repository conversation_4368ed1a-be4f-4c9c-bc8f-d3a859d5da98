import { Button } from "@mui/material";
import { downloadCsvFile } from "../../lib/download-csv-file";
import { useSnackbar } from "notistack";
import {
  fetchCompanies,
  fetchCompanyAdmin,
} from "../../models/company/company";

const CraftsmanEmailList = () => {
  const { enqueueSnackbar } = useSnackbar();

  return (
    <Button
      variant="contained"
      onClick={async () => {
        const companies = await fetchCompanies();

        const customerData = await Promise.all(
          companies.map(async (e) => {
            const user = await fetchCompanyAdmin(e);

            const companyName = e.name;
            const serviceCity = e.serviceCity?.join(", ") ?? "N/A";
            const firstName = user
              ? (user?.firstName ?? "No firstName")
              : "No admin";
            const lastName = user
              ? (user.lastName ?? "No lastName")
              : "No admin";
            const email = user ? (user?.email ?? "No email") : "No admin";

            return {
              companyName,
              serviceCity,
              name: `${firstName} ${lastName}`,
              email,
            };
          }),
        );

        const uniqueUserEntries = customerData
          // Remove null users
          .filter(Boolean)
          .filter(
            (value, index, self) =>
              index === self.findIndex((data) => data?.name === value?.name),
          );

        downloadCsvFile(uniqueUserEntries, `company_emails`);
        enqueueSnackbar("Company admin email addresses list was downloaded", {
          variant: "success",
        });
      }}
    >
      Download craftsman email list as CSV
    </Button>
  );
};

export default CraftsmanEmailList;
