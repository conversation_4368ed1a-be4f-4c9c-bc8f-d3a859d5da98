import { <PERSON><PERSON>, <PERSON><PERSON>, InputAdornment, Stack } from "@mui/material";
import { updateDoc, doc, collection, getFirestore } from "firebase/firestore";
import { Checkboxes } from "mui-rff";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";
import Form from "../common/Form";
import Loading from "../common/Loading";
import NumberField from "../form-fields/NumberField";

export default function AutoOffersSettings() {
  const { config, loading } = useAdminConfig();

  if (loading) return <Loading />;

  return (
    <Form
      initialValues={config?.autoOfferConfiguration}
      onSubmit={async (autoOfferConfiguration) => {
        await updateDoc(
          doc(collection(getFirestore(), "admin"), "configuration"),
          {
            autoOfferConfiguration,
          },
        );
      }}
    >
      <Stack spacing={2}>
        <Checkboxes name="enabled" data={{ label: "enabled", value: false }} />

        <Alert severity="warning">
          Disabling auto offers only affects new jobs.
        </Alert>

        <NumberField
          type="number"
          name="numberOfCompanies"
          label="Number of companies to offer to"
        />

        <NumberField
          name="maxRangeInKm"
          label="Max range"
          slotProps={{
            input: {
              endAdornment: <InputAdornment position="end">km</InputAdornment>,
            },
          }}
        />

        <NumberField
          type="number"
          name="initialDelayInSeconds"
          label="Initial delay"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">second</InputAdornment>
              ),
            },
          }}
        />

        <NumberField
          type="number"
          name="timeIntervalInSeconds"
          label="Time between offers"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">second</InputAdornment>
              ),
            },
          }}
        />

        <Button variant="contained" color="primary" type="submit">
          Save
        </Button>
      </Stack>
    </Form>
  );
}
