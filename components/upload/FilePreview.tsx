import CancelIcon from "@mui/icons-material/Cancel";
import TooltipToolbar from "../tooltip-toolbar/TooltipToolbar";
import { TooltipToolbarButton } from "../tooltip-toolbar/TooltipToolbarButton";
import ToolsStack from "../layout/ToolsStack";
import { useState, useEffect } from "react";
import PreviewCard from "../files/PreviewCard";
import { Box } from "@mui/system";

interface FilePreviewProps {
  file: File;
  onDelete?: () => void;
}

/**
 * Displays a preview of a file.
 */
export default function FilePreview({ file, onDelete }: FilePreviewProps) {
  const [objectUrl, setObjectUrl] = useState<string>();
  useEffect(() => {
    const url = URL.createObjectURL(file);
    setObjectUrl(url);
    return () => URL.revokeObjectURL(url);
  }, [file]);

  return (
    <TooltipToolbar
      tools={
        <ToolsStack spacing={0}>
          <TooltipToolbarButton
            title={"Remove file"}
            Icon={CancelIcon}
            onClick={onDelete}
          />
        </ToolsStack>
      }
    >
      <Box>
        <PreviewCard src={objectUrl} title={file.name} />
      </Box>
    </TooltipToolbar>
  );
}
