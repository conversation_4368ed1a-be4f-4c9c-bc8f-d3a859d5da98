import { useReducer } from "react";
import { FileQueueContext, fileReducer } from "./FileQueueContext";

interface FileQueueProps {
  children: React.ReactNode;
}

export default function FileQueue({ children }: FileQueueProps) {
  const [state, dispatch] = useReducer(fileReducer, {
    files: [],
  });

  return (
    <FileQueueContext.Provider value={{ state, dispatch }}>
      {children}
    </FileQueueContext.Provider>
  );
}
