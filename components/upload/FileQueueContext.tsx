import path from "path";
import { Dispatch, create<PERSON>ontext, useContext, useMemo } from "react";
import { assertNever } from "../../lib/assert-never";
import { uploadFile } from "../../lib/upload-file";
import {
  IdentifiableFile,
  UploadableFile,
  UploadedFile,
  fileToUploadableFile,
} from "./UploadableFile";

interface AddAction {
  type: "add";
  files: UploadableFile[];
}

interface RemoveAction {
  type: "remove";
  file: Pick<IdentifiableFile, "uuid">;
}

interface UpdateAction {
  type: "update";
  files: UploadableFile[];
}

interface ClearAction {
  type: "clear";
}

type Action = AddAction | RemoveAction | ClearAction | UpdateAction;

interface QueueState {
  files: UploadableFile[];
}

interface Context {
  state: QueueState;
  dispatch: Dispatch<Action>;
}

/**
 * Reducer for the file queue.
 * @param state The current state.
 * @param action The action to perform.
 * @returns The new state.
 */
export function fileReducer(state: QueueState, action: Action): QueueState {
  switch (action.type) {
    case "add":
      return {
        ...state,
        files: [...state.files, ...action.files],
      };
    case "remove":
      return {
        ...state,
        files: state.files.filter((file) => file.uuid !== action.file.uuid),
      };
    case "clear":
      return {
        ...state,
        files: [],
      };
    case "update":
      const updateMap = new Map(action.files.map((file) => [file.uuid, file]));
      return {
        ...state,
        files: state.files.map((file) => updateMap.get(file.uuid) ?? file),
      };
    default:
      assertNever(action);
  }
}

/**
 * Context for the file queue.
 */
export const FileQueueContext = createContext<Context>({
  dispatch: () => {
    throw new Error(
      "FileQueueContext not initialized. Make sure to wrap components that make use of `useFileQueue` in a <FileQueue>.",
    );
  },
  state: { files: [] },
});

interface FileQueueActions {
  /**
   * Add files to the queue.
   * @param files The files to add.
   */
  add: (files: FileList) => void;

  /**
   * Remove a file from the queue.
   * @param file The file to remove.
   */
  remove: (file: Pick<IdentifiableFile, "uuid">) => void;

  /**
   * Clear all files from the queue.
   */
  clear: () => void;

  /**
   * Upload all files in the queue.
   */
  upload: (basePath: string) => Promise<UploadableFile[]>;
}

/**
 * Hook to interact with the file upload queue.
 * @returns The current files in the queue and actions to manipulate the queue.
 */
export function useFileQueue() {
  const { state, dispatch } = useContext(FileQueueContext);
  const handler = useMemo<FileQueueActions>(() => {
    return {
      add: (files) => {
        const pendingFiles = [...files].map(fileToUploadableFile);
        dispatch({ type: "add", files: pendingFiles });
      },
      remove: (file) => dispatch({ type: "remove", file }),
      clear: () => dispatch({ type: "clear" }),
      upload: async (basePath: string) => {
        dispatch({
          type: "update",
          files: state.files.map((file) => ({ ...file, status: "uploading" })),
        });
        return uploadImages(state.files, basePath).then((uploadedFiles) => {
          dispatch({ type: "update", files: uploadedFiles });
          return uploadedFiles;
        });
      },
    };
  }, [dispatch, state]);
  return [state.files, handler] as const;
}

async function uploadImages(
  files: UploadableFile[],
  basePath: string,
  bucket?: string,
) {
  return await Promise.all(
    files.map(async (file) => {
      if (file.status !== "pending") return file;
      const result = await uploadFile(
        path.join(basePath, file.uuid),
        file.file,
        bucket,
      );
      const uploadedFile: UploadedFile = {
        ...file,
        status: "uploaded",
        ref: result.ref,
      };
      return uploadedFile;
    }),
  );
}
