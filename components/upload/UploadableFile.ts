import { StorageReference } from "firebase/storage";
import { v4 as uuid } from "uuid";

export interface IdentifiableFile {
  uuid: string;
  file: File;
}

export interface PendingFile extends IdentifiableFile {
  status: "pending";
}

export interface UploadedFile extends IdentifiableFile {
  status: "uploaded";
  ref: StorageReference;
}

export interface FailedFile extends IdentifiableFile {
  status: "error";
}

export interface InProgressFile extends IdentifiableFile {
  status: "uploading";
}

export type UploadableFile =
  | PendingFile
  | UploadedFile
  | FailedFile
  | InProgressFile;

export function isPendingFile(file: UploadableFile): file is PendingFile {
  return file.status === "pending";
}

export function isUploadedFile(file: UploadableFile): file is UploadedFile {
  return file.status === "uploaded";
}

export function isFailedFile(file: UploadableFile): file is FailedFile {
  return file.status === "error";
}

export function isInProgressFile(file: UploadableFile): file is InProgressFile {
  return file.status === "uploading";
}

export function isUploadableFile(file: UploadableFile): file is UploadableFile {
  return (
    isPendingFile(file) ||
    isUploadedFile(file) ||
    isFailedFile(file) ||
    isInProgressFile(file)
  );
}

export const fileToUploadableFile = (file: File): PendingFile => ({
  file,
  uuid: uuid(),
  status: "pending",
});
