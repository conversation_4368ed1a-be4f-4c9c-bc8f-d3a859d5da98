import { Backdrop, Stack, Typography, alpha, styled } from "@mui/material";
import UploadIcon from "@mui/icons-material/UploadFile";
import { useState } from "react";
import { FileDrop } from "react-file-drop";
import { Box, darken, lighten } from "@mui/system";
import { useFileQueue } from "./FileQueueContext";

interface FileDropAreaProps {
  /**
   * Callback when files are dropped.
   * Use this to handle the files yourself, otherwise wrap this component in a <FileQueue> and access the files using `useFileQueue`.
   * @param file The files that were dropped.
   */
  onDrop?: (file: FileList) => void;
  children: React.ReactNode;
}

/**
 * Display a full sized file drop zone.
 */
export default function FileDropArea({ children, onDrop }: FileDropAreaProps) {
  const [openBackdrop, setOpenBackdrop] = useState(false);
  const [_, { add }] = useFileQueue();

  return (
    <FileDrop
      onDrop={(files) => {
        setOpenBackdrop(false);
        if (!files) return;

        // If a callback is provided, we are in managed mode and the parent will handle the files.
        if (onDrop) {
          onDrop(files);
        } else {
          add(files);
        }
      }}
      onDragOver={() => {
        setOpenBackdrop(true);
      }}
      onDragLeave={() => {
        setOpenBackdrop(false);
      }}
    >
      <FileAreaBackdrop open={openBackdrop}>
        <Circle>
          <Stack alignItems="center">
            <UploadIcon fontSize="large" sx={{ fontSize: 40 }} />
            <Typography variant="subtitle1" gutterBottom>
              Drop files to upload
            </Typography>
          </Stack>
        </Circle>
      </FileAreaBackdrop>
      {children}
    </FileDrop>
  );
}

const Circle = styled(Box)(({ theme }) => ({
  backgroundColor:
    theme.palette.mode === "light"
      ? lighten(theme.palette.info.main, 0.8)
      : darken(theme.palette.info.main, 0.8),
  borderRadius: "50%",
  width: "11em",
  height: "11em",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: theme.palette.info.main,
}));

const FileAreaBackdrop = styled(Backdrop)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.background.paper, 0.5),
  backdropFilter: "blur(5px)",
  zIndex: theme.zIndex.drawer + 1,
}));
