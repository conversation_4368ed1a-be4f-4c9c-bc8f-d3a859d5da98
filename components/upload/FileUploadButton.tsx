import { styled } from "@mui/system";
import { useFileQueue } from "./FileQueueContext";
import { Box, IconButton } from "@mui/material";
import AttachmentIcon from "@mui/icons-material/Attachment";
import { useRef } from "react";

interface FileUploadButtonProps {
  multiple?: boolean;
  icon?: React.ReactNode;
}

export function FileUploadButton({
  multiple = false,
  icon,
}: FileUploadButtonProps) {
  const [_, { add }] = useFileQueue();
  const inputRef = useRef<HTMLInputElement>(null);
  return (
    <Box>
      <IconButton onClick={() => inputRef.current?.click()} size="small">
        {icon ? icon : <AttachmentIcon fontSize="inherit" />}
      </IconButton>
      <HiddenInput
        type="file"
        multiple={multiple}
        ref={inputRef}
        onChange={({ target }) => {
          if (!target.files) return;
          add(target.files);
        }}
      />
    </Box>
  );
}

const HiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});
