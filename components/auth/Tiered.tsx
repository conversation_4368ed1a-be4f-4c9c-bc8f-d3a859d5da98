import React from "react";
import { PartnerTier } from "../../models/other/partner";
import { useAuthUser } from "./AuthProvider";

interface TieredProps {
  tier?: ExactTier | RelativeTier;
  children: React.ReactNode;
}

interface ExactTier {
  exact: PartnerTier;
  atLeast?: never;
}

interface RelativeTier {
  exact?: never;
  atLeast: PartnerTier;
}

/**
 * Show children if the user has the required tier.
 */
function Tiered({
  tier = { atLeast: PartnerTier.Platform },
  children,
}: TieredProps) {
  const auth = useAuthUser();
  const partner = auth.partnerDoc;

  if (auth.isSuperAdmin && !tier.exact) {
    return <>{children}</>;
  }

  if (!partner) {
    return null;
  }

  const userTier = partner.tier ?? PartnerTier.Standard;

  if (tier.exact && userTier !== tier.exact) {
    return null;
  }

  if (tier.atLeast && userTier < tier.atLeast) {
    return null;
  }

  return <>{children}</>;
}

interface TierProps {
  /**
   * If true, the user must have exactly the tier.
   */
  exact?: boolean;
  children: React.ReactNode;
}

/**
 * Show children if the user has at least the platform tier.
 */
export function PlatformTier({ children, exact = false }: TierProps) {
  return (
    <Tiered
      tier={
        exact
          ? { exact: PartnerTier.Platform }
          : { atLeast: PartnerTier.Platform }
      }
    >
      {children}
    </Tiered>
  );
}

/**
 * Show children if the user has at least the standard tier.
 */
export function StandardTier({ children, exact = false }: TierProps) {
  return (
    <Tiered
      tier={
        exact
          ? { exact: PartnerTier.Standard }
          : { atLeast: PartnerTier.Standard }
      }
    >
      {children}
    </Tiered>
  );
}
