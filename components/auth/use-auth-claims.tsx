import { getAuth } from "@firebase/auth";
import { useAuthState } from "react-firebase-hooks/auth";
import { useEffect, useState } from "react";
import { IdTokenResult } from "firebase/auth";
import { AuthTokenClaims } from "../../models/user/auth-token-claims";

/**
 * This function fetch firebase user, user document and token claims.
 * Warning!: Do not use this function! Instead use `useAuthContext` hook.
 * */
export function useAuthClaims() {
  const [firebaseUser, loading] = useAuthState(getAuth());
  const [authTokenClaims, setAuthTokenClaims] = useState<AuthTokenClaims>();
  const [loadingTokenResult, setLoadingTokenResult] = useState(true);

  useEffect(() => {
    if (!firebaseUser && !loading) {
      setAuthTokenClaims(undefined);
      setLoadingTokenResult(false);
      return;
    }

    setLoadingTokenResult(true);

    firebaseUser
      ?.getIdTokenResult()
      .then((idTokenResult: IdTokenResult) => {
        setAuthTokenClaims(
          idTokenResult.claims
            ? (idTokenResult.claims as AuthTokenClaims)
            : undefined,
        );
      })
      .catch((error) => {
        console.error("Failed to read token", error);
      })
      .finally(() => {
        setLoadingTokenResult(false);
      });
  }, [firebaseUser, loading]);

  return {
    firebaseUser,
    claims: authTokenClaims,
    loading: loading || loadingTokenResult,
  };
}
