import {
  ReactElement,
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useAuthClaims } from "./use-auth-claims";
import { AuthUser } from "../../models/user/user";
import { useUser } from "../../hooks/user/use-user";
import { usePartner } from "../../hooks/partner/use-partner";
import { setUser } from "@sentry/react";
import { fullName } from "../../lib/full-name";

interface AuthContext {
  authUser: AuthUser;
  setPartner: (id: string | undefined) => void;
}

const AuthInfoContext = createContext<AuthContext>({
  authUser: {},
  setPartner: () => {
    throw new Error(
      "AuthProvider not initialized, this function must be called in a child component to AuthProvider",
    );
  },
});

/**
 * Returns information about the current user.
 */
export function useAuthUser() {
  const { authUser } = useContext(AuthInfoContext);
  return authUser;
}

/**
 * Allows super admin to change the partner.
 */
export function useBecomePartner() {
  const { setPartner } = useContext(AuthInfoContext);
  return setPartner;
}

interface AuthProviderProps {
  loadingComponent?: ReactElement;
  children: ReactNode;
}

export default function AuthProvider({
  children,
  loadingComponent,
}: AuthProviderProps) {
  // Get auth and any claims.
  const { claims, firebaseUser, loading } = useAuthClaims();
  const [partnerId, setPartnerId] = useState<string>();
  const [isSuperAdmin, setIsSuperAdmin] = useState<boolean>();
  const [isEmulatingPartner, setIsEmulatingPartner] = useState(false);

  // Create a callback to override partner, but only super admin can do it.
  const setPartner = useCallback(
    (partnerId: string | undefined) => {
      if (!claims?.is_super_admin) {
        throw new Error("Only super admin can change partner");
      }
      setPartnerId(partnerId);
      setIsSuperAdmin(!partnerId && claims?.is_super_admin);
      setIsEmulatingPartner(!!partnerId);
    },
    [claims?.is_super_admin],
  );

  // Load user and partner docs.
  const [userDoc, loadingUserData] = useUser(claims?.done_user_id);
  const [partnerDoc, loadingPartner] = usePartner(
    partnerId ?? claims?.partner?.id,
  );

  // Create auth user object.
  const authUser: AuthUser = useMemo(
    () => ({
      firebaseUser,
      userDoc,
      partnerDoc,
      partner: partnerId ?? claims?.partner?.id,
      isSuperAdmin: isSuperAdmin ?? claims?.is_super_admin,
      isEmulatingPartner,
    }),
    [
      claims?.is_super_admin,
      claims?.partner?.id,
      firebaseUser,
      partnerDoc,
      partnerId,
      userDoc,
      isSuperAdmin,
      isEmulatingPartner,
    ],
  );

  // Set user info for Sentry.
  useEffect(() => {
    setUser(
      userDoc
        ? {
            id: userDoc.reference.id,
            email: userDoc.email,
            name: fullName(userDoc),
          }
        : null,
    );
  }, [userDoc]);

  if ((loading || loadingUserData || loadingPartner) && loadingComponent) {
    return loadingComponent;
  }

  return (
    <AuthInfoContext.Provider value={{ authUser, setPartner }}>
      {children}
    </AuthInfoContext.Provider>
  );
}
