import "@toast-ui/calendar/dist/toastui-calendar.min.css";
import { Button, Grid2, IconButton, Typography } from "@mui/material";
import { Stack } from "@mui/system";
import CalendarBase from "@toast-ui/react-calendar";
import { createRef, useState, useEffect, useCallback } from "react";
import { formatTitle } from "../../lib/format-time-relative";
import { EventObject } from "@toast-ui/calendar";
import { doneColor } from "../../theme/colors";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import { upperFirst } from "lodash";

type CalendarBaseProps = React.ComponentProps<typeof CalendarBase>;
type View = NonNullable<CalendarBaseProps["view"]>;

interface CalendarProps {
  selectedEventId?: string;
  closedEventId?: string;
  events?: EventObject[];
  onEventClick?: (eventId: string) => void;
  onSelectDateTime?: (start: Date, end: Date) => void;
  onViewChange?: (from: Date, to: Date, view: View) => void;
  height?: string;
  isReadOnly?: boolean;
  initialView?: View;
}

/**
 * Calendar that can display events in different views.
 *
 * **Do not use this component directly, instead use `Calendar`**
 */
export default function InternalCalendar({
  events,
  onEventClick,
  onSelectDateTime,
  onViewChange,
  selectedEventId,
  closedEventId,
  height,
  isReadOnly = false,
  initialView = "month",
}: CalendarProps) {
  const calendarRef = createRef<CalendarBase>();

  const [view, setView] = useState<View>(initialView);

  const [displayDate, setDisplayDate] = useState<Date>();

  const viewChangeHandler = useCallback(() => {
    const calendar = calendarRef.current?.getInstance();
    if (!calendar) return;
    const from = calendar.getDateRangeStart().toDate();
    const to = calendar.getDateRangeEnd().toDate();
    if (!from || !to) return;
    onViewChange?.(from, to, view);
  }, [onViewChange, view, calendarRef]);

  const setSelected = useCallback(
    (eventId?: string) => {
      const calendar = calendarRef.current?.getInstance();
      if (!calendar) return;

      // Deselect the previous event
      if (selectedEventId) {
        calendar.updateEvent(selectedEventId, "", {
          customStyle: undefined,
        });
      }

      // Select the new event
      if (eventId) {
        const event = calendar.getEvent(eventId, "");
        if (!event) return;

        // Set the event select style
        calendar.updateEvent(eventId, "", {
          customStyle: {
            outlineColor: doneColor,
            outlineStyle: "solid",
            outlineWidth: 3,
          },
        });

        // Inform the parent that the event was selected
        onEventClick?.(eventId);
      }
    },
    [calendarRef, selectedEventId],
  );

  useEffect(() => {
    const calendar = calendarRef.current?.getInstance();
    if (!calendar) return;

    if (!displayDate) {
      setDisplayDate(calendar.getDate().toDate());
      viewChangeHandler();
    }

    calendar.on("clickEvent", (event) => {
      setSelected(event.event?.id);
    });

    calendar.on("selectDateTime", (event) => {
      onSelectDateTime?.(event.start, event.end);
    });
  }, [
    calendarRef,
    closedEventId,
    displayDate,
    onEventClick,
    onSelectDateTime,
    viewChangeHandler,
    setSelected,
  ]);

  // Clear the selection if the calendar is read-only
  useEffect(() => {
    if (isReadOnly) {
      const calendar = calendarRef.current?.getInstance();
      calendar?.clearGridSelections();
    }
  }, [calendarRef, isReadOnly]);

  return (
    <Stack flex={1}>
      <Grid2 container justifyContent="space-between" alignItems="center">
        <Grid2>
          <Grid2 container spacing={1} alignItems="center">
            <Grid2>
              <IconButton
                aria-label="back"
                onClick={() => {
                  calendarRef.current?.getInstance()?.prev();
                  setDisplayDate(
                    calendarRef.current?.getInstance()?.getDate().toDate(),
                  );
                  viewChangeHandler();
                }}
              >
                <ChevronLeft />
              </IconButton>
            </Grid2>
            <Grid2>
              <IconButton
                aria-label="forward"
                onClick={() => {
                  calendarRef.current?.getInstance()?.next();
                  setDisplayDate(
                    calendarRef.current?.getInstance()?.getDate().toDate(),
                  );
                  viewChangeHandler();
                }}
              >
                <ChevronRight />
              </IconButton>
            </Grid2>
            <Grid2>
              <Button
                onClick={() => {
                  calendarRef.current?.getInstance()?.today();
                  setDisplayDate(
                    calendarRef.current?.getInstance()?.getDate().toDate(),
                  );
                  viewChangeHandler();
                }}
              >
                Today
              </Button>
            </Grid2>
            <Grid2>
              <CalendarTitle date={displayDate} mode={view} />
            </Grid2>
          </Grid2>
        </Grid2>
        <Grid2>
          <SelectButton
            title={upperFirst(view)}
            onSelection={(value: View) => {
              setView(value);
              const calendar = calendarRef.current?.getInstance();
              if (selectedEventId) {
                const event = calendar?.getEvent(selectedEventId, "");
                if (event) {
                  calendar?.setDate(event.start);
                }
              }
              viewChangeHandler();
            }}
          >
            <SelectButtonItem value="month">Month</SelectButtonItem>
            <SelectButtonItem value="week">Week</SelectButtonItem>
            <SelectButtonItem value="day">Day</SelectButtonItem>
          </SelectButton>
        </Grid2>
      </Grid2>

      <CalendarBase
        isReadOnly={isReadOnly}
        usageStatistics={false}
        gridSelection={false}
        theme={{
          common: {
            saturday: {
              color: "red",
            },
          },
        }}
        events={events}
        ref={calendarRef}
        view={view}
        week={{
          taskView: false,
          startDayOfWeek: 1,
          eventView: ["time"],
        }}
        height={height}
        month={{
          startDayOfWeek: 1,
          dayNames: [
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
          ],
        }}
      />
    </Stack>
  );
}

function CalendarTitle({ date, mode }: { date?: Date; mode: View }) {
  return (
    <Typography color="text.primary">{formatTitle(date, mode)}</Typography>
  );
}
