import { addHours } from "date-fns";
import { Job } from "../../models/job/job";
import { EventObject } from "@toast-ui/calendar";
import uniqolor from "uniqolor";

/**
 * Creates a calendar event from a job.
 * @param job Job to create event from.
 * @returns Returns a calendar object.
 */
export function eventForJob(job: Job): EventObject {
  return {
    id: job.reference.id,
    title: `${
      job.cache?.customerLocation?.subLocality ??
      job.cache?.customerLocation?.postalTown ??
      job.cache?.customerName ??
      job.referenceNumber
    }`,
    category: "time",
    start: job.scheduledWorkStartTime!.toDate(),
    end: addHours(job.scheduledWorkStartTime!.toDate(), 2),
    reference: job.reference,
    backgroundColor: uniqolor(job.company?.id ?? "").color,
    isReadOnly: true,
  };
}
