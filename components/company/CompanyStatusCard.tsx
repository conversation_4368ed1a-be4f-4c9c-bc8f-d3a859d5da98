import { Company } from "../../models/company/company";
import { COMPANY_STATUSES } from "../../config/constants";
import { Select, Stack, Typography } from "@mui/material";
import { updateDoc } from "@firebase/firestore";

const CompanyStatusCard = ({ company }: { company: Company }) => {
  const setStatus = (status: any) => {
    updateDoc(company.reference, { status });
  };

  return (
    <Stack spacing={0.5}>
      <Typography variant="caption">Status</Typography>
      <Select
        native
        variant="outlined"
        size="small"
        value={company.status}
        onChange={(event) => setStatus(event.target.value)}
      >
        {COMPANY_STATUSES.map((status) => (
          <option value={status.value} key={status.value}>
            {status.label}
          </option>
        ))}
      </Select>
    </Stack>
  );
};

export default CompanyStatusCard;
