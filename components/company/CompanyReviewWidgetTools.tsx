import { useState } from "react";
import Link from "next/link";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { LoadingButton } from "@mui/lab";
import Markdown from "react-markdown";
import { Box, Button, Card, Stack } from "@mui/material";

/**
 * ReviewWidgetTools Component
 *
 * This component provides tools to generate and view a company's review widget.
 * It includes functionalities to generate a widget, view it, and display the necessary code
 * to embed the widget on a webpage.
 *
 * @param {{companyId: string}} props The company reference id.
 */
export default function ReviewWidgetTools({
  companyId,
}: {
  companyId: string;
}) {
  const [loading, setLoading] = useState(false);
  const [showCode, setShowCode] = useState(false);

  return (
    <Stack spacing={2}>
      <Stack direction="row" spacing={1}>
        <LoadingButton
          loading={loading}
          size="small"
          variant="outlined"
          onClick={async () => {
            setLoading(true);
            await httpsCallable(
              getFunctions(getApp(), "europe-west1"),
              "generateCompanyReviewsFiles",
            )({ companyId: companyId });
            setLoading(false);
          }}
        >
          Generate rating widget
        </LoadingButton>

        <Link
          href={`https://storage.googleapis.com/done-public-reviews/${companyId}/index.html`}
        >
          <Button variant="outlined" size="small">
            See rating widget
          </Button>
        </Link>

        <Button
          size="small"
          onClick={() => {
            setShowCode(!showCode);
          }}
        >
          {showCode ? "Hide code" : "Show code"}
        </Button>
      </Stack>
      {showCode && (
        <Card variant="outlined" sx={{ padding: 1 }}>
          <Box overflow="auto">
            <Markdown>
              {`\`\`\`javacsript
  <iframe id="done-reviews-widget" src="https://storage.googleapis.com/done-public-reviews/${companyId}/index.html" width="100%" style="border:none;background-color:transparent;" allowtransparency="true"></iframe>
  
  <script>
    function resizeDoneRatingIframe() {
        const iframe = document.getElementById('done-reviews-widget');
        if (window.innerWidth <= 600) {
            iframe.style.height = '292px';
        } else {
            iframe.style.height = '170px';
        }
    }
          
    resizeDoneRatingIframe();
  
    window.addEventListener('resize', resizeDoneRatingIframe);
    window.addEventListener('load', resizeDoneRatingIframe);
  </script>`}
            </Markdown>
          </Box>
        </Card>
      )}
    </Stack>
  );
}
