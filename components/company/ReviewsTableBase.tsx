import ReviewsTable from "./ReviewsTable";
import Stack from "@mui/system/Stack";
import { FC } from "react";
import { roundToTwo } from "../../lib/round-to-two";
import { TEST_USER_IDS } from "../../config/constants";
import { Typography } from "@mui/material";
import { useCollection } from "react-firebase-hooks/firestore";
import {
  getFirestore,
  query,
  collectionGroup,
  orderBy,
  where,
} from "@firebase/firestore";

const ReviewsTableBase: FC = () => {
  const collectionReference = query(
    query(
      collectionGroup(getFirestore(), "companyReviews"),
      where("status", "==", "complete"),
    ),
    orderBy("createTime", "desc"),
  );

  const [values, loading] = useCollection(collectionReference);

  let totalScore = 0;
  let totalReview = 0;

  values?.docs.forEach((e) => {
    const reviewData = e.data();
    if (!TEST_USER_IDS.includes(reviewData.author.reference.id)) {
      totalReview += 1;
      totalScore += reviewData.rating;
    }
  });

  const initRows =
    values?.docs
      .map((doc: any) => ({
        data: doc.data(),
        id: doc.id,
        jobTags: (doc.data().jobTags as string[])?.join(", "),
        createTime: doc.data().createTime.toDate(),
        jobDescription: doc.data().jobDescription,
        companyName: doc.data().company.name,
        reviewerId: doc.data().author.ref,
        reviewerName: doc.data().author.name,
        rating: doc.data().rating * 5,
        privateFeedback: doc.data().privateFeedback ?? "-",
        publicFeedback: doc.data().publicFeedback ?? "-",
        complaints: (doc.data().complaints || ["-"]).join(", "),
        compliments: (doc.data().compliments || ["-"]).join(", "),
        reviewerRecommends: doc.data().reviewerRecommends,
      }))
      .filter(
        (e: any) => !TEST_USER_IDS.includes(e.data.author.reference.id),
      ) ?? [];

  return (
    <Stack sx={{ color: "text.primary" }} padding={2} spacing={1}>
      {!loading && (
        <Typography variant="body2">
          Number of total reviews: {totalReview} | Average: 5 /{" "}
          {roundToTwo((totalScore * 5) / totalReview)}
        </Typography>
      )}

      <ReviewsTable isLoading={loading} initRows={initRows} />
    </Stack>
  );
};

export default ReviewsTableBase;
