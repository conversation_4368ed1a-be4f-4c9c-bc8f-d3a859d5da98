import { Stack } from "@mui/material";
import { Company } from "../../models/company/company";
import NetworkChip from "./NetworkChip";

interface CompanyNetworksProps {
  company: Pick<Company, "memberOf">;

  /**
   * If set, only show networks that are in this list.
   */
  visibleNetworks?: string[];
}

/**
 * Show networks that the company is a member of.
 */
export function CompanyNetworks({
  company,
  visibleNetworks,
}: CompanyNetworksProps) {
  return (
    <Stack direction="row" spacing={1}>
      {company.memberOf
        .filter(
          (network) => !visibleNetworks || visibleNetworks.includes(network),
        )
        .map((network) => (
          <NetworkChip key={network} network={network} />
        ))}
    </Stack>
  );
}
