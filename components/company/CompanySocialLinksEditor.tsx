import { Loading<PERSON>utton } from "@mui/lab";
import { useState } from "react";
import { Company } from "../../models/company/company";
import { useSnackbar } from "notistack";
import { Form } from "react-final-form";
import { TextField } from "mui-rff";
import { updateDoc } from "firebase/firestore";
import removeUndefined from "../../lib/remove-undefined";
import { Typography } from "@mui/material";
import { COMPANY_SOCIAL_URLS } from "../../config/constants";

const CompanySocialLinksEditor = ({ company }: { company: Company }) => {
  const { enqueueSnackbar } = useSnackbar();

  const [loading, setLoading] = useState(false);

  return (
    <>
      <Typography>Social urls</Typography>
      <div style={{ height: 20 }} />
      <Form
        initialValues={{ ...company.socialUrls }}
        onSubmit={async (newSocialUrls) => {
          setLoading(true);

          updateDoc(company.reference, {
            socialUrls: removeUndefined(newSocialUrls),
          });

          enqueueSnackbar("Urls updated!", {
            variant: "success",
          });

          setLoading(false);
        }}
        render={({ handleSubmit, invalid, submitting }) => (
          <form
            onSubmit={handleSubmit}
            style={{ display: "flex", flexDirection: "column" }}
          >
            <>
              {
                // Add new available social url fields to `COMPANY_SOCIAL_URLS`
                COMPANY_SOCIAL_URLS.map((e) => {
                  return (
                    <TextField
                      key={e.field}
                      style={{ marginBottom: 20 }}
                      name={e.field}
                      type="text"
                      inputMode="url"
                      size="small"
                      label={e.label}
                      variant="outlined"
                    />
                  );
                })
              }

              <LoadingButton
                style={{ float: "right" }}
                loading={loading}
                fullWidth
                color="primary"
                variant="contained"
                type="submit"
                disabled={invalid || submitting}
              >
                Save
              </LoadingButton>
            </>
          </form>
        )}
      />
    </>
  );
};

export default CompanySocialLinksEditor;
