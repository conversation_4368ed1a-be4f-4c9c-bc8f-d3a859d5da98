import { useDocumentData } from "react-firebase-hooks/firestore";
import { ListItem, ListItemText } from "@mui/material";
import formatAmount from "../../lib/format-amount";
import {
  DocumentReference,
  getFirestore,
  collection,
  doc,
} from "@firebase/firestore";

export const CompanyQuoteDefaultSettings = ({
  companyRef,
}: {
  companyRef: DocumentReference;
}) => {
  const db = getFirestore();
  const [quoteDefaultSettings, loading] = useDocumentData(
    doc(
      collection(
        doc(collection(db, "companies"), companyRef.id),
        "defaultQuoteSettings",
      ),
      companyRef.id,
    ),
  );

  if (loading) return null;
  if (!quoteDefaultSettings) return null;

  return (
    <>
      <ListItem divider>
        <ListItemText
          primary="Prices included vat"
          secondary={quoteDefaultSettings.enterPricesInclVat ? "Yes" : "No"}
        />
      </ListItem>

      <ListItem divider>
        <ListItemText
          primary="Default work cost type"
          secondary={quoteDefaultSettings.defaultWorkCostType}
        />
      </ListItem>

      <ListItem divider>
        <ListItemText
          primary="Default hourly rate"
          secondary={
            quoteDefaultSettings.defaultHourlyRate
              ? formatAmount(quoteDefaultSettings.defaultHourlyRate / 100)
              : "-"
          }
        />
      </ListItem>

      <ListItem divider>
        <ListItemText
          primary="Default transportation cost"
          secondary={
            quoteDefaultSettings.defaultTransportationCost
              ? formatAmount(
                  quoteDefaultSettings.defaultTransportationCost / 100,
                )
              : "-"
          }
        />
      </ListItem>

      <ListItem>
        <ListItemText
          primary="Default notes"
          secondary={quoteDefaultSettings.defaultNotes}
        />
      </ListItem>
    </>
  );
};
