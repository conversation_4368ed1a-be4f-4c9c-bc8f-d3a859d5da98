import JobsTable from "../job/table/JobsTable";
import { collection, getFirestore, query, where } from "@firebase/firestore";

import { DocumentReference, orderBy } from "firebase/firestore";

export const CompanyJobs = ({
  companyRef,
}: {
  companyRef: DocumentReference;
}) => {
  const companyJobsQuery = query(
    collection(getFirestore(), "jobs"),
    where("company", "==", companyRef),
    orderBy("createTime", "desc"),
  );

  return (
    <JobsTable initCollection={companyJobsQuery} defaultStatusFilter={"open"} />
  );
};

export default CompanyJobs;
