import JobsTable from "../job/table/JobsTable";
import { collection, getFirestore, query, where } from "@firebase/firestore";

import { DocumentReference, orderBy } from "firebase/firestore";
import JobsDataGrid from "../job/filterable-overview/JobsDataGrid";
import { companyJobsQuery } from "../../models/job/job-provider";
import { useCollectionData, useDocumentData } from "react-firebase-hooks/firestore";

export default function CompanyJobs({
  companyId,
}: {
  companyId: string;
}) {

  const query = companyJobsQuery(companyId);
  const [jobs = []] = useCollectionData(query)

  return (
        <JobsDataGrid
          jobs={jobs}
          configuration={'network'}
        />
  );
};
