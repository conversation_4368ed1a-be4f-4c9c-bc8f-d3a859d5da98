import TimedCheckbox from "../common/TimedCheckbox";
import { CERTIFICATES } from "../../config/constants";
import { Table, TableCell, TableHead, TableRow } from "@mui/material";
import { Company } from "../../models/company/company";

const CertificateRow = ({
  company,
  title,
  dataKey,
}: {
  company: Company;
  title: string;
  dataKey: string;
}) => (
  <TableRow>
    <TableCell>{title}</TableCell>
    <TableCell align="right">
      <TimedCheckbox documentRef={company.reference} dataKey={dataKey} />
    </TableCell>
  </TableRow>
);

export const CompanyCertificates = ({ company }: { company: Company }) => {
  return (
    <Table size="small">
      <TableHead>
        <TableRow>
          <TableCell>Title</TableCell>
          <TableCell align="right">Checked</TableCell>
        </TableRow>
      </TableHead>
      {CERTIFICATES.map((cert: any) => (
        <CertificateRow
          key={cert.dataKey}
          company={company}
          title={cert.label}
          dataKey={`certificates.${cert.dataKey}`}
        />
      ))}
    </Table>
  );
};

export default CompanyCertificates;
