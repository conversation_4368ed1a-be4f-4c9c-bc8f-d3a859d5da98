import { FC, useState } from "react";
import { Checkbox, TableCell, TableRow } from "@mui/material";
import { arrayUnion, arrayRemove, updateDoc } from "@firebase/firestore";
import { Company } from "../../models/company/company";
import { useSnackbar } from "notistack";

interface CompanyServiceCityItemProps {
  company: Company;
  city: string;
}

const CompanyServiceCityItem: FC<CompanyServiceCityItemProps> = ({
  company,
  city,
}) => {
  const [checked, setChecked] = useState(
    company.serviceCity && company.serviceCity.includes(city),
  );

  const { enqueueSnackbar } = useSnackbar();

  const onCheckboxClick = async (event: any) => {
    if (event.target.checked) {
      await updateDoc(company.reference, "serviceCity", arrayUnion(city));
      enqueueSnackbar(`${city} added to company`, { variant: "success" });
    } else {
      await updateDoc(company.reference, "serviceCity", arrayRemove(city));
      enqueueSnackbar(`${city} removed from company`, { variant: "error" });
    }

    setChecked(!event.target.checked);
  };

  return (
    <TableRow>
      <TableCell>{city}</TableCell>
      <TableCell align="right">
        <Checkbox checked={checked} onChange={onCheckboxClick} />
      </TableCell>
    </TableRow>
  );
};

export default CompanyServiceCityItem;
