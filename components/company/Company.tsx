import React, { useState } from "react";
import { getDownloadURL, getStorage, ref } from "@firebase/storage";
import { Company } from "../../models/company/company";
import { Avatar, Button } from "@mui/material";
import { useDialog } from "../dialogs/DialogManager";
import FileDropArea from "../upload/FileDropArea";
import { Edit } from "@mui/icons-material";

export function DocumentDropArea({
  children,
  company,
}: {
  children: React.ReactNode;
  company: Company;
}) {
  const { open: openUploadDocuments } = useDialog("upload-document");

  return (
    <FileDropArea
      onDrop={(files) => {
        if (!files || files.length === 0) return;
        openUploadDocuments({ files, company });
      }}
    >
      {children}
    </FileDropArea>
  );
}

export function EditCompanyButton() {
  const { open } = useDialog("edit-company");

  return (
    <Button
      size="small"
      variant="outlined"
      startIcon={<Edit />}
      onClick={() => {
        open();
      }}
    >
      Edit
    </Button>
  );
}

export const CompanyImage = ({ imageRef }: { imageRef?: string }) => {
  const [imageUrl, setImageUrl] = useState<null | string>(null);
  console.warn("imageRef", imageRef);
  if (!imageRef) return null;
  const storage = getStorage();

  const pathReference = ref(storage, imageRef);
  getDownloadURL(pathReference).then(setImageUrl);

  if (imageUrl) {
    return (
      <Avatar
        style={{ width: 32, height: 32 }}
        variant="circular"
        src={imageUrl ?? ""}
        onClick={() => window.open(imageUrl ?? "", "_blank")}
      />
    );
  } else {
    return null;
  }
};
