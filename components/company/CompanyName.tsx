import {
  Avatar,
  Box,
  Card,
  IconButton,
  ListItemIcon,
  ListItemText,
  Stack,
  Typography,
} from "@mui/material";
import { Company, useCompanyDocument } from "../../models/company/company";
import { DocumentReference } from "@firebase/firestore";
import UserAvatar from "../user/UserAvatar";
import { CompanyBankgiroInformationAlert } from "./CompanyBankgiroInformationAlert";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import { useUser } from "../../hooks/user/use-user";
import { isDefined } from "../../lib/filter-undefined";
import Link from "next/link";
import { Message, Call, CopyAll } from "@mui/icons-material";
import { useStorageURL } from "../../lib/hooks/use-storage-url";
import { User } from "../../models/user/user";
import { trackCall } from "../common/CallUserLink";
import { useAuthUser } from "../auth/AuthProvider";
import Internal from "../auth/Internal";

const CompanyName = ({
  companyRef,
  showExtras = true,
}: {
  companyRef: DocumentReference;
  showExtras?: boolean;
}) => {
  const { company, loading } = useCompanyDocument(companyRef.id);
  const auth = useAuthUser();

  if (loading || !company) return null;

  return (
    <Stack spacing={1}>
      <Card style={{ cursor: "pointer" }} variant="outlined">
        <Stack padding={1} spacing={1} direction="row" alignItems="center">
          <Link passHref href={`/companies/company?id=${companyRef.id}`}>
            <Stack spacing={1} direction="row" alignItems="center">
              <CompanyAvatarWithImage company={company} />
              <Stack>
                <Typography fontWeight={600}>{company.name}</Typography>
                <Typography
                  sx={{ display: "inline", color: "info.main" }}
                  component="span"
                  variant="body2"
                  color="text.primary"
                >
                  {(company.services || []).join(", ")}
                </Typography>
              </Stack>
            </Stack>
          </Link>

          <Box flex={1} />
          <IconButton
            sx={{ color: "text.disabled" }}
            onClick={() => {
              navigator.clipboard.writeText(company.name);
            }}
            size="small"
          >
            <CopyAll />
          </IconButton>
        </Stack>
      </Card>
      <Internal>
        {showExtras && (
          <Stack spacing={1} direction="row">
            <SelectButton
              size="small"
              style={{ maxWidth: 100, marginLeft: 12 }}
              color="info"
              title="Intercom"
              startIcon={<Message />}
              onSelection={(selectedUser: User) => {
                const url = `https://app.intercom.com/apps/gn8o0bfp/users/show?user_id=${selectedUser.reference.id}`;
                window.open(url, "_blank");
              }}
            >
              {company.usersV2 &&
                Object.entries(company.usersV2).map(([id, role]) => (
                  <CompanyUserIntercomSelectButtonItem
                    key={id}
                    userId={id}
                    role={role.role}
                  />
                ))}
            </SelectButton>
            <SelectButton
              size="small"
              style={{ maxWidth: 100, marginLeft: 12 }}
              title="Call"
              startIcon={<Call />}
              onSelection={async (selectedUser: User) => {
                trackCall(auth, selectedUser);
                const url = `tel:${selectedUser.phoneNumber}`;
                window.open(url, "_blank");
              }}
            >
              {company.usersV2 &&
                Object.entries(company.usersV2).map(([id, role]) => (
                  <CompanyUserIntercomSelectButtonItem
                    key={id}
                    userId={id}
                    role={role.role}
                  />
                ))}
            </SelectButton>
          </Stack>
        )}
        {showExtras && <CompanyBankgiroInformationAlert company={company} />}
      </Internal>
    </Stack>
  );
};

export const CompanyAvatarWithImage = ({ company }: { company: Company }) => {
  const [fileUrl] = useStorageURL(company.logoRef);

  if (fileUrl) {
    return <Avatar sx={{ width: 24, height: 24 }} src={fileUrl} />;
  }
  return (
    <Avatar sx={{ width: 24, height: 24 }} style={{ border: `1px solid gray` }}>
      {company.name[0]}
    </Avatar>
  );
};

export default CompanyName;

function CompanyUserIntercomSelectButtonItem({
  userId,
  role,
}: {
  userId: string;
  role: string;
}) {
  const [user, loading] = useUser(userId);

  if (!user || loading) return null;

  return (
    <SelectButtonItem value={user}>
      <ListItemIcon>
        <UserAvatar size="small" userRef={user.reference} />
      </ListItemIcon>
      <ListItemText>
        {[user.firstName, user.lastName].filter(isDefined).join(" ")} - {role}
      </ListItemText>
    </SelectButtonItem>
  );
}
