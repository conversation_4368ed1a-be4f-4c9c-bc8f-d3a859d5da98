import { COMPANY_STATUSES } from "../../config/constants";
import {
  Button,
  Dialog,
  FormControl,
  Grid2,
  InputLabel,
  MenuItem,
  Select,
  Stack,
} from "@mui/material";
import { FC, useMemo, useState } from "react";
import { getFirestore, collection } from "@firebase/firestore";
import Loading from "../common/Loading";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";
import { tagToLabeledValue } from "../../models/other/admin-config";
import { GridCellParams } from "@mui/x-data-grid";
import React from "react";
import { UserRoleMap } from "../../models/user/user-role";
import PaginatedDataGrid from "../common/table/PaginatedDataGrid";
import PageContainer from "../layout/PageContainer";
import PageGridContainer from "../layout/PageGridContainer";
import CompanyRatings from "./CompanyRatings";
import CompanyDetails from "./CompanyDetails";
import { generateCompanyRow } from "../../models/company/company_row";
import { useMediaQuery } from "react-responsive";
import { orderBy, query, where } from "firebase/firestore";
import { useAuthUser } from "../auth/AuthProvider";
import Internal from "../auth/Internal";
import { isDefined } from "../../lib/filter-undefined";
import {
  dateTimeColumn,
  ExtendedGridColDef,
  numberColumn,
  optional,
} from "../common/table/columns";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import WriteInstallerDialog from "./WritePartnerInstallerDialog";

interface FilterProps {
  filter: string | null;
  label: string;
  onFilterChange: (filter: string) => void;
  filterConfig: any;
}

export const FilterTabs: FC<FilterProps> = ({
  filter,
  label,
  onFilterChange,
  filterConfig,
}) => {
  const filters = [{ value: null, label: "All" }, ...filterConfig];

  return (
    <FormControl sx={{ width: 200 }}>
      <InputLabel id="company-filter">{label}</InputLabel>
      <Select
        style={{ marginTop: 8 }}
        size="small"
        color="primary"
        label={label}
        labelId="company-filter"
        value={filter}
        onChange={(event) => onFilterChange(event.target.value as string)}
      >
        {filters.map((s) => (
          <MenuItem key={s.value} value={s.value}>
            {s.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

const columns = (forPartner: boolean): ExtendedGridColDef[] =>
  [
    {
      field: "name",
      headerName: "Name",
      width: 220,
    },
    optional(forPartner, {
      field: "actionNeeded",
      headerName: "Action needed",
      width: 220,
      renderCell: (params: GridCellParams) => {
        return (
          <>
            {Object.values(params.row.company.usersV2 as UserRoleMap).filter(
              (e) => e.role === "owner",
            ).length === 0 && <> ‼️ No company owner set </>}
          </>
        );
      },
    }),
    dateTimeColumn({
      field: "createTime",
      type: "dateTime",
      headerName: "Created at",
      width: 160,
    }),
    numberColumn({
      field: "numberOfCompletedJobs",
      headerName: "Completed jobs",
      type: "number",
      width: 132,
    }),
    dateTimeColumn({
      field: "lastOfferReplyTime",
      type: "dateTime",
      headerName: "Last active",
      width: 160,
    }),
    optional(forPartner, { field: "tags", headerName: "Tags", width: 160 }),
    optional(forPartner, {
      field: "services",
      headerName: "Services",
      width: 160,
    }),
    optional(forPartner, {
      field: "serviceCity",
      headerName: "Service cities",
      width: 160,
    }),
    optional(forPartner, { field: "orgNo", headerName: "Org no", width: 160 }),
    {
      field: "rating",
      headerName: "Rating",
      width: 220,
      renderCell: (params: GridCellParams) => {
        return <CompanyRatings companyRef={params.row.reference} />;
      },
    },
  ].filter(isDefined);

function CreateCompanyButton() {
  const { open: openCreateCompanyDialog } = useDialog("create-installer");

  return (
    <Button onClick={() => openCreateCompanyDialog()}>New installer</Button>
  );
}

export default function PartnerInstallersTable() {
  const authUser = useAuthUser();
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 700px)" });
  const [companyId, setCompanyId] = React.useState<null | string>();
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [tagFilter, setTagFilter] = useState<string | null>(null);

  const companiesQuery = useMemo(() => {
    let queryRef = query(
      collection(getFirestore(), "companies"),
      where("memberOf", "array-contains", authUser.partner ?? null),
      orderBy("createTime", "desc"),
    );

    if (statusFilter) {
      queryRef = query(queryRef, where("status", "==", statusFilter));
    }

    if (tagFilter) {
      queryRef = query(
        queryRef,
        where("tags", "array-contains-any", [tagFilter]),
      );
    }

    return queryRef;
  }, [statusFilter, tagFilter]);

  const configSnap = useAdminConfig();

  if (configSnap.loading) return <Loading />;

  return (
    <DialogManager>
      <ManagedDialog id="create-installer" component={WriteInstallerDialog} />
      <PageContainer>
        <PageGridContainer>
          <Stack padding={1} direction="row" spacing={1}>
            <Internal>
              <FilterTabs
                label="Status"
                filter={statusFilter}
                onFilterChange={(filter) => setStatusFilter(filter)}
                filterConfig={COMPANY_STATUSES}
              />

              <FilterTabs
                label="Tag"
                filter={tagFilter}
                onFilterChange={(filter) => setTagFilter(filter)}
                filterConfig={tagToLabeledValue(configSnap?.config?.tags, [
                  "companies",
                ])}
              />
            </Internal>
            <CreateCompanyButton />
          </Stack>
          <Dialog
            maxWidth="lg"
            fullWidth
            open={isTabletOrMobile && Boolean(companyId)}
            onClose={() => {
              setCompanyId(null);
            }}
          >
            <Button
              style={{ width: 100 }}
              onClick={() => {
                setCompanyId(null);
              }}
            >
              Close
            </Button>
            {companyId && <CompanyDetails companyId={companyId} />}
          </Dialog>
          <Grid2 size="grow" container spacing={1}>
            <Grid2 size="grow">
              <PaginatedDataGrid
                onRowSelected={(id) => {
                  setCompanyId(companyId === id ? null : id);
                }}
                rowGenerator={generateCompanyRow}
                query={companiesQuery}
                columns={columns(authUser.partner !== null)}
              />
            </Grid2>
            {!isTabletOrMobile && Boolean(companyId) && (
              <Grid2
                size={{ xs: 7 }}
                style={{
                  overflow: "scroll",
                  height: "94vh", // TODO: Remove this workaround when layout issues in JobContainer is fixed.
                }}
              >
                {companyId && (
                  <CompanyDetails companyId={companyId as string} />
                )}
              </Grid2>
            )}
          </Grid2>
        </PageGridContainer>
      </PageContainer>
    </DialogManager>
  );
}
