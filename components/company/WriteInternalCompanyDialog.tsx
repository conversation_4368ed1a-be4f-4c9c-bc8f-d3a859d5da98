import {
  collection,
  doc,
  getFirestore,
  serverTimestamp,
  setDoc,
} from "@firebase/firestore";

import { FormSpy } from "react-final-form";
import { Company, CompanyInput } from "../../models/company/company";
import removeUndefined from "../../lib/remove-undefined";
import Loading from "../common/Loading";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";
import { tagToLabeledValue } from "../../models/other/admin-config";
import FormDialog from "../dialogs/FormDialog";
import { DialogProps } from "../dialogs/default-dialog-props";
import ServicesField from "../form-fields/ServicesField";
import AutocompleteLabeledValueField from "../form-fields/AutocompleteField";
import Organisationsnummer from "organisationsnummer";
import { Button } from "@mui/material";
import Link from "next/link";
import { updateDoc } from "firebase/firestore";
import TextField from "../form-fields/TextField";
import { DonePartnerId } from "../../models/other/partner";

interface WriteInternalCompanyDialogProps extends DialogProps {
  company?: Company;
}

export default function WriteInternalCompanyDialog({
  isOpen,
  close,
  company,
}: WriteInternalCompanyDialogProps) {
  const db = getFirestore();

  const { config, loading } = useAdminConfig();

  if (loading) return <Loading />;

  const serviceCityOptions =
    config?.areas?.map((city: any) => {
      return { value: city, label: city };
    }) ?? [];

  const companyTagOptions = tagToLabeledValue(config?.tags, ["companies"]);

  return (
    <FormDialog
      title={company ? `Edit ${company.name}` : "Create company"}
      isOpen={isOpen}
      initialValues={company}
      close={close}
      onSubmit={async (newCompany) => {
        if (company) {
          const companyInput = {
            ...newCompany,
            address: removeUndefined(newCompany.address),
          };

          await updateDoc(company.reference, removeUndefined(companyInput));
        } else {
          const companyInput: CompanyInput = {
            ...newCompany,
            name: newCompany.name.trim(),
            legalName: newCompany.legalName.trim(),
            address: removeUndefined(newCompany.address),
            createTime: serverTimestamp(),
            status: "new",
            usersV2: {},
            users: [],
            statistics: {},
            memberOf: [DonePartnerId],
          };

          await setDoc(
            doc(collection(db, "companies")),
            removeUndefined(companyInput),
          );
        }
      }}
    >
      <TextField required name="name" type="text" label="Company name" />
      <TextField required name="legalName" type="text" label="Legal name" />

      <TextField
        validator={(value) => {
          return !Organisationsnummer.valid(value)
            ? "Organisationsnummer is invalid"
            : undefined;
        }}
        required
        name="orgNo"
        type="text"
        label="Organisation number"
      />

      <FormSpy>
        {(props) => (
          <Link
            passHref
            target="_blank"
            rel="noopener noreferrer"
            href={`https://www.bankgirot.se/sok-bankgironummer?bgnr=&company=&city=&orgnr=${props.values?.orgNo}`}
          >
            <Button
              disabled={!props.values?.orgNo?.length}
              fullWidth
              variant="outlined"
            >
              Check Bankgiro with org number
            </Button>
          </Link>
        )}
      </FormSpy>

      {!company ? (
        <>
          <ServicesField name="services" />

          <AutocompleteLabeledValueField
            name="tags"
            multiple
            label={"Tags"}
            items={companyTagOptions}
          />

          <AutocompleteLabeledValueField
            name="serviceCity"
            multiple
            label={"Service city"}
            items={serviceCityOptions}
          />
        </>
      ) : (
        <></>
      )}

      <TextField
        name="billingSettings.paymentInfo.bankgiro"
        type="text"
        label="Bankgiro"
      />

      <TextField
        name="billingSettings.paymentInfo.bankAccount"
        type="text"
        label="Bank account"
      />

      <TextField
        required
        name="address.line1"
        type="text"
        label="Address line 1"
      />

      <TextField name="address.line2" type="text" label="Address line 2" />

      <TextField required name="address.zip" type="text" label="Zip code" />

      <TextField required name="address.city" type="text" label="City" />

      {company ? (
        <>
          <TextField
            name="socialUrls.facebookUrl"
            type="text"
            label="Facebook url"
          />
          <TextField
            name="socialUrls.instagramUrl"
            type="text"
            label="Instagram url"
          />
          <TextField
            name="socialUrls.websiteUrl"
            type="text"
            label="Website url"
          />
          <TextField
            name="profileText"
            type="text"
            multiline
            label="Profile text"
          />
        </>
      ) : (
        <></>
      )}
    </FormDialog>
  );
}
