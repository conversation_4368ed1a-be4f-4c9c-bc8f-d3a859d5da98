import { Company } from "../../models/company/company";
import { COMPANY_INSURED_BY } from "../../config/constants";
import { MenuItem, Select, Stack, Typography } from "@mui/material";
import { deleteField, updateDoc } from "@firebase/firestore";
import { FC } from "react";

type CompanyInsuredByCardProps = {
  company: Company;
};

const CompanyInsuredByCard: FC<CompanyInsuredByCardProps> = (props) => {
  const setInsuredBy = (insuredBy: string) => {
    if (!insuredBy) {
      updateDoc(props.company.reference, { insuredBy: deleteField() });
      return;
    }

    updateDoc(props.company.reference, { insuredBy });
  };

  return (
    <Stack spacing={0.5}>
      <Typography variant="caption">Insured by</Typography>
      <Select
        variant="outlined"
        size="small"
        displayEmpty
        value={props.company.insuredBy}
        onChange={(event) => setInsuredBy(event.target.value)}
      >
        <MenuItem value={undefined}>{"No insurance"}</MenuItem>

        {COMPANY_INSURED_BY.map((insurance: any) => (
          <MenuItem value={insurance.value} key={insurance.label}>
            {insurance.label}
          </MenuItem>
        ))}
      </Select>
    </Stack>
  );
};

export default CompanyInsuredByCard;
