import TimedCheckbox from "../common/TimedCheckbox";
import { Table, TableCell, TableHead, TableRow } from "@mui/material";

const VettingRow = ({
  company,
  companyRef,
  title,
  dataKey,
}: {
  company: any;
  companyRef: any;
  title: any;
  dataKey: any;
}) => (
  <TableRow>
    <TableCell>{title}</TableCell>
    <TableCell align="right">
      <TimedCheckbox documentRef={companyRef} dataKey={dataKey} />
    </TableCell>
  </TableRow>
);

export const CompanyVetting = ({
  company,
  companyRef,
}: {
  company: any;
  companyRef: any;
}) => {
  return (
    <Table size="small">
      <TableHead>
        <TableRow>
          <TableCell>Title</TableCell>
          <TableCell align="right">Checked</TableCell>
        </TableRow>
      </TableHead>
      <VettingRow
        company={company}
        companyRef={companyRef}
        title="References"
        dataKey="vetting.referencesChecked"
      />
      <VettingRow
        company={company}
        companyRef={companyRef}
        title="Insurance"
        dataKey="vetting.insuranceChecked"
      />
      <VettingRow
        company={company}
        companyRef={companyRef}
        title="Credit"
        dataKey="vetting.creditChecked"
      />
      <VettingRow
        company={company}
        companyRef={companyRef}
        title="F-Tax"
        dataKey="vetting.ftaxChecked"
      />
      <VettingRow
        company={company}
        companyRef={companyRef}
        title="Vat"
        dataKey="vetting.vatChecked"
      />
    </Table>
  );
};

export default CompanyVetting;
