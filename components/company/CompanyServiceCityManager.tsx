import CompanyServiceCityItem from "./CompanyServiceCityItem";
import Loading from "../common/Loading";
import { Company } from "../../models/company/company";
import { FC } from "react";
import { Table, TableCell, TableHead, TableRow } from "@mui/material";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";

interface CompanyServiceCityManagerProps {
  company: Company;
}

export const CompanyServiceCityManager: FC<CompanyServiceCityManagerProps> = ({
  company,
}) => {
  const { config, loading } = useAdminConfig();

  if (loading) return <Loading />;

  return (
    <Table size="small">
      <TableHead>
        <TableRow>
          <TableCell>Title</TableCell>
          <TableCell align="right">Checked</TableCell>
        </TableRow>
      </TableHead>
      {config?.areas?.map((city: any) => (
        <CompanyServiceCityItem key={"city"} company={company} city={city} />
      ))}
    </Table>
  );
};

export default CompanyServiceCityManager;
