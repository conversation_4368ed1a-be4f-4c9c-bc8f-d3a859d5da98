import { collection, query, where } from "@firebase/firestore";
import { Rating, Typography, Skeleton, Grid2 } from "@mui/material";
import { useCollection } from "react-firebase-hooks/firestore";

/**
 * Displays the average rating and number of reviews for a company.
 */
export default function CompanyRatings({ companyRef }: { companyRef: any }) {
  const [value, loading] = useCollection(
    query(
      collection(companyRef, "companyReviews"),
      where("status", "==", "complete"),
    ),
  );

  if (loading)
    return (
      <Grid2 container>
        <Rating readOnly style={{ marginRight: 8 }} value={0} />
        <Skeleton variant="text" sx={{ fontSize: "1.2rem" }} width={140} />
      </Grid2>
    );

  const reviews = value?.docs ?? [];

  if (reviews.length === 0)
    return (
      <Grid2 container>
        <Rating readOnly style={{ marginRight: 8 }} value={0} />
        <Typography>Not rated yet</Typography>
      </Grid2>
    );

  const totalRating = reviews.reduce(
    (acc, review) => acc + (review.data().rating ?? 0),
    0,
  );
  const averageRating = (totalRating / reviews.length) * 5;

  return (
    <Grid2 container>
      <Rating
        readOnly
        style={{ marginRight: 8 }}
        value={averageRating}
        precision={0.5}
      />
      <Typography>
        <b>{averageRating.toFixed(2)}</b> stars based on <b>{reviews.length}</b>{" "}
        reviews
      </Typography>
    </Grid2>
  );
}
