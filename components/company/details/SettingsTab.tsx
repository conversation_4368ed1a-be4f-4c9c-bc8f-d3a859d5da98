import { DocumentReference } from "@firebase/firestore";
import { Company } from "../../../models/company/company";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CompanyUsers from "../CompanyUsers";
import TextAreaEditor from "../../common/TextAreaEditor";
import CompanySocialLinksEditor from "../CompanySocialLinksEditor";
import CompanyHomeLocation from "../CompanyHomeLocation";
import CompanyFixedPriceJobs from "../CompanyFixedPriceJobs";
import { CompanyQuoteDefaultSettings } from "../CompanyQuoteDefaultSettings";
import { Visible } from "../../common/Conditional";
import Internal from "../../auth/Internal";

interface SettingsTabProps {
  company: Company;
  companyRef: DocumentReference;
  showEditControls: boolean;
}

export default function SettingsTab({
  company,
  companyRef,
  showEditControls,
}: SettingsTabProps) {
  return (
    <>
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Users</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <CompanyUsers company={company} showEditControls={showEditControls} />
        </AccordionDetails>
      </Accordion>

      <Visible if={showEditControls}>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Profile</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>Profile text</Typography>
            <div style={{ height: 12 }} />
            <TextAreaEditor docRef={companyRef} dataKey="profileText" />
            <br />
            <CompanySocialLinksEditor company={company} />
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Home Location</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyHomeLocation company={company} />
          </AccordionDetails>
        </Accordion>
      </Visible>

      <Internal>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Fixed price jobs</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyFixedPriceJobs company={company} />
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Default quote settings</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyQuoteDefaultSettings companyRef={companyRef} />
          </AccordionDetails>
        </Accordion>
      </Internal>
    </>
  );
}
