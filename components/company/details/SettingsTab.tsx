import { DocumentReference } from "@firebase/firestore";
import { Company } from "../../../models/company/company";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Stack,
  Card,
  Divider,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CompanyUsers from "../CompanyUsers";
import TextAreaEditor from "../../common/TextAreaEditor";
import CompanySocialLinksEditor from "../CompanySocialLinksEditor";
import CompanyHomeLocation from "../CompanyHomeLocation";
import CompanyFixedPriceJobs from "../CompanyFixedPriceJobs";
import { CompanyQuoteDefaultSettings } from "../CompanyQuoteDefaultSettings";
import CompanyTags from "../CompanyTags";
import CompanyServices from "../CompanyServices";
import InfoItemRow from "../../common/InfoItemRow";
import { formatTime } from "../../../lib/format-time-relative";
import { Visible } from "../../common/Conditional";
import Internal from "../../auth/Internal";

interface SettingsTabProps {
  company: Company;
  companyRef: DocumentReference;
  showEditControls: boolean;
}

export default function SettingsTab({
  company,
  companyRef,
  showEditControls,
}: SettingsTabProps) {
  return (
    <>
      <Card variant="outlined" style={{ padding: 12 }}>
        <Stack spacing={1} divider={<Divider flexItem />}>
          <InfoItemRow title="Legal name" value={company.legalName} />

          <InfoItemRow title="Organisation number" value={company.orgNo} />
          <Internal>
            <InfoItemRow
              title="Bankgiro"
              value={company?.billingSettings?.paymentInfo?.bankgiro}
            />
            <InfoItemRow
              title="Bank account"
              value={company?.billingSettings?.paymentInfo?.bankAccount}
            />
          </Internal>

          <Visible if={showEditControls}>
            <InfoItemRow
              title="Last offer reply time"
              noCopy
              value={
                formatTime(company.statistics?.lastOfferReplyTime?.toDate()) ??
                "Not reply yet"
              }
            />

            <InfoItemRow
              title="Completed jobs"
              noCopy
              value={(
                company.statistics?.numberOfCompletedJobs ?? 0
              ).toString()}
            />
          </Visible>
        </Stack>
      </Card>

      <Internal>
        <Stack spacing={0.5}>
          <Typography variant="caption">Tags</Typography>
          <CompanyTags company={company} />
        </Stack>

        <Stack spacing={0.5}>
          <Typography variant="caption">Services</Typography>
          <CompanyServices company={company} />
        </Stack>
      </Internal>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Users</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <CompanyUsers company={company} showEditControls={showEditControls} />
        </AccordionDetails>
      </Accordion>

      <Visible if={showEditControls}>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Profile</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>Profile text</Typography>
            <div style={{ height: 12 }} />
            <TextAreaEditor docRef={companyRef} dataKey="profileText" />
            <br />
            <CompanySocialLinksEditor company={company} />
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Home Location</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyHomeLocation company={company} />
          </AccordionDetails>
        </Accordion>
      </Visible>

      <Internal>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Fixed price jobs</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyFixedPriceJobs company={company} />
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Default quote settings</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyQuoteDefaultSettings companyRef={companyRef} />
          </AccordionDetails>
        </Accordion>
      </Internal>
    </>
  );
}
