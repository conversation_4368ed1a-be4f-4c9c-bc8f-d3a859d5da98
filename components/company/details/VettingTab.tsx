import { DocumentReference } from "@firebase/firestore";
import { Company } from "../../../models/company/company";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Stack,
  Card,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CompanyVetting from "../CompanyVetting";
import CompanyStatusCard from "../CompanyStatusCard";
import CompanyInsuredByCard from "../CompanyInsuredByCard";
import CompanyTags from "../CompanyTags";
import CompanyServices from "../CompanyServices";
import CompanyRatings from "../CompanyRatings";
import ReviewWidgetTools from "../CompanyReviewWidgetTools";
import CompanyCertificates from "../CompanyCertificates";
import CompanyDocuments from "../CompanyDocuments";
import Internal from "../../auth/Internal";
import { Visible } from "../../common/Conditional";

interface VettingTabProps {
  company: Company;
  companyRef: DocumentReference;
  showEditControls: boolean;
}

export default function VettingTab({
  company,
  companyRef,
  showEditControls,
}: VettingTabProps) {
  return (
    <>
      <Visible if={showEditControls}>
        <Card variant="outlined" style={{ padding: 12 }}>
          <Stack spacing={2}>
            <CompanyRatings companyRef={companyRef} />

            <Internal>
              {company.statistics?.numberOfReviews && (
                <ReviewWidgetTools companyId={company.reference.id} />
              )}
            </Internal>
          </Stack>
        </Card>

        <CompanyStatusCard company={company} />
        <CompanyInsuredByCard company={company} />
      </Visible>

      <Internal>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Vetting</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyVetting company={company} companyRef={companyRef} />
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Certificates</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyCertificates company={company} />
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Documents</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <CompanyDocuments companyId={company.reference.id} />
          </AccordionDetails>
        </Accordion>

        <Stack spacing={0.5}>
          <Typography variant="caption">Tags</Typography>
          <CompanyTags company={company} />
        </Stack>

        <Stack spacing={0.5}>
          <Typography variant="caption">Services</Typography>
          <CompanyServices company={company} />
        </Stack>
      </Internal>
    </>
  );
}
