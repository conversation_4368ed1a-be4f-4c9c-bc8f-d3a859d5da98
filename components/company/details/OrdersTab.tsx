import { DocumentReference } from "@firebase/firestore";
import ComponentContainer from "../../common/ComponentContainer";
import CompanyJobs from "../CompanyJobs";
import Internal from "../../auth/Internal";

interface OrdersTabProps {
  companyRef: DocumentReference;
}

export default function OrdersTab({ companyRef }: OrdersTabProps) {
  return (
    <Internal>
      <ComponentContainer title="Company jobs">
        <CompanyJobs companyRef={companyRef} />
      </ComponentContainer>
    </Internal>
  );
}
