import { DocumentReference } from "@firebase/firestore";
import ComponentContainer from "../../common/ComponentContainer";
import CompanyJobs from "../CompanyJobs";
import OfferedJobsTable from "../../job/OfferedJobsTable";
import Internal from "../../auth/Internal";

interface OrdersTabProps {
  companyRef: DocumentReference;
}

export default function OrdersTab({ companyRef }: OrdersTabProps) {
  return (
    <Internal>
      <ComponentContainer title="Company jobs">
        <CompanyJobs companyRef={companyRef} />
      </ComponentContainer>
      <ComponentContainer title="Offered jobs">
        <OfferedJobsTable companyReference={companyRef} />
      </ComponentContainer>
    </Internal>
  );
}
