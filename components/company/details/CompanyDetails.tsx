import Loading from "../../common/Loading";
import WriteCompanyDialog from "../WriteCompanyDialog";
import UploadImageButton from "../../common/UploadImageButton";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { doc, getFirestore } from "@firebase/firestore";
import { formatTime } from "../../../lib/format-time-relative";
import { useCompanyDocument } from "../../../models/company/company";
import {
  Card,
  Typography,
  Stack,
  Alert,
  Divider,
  IconButton,
  AlertTitle,
  Tabs,
  Tab,
  Box,
} from "@mui/material";
import { FIREBASE_URL } from "../../../config/constants";
import PageContainer from "../../layout/PageContainer";
import DialogManager from "../../dialogs/DialogManager";
import ManagedDialog from "../../dialogs/ManagedDialog";
import UploadDocumentDialog from "../dialogs/UploadDocumentDialog";
import ComponentContainer from "../../common/ComponentContainer";
import { DocumentDropArea, CompanyImage, EditCompanyButton } from "../Company";
import InfoItemRow from "../../common/InfoItemRow";
import SubjectInternalCommentsSection from "../../job/internal-comments/SubjectInternalCommentsSection";
import { SdCardAlert } from "@mui/icons-material";
import { startCase } from "lodash";
import FileQueue from "../../upload/FileQueue";
import Internal from "../../auth/Internal";
import { DonePartnerId } from "../../../models/other/partner";
import { useAuthUser } from "../../auth/AuthProvider";
import { Visible } from "../../common/Conditional";
import { CompanyNetworks } from "../CompanyNetworks";
import OrdersTab from "./OrdersTab";
import OffersTab from "./OffersTab";
import SettingsTab from "./SettingsTab";
import VettingTab from "./VettingTab";
import TabPanel, { a11yProps } from "./TabPanel";
import { useState } from "react";

interface CompanyDetailsProps {
  companyId: string;
  hideInternalComments?: boolean;
}

export default function CompanyDetails({
  companyId,
  hideInternalComments = false,
}: CompanyDetailsProps) {
  const { userDoc, partnerDoc } = useAuthUser();
  const companyRef = doc(getFirestore(), `companies/${companyId}`);
  const { company, loading } = useCompanyDocument(companyRef.id);
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading) return <Loading />;
  if (!company) return null;

  const isSuperUser = userDoc?.isSuperUser ?? false;
  const isDoneInstaller = company.memberOf.includes(DonePartnerId);
  const showEditControls = !isDoneInstaller || isSuperUser;

  return (
    <FileQueue>
      <DialogManager>
        <DocumentDropArea company={company}>
          <PageContainer tight>
            <Visible if={showEditControls}>
              <ManagedDialog
                id={"upload-document"}
                component={UploadDocumentDialog}
              />

              <ManagedDialog
                id="edit-company"
                company={company}
                component={WriteCompanyDialog}
              />
            </Visible>
            <Card variant="outlined" square sx={{ padding: 2, margin: 0.5 }}>
              <Stack spacing={2}>
                <Stack alignItems="center" direction="row" spacing={1}>
                  <CompanyImage imageRef={company.logoRef} />
                  <Typography variant="h5">{company.name}</Typography>
                  <CompanyNetworks
                    company={company}
                    visibleNetworks={partnerDoc?.networks}
                  />
                </Stack>

                <Visible if={showEditControls}>
                  <Stack alignItems="center" direction="row" spacing={1}>
                    <EditCompanyButton />

                    <UploadImageButton
                      documentRef={companyRef}
                      dataKey="logoRef"
                      fileKey={`company-profile-images-v1/${companyId}/original.png`}
                    />
                    <Internal>
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href={`${FIREBASE_URL}/firestore/data/~2Fcompanies~2F${companyId}`}
                      >
                        <IconButton color="warning" size="small">
                          <WhatshotIcon />
                        </IconButton>
                      </a>
                    </Internal>
                  </Stack>
                </Visible>
              </Stack>
            </Card>

            <ComponentContainer title="">
              <Stack spacing={2}>
                <Internal>
                  {company.checkStatus === "failed" && (
                    <Alert severity="error">
                      <Stack spacing={1}>
                        <AlertTitle>Company checks failed</AlertTitle>

                        {company.checks &&
                          Object.entries(company.checks)
                            .filter(([_, value]) => value === false)
                            .map(([key, _]) => {
                              return (
                                <Stack direction="row" spacing={1} key={key}>
                                  <SdCardAlert color="error" />{" "}
                                  <Typography color="error.main">
                                    {startCase(key)} is missing!
                                  </Typography>
                                </Stack>
                              );
                            })}
                      </Stack>
                    </Alert>
                  )}
                </Internal>

                <Card variant="outlined" style={{ padding: 12 }}>
                  <Stack spacing={1} divider={<Divider flexItem />}>
                    <InfoItemRow title="Legal name" value={company.legalName} />

                    <InfoItemRow
                      title="Organisation number"
                      value={company.orgNo}
                    />
                    <Internal>
                      <InfoItemRow
                        title="Bankgiro"
                        value={company?.billingSettings?.paymentInfo?.bankgiro}
                      />
                      <InfoItemRow
                        title="Bank account"
                        value={
                          company?.billingSettings?.paymentInfo?.bankAccount
                        }
                      />
                    </Internal>

                    <Visible if={showEditControls}>
                      <InfoItemRow
                        title="Last offer reply time"
                        noCopy
                        value={
                          formatTime(
                            company.statistics?.lastOfferReplyTime?.toDate(),
                          ) ?? "Not reply yet"
                        }
                      />

                      <InfoItemRow
                        title="Completed jobs"
                        noCopy
                        value={(
                          company.statistics?.numberOfCompletedJobs ?? 0
                        ).toString()}
                      />
                    </Visible>
                  </Stack>
                </Card>
              </Stack>
            </ComponentContainer>

            <ComponentContainer>
              <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <Tabs
                  value={tabValue}
                  onChange={handleTabChange}
                  aria-label="company details tabs"
                >
                  <Tab label="Orders" {...a11yProps(0)} />
                  <Tab label="Offers" {...a11yProps(1)} />
                  <Tab label="Settings" {...a11yProps(2)} />
                  <Tab label="Vetting" {...a11yProps(3)} />
                </Tabs>
              </Box>

              <TabPanel value={tabValue} index={0}>
                <OrdersTab companyRef={companyRef} />
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <OffersTab company={company} companyRef={companyRef} />
              </TabPanel>

              <TabPanel value={tabValue} index={2}>
                <SettingsTab
                  company={company}
                  companyRef={companyRef}
                  showEditControls={showEditControls}
                />
              </TabPanel>

              <TabPanel value={tabValue} index={3}>
                <VettingTab
                  company={company}
                  companyRef={companyRef}
                  showEditControls={showEditControls}
                />
              </TabPanel>
            </ComponentContainer>

            <ComponentContainer
              title="Company internal comments"
              hidden={hideInternalComments}
            >
              <SubjectInternalCommentsSection subject={company.reference} />
            </ComponentContainer>
          </PageContainer>
        </DocumentDropArea>
      </DialogManager>
    </FileQueue>
  );
}
