import { DocumentReference } from "@firebase/firestore";
import ComponentContainer from "../../common/ComponentContainer";
import OfferedJobsTable from "../../job/OfferedJobsTable";
import Internal from "../../auth/Internal";

interface OffersTabProps {
  companyRef: DocumentReference;
}

export default function OffersTab({ companyRef }: OffersTabProps) {
  return (
    <Internal>
      <ComponentContainer title="Offered jobs">
        <OfferedJobsTable companyReference={companyRef} />
      </ComponentContainer>
    </Internal>
  );
}
