import { DocumentReference } from "@firebase/firestore";
import { Company } from "../../../models/company/company";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CompanyFixedPriceJobs from "../CompanyFixedPriceJobs";
import { CompanyQuoteDefaultSettings } from "../CompanyQuoteDefaultSettings";
import Internal from "../../auth/Internal";

interface OffersTabProps {
  company: Company;
  companyRef: DocumentReference;
}

export default function OffersTab({ company, companyRef }: OffersTabProps) {
  return (
    <Internal>
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Fixed price jobs</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <CompanyFixedPriceJobs company={company} />
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Default quote settings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <CompanyQuoteDefaultSettings companyRef={companyRef} />
        </AccordionDetails>
      </Accordion>
    </Internal>
  );
}
