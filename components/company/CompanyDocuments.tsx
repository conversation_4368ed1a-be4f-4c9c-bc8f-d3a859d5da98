import { Tooltip } from "@mui/material";
import {
  DataGrid,
  GridActionsCellItem,
  GridActionsColDef,
} from "@mui/x-data-grid";
import OpenFileIcon from "@mui/icons-material/Launch";
import { query, collection, getFirestore } from "firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";
import { formatDate } from "../../lib/format-time-relative";
import {
  getEditable,
  useDocumentUpdate,
} from "../../lib/hooks/use-document-update";
import { COMPANY_DOCUMENT_TYPES } from "../../models/company/company-document";
import { getFileRef } from "../../lib/upload-file";
import { getDownloadURL } from "firebase/storage";
import { ExtendedGridColDef } from "../common/table/columns";

const columns: ExtendedGridColDef[] = [
  {
    field: "name",
    headerName: "Name",
    width: 200,
    editable: true,
  },
  {
    field: "type",
    headerName: "Type",
    type: "singleSelect",
    editable: true,
    valueOptions: COMPANY_DOCUMENT_TYPES,
  },
  {
    field: "active",
    headerName: "Active",
    type: "boolean",
    editable: true,
  },
  {
    field: "expiresAt",
    headerName: "Expires",
    type: "date",
    editable: true,
    width: 150,
    valueFormatter: (value) => formatDate(value),
  },
  {
    field: "id",
    headerName: "Actions",
    type: "actions",
    getActions: ({ row }) => [
      <OpenDocumentButton key="open" fileRef={row.fileRef} />,
    ],
  } as GridActionsColDef,
];

const editableProps = getEditable(columns);

function OpenDocumentButton({ fileRef }: { fileRef: string }) {
  return (
    <GridActionsCellItem
      disabled={!fileRef}
      icon={
        <Tooltip title="Open document" arrow>
          <OpenFileIcon />
        </Tooltip>
      }
      onClick={() => {
        getDownloadURL(getFileRef(fileRef)).then((url) => {
          window.open(url, "_blank");
        });
      }}
      label="Open document"
    />
  );
}

interface CompanyDocumentsProps {
  companyId: string;
}

export default function CompanyDocuments({ companyId }: CompanyDocumentsProps) {
  const collectionRef = collection(
    getFirestore(),
    "companies",
    companyId,
    "documents",
  );
  const documentsQuery = query(collectionRef);

  const [documents, loading] = useCollection(documentsQuery);
  const docs = documents?.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

  const [commitUpdate, updating] = useDocumentUpdate(
    collectionRef,
    editableProps,
  );

  return (
    <DataGrid
      rows={docs ?? []}
      columns={columns}
      slotProps={{
        loadingOverlay: {
          variant: "linear-progress",
        },
      }}
      loading={loading || updating}
      disableRowSelectionOnClick
      style={{ height: 400 }}
      editMode={"row"}
      processRowUpdate={(row) => {
        commitUpdate({ ...row });
        return row;
      }}
    />
  );
}
