import ImageWithReference from "../common/ImageWithReference";
import Link from "next/link";
import QuickSearchToolbar from "../tables/QuickSearchToolbar";
import TableLoadingOverlay from "../tables/TableLoadingOverlay";
import { Box, Button, Tooltip, Typography } from "@mui/material";
import { DataGrid, GridCellParams } from "@mui/x-data-grid";
import { escapeRegExp } from "../../lib/escape-regex-values";
import { formatDateToExport } from "../../lib/format-date-to-export";
import { useEffect, useState } from "react";
import { ExtendedGridColDef } from "../common/table/columns";

const ReviewsTable = ({
  isLoading,
  initRows,
}: {
  isLoading: boolean;
  initRows: any;
}) => {
  const [searchText, setSearchText] = useState("");
  const [rows, setRows] = useState(initRows);

  useEffect(() => {
    setRows(initRows);
  }, [initRows]);

  const columns: ExtendedGridColDef[] = [
    {
      field: "createTime",
      type: "dateTime",
      headerName: "Create time",
      width: 170,
    },
    {
      field: "jobTags",
      headerName: "Job tags",
      width: 130,
    },
    {
      field: "jobDescription",
      headerName: "Description",
      width: 250,
      renderCell: (params: GridCellParams) => {
        const input = params.row.data.jobDescription ?? "No description";
        const text = input.length > 32 ? `${input.substring(0, 32)}...` : input;

        return (
          <Link passHref href={`/jobs/job/?id=${params.row.id}`}>
            <Button color="primary" size="small">
              {text}
            </Button>
          </Link>
        );
      },
    },
    {
      field: "companyName",
      headerName: "Company",
      width: 200,
      renderCell: (params: GridCellParams) => {
        const input = params.row.companyName ?? "No company";
        const text = input.length > 20 ? `${input.substring(0, 20)}...` : input;

        return (
          <Link
            passHref
            href={`/companies/company?id=${params.row.data.company.reference.id}`}
          >
            <Button color="primary" size="small">
              {text}
            </Button>
          </Link>
        );
      },
    },
    {
      field: "reviewerName",
      headerName: "Reviewer",
      width: 200,
      renderCell: (params: GridCellParams) => {
        return (
          <Link
            passHref
            href={`/users/user/?id=${params.row.data.author.reference.id}`}
          >
            <Button color="primary" size="small">
              {params.row.data.author.name}
            </Button>
          </Link>
        );
      },
    },
    { field: "rating", headerName: "Rating", width: 60 },
    {
      field: "publicFeedback",
      headerName: "Public feedback",
      width: 200,
      renderCell: (params: GridCellParams) => {
        const input = params.row.publicFeedback ?? "-";
        const text = input.length > 20 ? `${input.substring(0, 20)}...` : input;

        return (
          <Tooltip title={input}>
            <Typography variant="body2"> {text} </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: "privateFeedback",
      headerName: "Private feedback",
      width: 200,
      renderCell: (params: GridCellParams) => {
        const input = params.row.privateFeedback ?? "-";
        const text = input.length > 20 ? `${input.substring(0, 20)}...` : input;

        return (
          <Tooltip title={input}>
            <Typography variant="body2"> {text} </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: "images",
      headerName: "Images",
      width: 300,
      renderCell: (params: GridCellParams) => {
        return params.row.data.images?.map((image: any) => (
          <Box key="any" marginRight={2}>
            <ImageWithReference imageRef={image} />
          </Box>
        ));
      },
    },
    { field: "complaints", headerName: "Complaints", width: 200 },
    { field: "compliments", headerName: "Compliments", width: 200 },
    { field: "reviewerRecommends", headerName: "Recommended", width: 200 },
  ];

  const requestSearch = (searchValue: string) => {
    setSearchText(searchValue);
    const searchRegex = new RegExp(escapeRegExp(searchValue), "i");
    const filteredRows = initRows.filter((row: any) => {
      return Object.keys(row).some((field: any) => {
        return searchRegex.test(row[field]?.toString() ?? "");
      });
    });
    setRows(filteredRows);
  };

  return (
    <Box sx={{ height: "calc(100vh - 60px)", width: "100%" }}>
      <QuickSearchToolbar
        exportFileName="reviews"
        rowsToExport={rows.map((e: any) => {
          return { ...e, createTime: formatDateToExport(e.createTime) };
        })}
        value={searchText}
        onChange={(event: any) => requestSearch(event.target.value)}
        clearSearch={() => requestSearch("")}
      />
      <DataGrid
        disableRowSelectionOnClick
        rows={rows}
        loading={isLoading}
        columns={columns}
        slots={{
          loadingOverlay: TableLoadingOverlay,
        }}
      />
    </Box>
  );
};

export default ReviewsTable;
