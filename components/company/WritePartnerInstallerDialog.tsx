import {
  collection,
  doc,
  getFirestore,
  serverTimestamp,
  setDoc,
} from "@firebase/firestore";

import { Company, CompanyInput } from "../../models/company/company";
import removeUndefined from "../../lib/remove-undefined";
import FormDialog from "../dialogs/FormDialog";
import { DialogProps } from "../dialogs/default-dialog-props";
import { updateDoc } from "firebase/firestore";
import TextField from "../form-fields/TextField";
import { useAuthUser } from "../auth/AuthProvider";

interface WriteCompanyDialogProps extends DialogProps {
  company?: Company;
}

export default function WriteCompanyDialog({
  isOpen,
  close,
  company,
}: WriteCompanyDialogProps) {
  const authUser = useAuthUser();

  return (
    <FormDialog
      title={company ? `Edit ${company.name}` : "New installer"}
      isOpen={isOpen}
      initialValues={{ name: "", orgNo: "", ...(company ?? {}) }}
      close={close}
      onSubmit={async (newCompany) => {
        if (!authUser?.partner) {
          return;
        }
        if (company) {
          const companyInput = {
            ...newCompany,
          };

          await updateDoc(company.reference, removeUndefined(companyInput));
        } else {
          const companyInput: CompanyInput = {
            ...newCompany,
            name: newCompany.name.trim(),
            legalName: newCompany.name.trim(),
            checkStatus: "failed",
            address: {},
            createTime: serverTimestamp(),
            status: "active",
            usersV2: {},
            users: [],
            statistics: {},
            services: authUser.partnerDoc?.apiSettings?.defaultServices ?? [
              "electrician",
            ],
            tags: ["EV installer"],
            memberOf: [authUser.partner],
          };

          await setDoc(
            doc(collection(getFirestore(), "companies")),
            removeUndefined(companyInput),
          );
        }
      }}
    >
      <TextField required name="name" type="text" label="Installer name" />
      <TextField name="legalName" type="text" label="Legal name" />

      <TextField
        name="billingSettings.paymentInfo.bankgiro"
        type="text"
        label="Bankgiro"
      />
    </FormDialog>
  );
}
