import { Company } from "../../models/company/company";
import { useAuthUser } from "../auth/AuthProvider";
import { DialogProps } from "../dialogs/default-dialog-props";
import WriteInternalCompanyDialog from "./WriteInternalCompanyDialog";
import WritePartnerInstallerDialog from "./WritePartnerInstallerDialog";

interface WriteCompanyDialogProps extends DialogProps {
  company?: Company;
}

export default function WriteCompanyDialog(props: WriteCompanyDialogProps) {
  const authUser = useAuthUser();

  if (authUser?.isSuperAdmin) {
    return <WriteInternalCompanyDialog {...props} />;
  }

  return <WritePartnerInstallerDialog {...props} />;
}
