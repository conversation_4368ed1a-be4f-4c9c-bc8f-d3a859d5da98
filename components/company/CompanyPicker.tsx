import CompanyName from "./CompanyName";
import { <PERSON><PERSON>, Button } from "@mui/material";
import { DocumentReference, doc, getFirestore } from "firebase/firestore";
import { useDialog } from "../dialogs/DialogManager";
import { SearchData } from "../../lib/services/global-search";
import Internal from "../auth/Internal";

const CompanyPicker = ({
  companyReference,
  onChange,
}: {
  companyReference?: DocumentReference;
  onChange: (company?: DocumentReference | undefined) => void;
}) => {
  const db = getFirestore();
  const { open: openGlobalSearchDialog } = useDialog("global-search");

  return (
    <>
      {companyReference && <CompanyName companyRef={companyReference} />}

      {!companyReference && (
        <Alert
          action={
            <Internal>
              <Button
                size="small"
                color="inherit"
                onClick={() =>
                  openGlobalSearchDialog({
                    title: "Select installer",
                    defaultOptions: ["companies"],
                    onItemSelected: async (selectedItem: SearchData) => {
                      onChange(doc(db, "companies", selectedItem.id));
                    },
                  })
                }
              >
                Select installer
              </Button>
            </Internal>
          }
          severity="info"
        >
          No installer assigned
        </Alert>
      )}

      {companyReference && (
        <Button
          style={{ marginBottom: 6, marginTop: 6 }}
          onClick={() => {
            onChange && onChange(undefined);
          }}
          color="error"
          size="small"
        >
          Remove company
        </Button>
      )}
    </>
  );
};

export default CompanyPicker;
