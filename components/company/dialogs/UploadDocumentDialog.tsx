import { addYears } from "date-fns";
import {
  addDoc,
  collection,
  getFirestore,
  serverTimestamp,
  Timestamp,
  updateDoc,
} from "firebase/firestore";
import { Checkboxes, TextField } from "mui-rff";
import { useState } from "react";
import { Field } from "react-final-form";
import { formatDate } from "../../../lib/format-time-relative";
import removeUndefined from "../../../lib/remove-undefined";
import { uploadFile } from "../../../lib/upload-file";
import { Company } from "../../../models/company/company";
import { COMPANY_DOCUMENT_TYPES } from "../../../models/company/company-document";
import FormDialog from "../../dialogs/FormDialog";
import Condition from "../../form-fields/Condition";
import LabeledValueField from "../../form-fields/LabeledValueField";
import { getDate } from "../../../lib/date-or-timestamp";

interface UploadDocumentDialogProps {
  isOpen: boolean;
  close: () => void;
  company?: Company;
  files?: FileList;
}

export default function UploadDocumentDialog({
  isOpen,
  close,
  company,
  files,
}: UploadDocumentDialogProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  return (
    <FormDialog
      title={"New document"}
      initialValues={{
        name: files?.[currentIndex]?.name,
        documentExpires: false,
        expiresAt: formatDate(addYears(new Date(), 1)),
      }}
      isOpen={isOpen}
      close={() => {
        setCurrentIndex(0);
        close();
      }}
      keepOpen
      onSubmit={async (values) => {
        const currentFile = files?.[currentIndex];

        if (!files || !currentFile) {
          throw new Error("No file selected");
        }

        if (!company) {
          throw new Error("No company set");
        }

        const { reference, name } = company;
        const expiry = values.documentExpires
          ? getDate(values.expiresAt)
          : null;

        const ref = await addDoc(
          collection(getFirestore(), "companies", reference.id, "documents"),
          removeUndefined({
            createTime: serverTimestamp(),
            active: true,
            cache: {
              companyName: name,
            },
            ...values,
            expiresAt: expiry ? Timestamp.fromDate(expiry) : null,
          }),
        );

        const result = await uploadFile(
          `company-documents-v1/${reference.id}/${ref.id}`,
          currentFile,
        );

        await updateDoc(ref, {
          fileRef: result.ref.fullPath,
        });

        const nextIndex = currentIndex + 1;

        if (nextIndex === files.length) {
          setCurrentIndex(0);
          close();
          return;
        }

        setCurrentIndex(nextIndex);
      }}
    >
      <Field
        name="name"
        render={({ input }) => (
          <TextField
            {...input}
            required
            fullWidth
            type="text"
            label="Title"
            variant="outlined"
          />
        )}
      />
      <LabeledValueField
        name="type"
        label="Type"
        items={COMPANY_DOCUMENT_TYPES}
      />
      <Checkboxes
        name="documentExpires"
        data={{ label: "Document expires", value: false }}
      />
      <Condition when="documentExpires" is={true}>
        <Field
          name="expiresAt"
          render={({ input }) => (
            <TextField
              {...input}
              fullWidth
              required
              type="date"
              label="Expires"
              variant="outlined"
              slotProps={{
                inputLabel: { shrink: true },
              }}
            />
          )}
        />
      </Condition>
    </FormDialog>
  );
}
