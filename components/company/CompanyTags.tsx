import { Company } from "../../models/company/company";
import { updateDoc } from "@firebase/firestore";
import { useState } from "react";
import Loading from "../common/Loading";
import { useAdminConfig } from "../../hooks/other/adminConfigHook";
import { MenuItem, Select } from "@mui/material";

const CompanyTags = ({ company }: { company: Company }) => {
  const [tags, setTags] = useState(company.tags ?? []);

  const { config, loading } = useAdminConfig();

  if (loading) return <Loading />;

  const handleChange = (selectedOption: any) => {
    setTags(selectedOption.target.value);
    updateDoc(company.reference, {
      tags: selectedOption.target.value,
    });
  };

  return (
    <Select size="small" onChange={handleChange} multiple value={tags}>
      {config?.tags &&
        Object.entries(config?.tags).map(([key, value]) => (
          <MenuItem key={key} value={key}>
            {value.name}
          </MenuItem>
        ))}
    </Select>
  );
};

export default CompanyTags;
