import {
  TableCell,
  ListItemText,
  MenuItem,
  IconButton,
  Select,
  Tooltip,
  TableRow,
  Typography,
} from "@mui/material";
import {
  DocumentReference,
  updateDoc,
  deleteField,
  serverTimestamp,
} from "firebase/firestore";
import { useSnackbar } from "notistack";
import { COMPANY_ROLES } from "../../config/constants";
import UserAvatar from "../user/UserAvatar";
import { AccountRemove as RemoveIcon } from "mdi-material-ui";
import Link from "next/link";
import { useUser } from "../../hooks/user/use-user";
import { FC } from "react";
import { Company, CompanyUserRole } from "../../models/company/company";
import { useAuthUser } from "../auth/AuthProvider";
import parsePhoneNumber from "libphonenumber-js";

interface CompanyUserRowProps {
  userReference: DocumentReference;
  company: Company;
  role: CompanyUserRole;
  showEditControls?: boolean;
}

const CompanyUserRow: FC<CompanyUserRowProps> = ({
  userReference,
  company,
  role,
  showEditControls = true,
}) => {
  const [user, loading] = useUser(userReference.id);
  const { enqueueSnackbar } = useSnackbar();
  const authUser = useAuthUser();

  const onRemove = async () => {
    if (!window.confirm("Are you sure?")) return;
    await updateDoc(company.reference, {
      [`usersV2.${userReference.id}`]: deleteField(),
    });

    enqueueSnackbar("User removed from company", { variant: "success" });
  };

  if (loading) return null;

  const isMe = authUser.userDoc?.reference.id === userReference.id;

  return (
    <TableRow>
      <TableCell style={{ paddingRight: 0 }}>
        <UserAvatar userRef={userReference} />
      </TableCell>
      <TableCell>
        <Link passHref href={user ? `/users/user/?id=${userReference.id}` : ""}>
          <ListItemText
            primary={`${user?.firstName || "N/A"} ${user?.lastName || ""}${
              user?.isSuperUser ? " 💯" : ""
            }${isMe ? " (Me)" : ""}`}
            secondary={
              user?.phoneNumber
                ? parsePhoneNumber(user?.phoneNumber)?.formatInternational()
                : "-"
            }
            classes={{ primary: "user-primary" }}
          />
        </Link>
      </TableCell>
      <TableCell padding="checkbox">
        {showEditControls ? (
          <Select
            size="small"
            onChange={async (event) => {
              await updateDoc(company.reference, {
                lastUpdatedAt: serverTimestamp(),
                [`usersV2.${userReference.id}.role`]: event.target.value,
              });

              // Support old versions
              if (event.target.value === "owner") {
                await updateDoc(company.reference, {
                  admin: userReference,
                });
              } else if (company.admin?.id === userReference.id) {
                await updateDoc(company.reference, {
                  admin: deleteField(),
                });
              }
            }}
            value={role}
          >
            {COMPANY_ROLES.map((e) => (
              <MenuItem value={e.value} key={e.value}>
                {e.label}
              </MenuItem>
            ))}
          </Select>
        ) : (
          <Typography>
            {COMPANY_ROLES.find((r) => r.value === role)?.label || role}
          </Typography>
        )}
      </TableCell>
      {showEditControls && (
        <TableCell padding="checkbox">
          <Tooltip title="Remove user">
            <IconButton onClick={onRemove}>
              <RemoveIcon />
            </IconButton>
          </Tooltip>
        </TableCell>
      )}
    </TableRow>
  );
};

export default CompanyUserRow;
