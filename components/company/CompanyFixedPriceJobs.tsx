import React from "react";
import { Checkbox, Table, TableCell, TableHead, TableRow } from "@mui/material";
import { Company } from "../../models/company/company";
import { FixedPriceJob } from "../../models/job/fixed_price_job";
import { useCollection } from "react-firebase-hooks/firestore";
import {
  arrayUnion,
  updateDoc,
  arrayRemove,
  collection,
  orderBy,
  query,
  getFirestore,
} from "@firebase/firestore";

const CompanyFixedPriceJobRow = ({
  company,
  fixedPriceJob,
}: {
  company: Company;
  fixedPriceJob: FixedPriceJob;
}) => {
  const [checked, setChecked] = React.useState(
    company.activeFixedPriceJobs &&
      company.activeFixedPriceJobs.includes(fixedPriceJob.reference.id),
  );

  const onCheckboxClick = (event: any) => {
    if (event.target.checked) {
      updateDoc(
        company.reference,
        "activeFixedPriceJobs",
        arrayUnion(fixedPriceJob.reference.id),
      );
    } else {
      updateDoc(
        company.reference,
        "activeFixedPriceJobs",
        arrayRemove(fixedPriceJob.reference.id),
      );
    }

    setChecked(event.target.checked);
  };

  return (
    <TableRow>
      <TableCell>
        {fixedPriceJob.title} <br /> {fixedPriceJob.reference.id}
      </TableCell>
      <TableCell align="right">
        <Checkbox checked={checked} onChange={onCheckboxClick} />
      </TableCell>
    </TableRow>
  );
};

export const CompanyFixedPriceJobs = ({ company }: { company: Company }) => {
  const db = getFirestore();
  const [value, loading] = useCollection(
    query(collection(db, "fixed-price-jobs"), orderBy("order")),
  );

  if (loading) return null;
  if (!value) return null;

  const fixedPriceJobsListData = value.docs;

  if (!fixedPriceJobsListData) return null;

  return (
    <Table size="small">
      <TableHead>
        <TableRow>
          <TableCell>Title</TableCell>
          <TableCell align="right">Checked</TableCell>
        </TableRow>
      </TableHead>
      {fixedPriceJobsListData.map((fixedPriceJobSnap) => (
        <CompanyFixedPriceJobRow
          key={"fixedPriceJob"}
          company={company}
          fixedPriceJob={
            {
              ...fixedPriceJobSnap.data(),
              reference: fixedPriceJobSnap.ref,
            } as FixedPriceJob
          }
        />
      ))}
    </Table>
  );
};

export default CompanyFixedPriceJobs;
