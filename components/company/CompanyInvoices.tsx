import { Paper } from "@mui/material";
import {
  getFirestore,
  collection,
  where,
  query,
  DocumentReference,
} from "@firebase/firestore";
import InvoicesTableBase from "../invoice/InvoicesTableBase";

export const CompanyInvoices = ({
  companyRef,
}: {
  companyRef: DocumentReference;
}) => {
  const db = getFirestore();
  const collectionReference = query(
    collection(db, "invoices"),
    where("company.reference", "==", companyRef),
  );

  return (
    <Paper variant="outlined" square>
      <InvoicesTableBase
        initCollection={collectionReference}
        companyTable={true}
      />
    </Paper>
  );
};

export default CompanyInvoices;
