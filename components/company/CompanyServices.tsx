import { Company } from "../../models/company/company";
import { COMPANY_SERVICES } from "../../config/constants";
import { updateDoc } from "@firebase/firestore";
import { MenuItem, Select } from "@mui/material";

const CompanyServices = ({ company }: { company: Company }) => {
  const handleChange = (selectedOptions: any) => {
    updateDoc(company.reference, {
      services: selectedOptions.target.value,
    });
  };

  return (
    <Select
      size="small"
      onChange={handleChange}
      multiple
      value={company.services ?? []}
    >
      {COMPANY_SERVICES.map((item) => (
        <MenuItem key={item.value} value={item.value}>
          {item.label}
        </MenuItem>
      ))}
    </Select>
  );
};

export default CompanyServices;
