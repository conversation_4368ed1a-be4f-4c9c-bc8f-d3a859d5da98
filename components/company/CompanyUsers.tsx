import CompanyUserRow from "./CompanyUserRow";
import GlobalSearch from "../search/GlobalSearchModel";
import { collection, serverTimestamp } from "firebase/firestore";
import { COMPANY_ROLE_ORDERS } from "../../config/constants";
import { Company } from "../../models/company/company";
import {
  CardActions,
  Button,
  Table,
  TableRow,
  TableHead,
  TableCell,
  TableBody,
} from "@mui/material";
import { updateDoc, doc, getFirestore } from "@firebase/firestore";
import { useDialog } from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import CreateUserDialog from "../user/CreateUserDialog";
import { SearchData } from "../../lib/services/global-search";

interface CompanyUsersProps {
  company: Company;
  showEditControls?: boolean;
}

export default function CompanyUsers({
  company,
  showEditControls = true,
}: CompanyUsersProps) {
  const { open: openCreateUserDialog } = useDialog("create-company-user");
  const { open: openGlobalSearchDialog } = useDialog("global-search");

  const db = getFirestore();

  return (
    <>
      <Table size="small">
        <colgroup>
          <col span={1} style={{ width: "45px" }} />
          <col span={1} style={{ width: "auto" }} />
          <col span={1} style={{ width: "45px" }} />
          {showEditControls && <col span={1} style={{ width: "45px" }} />}
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell colSpan={2}></TableCell>
            <TableCell padding="checkbox">Role</TableCell>
            {showEditControls && (
              <TableCell padding="checkbox">Action</TableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {company.usersV2 &&
            Object.values(company.usersV2)
              .sort((a, b) => {
                return (
                  COMPANY_ROLE_ORDERS.indexOf(a.role) -
                  COMPANY_ROLE_ORDERS.indexOf(b.role)
                );
              })
              .map((e) => (
                <CompanyUserRow
                  role={e.role}
                  key={e.reference.path}
                  company={company}
                  userReference={e.reference}
                  showEditControls={showEditControls}
                />
              ))}
        </TableBody>
      </Table>

      {showEditControls && (
        <CardActions>
          <Button
            variant="contained"
            color="primary"
            onClick={() =>
              openGlobalSearchDialog({
                title: "Select user to add company",
                filteredIds: company.usersV2 && Object.keys(company.usersV2),
                defaultOptions: ["users"],
                onItemSelected: async (selectedItem: SearchData) => {
                  const userDoc = doc(collection(db, "users"), selectedItem.id);
                  await updateDoc(company.reference, {
                    [`usersV2.${userDoc.id}`]: {
                      role:
                        Object.values(company.usersV2).filter(
                          (e) => e.role === "owner",
                        ).length === 0
                          ? "owner"
                          : "user",
                      reference: userDoc,
                      lastUpdatedAt: serverTimestamp(),
                    },
                  });
                },
              })
            }
          >
            + Add user
          </Button>
          <Button
            onClick={() => openCreateUserDialog()}
            color="primary"
            variant="contained"
          >
            Create user
          </Button>
        </CardActions>
      )}

      {showEditControls && (
        <>
          <ManagedDialog
            companyRef={company.reference}
            id="create-company-user"
            component={CreateUserDialog}
          />

          <ManagedDialog id="global-search" component={GlobalSearch} />
        </>
      )}
    </>
  );
}
