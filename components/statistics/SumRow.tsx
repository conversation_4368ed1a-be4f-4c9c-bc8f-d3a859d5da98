import { Stack, StackProps, Typography, TypographyProps } from "@mui/material";
import React from "react";

interface SumRowProps
  extends Omit<TypographyProps, "title">,
    Pick<StackProps, "justifyContent"> {
  title: React.ReactNode;
  value: React.ReactNode;
  showZero?: boolean;
}

/**
 * Displays a value and a title in a row.
 */
export default function SumRow({
  title,
  value,
  showZero = false,
  variant = "inherit",
  justifyContent = "space-between",
  ...props
}: SumRowProps) {
  if (value === 0 && !showZero) return null;
  return (
    <Stack direction={"row"} justifyContent={justifyContent} spacing={1}>
      <Typography variant={variant} {...props}>
        {title}
      </Typography>
      <Typography variant={variant} {...props}>
        {value}
      </Typography>
    </Stack>
  );
}
