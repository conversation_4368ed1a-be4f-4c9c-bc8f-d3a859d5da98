import {
  Dialog,
  DialogTitle,
  DialogContent,
  Stack,
  DialogActions,
  Button,
  LinearProgress,
  Alert,
  AlertTitle,
  DialogContentText,
  DialogProps,
  FormControlLabel,
  FormGroup,
  Switch,
  Typography,
} from "@mui/material";
import { ReactNode, useState } from "react";
import Form from "../common/Form";
import { FormApi } from "final-form";

interface FormDialogProps<T>
  extends Omit<DialogProps, "children" | "onSubmit" | "open" | "onClose"> {
  title: string;
  description?: string;
  isOpen: boolean;
  positiveButtonText?: string;
  close: () => void;
  children: ReactNode;
  initialValues?: Partial<T>;

  /**
   * Whether to keep the dialog open after the form is submitted.
   * Defaults to false.
   */
  keepOpen?: boolean;

  /**
   * Whether to allow the user to keep the dialog open after the form is submitted.
   */
  allowKeepOpen?: boolean;

  /**
   * The label for the keep open checkbox.
   * @default "Keep open"
   */
  keepOpenText?: string;

  /**
   * If submitting is enabled or not.
   * @default true
   */
  allowSubmit?: boolean;

  /**
   * Called when the form is submitted.
   * @note Any errors thrown by this function will be displayed to the user.
   * @param values The values of the form.
   */
  onSubmit: (values: T, form: FormApi) => Promise<void>;
}

/**
 * A dialog containg a form.
 */
export default function FormDialog<T extends Record<string, any>>({
  title,
  positiveButtonText = "Save",
  description,
  isOpen,
  close,
  children,
  initialValues,
  keepOpen = false,
  allowKeepOpen = false,
  keepOpenText = "Keep open",
  onSubmit,
  maxWidth = "xs",
  allowSubmit = true,
  ...dialogProps
}: FormDialogProps<T>) {
  const [isWorking, setIsWorking] = useState(false);
  const [error, setError] = useState<String>();
  const [keepOpenState, setKeepOpenState] = useState(keepOpen);

  return (
    <Dialog
      {...dialogProps}
      maxWidth={maxWidth}
      open={isOpen}
      onClose={(_, reason) => {
        // Don't close if the user clicked outside the dialog to prevent data loss.
        if (reason === "backdropClick") return;
        close();
      }}
      fullWidth
      slotProps={{
        transition: {
          onExited: () => {
            setIsWorking(false);
            setError(undefined);
          },
        },
      }}
    >
      <Form
        onSubmit={async (values, form) => {
          setIsWorking(true);
          setError(undefined);
          await onSubmit(values as unknown as T, form)
            .then(() => {
              if (!keepOpenState) close();
              form.reset();
            })
            .catch((error) => {
              console.error(error);
              setError(error.message || error);
            })
            .finally(() => {
              setIsWorking(false);
            });
        }}
        initialValues={initialValues}
      >
        <DialogTitle>{title}</DialogTitle>
        <DialogContent
          style={{
            paddingTop:
              "0.5em" /* FIXME: This should not be needed, something is wrong with our styles */,
          }}
        >
          <Stack spacing={2}>
            {description && (
              <DialogContentText>{description}</DialogContentText>
            )}
            {children}
          </Stack>
        </DialogContent>
        <DialogActions>
          {allowKeepOpen ? (
            <KeepOpenSwitch
              label={keepOpenText}
              isEnabled={keepOpenState}
              onToggle={() => setKeepOpenState(!keepOpenState)}
            />
          ) : null}
          <Button
            onClick={() => {
              close();
            }}
          >
            Cancel
          </Button>
          <Button type={"submit"} disabled={!allowSubmit || isWorking}>
            {positiveButtonText}
          </Button>
        </DialogActions>
        {isWorking ? <LinearProgress /> : null}
        {error ? (
          <Alert severity="error">
            <AlertTitle>Failed to perform your request</AlertTitle>
            {error}
          </Alert>
        ) : null}
      </Form>
    </Dialog>
  );
}

function KeepOpenSwitch({
  label,
  isEnabled,
  onToggle,
}: {
  label: string;
  isEnabled: boolean;
  onToggle: () => void;
}) {
  return (
    <FormGroup>
      <FormControlLabel
        value={isEnabled}
        onChange={() => {
          onToggle();
        }}
        control={<Switch size="small" />}
        label={
          <Typography variant="body2" color="textSecondary">
            {label}
          </Typography>
        }
      />
    </FormGroup>
  );
}
