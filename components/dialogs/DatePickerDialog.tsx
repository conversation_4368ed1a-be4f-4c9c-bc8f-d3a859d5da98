import { DateTimePicker } from "mui-rff";
import FormDialog from "./FormDialog";
import { DialogProps } from "./default-dialog-props";

interface DatePickerDialogProps extends DialogProps {
  onDatePicked?: (date: Date) => void;
  defaultNow?: boolean;
}

export default function DatePickerDialog({
  onDatePicked,
  isOpen,
  close,
  defaultNow = false,
}: DatePickerDialogProps) {
  return (
    <FormDialog
      title="Set date"
      isOpen={isOpen}
      close={close}
      initialValues={{
        date: defaultNow ? new Date() : undefined,
      }}
      onSubmit={async ({ date }) => {
        onDatePicked?.(date);
      }}
    >
      <DateTimePicker name="date" required autoFocus />
    </FormDialog>
  );
}
