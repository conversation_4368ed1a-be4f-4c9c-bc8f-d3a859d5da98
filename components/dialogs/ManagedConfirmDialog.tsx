import { DialogProps } from "./default-dialog-props";
import ConfirmDialog from "./ConfirmDialog";
import ManagedDialog from "./ManagedDialog";

interface ManagedConfirmDialogProps {
  id?: string;
  title?: string;
  description?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

function BasicConfirmDialog({
  title = "Confirm?",
  description = "Are you sure you want to continue?",
  onConfirm,
  onCancel,
  isOpen,
  close,
}: Omit<ManagedConfirmDialogProps & DialogProps, "id">) {
  return (
    <ConfirmDialog
      title={title}
      description={description}
      actions={[
        { title: "Cancel", onClick: onCancel },
        { title: "Confirm", onClick: onConfirm },
      ]}
      onClose={() => {
        close();
      }}
      isOpen={isOpen}
    />
  );
}

/**
 * A managed confirm dialog.
 */
export default function ManagedConfirmDialog({
  id = "confirm-dialog",
  ...props
}: ManagedConfirmDialogProps) {
  return <ManagedDialog id={id} component={BasicConfirmDialog} {...props} />;
}
