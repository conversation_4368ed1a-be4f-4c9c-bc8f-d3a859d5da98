import { ReactElement, createElement } from "react";
import { useDialog } from "./DialogManager";

interface ManagedDialogWithComponentProps<T> {
  /**
   * The dialog to render.
   */
  component: React.ComponentType<T>;
  render?: never;
}

interface ManagedDialogWithRenderProps {
  /**
   * Function that renders a dialog.
   */
  render: (props: ManagedDialogComponentProps) => ReactElement;
  component?: never;
}

type ManagedDialogProps<T> = {
  /**
   * Id of the dialog.
   */
  id: string;
  [key: string]: unknown;
} & (ManagedDialogWithComponentProps<T> | ManagedDialogWithRenderProps);

interface ManagedDialogComponentProps {
  isOpen: boolean;
  close: () => void;
  [key: string]: unknown | undefined;
}

/**
 * Creates a managed dialog.
 * Managed dialogs recieve the `isOpen` and `close` props together with any arguments passed when opened.
 *
 * @note Must be used together with a `<DialogManager>` component.
 * @note As arguments passed via `useDialog` cannot be guaranteed, their corresponding prop must be marked as optional.
 * @example
 * <DialogManager>
 *   <ManagedDialog id="my-dialog" component={MyDialog} />
 * </DialogManager>
 */
export default function ManagedDialog<
  T extends ManagedDialogComponentProps & P,
  P = {},
>({ component, render, id, ...rest }: ManagedDialogProps<T> & P) {
  const { isOpen, close, args } = useDialog(id);
  const props = { isOpen, close, ...args, ...rest } as unknown as T;

  if (render) {
    return createElement(render, props);
  }

  if (component) {
    return createElement(component, props);
  }

  console.error("Either `component` or `render` must be provided.");
  return null;
}
