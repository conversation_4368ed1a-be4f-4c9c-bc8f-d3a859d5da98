import {
  create<PERSON>ontext,
  use<PERSON>ontext,
  useReduce<PERSON>,
  Di<PERSON><PERSON>,
  useMemo,
  use<PERSON>allback,
} from "react";

interface DialogArguments {
  [key: string]: unknown;
}

interface DialogState<T = DialogArguments> {
  /**
   * Whether the dialog is open or not.
   */
  isOpen: boolean;

  /**
   * Arguments to pass to the dialog.
   */
  args?: T;
}

interface Registry {
  [id: string]: DialogState;
}

interface Action {
  id: string;
  type: "open" | "close";
  args?: DialogArguments;
}

interface Context {
  state: Registry;
  dispatch: Dispatch<Action>;
}

const DialogManagerContext = createContext<Context>({
  dispatch: () => {
    throw new Error(
      "DialogManagerContext not initialized. Make sure to wrap dialogs and components that make use of `useDialog` in a <DialogManager>.",
    );
  },
  state: {},
});

interface UseDialog<T> extends DialogState {
  /**
   * Closes the dialog.
   */
  close: () => void;

  /**
   * Opens the dialog.
   */
  open: (args?: T) => void;
}

const defaultState: DialogState = {
  isOpen: false,
};

function dialogReducer(state: Registry, action: Action): Registry {
  switch (action.type) {
    case "open":
      return { ...state, [action.id]: { isOpen: true, args: action.args } };
    case "close":
      return { ...state, [action.id]: { isOpen: false } };
    default:
      return state;
  }
}

/**
 * Hook to interact with a dialog.
 * @note This hook should only be used by components that are children of a `DialogManager`.
 * @param id The id of the dialog to get the state of.
 * @returns Returns the current state and dispatch functions of the dialog.
 *
 * @example
 * const { open } = useDialog<{ foo: string }>('my-dialog');
 * return (
 *  <Button onClick={() => { open({ foo: 'bar' }) )}>Open Dialog</Button>
 * )
 *
 * @example
 * const { isOpen, close, args } = useDialog('my-dialog');
 * return (
 *  <Dialog open={isOpen} onClose={close}>
 *   <DialogTitle>{args.foo}</DialogTitle>
 *  ...
 * )
 */
export function useDialog<T = DialogArguments>(
  id: string,
): UseDialog<Partial<T>> {
  const { state, dispatch } = useContext(DialogManagerContext);
  const dialogState = state[id] ?? defaultState;

  // Memoize the handler functions separately
  const close = useCallback(() => {
    dispatch({ type: "close", id });
  }, [dispatch, id]);

  const open = useCallback(
    (args?: DialogArguments) => {
      dispatch({ type: "open", id, args });
    },
    [dispatch, id],
  );

  // Return a stable object that only changes when necessary
  return useMemo(
    () => ({
      isOpen: dialogState.isOpen,
      args: dialogState.args,
      close,
      open,
    }),
    [dialogState.isOpen, dialogState.args, close, open],
  );
}

/**
 * Manages the state of dialogs within its children.
 * @note To interact with a dialog, use the `useDialog` hook.
 * @note Dialog components can be wrapped using the `<ManagedDialog>` component or use the `useDialog` hook directly.
 */
export default function DialogManager({
  children,
}: {
  children: React.ReactNode;
}) {
  const [state, dispatch] = useReducer(dialogReducer, {});
  return (
    <DialogManagerContext.Provider value={{ state, dispatch }}>
      {children}
    </DialogManagerContext.Provider>
  );
}
