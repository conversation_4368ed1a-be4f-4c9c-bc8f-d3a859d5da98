import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  Stack,
  DialogContentText,
  Button,
  ButtonProps,
  DialogActions,
  Alert,
  AlertTitle,
  LinearProgress,
} from "@mui/material";
import { ReactElement, useState } from "react";

interface ConfirmDialogAction {
  title: string;
  id?: string;
  onClick?: () => void | Promise<void>;
  color?: ButtonProps["color"];
}

interface ConfirmDialogProps {
  title: string;
  description?: string;
  actions?: ConfirmDialogAction[];
  onClose: (action?: string) => void;
  isOpen: boolean;
  children?: ReactElement | ReactElement[];
}

export const ConfirmActions: { [key: string]: ConfirmDialogAction } = {
  ok: { title: "Ok", id: "ok" },
  cancel: { title: "Cancel", id: "cancel" },
} as const;

/**
 * A confirmation dialog.
 */
export default function ConfirmDialog({
  title,
  description,
  actions = [ConfirmActions.ok],
  onClose,
  isOpen,
  children,
}: ConfirmDialogProps) {
  const [isWorking, setIsWorking] = useState(false);
  const [error, setError] = useState<String>();

  return (
    <Dialog
      open={isOpen}
      onClose={(_, reason) => {
        // Don't close if the user clicked outside the dialog when working to prevent data loss.
        if (reason === "backdropClick" && isWorking) return;
        onClose();
      }}
      TransitionProps={{
        onExited: () => {
          setIsWorking(false);
          setError(undefined);
        },
      }}
    >
      <DialogTitle>{title}</DialogTitle>
      <DialogContent
        style={{
          paddingTop:
            "0.5em" /* FIXME: This should not be needed, something is wrong with our styles */,
        }}
      >
        <Stack spacing={2}>
          {description && <DialogContentText>{description}</DialogContentText>}
          {children}
        </Stack>
      </DialogContent>
      <DialogActions>
        {actions.map(({ id, title, onClick, color }) => (
          <Button
            color={color}
            key={id || title}
            onClick={async () => {
              const result = onClick?.();

              if (!result) {
                onClose(id || title);
                return;
              }

              setIsWorking(true);

              await result
                .then(() => {
                  onClose(id || title);
                })
                .catch((error) => {
                  console.error(error);
                  setError(error.message || error);
                })
                .finally(() => {
                  setIsWorking(false);
                });
            }}
          >
            {title}
          </Button>
        ))}
      </DialogActions>
      {isWorking ? <LinearProgress /> : null}
      {error ? (
        <Alert severity="error">
          <AlertTitle>Failed to perform your request</AlertTitle>
          {error}
        </Alert>
      ) : null}
    </Dialog>
  );
}
