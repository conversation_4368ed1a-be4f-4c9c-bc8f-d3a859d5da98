import { Grid2, Grid2Props, Typography } from "@mui/material";
import CakeIcon from "@mui/icons-material/Cake";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { DetailedError } from "../utilities/DetailedError";

interface InternalCommentsListProps
  extends Omit<
    Grid2Props,
    "direction" | "alignItems" | "justifyContent" | "container"
  > {
  title?: string;
  icon?: React.ReactNode;
  error?: boolean | Error;
}

export default function EmptyState({
  title,
  icon,
  error = false,
  ...props
}: InternalCommentsListProps) {
  const { sx = {}, ...gridProps } = props;
  return (
    <Grid2
      container
      direction="column"
      alignItems="center"
      justifyContent="flex-end"
      sx={{ width: "100%", height: "50%", color: "text.disabled", ...sx }}
      {...gridProps}
    >
      <Grid2 size="auto">
        {icon !== undefined ? (
          icon
        ) : (
          <EmptyStateDefaultIcon error={Boolean(error)} />
        )}
      </Grid2>
      <Grid2 size="auto">
        <Typography variant="caption">
          {title ?? (error ? "An error occurred" : "Nothing here")}
        </Typography>
      </Grid2>
      <DetailedError error={error} />
    </Grid2>
  );
}

function EmptyStateDefaultIcon({ error = false }: { error?: boolean }) {
  return error ? (
    <ErrorOutlineIcon fontSize="large" />
  ) : (
    <CakeIcon fontSize="large" />
  );
}
