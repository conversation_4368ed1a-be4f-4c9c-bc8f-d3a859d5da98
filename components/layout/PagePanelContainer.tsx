import React from "react";
import <PERSON>Container from "./PageContainer";
import PageGridContainer from "./PageGridContainer";

interface PagePanelContainerProps {
  children: React.ReactNode;
}

export default function PagePanelContainer({
  children,
}: PagePanelContainerProps) {
  return (
    <PageContainer tight>
      <PageGridContainer panels tight>
        {children}
      </PageGridContainer>
    </PageContainer>
  );
}
