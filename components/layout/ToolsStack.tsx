import { Divider, Stack } from "@mui/material";

interface ToolsStackProps {
  children: React.ReactNode;
  spacing?: number;
}

/**
 * A stack that lays out its children horizontally, with a divider between each.
 */
export default function ToolsStack({ children, spacing = 2 }: ToolsStackProps) {
  return (
    <Stack
      direction="row"
      divider={<Divider orientation="vertical" flexItem />}
      justifyContent="flex-start"
      alignItems="center"
      spacing={spacing}
    >
      {children}
    </Stack>
  );
}
