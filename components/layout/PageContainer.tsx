import { Container } from "@mui/material";

interface PageContainerProps {
  children: React.ReactNode;
  tight?: boolean;
}

/**
 * A container that fills the entire page.
 */
export default function PageContainer({ tight, children }: PageContainerProps) {
  return (
    <Container
      sx={{ color: "text.primary" }}
      maxWidth={false}
      disableGutters={tight}
    >
      {children}
    </Container>
  );
}
