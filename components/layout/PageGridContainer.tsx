import { Grid2 } from "@mui/material";

interface PageGridContainerProps {
  children: React.ReactNode;
  tight?: boolean;
  panels?: boolean;
  padding?: number;
  spacing?: number;
}

/**
 * A container that fills the entire page.
 * Suitable as a child of PageContainer.
 */
export default function PageGridContainer({
  tight,
  panels,
  padding = 1,
  spacing = 1,
  children,
}: PageGridContainerProps) {
  return (
    <Grid2
      color="text.primary"
      container
      direction={panels ? "row" : "column"}
      style={{ minHeight: "100vh" }}
      spacing={tight ? 0 : spacing}
      p={tight ? 0 : padding}
    >
      {children}
    </Grid2>
  );
}
