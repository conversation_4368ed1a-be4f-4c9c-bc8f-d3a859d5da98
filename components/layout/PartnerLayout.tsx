import NavBarSlider from "./side-bar/NavBarSlider";
import PartnersJob from "../job/PartnersJob";
import SideBarSearchButton from "./side-bar/buttons/SideBarSearchButton";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  CircularProgress,
  Divider,
  Drawer,
  Fab,
  IconButton,
  List,
  Stack,
  Theme,
  Tooltip,
  Typography,
  useTheme,
} from "@mui/material";
import { Box } from "@mui/system";
import { createStyles, makeStyles } from "@mui/styles";
import { ReactNode, useCallback, useContext, useEffect, useState } from "react";
import { getAuth, signOut } from "firebase/auth";
import { useAuthUser, useBecomePartner } from "../auth/AuthProvider";
import { ColorModeContext } from "../common/App";
import {
  BarChart,
  DarkMode,
  ElectricCar,
  Engineering,
  LightMode,
  Logout,
  MenuOpen,
  Dns,
  Close,
  ShoppingCart,
  Extension,
  Assignment,
  AssignmentInd,
  AssignmentTurnedIn,
  AssignmentOutlined,
} from "@mui/icons-material";
import Version from "../settings/Version";
import Router from "next/router";
import SearchIcon from "@mui/icons-material/Search";

import SideBarNavButton from "./side-bar/buttons/SideBarNavButton";
import { InboxSideBarNavButtons } from "./side-bar/SideBar";
import DoneLogo from "../common/DoneLogo";
import { useDialog } from "../dialogs/DialogManager";
import { SearchData } from "../../lib/services/global-search";
import ManagedDialog from "../dialogs/ManagedDialog";
import GlobalSearch from "../search/GlobalSearchModel";
import SideBarButton from "./side-bar/buttons/SideBarButton";
import CreateOrderDialog from "../job/dialogs/CreateOrderDialog";
import UpdateBanner from "../updates/UpdateBanner";
import { isDev } from "../../lib/environment";
import MessageBanner from "../common/MessageBanner";
import { PlatformTier } from "../auth/Tiered";
import FeedbackButton from "../feedback/FeedbackButton";
import SupportButton from "../feedback/SupportButton";
import {
  InvoicingBatch,
  latestInvoicingBatchQuery,
} from "../../models/invoicing-batch/invoicing-batch";
import { useCollectionData } from "react-firebase-hooks/firestore";
import { DocumentReference } from "firebase/firestore";

const drawerWidth = 220;

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    title: {
      fontWeight: 1000,
    },
    root: {
      display: "flex",
    },
    drawer: {
      width: drawerWidth,
      flexShrink: 0,
      whiteSpace: "nowrap",
    },
  }),
);

function StopEmulatingButton() {
  const becomePartner = useBecomePartner();

  return (
    <Tooltip title="Stop emulating partner" arrow>
      <IconButton
        color="inherit"
        size="small"
        onClick={() => becomePartner(undefined)}
      >
        <Close />
      </IconButton>
    </Tooltip>
  );
}

interface PartnerLayoutProps {
  children: ReactNode;
}

export default function PartnerLayout({ children }: PartnerLayoutProps) {
  const colorMode = useContext(ColorModeContext);
  const classes = useStyles();
  const auth = useAuthUser();
  const partnerDoc = auth.partnerDoc;

  const theme = useTheme();
  const [searchJobResult, setJobSearchResult] = useState<string | undefined>();
  const { open: openGlobalSearchDialog } = useDialog("global-search");
  const { open: openCreateOrderDialog } = useDialog("create-order");
  const [open, setOpen] = useState(true);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if ((e.ctrlKey && e.key === "k") || (e.metaKey && e.key === "k")) {
        e.preventDefault();
        openGlobalSearchDialog({
          onItemSelected: (selectedItem: SearchData) => {
            setJobSearchResult(selectedItem.id);
          },
        });
      }
    },
    [openGlobalSearchDialog, setJobSearchResult],
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  if (!partnerDoc) return null;

  return (
    <div className={classes.root}>
      {!open && (
        <Fab
          color="primary"
          onClick={() => setOpen(!open)}
          style={{
            zIndex: 9999999,
            position: "fixed",
            left: 10,
            bottom: 10,
          }}
        >
          <MenuOpen />
        </Fab>
      )}
      {!open && (
        <Fab
          size="small"
          color="inherit"
          onClick={() =>
            openGlobalSearchDialog({
              onItemSelected: (selectedItem: SearchData) => {
                setJobSearchResult(selectedItem.id);
              },
              defaultOptions: ["jobs"],
            })
          }
          style={{
            zIndex: 9999999,
            position: "fixed",
            left: 80,
            bottom: 16,
          }}
        >
          <SearchIcon />
        </Fab>
      )}
      <ManagedDialog
        defaultOptions={["jobs"]}
        id="global-search"
        component={GlobalSearch}
      />
      <ManagedDialog id="create-order" component={CreateOrderDialog} />
      {open && (
        <Drawer
          variant="permanent"
          className={classes.drawer}
          classes={{
            paper: classes.drawer,
          }}
        >
          {isDev && (
            <Alert icon={<Dns />} severity="info">
              Connected to <strong>dev</strong>
            </Alert>
          )}

          {auth.isEmulatingPartner && (
            <Alert
              icon={false}
              severity="info"
              action={<StopEmulatingButton />}
            >
              Emulating partner
            </Alert>
          )}

          <UpdateBanner />

          <Box marginTop={2} />

          <Stack spacing={1}>
            <Stack spacing={1} direction="row" alignItems="center">
              <Box
                flexGrow={1}
                onClick={() => {
                  Router.push("/");
                }}
                sx={{
                  height: 40,
                  marginLeft: 2,
                  backgroundImage: `url(${partnerDoc?.logo})`,
                  backgroundSize: "contain",
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "center",
                }}
              />

              <IconButton
                onClick={() => {
                  setOpen(false);
                }}
              >
                <MenuOpen />
              </IconButton>
            </Stack>
            <Stack style={{ marginLeft: 14 }}>
              <Typography fontSize={14} variant="body1">
                Welcome{" "}
                <span style={{ fontWeight: 600 }}>
                  {auth.userDoc?.firstName}
                </span>{" "}
                ☀️
              </Typography>
            </Stack>

            <div
              onClick={() => {
                openGlobalSearchDialog({
                  onItemSelected: (selectedItem: SearchData) => {
                    setJobSearchResult(selectedItem.id);
                  },
                  defaultOptions: ["jobs"],
                });
              }}
            >
              <SideBarSearchButton />
            </div>
            <Stack paddingLeft={1.5} paddingRight={1.5} spacing={1}>
              <SideBarButton
                onClick={function (): void {
                  openCreateOrderDialog();
                }}
                icon={<ElectricCar />}
                title={"Create order"}
              />
            </Stack>
            <Box marginTop={2} />
            <List disablePadding>
              <InboxSideBarNavButtons authUser={auth} />

              <PlatformTier>
                <SideBarNavButton
                  title={"Matching"}
                  pagePath={"/matching/"}
                  icon={<AssignmentInd fontSize="small" />}
                />
              </PlatformTier>
              <PlatformTier>
                <InvoicingButton partnerRef={partnerDoc?.reference} />
              </PlatformTier>
              <Divider />
              <SideBarNavButton
                title={"Open Orders"}
                pagePath={"/orders/open/"}
                icon={<Assignment fontSize="small" />}
              />
              <SideBarNavButton
                title={"All Orders"}
                pagePath={"/orders/all/"}
                icon={<AssignmentOutlined fontSize="small" />}
              />
              <Divider />
              <PlatformTier>
                <SideBarNavButton
                  title={"Installers"}
                  pagePath={"/installers/"}
                  icon={<Engineering fontSize="small" />}
                />
              </PlatformTier>
              <Divider />
              {auth.partnerDoc?.reportUrl && (
                <SideBarNavButton
                  title={"Statistics"}
                  pagePath={"/partnerReport/"}
                  icon={<BarChart fontSize="small" />}
                />
              )}
              <SideBarNavButton
                title={"Price Lists"}
                pagePath={"/price-lists/"}
                icon={<ShoppingCart />}
              />
              <SideBarNavButton
                title={"Integrations"}
                pagePath={"/integrations/"}
                icon={<Extension />}
              />
            </List>
            <Divider />
            <Stack alignItems="center" spacing={2}>
              <SupportButton fullWidth />
              <FeedbackButton fullWidth />
              <Button
                startIcon={<Logout />}
                fullWidth
                color="warning"
                onClick={() => {
                  if (!window.confirm("Log out?")) return;
                  signOut(getAuth());
                }}
              >
                Log out
              </Button>

              <Button
                size="small"
                startIcon={
                  theme.palette.mode === "dark" ? <DarkMode /> : <LightMode />
                }
                onClick={colorMode.toggleColorMode}
                color="inherit"
              >
                {theme.palette.mode}
              </Button>

              <Stack spacing={2} direction="row">
                <DoneLogo />
                <Version />
              </Stack>
            </Stack>
          </Stack>
        </Drawer>
      )}

      {searchJobResult && (
        <NavBarSlider
          title="From search"
          child={<PartnersJob jobId={searchJobResult} />}
          onClose={() => setJobSearchResult(undefined)}
          open={Boolean(searchJobResult)}
        />
      )}

      <main
        style={{
          width: open ? "calc(100vw - 220px)" : "100vw",
          height: "100vh",
        }}
      >
        <MessageBanner message={partnerDoc?.backofficeMessage} />
        {children}
      </main>
    </div>
  );
}

function InvoicingButton({ partnerRef }: { partnerRef: DocumentReference }) {
  const [invoicingBatches = []] = useCollectionData(
    latestInvoicingBatchQuery(partnerRef),
  );

  const [lastInvoicingBatch] = invoicingBatches;

  return (
    <SideBarNavButton
      title={"Invoicing"}
      pagePath={"/invoicing/"}
      icon={<AssignmentTurnedIn fontSize="small" />}
      accessory={<InvoicingProgress batch={lastInvoicingBatch} />}
    />
  );
}

function InvoicingProgress({ batch }: { batch?: InvoicingBatch }) {
  if (!batch || batch.status !== "processing") return null;

  const progress = (batch.progress.completed / batch.progress.total) * 100;

  return (
    <Tooltip title={`Invoicing in progress (${progress.toFixed(0)}%)`} arrow>
      <CircularProgress
        size={20}
        thickness={4}
        variant="determinate"
        value={progress}
      />
    </Tooltip>
  );
}
