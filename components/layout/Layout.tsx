import CreateDialogGroup from "../common/CreateDialogGroup";
import GlobalSearch from "../search/GlobalSearchModel";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import SearchIcon from "@mui/icons-material/Search";
import { Fab } from "@mui/material";
import { createStyles, makeStyles } from "@mui/styles";
import { FC, ReactNode, useEffect, useState, useCallback } from "react";
import { SideBar } from "./side-bar/SideBar";
import { useMediaQuery } from "react-responsive";
import JobUploader from "../job/JobUploader";
import { useDialog } from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";

const useStyles = makeStyles(() =>
  createStyles({
    root: {
      display: "flex",
    },
  }),
);

interface LayoutProps {
  children: ReactNode;
}

export const Layout: FC<LayoutProps> = ({ children }) => {
  const classes = useStyles();

  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 1200px)" });
  const [open, setOpen] = useState(!isTabletOrMobile);
  const { open: openGlobalSearchDialog } = useDialog("global-search");

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if ((e.ctrlKey && e.key === "k") || (e.metaKey && e.key === "k")) {
        e.preventDefault();
        openGlobalSearchDialog();
      }

      if ((e.ctrlKey && e.key === "j") || (e.metaKey && e.key === "j")) {
        e.preventDefault();
        setOpen((prev) => !prev);
      }
    },
    [openGlobalSearchDialog],
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  return (
    <>
      <ManagedDialog id="global-search" component={GlobalSearch} />
      <ManagedDialog id="job-uploader" component={JobUploader} />

      {!open && (
        <Fab
          color="primary"
          onClick={() => setOpen(!open)}
          style={{
            zIndex: 9999999,
            position: "fixed",
            left: 10,
            bottom: 10,
          }}
        >
          <MenuOpenIcon />
        </Fab>
      )}
      {!open && (
        <Fab
          size="small"
          color="inherit"
          onClick={() => openGlobalSearchDialog()}
          style={{
            zIndex: 9999999,
            position: "fixed",
            left: 80,
            bottom: 16,
          }}
        >
          <SearchIcon />
        </Fab>
      )}
      <div className={classes.root}>
        <CreateDialogGroup />

        {open && <SideBar onHideDrawer={() => setOpen(false)} />}

        <main
          style={{
            width: open ? "calc(100vw - 220px)" : "100vw",
            height: "100vh",
          }}
        >
          {children}
        </main>
      </div>
    </>
  );
};

export default Layout;
