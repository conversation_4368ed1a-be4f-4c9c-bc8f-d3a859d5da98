import { FC, ReactNode } from "react";
import { useAuthUser } from "../auth/AuthProvider";
import RedirectPage from "./RedirectPage";

interface PageRestrictedProps {
  children: ReactNode;
}

/** This component blocks partners to see irrelevant pages */
const PageRestricted: FC<PageRestrictedProps> = ({ children }) => {
  const authUser = useAuthUser();

  return <> {authUser.partner ? <RedirectPage /> : children} </>;
};

export default PageRestricted;
