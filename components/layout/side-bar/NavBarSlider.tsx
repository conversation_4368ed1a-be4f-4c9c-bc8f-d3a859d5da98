import { But<PERSON>, Card, Stack } from "@mui/material";
import { createStyles, makeStyles } from "@mui/styles";
import { JSX } from "react";

const useStyles = makeStyles((_) =>
  createStyles({
    sideBar: {
      width: 650,

      height: "100vh",
      overflowY: "scroll",
    },
  }),
);

const NavBarSlider = ({
  child,
  onClose,
  open,
  title,
}: {
  child: JSX.Element;
  open: boolean;
  onClose: any;
  title: string;
}) => {
  const classes = useStyles();

  return open ? (
    <Card square className={classes.sideBar}>
      <Stack direction="row" alignItems="center">
        <Button
          style={{ marginTop: 6, marginLeft: 12, marginRight: 100 }}
          onClick={onClose}
        >
          Close
        </Button>
        <span style={{ fontWeight: 600 }}> {title} </span>
      </Stack>
      {child}
    </Card>
  ) : null;
};

export default NavBarSlider;
