import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import AddBusinessIcon from "@mui/icons-material/AddBusiness";
import AddIcon from "@mui/icons-material/Add";
import DoneLogo from "../../common/DoneLogo";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import GroupIcon from "@mui/icons-material/Group";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import Link from "next/link";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import ReceiptIcon from "@mui/icons-material/Receipt";
import Router from "next/router";
import SideBarButton from "./buttons/SideBarButton";
import SidebarCountButton from "./buttons/SidebarCountButton";
import SidebarInboxButton from "./buttons/SidebarInboxButton";
import SideBarNavButton from "./buttons/SideBarNavButton";
import SideBarSearchButton from "./buttons/SideBarSearchButton";
import StoreIcon from "@mui/icons-material/Store";
import UserAvatar from "../../user/UserAvatar";
import Version from "../../settings/Version";
import { AuthUser } from "../../../models/user/user";
import { ColorModeContext } from "../../common/App";
import { createStyles, makeStyles } from "@mui/styles";
import { FC, useContext, useMemo } from "react";
import { getAuth, signOut } from "firebase/auth";
import { MessageArrowRight } from "mdi-material-ui";
import { Stack } from "@mui/system";
import { Theme, useTheme } from "@mui/material/styles";
import { useAuthUser } from "../../auth/AuthProvider";
import { useDialog } from "../../dialogs/DialogManager";
import { useFirestoreCount } from "../../../lib/hooks/use-firestore-count";
import { usePeriodicRender } from "../../../lib/hooks/periodic-render";
import {
  Drawer,
  IconButton,
  List,
  Box,
  ListItem,
  ListItemText,
  Typography,
  Button,
  Divider,
  Alert,
} from "@mui/material";
import {
  DarkMode,
  Dns,
  LightMode,
  Logout,
  Star,
  RequestQuote,
  AlternateEmail,
  Grading,
  Settings,
  // CalendarMonth,
  Assignment,
  AssignmentOutlined,
} from "@mui/icons-material";
import {
  collection,
  getFirestore,
  orderBy,
  query,
  where,
} from "firebase/firestore";
import { isDev } from "../../../lib/environment";
import SidebarInboxMentionsButton from "./buttons/SidebarInboxMentionsButton";
import UpdateBanner from "../../updates/UpdateBanner";
import FeedbackButton from "../../feedback/FeedbackButton";

const drawerWidth = 220;

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    title: {
      fontWeight: 1000,
    },
    root: {
      display: "flex",
    },
    drawer: {
      width: drawerWidth,
      flexShrink: 0,
      whiteSpace: "nowrap",
    },
  }),
);

type SideBarProps = {
  onHideDrawer: () => void;
};

export const SideBar: FC<SideBarProps> = (props) => {
  const theme = useTheme();
  const classes = useStyles();
  const colorMode = useContext(ColorModeContext);
  const auth = useAuthUser();

  const { open: openCreateJobDialog } = useDialog("create-job");
  const { open: openCreateUserDialog } = useDialog("create-user");
  const { open: openCreateCompanyDialog } = useDialog("create-company");
  const { open: openGlobalSearchDialog } = useDialog("global-search");
  const { open: openJobUploaderDialog } = useDialog("job-uploader");

  return (
    <Drawer
      variant="permanent"
      className={classes.drawer}
      classes={{
        paper: classes.drawer,
      }}
    >
      {isDev && (
        <Alert icon={<Dns />} severity="info" variant="standard">
          Connected to <strong>dev</strong>
        </Alert>
      )}

      <UpdateBanner />

      <Box marginTop={1} />

      <Stack spacing={1}>
        <Stack
          direction="row"
          alignItems="center"
          spacing={1}
          style={{ cursor: "pointer" }}
        >
          <Stack
            onClick={() => {
              Router.push("/");
            }}
            spacing={1}
            direction="row"
            alignItems="center"
          >
            <DoneLogo style={{ marginLeft: 16 }} />

            <Typography variant="h6" style={{ fontWeight: 600 }}>
              Backoffice
            </Typography>
          </Stack>

          <Box flexGrow={1} />

          <IconButton
            onClick={() => {
              props.onHideDrawer();
            }}
          >
            <MenuOpenIcon />
          </IconButton>
        </Stack>

        <Link passHref href={`/users/user/?id=${auth.userDoc?.reference.id}`}>
          <Stack marginLeft={2}>
            <Typography variant="body1">
              Welcome{" "}
              <span style={{ fontWeight: 600 }}>{auth.userDoc?.firstName}</span>{" "}
              ☀️
            </Typography>
          </Stack>
        </Link>

        <Box onClick={() => openGlobalSearchDialog()}>
          <SideBarSearchButton />
        </Box>

        <Stack paddingLeft={1.5} paddingRight={1.5} spacing={1}>
          <SideBarButton
            title="Create user"
            icon={<PersonAddIcon />}
            onClick={() => openCreateUserDialog()}
          />

          <SideBarButton
            title="Create company"
            icon={<AddBusinessIcon />}
            onClick={() => openCreateCompanyDialog()}
          />

          <SideBarButton
            title="Create order"
            icon={<AddIcon />}
            onClick={() => openCreateJobDialog()}
          />

          <SideBarButton
            title="Order uploader"
            icon={<FileUploadIcon />}
            onClick={() => openJobUploaderDialog()}
          />
        </Stack>
      </Stack>

      <Box overflow={"hidden"} flexDirection="column" display={"flex"}>
        <Box flexGrow={1} display={"flex"} overflow={"hidden"}>
          <Box flexBasis={0} flexGrow={1} overflow="auto">
            <List disablePadding>
              <InboxSideBarNavButtons authUser={auth} />

              <OfferSideBarNavButton />

              <SideBarNavButton
                title={"Open orders"}
                pagePath={"/jobs/open/"}
                icon={<Assignment fontSize="small" />}
              />

              <SideBarNavButton
                title={"All orders"}
                pagePath={"/jobs/legacy/"}
                icon={<AssignmentOutlined fontSize="small" />}
              />

              {/* <SideBarNavButton
                pageName={"calendar"}
                title={"Calendar"}
                pagePath={"/calendar/"}
                icon={<CalendarMonth fontSize="small" />}
              /> */}

              <Divider />

              <SideBarNavButton
                title={"Invoicing"}
                pagePath={"/invoicing/"}
                icon={<RequestQuote fontSize="small" />}
              />

              <SideBarNavButton
                title={"Quotes"}
                pagePath={"/quotes/"}
                icon={<ReceiptIcon />}
              />

              <Divider />

              <SideBarNavButton
                title={"Companies"}
                pagePath={"/companies/"}
                icon={<StoreIcon />}
              />

              <CompanyCheckSideBarNavButton />

              <SideBarNavButton
                title={"Price Lists"}
                pagePath={"/price-lists/"}
                icon={<ShoppingCartIcon />}
              />

              <Divider />

              <SideBarNavButton
                title={"Institution requests"}
                pagePath={"/institutionRequests/"}
                icon={<AccountBalanceIcon />}
              />

              <SideBarNavButton
                title="Company reviews"
                pagePath={"/companyReviews/"}
                icon={<Star />}
              />

              <Divider />

              <SideBarNavButton
                title={"Partners manager"}
                pagePath={"/partners/"}
                icon={<ManageAccountsIcon />}
              />

              <Divider />

              <SideBarNavButton
                title={"Settings"}
                pagePath={"/settings/"}
                icon={<Settings />}
              />

              <Divider />

              <Stack marginTop={2} spacing={2}>
                <FeedbackButton fullWidth />
                <Button
                  color="warning"
                  fullWidth
                  startIcon={<Logout />}
                  onClick={() => {
                    if (!window.confirm("Log out?")) return;
                    signOut(getAuth());
                  }}
                >
                  Log out
                </Button>

                <Button
                  fullWidth
                  startIcon={
                    theme.palette.mode === "dark" ? <DarkMode /> : <LightMode />
                  }
                  onClick={colorMode.toggleColorMode}
                  color="inherit"
                >
                  {theme.palette.mode}
                </Button>

                <ListItem>
                  <ListItemText style={{ textAlign: "center" }}>
                    <Version />
                  </ListItemText>
                </ListItem>
              </Stack>
            </List>
          </Box>
        </Box>
      </Box>
    </Drawer>
  );
};

export function InboxSideBarNavButtons({ authUser }: { authUser: AuthUser }) {
  if (!authUser.userDoc?.reference) return null;
  const participant = authUser.partner ? "partner" : "done";

  return (
    <>
      <Divider />
      <SidebarInboxButton
        important
        viewer={authUser.userDoc.reference}
        queryOptions={{
          status: "open",
          participant,
          assignee: authUser.userDoc.reference,
          participantReference: authUser.partnerDoc?.reference,
        }}
        title={"My inbox"}
        pagePath={"/inbox/my-inbox/"}
        icon={<UserAvatar size="small" userRef={authUser.userDoc.reference} />}
      />
      <SidebarInboxMentionsButton
        important
        viewer={authUser.userDoc.reference}
        queryOptions={{
          participant,
          mentioned: authUser.userDoc.reference,
          participantReference: authUser.partnerDoc?.reference,
        }}
        title={"Mentions"}
        pagePath={"/inbox/mentions/"}
        icon={<AlternateEmail fontSize="small" />}
      />
      <SidebarInboxButton
        viewer={authUser.userDoc.reference}
        queryOptions={{
          status: "open",
          assignee: null,
          participant,
          participantReference: authUser.partnerDoc?.reference,
        }}
        title={"Unassigned"}
        pagePath={"/inbox/unassigned/"}
        icon={<PersonOutlineIcon fontSize="small" />}
      />
      <SidebarInboxButton
        viewer={authUser.userDoc.reference}
        queryOptions={{
          status: "open",
          participant,
          participantReference: authUser.partnerDoc?.reference,
        }}
        title={"All tasks"}
        pagePath={"/inbox/all/"}
        icon={<GroupIcon fontSize="small" />}
      />
      <Divider />
    </>
  );
}

function useOffersCount() {
  const tick = usePeriodicRender();

  const offerQuery = useMemo(() => {
    tick;

    return query(
      collection(getFirestore(), "jobOffers"),
      where("jobStatus", "==", "inbox"),
      orderBy("createTime", "desc"),
    );
  }, [tick]);

  const [count = 0] = useFirestoreCount(offerQuery);

  return count;
}

function useOffersNotSendYetCount() {
  const tick = usePeriodicRender();

  const offerQuery = useMemo(() => {
    tick;

    return query(
      collection(getFirestore(), "jobOffers"),
      where("jobStatus", "==", "inbox"),
      where("offeredTo", "==", []),
      orderBy("createTime", "desc"),
    );
  }, [tick]);

  const [count = 0] = useFirestoreCount(offerQuery);

  return count;
}

export function OfferSideBarNavButton() {
  const count = useOffersCount();
  const notOfferedYet = useOffersNotSendYetCount();

  return (
    <SidebarCountButton
      important
      badgeCount={notOfferedYet}
      count={count}
      title={"Matching"}
      pagePath={"/offer/"}
      icon={<MessageArrowRight fontSize="small" />}
    />
  );
}

function useFailedChecksCount() {
  const tick = usePeriodicRender();
  const failedChecksQueryOnCompanies = useMemo(() => {
    tick;
    return query(
      collection(getFirestore(), "companies"),
      where("checkStatus", "==", "failed"),
      where("status", "in", ["active", "new"]),
    );
  }, [tick]);

  const [count = 0] = useFirestoreCount(failedChecksQueryOnCompanies);

  return count;
}

export function CompanyCheckSideBarNavButton() {
  const count = useFailedChecksCount();

  return (
    <SidebarCountButton
      title={"Company checks"}
      pagePath={"/companyChecks/"}
      icon={<Grading fontSize="small" />}
      count={count}
    />
  );
}
