import { useMemo } from "react";
import { DocumentReference } from "firebase/firestore";
import {
  InboxMentionsQueryOptions,
  inboxMentionsQuery,
} from "../../../../models/inbox/provider";
import SidebarCountButton, {
  SidebarCountButtonProps,
} from "./SidebarCountButton";
import { useCollection } from "react-firebase-hooks/firestore";

function useInboxCount(options: InboxMentionsQueryOptions) {
  const query = useMemo(() => inboxMentionsQuery(options), [options]);

  const [result] = useCollection(query);

  return result?.size ?? 0;
}

interface SidebarInboxMentionsButtonProps
  extends Omit<SidebarCountButtonProps, "count" | "badgeCount"> {
  viewer: DocumentReference;
  mentioned?: DocumentReference;
  queryOptions: Omit<InboxMentionsQueryOptions, "status">;
  hideBadge?: boolean;
  hideCount?: boolean;
}

export default function SidebarInboxMentionsButton({
  viewer,
  queryOptions,
  hideBadge,
  ...props
}: SidebarInboxMentionsButtonProps) {
  const unreadCount = useInboxCount({ status: "unread", ...queryOptions });

  return (
    <SidebarCountButton
      {...props}
      count={unreadCount > 0 ? undefined : 0}
      badgeCount={hideBadge ? undefined : unreadCount}
      onlyBadge
    />
  );
}
