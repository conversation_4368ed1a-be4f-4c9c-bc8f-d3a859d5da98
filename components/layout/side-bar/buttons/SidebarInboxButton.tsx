import { useMemo } from "react";
import { DocumentReference } from "firebase/firestore";
import {
  InboxQueryOptions,
  inboxQuery,
} from "../../../../models/inbox/provider";
import SidebarCountButton, {
  SidebarCountButtonProps,
} from "./SidebarCountButton";
import { useCollection } from "react-firebase-hooks/firestore";
import { User } from "../../../../models/user/user";

function useInboxCount(
  options: InboxQueryOptions,
  viewer: DocumentReference<User>,
) {
  const query = useMemo(() => inboxQuery(options), [options]);

  const [result] = useCollection(query);

  const unread = result?.docs.reduce(
    (acc, doc) =>
      doc.data().viewedBy?.find((ref) => ref.path === viewer.path)
        ? acc
        : acc + 1,
    0,
  );

  return [result?.size ?? 0, unread ?? 0] as const;
}

interface SidebarInboxButtonProps
  extends Omit<SidebarCountButtonProps, "count" | "badgeCount"> {
  viewer: DocumentReference;
  mentioned?: DocumentReference;
  queryOptions: InboxQueryOptions;
  hideBadge?: boolean;
  hideCount?: boolean;
}

export default function SidebarInboxButton({
  viewer,
  queryOptions,
  hideBadge,
  hideCount,
  ...props
}: SidebarInboxButtonProps) {
  const [totalCount, unreadCount] = useInboxCount(
    queryOptions,
    viewer as DocumentReference<User>,
  );

  return (
    <SidebarCountButton
      {...props}
      count={hideCount ? undefined : totalCount}
      badgeCount={hideBadge ? undefined : unreadCount}
    />
  );
}
