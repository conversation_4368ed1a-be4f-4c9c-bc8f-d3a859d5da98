import {
  ListItem,
  <PERSON>I<PERSON>Button,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import { usePathname, useRouter } from "next/navigation";

export interface SideBarNavButtonProps {
  title: string;
  pagePath: string;
  icon?: React.ReactNode;
  subTitle?: string;
  accessory?: React.ReactNode;
}

export default function SideBarNavButton({
  title,
  subTitle,
  pagePath,
  icon,
  accessory,
}: SideBarNavButtonProps) {
  const pathname = usePathname();
  const router = useRouter();
  const isSelected = pathname === pagePath;

  return (
    <ListItem disablePadding>
      <ListItemButton
        selected={isSelected}
        onClick={() => router.push(pagePath)}
      >
        {icon && <ListItemIcon style={{ minWidth: 36 }}>{icon}</ListItemIcon>}

        <ListItemText
          primary={<Typography variant="body2">{title}</Typography>}
          secondary={
            <Typography color="text.secondary" variant="caption">
              {subTitle}
            </Typography>
          }
        />
        {accessory}
      </ListItemButton>
    </ListItem>
  );
}
