import { Button } from "@mui/material";
import React from "react";

const SideBarButton = ({
  onClick,
  icon,
  title,
}: {
  onClick: () => void;
  icon: React.ReactNode;
  title: string;
}) => {
  return (
    <Button
      variant="outlined"
      onClick={onClick}
      color="inherit"
      sx={{
        boxShadow: "none",
        justifyContent: "flex-start",
      }}
      size="small"
      startIcon={icon}
    >
      {title}
    </Button>
  );
};

export default SideBarButton;
