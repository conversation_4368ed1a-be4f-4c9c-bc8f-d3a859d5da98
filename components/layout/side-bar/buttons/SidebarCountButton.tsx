import SideBarNavButton, { SideBarNavButtonProps } from "./SideBarNavButton";
import { Badge, BadgeProps, Typography, styled } from "@mui/material";

interface CountBadgeProps extends BadgeProps {
  centered?: boolean;
}

const CountBadge = styled(Badge, {
  // Don't forward the centered prop to the HTML element
  // because it's a bool and HTML only uses string attributes.
  shouldForwardProp: (prop) => prop !== "centered",
})<CountBadgeProps>(({ centered = false }) => ({
  "& .MuiBadge-badge": centered
    ? {
        marginRight: 4,
      }
    : {
        right: "100%",
        top: "50%",
        marginRight: "100%",
      },
}));

export interface SidebarCountButtonProps
  extends Omit<SideBarNavButtonProps, "secondaryAction"> {
  count?: number;
  badgeCount?: number;
  important?: boolean;
  onlyBadge?: boolean;
}

export default function SidebarCountButton({
  count,
  badgeCount,
  important = false,
  onlyBadge = false,
  ...props
}: SidebarCountButtonProps) {
  return (
    <SideBarNavButton
      {...props}
      accessory={
        <CountBadge
          badgeContent={badgeCount}
          color={important ? "error" : "primary"}
          centered={onlyBadge}
        >
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              paddingLeft: "0.2em",
              minWidth: "1em",
              textAlign: "right",
            }}
          >
            {count}
          </Typography>
        </CountBadge>
      }
    />
  );
}
