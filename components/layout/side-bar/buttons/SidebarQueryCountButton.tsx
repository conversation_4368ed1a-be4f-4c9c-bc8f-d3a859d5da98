import { Query } from "firebase/firestore";
import { useFirestoreCount } from "../../../../lib/hooks/use-firestore-count";
import SidebarCountButton, {
  SidebarCountButtonProps,
} from "./SidebarCountButton";

interface SidebarQueryCountButtonProps
  extends Omit<SidebarCountButtonProps, "count"> {
  query: Query;
}

export default function SidebarQueryCountButton({
  query,
  ...props
}: SidebarQueryCountButtonProps) {
  const [count = 0] = useFirestoreCount(query);
  return <SidebarCountButton {...props} count={count} />;
}
