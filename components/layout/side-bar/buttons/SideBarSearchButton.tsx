import { InputAdornment, TextField } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";

const SideBarSearchButton = () => {
  return (
    <TextField
      disabled
      style={{
        pointerEvents: "none",
        marginRight: 12,
        marginLeft: 12,
      }}
      placeholder={`Search...`}
      size="small"
      slotProps={{
        input: {
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <kbd>⌘ + k</kbd>
            </InputAdornment>
          ),
        },
      }}
    />
  );
};

export default SideBarSearchButton;
