import { Box, BoxProps, styled } from "@mui/material";

const FooterContainer = styled(Box)(({ theme }) => ({
  width: "100%",
  flexShrink: 0,
  zIndex: 20,
  backgroundColor: theme.palette.background.paper,
  paddingLeft: theme.spacing(2),
  paddingRight: theme.spacing(2),
}));

export default function Footer({ children, ...boxProps }: BoxProps) {
  return (
    <FooterContainer {...boxProps}>
      <footer>{children}</footer>
    </FooterContainer>
  );
}
