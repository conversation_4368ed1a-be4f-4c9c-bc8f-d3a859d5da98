import { Grid2, Grid2Props, styled } from "@mui/material";
import { Panel } from "react-resizable-panels";
import { forwardRef } from "react";
import { ImperativePanelHandle } from "react-resizable-panels";

const ContentPanelGrid = styled(Grid2)(({ theme }) => ({
  height: "100vh",
  display: "flex",
  flexDirection: "column",
  backgroundColor: theme.palette.background.default,
  borderRight: `1px solid ${theme.palette.divider}`,
}));

interface PagePanelProps extends Omit<Grid2Props, "item" | "ref"> {
  children: React.ReactNode;
  fullWidth?: boolean;
  hidden?: boolean;
  defaultSize?: number;
  maxSize?: number;
  minSize?: number;
  /** If set, will allow the panel to be collapsed to size by using the ref 0*/
  collapsible?: boolean;
}

const PagePanel = forwardRef<HTMLDivElement, PagePanelProps>(
  ({ hidden, children, ...gridProps }, ref) => {
    if (hidden) {
      return null;
    }
    return (
      <ContentPanelGrid ref={ref} {...gridProps}>
        {children}
      </ContentPanelGrid>
    );
  },
);

export const MenuPanel = forwardRef<ImperativePanelHandle, PagePanelProps>(
  (props, ref) => {
    const { collapsible, ...restProps } = props;

    return (
      <Panel
        minSize={props.minSize ?? 15}
        defaultSize={props.defaultSize ?? 20}
        maxSize={props.maxSize ?? 40}
        collapsible={collapsible}
        collapsedSize={collapsible ? 0 : undefined}
        ref={ref}
      >
        <PagePanel {...restProps} />
      </Panel>
    );
  },
);

export const MainPanel = forwardRef<ImperativePanelHandle, PagePanelProps>(
  (props, ref) => {
    const { collapsible, ...restProps } = props;
    return (
      <Panel
        minSize={props.minSize ?? 20}
        defaultSize={props.defaultSize ?? 50}
        maxSize={props.maxSize}
        ref={ref}
      >
        <PagePanel {...restProps} />
      </Panel>
    );
  },
);

export const DetailsPanel = forwardRef<ImperativePanelHandle, PagePanelProps>(
  (props, ref) => {
    const { collapsible, ...restProps } = props;
    return (
      <Panel
        minSize={props.minSize ?? 20}
        defaultSize={props.defaultSize ?? 30}
        maxSize={props.maxSize ?? 40}
        collapsible={collapsible}
        collapsedSize={collapsible ? 0 : undefined}
        ref={ref}
      >
        <PagePanel {...restProps} />
      </Panel>
    );
  },
);
