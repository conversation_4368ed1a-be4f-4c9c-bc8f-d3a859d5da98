import { Box, BoxProps, Grid2, Stack, Typography, styled } from "@mui/material";
import { ReactNode } from "react";

const HeaderContainer = styled(Box)(({ theme }) => ({
  color: theme.palette.text.primary,
  backgroundColor: theme.palette.background.paper,
  borderBottom: `1px solid ${theme.palette.divider}`,
  width: "100%",
  padding: theme.spacing(1),
  paddingLeft: theme.spacing(2),
  paddingRight: theme.spacing(2),
  flexShrink: 0,
}));

interface HeaderProps extends BoxProps {
  title?: string;
  titleSecondary?: ReactNode;
}
export default function Header({
  title,
  titleSecondary,
  children,
  ...boxProps
}: HeaderProps) {
  return (
    <HeaderContainer {...boxProps}>
      <header>
        <Grid2 container alignItems={"center"} sx={{ height: 36 }}>
          <Grid2 size="grow">
            <Stack
              direction={"row"}
              spacing={1}
              padding={0}
              alignItems={"center"}
            >
              {title ? <Typography variant="h6">{title}</Typography> : null}
              {titleSecondary}
            </Stack>
          </Grid2>
          <Grid2 size="auto">{children}</Grid2>
        </Grid2>
      </header>
    </HeaderContainer>
  );
}
