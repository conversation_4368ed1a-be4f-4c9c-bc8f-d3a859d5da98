import { Box, styled } from "@mui/material";
import { BoxProps } from "@mui/system";

const ContentContainer = styled(Box)(({ theme }) => ({
  flexShrink: 1,
  flexGrow: 1,
  flexBasis: "auto",
  overflow: "auto",
  backgroundColor: theme.palette.background.default,
}));

export default function Content({ children, ...boxProps }: BoxProps) {
  return <ContentContainer {...boxProps}>{children}</ContentContainer>;
}
