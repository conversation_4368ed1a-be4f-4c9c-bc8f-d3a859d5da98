import { DataGrid, GridActionsCellItem } from "@mui/x-data-grid";
import {
  Check,
  ClearOutlined,
  DeleteOutline,
  Edit,
  Pause,
} from "@mui/icons-material";
import { Chip, Tooltip } from "@mui/material";
import { CompletedPartnerInstallation } from "../../models/installation-report/installation-report";
import { actionsColumn } from "../common/table/columns";
import { Visible } from "../common/Conditional";
import { updateStatus } from "../../models/installation-report/provider";
import { useDialog } from "../dialogs/DialogManager";
import { InvoicingBatchStatus } from "../../models/invoicing-batch/invoicing-batch";

const columns = [
  {
    field: "status",
    headerName: "Status",
    renderCell: ({ row }: { row: BatchedReport }) => {
      if (row.batchStatus) {
        return (
          <Chip
            label={row.batchStatus}
            size="small"
            color={"error"}
            variant="outlined"
          />
        );
      }
      return (
        <Chip
          label={row.status}
          size="small"
          color={"default"}
          variant="outlined"
        />
      );
    },
  },
  {
    field: "partnerReferenceNumber",
    headerName: "Order #",
  },
  {
    field: "customerName",
    headerName: "Customer",
    width: 200,
  },
  {
    field: "companyName",
    headerName: "Installer",
    width: 200,
  },
  actionsColumn({
    field: "action",
    headerName: "Tools",
    width: 170,
    renderCell: ({ row }) => {
      return [
        <Visible
          if={["pending", "paused"].includes(row.status)}
          key={"action-stage"}
        >
          <Tooltip title="Stage" arrow>
            <GridActionsCellItem
              icon={<Check />}
              label="Stage"
              onClick={() => {
                updateStatus(row.reference.id, "queued");
              }}
            />
          </Tooltip>
        </Visible>,
        <Visible if={row.status === "queued"} key={"action-unstage"}>
          <Tooltip title="Unstage" arrow>
            <GridActionsCellItem
              icon={<ClearOutlined />}
              label="Unstage"
              onClick={() => {
                updateStatus(row.reference.id, "pending");
              }}
            />
          </Tooltip>
        </Visible>,
        <Visible
          if={["pending", "queued"].includes(row.status)}
          key={"action-pause"}
        >
          <Tooltip title="Pause" arrow>
            <GridActionsCellItem
              icon={<Pause />}
              label="Pause"
              onClick={() => {
                updateStatus(row.reference.id, "paused");
              }}
            />
          </Tooltip>
        </Visible>,
        <Visible
          if={["pending", "queued", "paused"].includes(row.status)}
          key={"action-edit"}
        >
          <Tooltip title="Edit" arrow>
            <EditInvoiceActionItem report={row} />
          </Tooltip>
          <Tooltip title="Delete" arrow>
            <DeleteActionItem report={row} />
          </Tooltip>
        </Visible>,
      ];
    },
  }),
];

function EditInvoiceActionItem({
  report,
}: {
  report: CompletedPartnerInstallation;
}) {
  const { open } = useDialog("edit-invoice");

  return (
    <GridActionsCellItem
      icon={<Edit />}
      label="Edit"
      onClick={() => {
        open({
          report,
        });
      }}
    />
  );
}

function DeleteActionItem({ report }: { report: BatchedReport }) {
  const { open } = useDialog("confirm-dialog");
  return (
    <GridActionsCellItem
      icon={<DeleteOutline />}
      label="Delete"
      onClick={() => {
        open({
          title: "Delete report",
          description: "Are you sure you want to delete this report?",
          onConfirm: () => {
            updateStatus(report.reference.id, "trashed");
          },
        });
      }}
    />
  );
}

export interface BatchedReport extends CompletedPartnerInstallation {
  batchStatus?: InvoicingBatchStatus;
}

interface ReportsTableProps {
  loading?: boolean;
  reports: BatchedReport[];
  onRowClick: (row: CompletedPartnerInstallation) => void;
}

export default function ReportsTable({
  loading,
  reports,
  onRowClick,
}: ReportsTableProps) {
  return (
    <DataGrid
      loading={loading}
      sx={{ backgroundColor: "background.paper" }}
      density="compact"
      getRowId={(row) => row.reference.id}
      rows={reports}
      onRowClick={({ row }) => {
        onRowClick(row);
      }}
      columns={columns}
    />
  );
}
