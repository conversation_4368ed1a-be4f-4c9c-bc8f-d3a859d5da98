import { Job, JobEvents } from "../../models/job/job";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import {
  <PERSON>,
  Footer,
  Header,
  DetailsPanel,
  MainPanel,
  MenuPanel,
} from "../layout/panels";
import { Divider, List, ListItemIcon, ListItemText } from "@mui/material";
import EmptyState from "../empty-state/EmptyState";
import InvoicingJobListItem from "./InvoicingJobListItem";
import ManagedJobDialogs from "../job/details/ManagedJobDialogs";
import { useAuthUser } from "../auth/AuthProvider";
import JobDetails from "../job/Job";
import { updateDoc, deleteField, Timestamp } from "firebase/firestore";
import { useJob } from "../../hooks/job/jobHook";
import InvoiceLinesContainer from "../job/details/InvoiceLinesContainer";
import InvoicesContainer from "../job/details/InvoicesContainer";
import JobReportsContainer from "../job/details/JobReportsContainer";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import {
  CheckCircle,
  DeleteForever,
  ErrorOutline,
  Schedule,
} from "@mui/icons-material";
import Internal from "../auth/Internal";
import { PlatformTier, StandardTier } from "../auth/Tiered";
import InstallerInvoicesContainer from "../job/details/InstallerInvoicesContainer";
import PartnersJob from "../job/PartnersJob";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { usePanelSelection } from "../../lib/hooks/use-panel-selection";

interface InvoicingPageContentProps {
  filteredJobs: Job[];
  error: any;
  tools?: React.ReactNode;
}

export default function InvoicingPageContent({
  filteredJobs,
  error,
  tools,
}: InvoicingPageContentProps) {
  const auth = useAuthUser();
  const [selectedJob, , detailsPanelRef, handleJobSelect, handleClose] =
    usePanelSelection<Job>();

  return (
    <DialogManager>
      {selectedJob ? <ManagedJobDialogs job={selectedJob} /> : null}
      <PanelGroup autoSaveId="invoicing-page" direction="horizontal">
        <MenuPanel>
          <Header title={"Invoicing"}>{tools}</Header>
          <Content>
            {filteredJobs?.length ? null : <EmptyState error={error} />}
            <List dense disablePadding>
              {filteredJobs?.map((job) => (
                <InvoicingJobListItem
                  key={job.reference.id}
                  job={job}
                  selected={selectedJob?.reference.id === job.reference.id}
                  onClick={() => handleJobSelect(job)}
                />
              ))}
            </List>
          </Content>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          <MainPanelContent
            job={selectedJob ?? undefined}
            onClose={handleClose}
          />
        </MainPanel>
        <PanelResizeHandle />
        <DetailsPanel ref={detailsPanelRef} collapsible>
          {selectedJob &&
            (auth?.partner ? (
              <PartnersJob jobId={selectedJob.reference.id} />
            ) : (
              <JobDetails slim jobId={selectedJob.reference.id} />
            ))}
        </DetailsPanel>
      </PanelGroup>
    </DialogManager>
  );
}

interface ContentProps {
  job: Job;
  onClose?: () => void;
}

function MainPanelContent({
  job: originalJob,
  onClose,
}: Partial<ContentProps>) {
  const { job = originalJob } = useJob(originalJob?.reference.id);
  if (!job) return null;

  const isEvJob = job.tags?.includes("ev-chargers");

  return (
    <>
      <Header title={"Order invoicing"}>
        <JobBillingEventButton
          title={"Customer"}
          event={"invoiceSent"}
          job={job}
        />
        <PlatformTier>
          <JobBillingEventButton
            title={"Installer"}
            event={"craftsmanBilled"}
            job={job}
          />
        </PlatformTier>
        <Internal>
          <JobBillingEventButton
            title={"Partner"}
            event={"partnerBilled"}
            job={job}
          />
        </Internal>
      </Header>
      <Content>
        {isEvJob ? (
          <PartnerInvoicingContent job={job} />
        ) : (
          <MarketplaceInvoicingContent job={job} />
        )}
        <Internal>
          <InvoiceLinesContainer jobReference={job.reference} />
        </Internal>
      </Content>
      <Footer paddingBottom={2}></Footer>
    </>
  );
}

function JobBillingEventButton({
  job,
  event,
  title,
}: {
  job: Job;
  event: keyof JobEvents;
  title: string;
}) {
  const { open } = useDialog("set-event-date");
  const value = job.events?.[event];
  return (
    <BillingEventButton
      title={title}
      value={value}
      onChange={async (value) => {
        switch (value) {
          case "clear":
            await updateDoc(job.reference, {
              [`events.${event}`]: null,
            });
            break;
          case "set":
            await updateDoc(job.reference, {
              [`events.${event}`]: Timestamp.now(),
            });
            break;
          case "set-at-date":
            open({ event, defaultToNow: true });
            break;
          case "remove":
            await updateDoc(job.reference, {
              [`events.${event}`]: deleteField(),
            });
            break;
          default:
            break;
        }
      }}
    />
  );
}

function BillingEventButton({
  title,
  value,
  onChange,
}: {
  title: string;
  value: Timestamp | null | undefined;
  onChange?: (value: "clear" | "set" | "remove" | "set-at-date") => void;
}) {
  if (value === undefined) return null;

  return (
    <SelectButton
      startIcon={value ? <CheckCircle /> : <ErrorOutline />}
      endIcon={undefined}
      title={title}
      onSelection={(event: "clear" | "set" | "remove" | "set-at-date") => {
        onChange?.(event);
      }}
      color={value ? "success" : undefined}
    >
      <SelectButtons eventIsSet={Boolean(value)} />
      <SelectButtonItem value={"remove"}>
        <ListItemIcon>
          <DeleteForever />
        </ListItemIcon>
        <ListItemText>Remove event</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}

function SelectButtons({ eventIsSet }: { eventIsSet: boolean }) {
  if (eventIsSet) {
    return (
      <SelectButtonItem value={"clear"}>
        <ListItemIcon>
          <ErrorOutline />
        </ListItemIcon>
        <ListItemText>Mark as not billed</ListItemText>
      </SelectButtonItem>
    );
  }
  return (
    <>
      <SelectButtonItem value={"set"}>
        <ListItemIcon>
          <CheckCircle />
        </ListItemIcon>
        <ListItemText>Mark as billed</ListItemText>
      </SelectButtonItem>
      <Divider />
      <SelectButtonItem value={"set-at-date"}>
        <ListItemIcon>
          <Schedule />
        </ListItemIcon>
        <ListItemText>Billed at date</ListItemText>
      </SelectButtonItem>
    </>
  );
}

function MarketplaceInvoicingContent({ job }: ContentProps) {
  return <InvoicesContainer job={job} isSubcontractorJob />;
}

function PartnerInvoicingContent({ job }: ContentProps) {
  return (
    <>
      <StandardTier exact>
        <JobReportsContainer jobReference={job.reference} />
      </StandardTier>
      <PlatformTier>
        <InstallerInvoicesContainer job={job} />
      </PlatformTier>
    </>
  );
}
