import { Suspense, use } from "react";
import { InvoicingBatch } from "../../models/invoicing-batch/invoicing-batch";
import {
  Stack,
  Alert,
  Paper,
  Typography,
  Grid2,
  AlertTitle,
  LinearProgress,
} from "@mui/material";
import { CompletedPartnerInstallation } from "../../models/installation-report/installation-report";
import { fetchReport } from "../../models/installation-report/provider";
import ReportsTable, { BatchedReport } from "./ReportsTable";
import { CustomerSummary } from "./InstallationInvoiceReport";
import { InstallerSummary } from "./InstallationInvoiceReport";
import { isDefined } from "../../lib/filter-undefined";

export default function BatchSummary({ batch }: { batch?: InvoicingBatch }) {
  if (!batch) return null;

  /**
   * Fetches the reports for the batch.
   * @returns The reports for the batch.
   */
  async function getReports(): Promise<BatchedReport[]> {
    if (!batch) return [];

    // Get ids of reports that failed
    const allFailedIds = batch.errors
      ?.flatMap((error) => error.affectedReports)
      ?.map((ref) => ref.id);

    // Fetch reports
    const reports = await Promise.all(batch.reports.map(fetchReport));

    // Add batch status to reports
    return reports.map((report) => ({
      ...report,
      batchStatus: allFailedIds?.includes(report.reference.id)
        ? "failed"
        : undefined,
    }));
  }

  return (
    <Grid2 container>
      <Grid2 size={12}>
        {["processing", "pending"].includes(batch.status) && (
          <LinearProgress variant="indeterminate" />
        )}
        {batch.draftOnly && (
          <Alert severity="info">
            Only drafts where created during this run
          </Alert>
        )}
        {batch.errors?.length && (
          <Alert severity="error">
            <AlertTitle>Run failed</AlertTitle>
            <ul>
              {batch.errors.map((error) => (
                <li key={error.error}>{error.error}</li>
              ))}
            </ul>
          </Alert>
        )}
      </Grid2>
      <Grid2 size={12} container spacing={1} padding={1}>
        <ProgressItem label="Total" value={batch.progress.total} />
        <ProgressItem label="Completed" value={batch.progress.completed} />
        <ProgressItem label="Failed" value={batch.progress.failed} />
      </Grid2>
      <Suspense fallback={<ReportsDetails reports={[]} loading />}>
        <ReportsDetailsLoader getReports={getReports()} />
      </Suspense>
    </Grid2>
  );
}

function ProgressItem({ label, value }: { label: string; value: number }) {
  return (
    <Grid2 size={4}>
      <Paper variant="outlined">
        <Stack direction="column" spacing={1} padding={1}>
          <Typography variant="caption" align="center">
            {label}
          </Typography>
          <Typography variant="h4" align="center">
            {value}
          </Typography>
        </Stack>
      </Paper>
    </Grid2>
  );
}

function ReportsDetailsLoader({
  getReports,
}: {
  getReports: Promise<BatchedReport[]>;
}) {
  const reports = use(getReports);
  return <ReportsDetails reports={reports} />;
}

function ReportsDetails({
  reports,
  loading,
}: {
  reports: CompletedPartnerInstallation[];
  loading?: boolean;
}) {
  const allLines = reports.flatMap((report) => report.lines).filter(isDefined);
  return (
    <Grid2 container spacing={1} padding={1}>
      <Grid2 size={6}>
        <CustomerSummary lines={allLines} title={"Customer"} />
      </Grid2>
      <Grid2 size={6}>
        <InstallerSummary lines={allLines} title={"Installers"} />
      </Grid2>
      <Grid2 size={12}>
        <ReportsTable
          reports={reports}
          onRowClick={() => {}}
          loading={loading}
        />
      </Grid2>
    </Grid2>
  );
}
