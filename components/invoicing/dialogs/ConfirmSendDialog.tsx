import { DialogContentText } from "@mui/material";
import { CompletedPartnerInstallation } from "../../../models/installation-report/installation-report";
import { createInvoicingBatch } from "../../../models/invoicing-batch/invoicing-batch";
import { useAuthUser } from "../../auth/AuthProvider";
import { Switches } from "mui-rff";
import FormDialog from "../../dialogs/FormDialog";
interface ConfirmSendDialogProps {
  reports?: CompletedPartnerInstallation[];
  isOpen: boolean;
  close: () => void;
}

export default function ConfirmSendDialog({
  reports,
  isOpen,
  close,
}: ConfirmSendDialogProps) {
  const { userDoc, partnerDoc } = useAuthUser();

  return (
    <FormDialog
      isOpen={isOpen}
      close={close}
      title={"Confirm send"}
      initialValues={{
        draftOnly: true,
      }}
      onSubmit={async ({ draftOnly }) => {
        if (!partnerDoc || !userDoc) throw new Error("Not authorized");
        if (!reports) throw new Error("No reports");

        await createInvoicingBatch(
          partnerDoc.reference,
          userDoc?.reference,
          reports.map((report) => report.reference),
          draftOnly,
        );
      }}
    >
      <DialogContentText>
        Are you sure you want to send all staged invoices?
      </DialogContentText>
      <Switches
        name="draftOnly"
        data={{
          label: "Create draft invoices only",
          value: true,
        }}
      />
    </FormDialog>
  );
}
