import { v4 as uuid } from "uuid";
import FormDialog from "../../dialogs/FormDialog";
import { LinesDataGridField } from "../../invoice/editing";
import { CompletedPartnerInstallation } from "../../../models/installation-report/installation-report";
import { InvoiceLineItem } from "../../../models/invoice/invoice-line-item";
import { updateLines } from "../../../models/installation-report/provider";
import { Field } from "react-final-form";
import ManagedDialog from "../../dialogs/ManagedDialog";
import AddAttachmentDialog from "../../job/dialogs/AddAttachmentDialog";
import { useDocumentData } from "react-firebase-hooks/firestore";
import { jobReference } from "../../../models/job/job";
import { useMemo } from "react";

interface EditInvoiceDialogProps {
  report?: CompletedPartnerInstallation;
  isOpen: boolean;
  close: () => void;
}

export default function EditInvoiceDialog({
  report,
  isOpen,
  close,
}: EditInvoiceDialogProps) {
  const initialLines = useMemo(
    () =>
      report?.lines?.map<InvoiceLineItem>((line) => ({
        uid: line.uid ?? uuid(),
        workedHours: null,
        detailedDeductionType: null,
        ...line,
      })) ?? [],
    [report],
  );

  const [job] = useDocumentData(jobReference(report?.job?.id));

  return (
    <>
      <ManagedDialog id="add-article" component={AddAttachmentDialog} />
      <FormDialog
        title="Edit invoice"
        maxWidth="xl"
        fullWidth
        initialValues={{ lines: initialLines }}
        isOpen={isOpen}
        close={close}
        onSubmit={async (values) => {
          if (!report) {
            return;
          }
          await updateLines(report.reference.id, values.lines);
        }}
      >
        <Field name="lines">
          {({ input }) => (
            <LinesDataGridField
              hideFooter
              editable
              articleGroupId={job?.articleGroups?.[0]?.reference.id}
              lines={input.value}
              mode="customer"
              onUpdate={async (newLines) => {
                input.onChange(newLines);
              }}
            />
          )}
        </Field>
      </FormDialog>
    </>
  );
}
