import { ListItemIcon, ListItemText, capitalize } from "@mui/material";
import SelectButton, { SelectButtonItem } from "../menu/SelectButton";
import { ElectricCar, ShoppingCart } from "@mui/icons-material";

interface InvoicingFilterButtonProps {
  onChange: (status: string) => void;
  title: string;
  initialSegment?: string;
}

export default function InvoicingFilterButton({
  onChange,
  title,
  initialSegment,
}: InvoicingFilterButtonProps) {
  return (
    <SelectButton
      persistent
      initialSelection={initialSegment}
      title={capitalize(title)}
      onSelection={(value: string) => {
        onChange(value);
      }}
    >
      <SelectButtonItem value={"partner"}>
        <ListItemIcon>
          <ElectricCar fontSize="small" />
        </ListItemIcon>
        <ListItemText>Partner</ListItemText>
      </SelectButtonItem>
      <SelectButtonItem value={"subcontractor"}>
        <ListItemIcon>
          <ShoppingCart fontSize="small" />
        </ListItemIcon>
        <ListItemText>Subcontractor</ListItemText>
      </SelectButtonItem>
    </SelectButton>
  );
}

export function titleForInvoicingFilter(filter?: string | string[]) {
  switch (filter) {
    case "partner":
      return "Partner";
    case "subcontractor":
      return "Subcontractor";
  }
  return "All";
}
