import DialogManager, { useDialog } from "../dialogs/DialogManager";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  DetailsPanel,
  MainPanel,
  MenuPanel,
} from "../layout/panels";
import {
  Divider,
  List,
  Typography,
  Tooltip,
  Button,
  Stack,
  LinearProgress,
  ListItem,
  ListItemButton,
  ListItemText,
  CircularProgress,
  Grid2,
  ListItemIcon,
  Alert,
  AlertTitle,
  IconButton,
} from "@mui/material";
import PartnersJob from "../job/PartnersJob";
import {
  DocumentReference,
  writeBatch,
  getFirestore,
} from "firebase/firestore";
import {
  Check,
  CheckCircle,
  CheckCircleOutlined,
  ClearOutlined,
  ErrorOutline,
  FolderOutlined,
  Outbox,
  Pause,
  PauseCircleOutline,
  PendingOutlined,
  PlayCircleOutline,
  Send,
  DescriptionOutlined,
  Close,
} from "@mui/icons-material";
import ExpandableItem from "../common/lists/ExpandableItem";
import { useCollectionData } from "react-firebase-hooks/firestore";
import { openInstallationReportsForPartner } from "../../models/installation-report/provider";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import {
  CompletedPartnerInstallation,
  InstallationBillingStatus,
} from "../../models/installation-report/installation-report";
import formatTimeRelative, {
  formatTime,
  formatTinyDistance,
} from "../../lib/format-time-relative";
import { CustomerSummary, SummaryContainer } from "./InstallationInvoiceReport";
import { InstallerSummary } from "./InstallationInvoiceReport";
import { isDefined } from "../../lib/filter-undefined";
import ManagedDialog from "../dialogs/ManagedDialog";
import ConfirmSendDialog from "./dialogs/ConfirmSendDialog";
import {
  InvoicingBatch,
  InvoicingBatchStatus,
  latestInvoicingBatchQuery,
} from "../../models/invoicing-batch/invoicing-batch";
import EditInvoiceDialog from "./dialogs/EditInvoiceDialog";
import BatchSummary from "./BatchSummary";
import ReportsTable from "./ReportsTable";
import ManagedConfirmDialog from "../dialogs/ManagedConfirmDialog";
import { ErrorBoundary, FallbackRender } from "@sentry/react";
import Job from "../job/Job";
import { PlatformTier } from "../auth/Tiered";
import Internal from "../auth/Internal";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import EmptyState from "../empty-state/EmptyState";
import { usePanelSelection } from "../../lib/hooks/use-panel-selection";

interface InvoicingPageContentProps {
  partnerRef: DocumentReference;
  tools?: React.ReactNode;
}

export default function PlatformInvoicingPageContent({
  partnerRef,
  tools,
}: InvoicingPageContentProps) {
  const [selectedFilter, setSelectedFilter] = useState<string>();
  const [status, setStatus] = useState<InstallationBillingStatus>();
  const [selectedJobId, , detailsPanelRef, handleJobSelect, handleClose] =
    usePanelSelection<string>();

  const [openReports = []] = useCollectionData(
    openInstallationReportsForPartner(partnerRef),
  );

  const [invoicingBatches = []] = useCollectionData(
    latestInvoicingBatchQuery(partnerRef, 10),
  );

  const [lastInvoicingBatch] = invoicingBatches;
  const isProcessing = lastInvoicingBatch?.status === "processing";

  // Not the most eligant but state needs to flow to both menu and main panel...
  const [stagedReports, pausedReports, pendingReports] = useMemo(() => {
    return [
      _.filter(openReports, ["status", "queued"]),
      _.filter(openReports, ["status", "paused"]),
      _.filter(openReports, ["status", "pending"]),
    ];
  }, [openReports]);

  const reports = useMemo(() => {
    if (!selectedFilter) return [];
    const [status, company, reportId] = selectedFilter.split("/");

    let reports = [];
    switch (status) {
      case "paused":
        reports = pausedReports;
        break;
      case "queued":
        reports = stagedReports;
        break;
      case "pending":
        reports = pendingReports;
        break;
      default:
        return [];
    }

    if (company) {
      reports = reports.filter((report) => report.companyName === company);
    }

    if (reportId) {
      reports = reports.filter((report) => report.reference.id === reportId);
    }

    return reports;
  }, [pausedReports, stagedReports, pendingReports, selectedFilter]);

  return (
    <DialogManager>
      <ManagedDialog id="confirm-send" component={ConfirmSendDialog} />
      <ManagedDialog id="edit-invoice" component={EditInvoiceDialog} />
      <ManagedConfirmDialog />
      <PanelGroup autoSaveId="platform-invoicing" direction="horizontal">
        <MenuPanel defaultSize={25}>
          <Header title={"Invoicing"}>{tools}</Header>
          <Content>
            <List dense disablePadding>
              <StatusItem
                title={"Paused"}
                reports={pausedReports}
                status="paused"
                onSelected={(path) => {
                  setStatus("paused");
                  setSelectedFilter(path);
                }}
                selected={selectedFilter}
                icon={<PauseCircleOutline />}
              />
              <StatusItem
                title={"Staged"}
                reports={stagedReports}
                status="queued"
                onSelected={(path) => {
                  setStatus("queued");
                  setSelectedFilter(path);
                }}
                selected={selectedFilter}
                icon={<CheckCircle />}
              />
              <StatusItem
                title={"Pending"}
                reports={pendingReports}
                status="pending"
                onSelected={(path) => {
                  setStatus("pending");
                  setSelectedFilter(path);
                }}
                selected={selectedFilter}
                icon={<PendingOutlined />}
              />
            </List>
          </Content>
          <Footer sx={{ padding: 0, maxHeight: "40%" }}>
            <Divider />
            {lastInvoicingBatch?.status === "pending" && (
              <LinearProgress variant="indeterminate" />
            )}
            {lastInvoicingBatch?.status === "processing" && (
              <LinearProgress
                variant="determinate"
                value={
                  (lastInvoicingBatch.progress.completed /
                    lastInvoicingBatch.progress.total) *
                  100
                }
              />
            )}
            <InvoicingBatchStatusItem
              title={"Processing"}
              batches={invoicingBatches}
              onSelected={(path) => {
                setStatus("processing");
                setSelectedFilter(path);
              }}
              selected={selectedFilter}
              icon={<Outbox />}
            />
          </Footer>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          {status === "processing" ? (
            <BatchMainPanelContent
              batch={invoicingBatches.find(
                (batch) => batch.reference.id === selectedFilter,
              )}
            />
          ) : (
            <MainPanelContent
              reports={reports}
              status={status}
              onJobSelected={handleJobSelect}
              readOnly={isProcessing}
            />
          )}
        </MainPanel>
        <PanelResizeHandle />
        <DetailsPanel ref={detailsPanelRef} collapsible>
          <DetailsPanelContent jobId={selectedJobId} onClose={handleClose} />
        </DetailsPanel>
      </PanelGroup>
    </DialogManager>
  );
}

interface StatusItemProps extends Omit<TreeItemProps, "parent"> {
  title: string;
  status: InstallationBillingStatus;
  icon: React.ReactNode;
  reports: CompletedPartnerInstallation[];
}

interface TreeItemProps {
  onSelected: (path: string) => void;
  selected?: string;
  parent?: string;
}

function AccessoryLabel({
  children,
  tooltip,
}: {
  children: React.ReactNode;
  tooltip?: string;
}) {
  return (
    <Tooltip title={tooltip} arrow>
      <Typography variant="caption" color="textSecondary">
        {children}
      </Typography>
    </Tooltip>
  );
}

interface ItemProps {
  children?: React.ReactNode;
  selected?: boolean;
  level?: number;
  title: string;
  subtitle?: string;
  onClick?: () => void;
}

function Item({
  children,
  selected,
  level = 1,
  title,
  subtitle,
  onClick,
}: ItemProps) {
  return (
    <ListItem
      disablePadding
      divider
      sx={{ backgroundColor: "background.paper" }}
    >
      <ListItemButton
        onClick={onClick}
        selected={selected}
        sx={{ pl: 1 * level }}
      >
        <ListItemText primary={title} secondary={subtitle} />
        {children}
      </ListItemButton>
    </ListItem>
  );
}

interface InvoicingBatchStatusItemProps extends Omit<TreeItemProps, "parent"> {
  title: string;
  icon: React.ReactNode;
  batches: InvoicingBatch[];
}

function InvoicingBatchStatusItem({
  title,
  icon,
  onSelected,
  selected,
  batches,
}: InvoicingBatchStatusItemProps) {
  const [batch, ...rest] = batches;
  return (
    <ExpandableItem
      title={title}
      accessory={
        <StatusIcon status={batch?.status} draftOnly={batch?.draftOnly} />
      }
      icon={icon}
      onClick={() => onSelected(batch?.reference.id)}
      selected={selected === batch?.reference.id}
    >
      <List dense disablePadding>
        {rest
          .sort(
            (a, b) =>
              b.createTime.toDate().getTime() - a.createTime.toDate().getTime(),
          )
          .map((batch) => (
            <Item
              key={batch.reference.id}
              title={formatTimeRelative(batch.createTime.toDate()) ?? ""}
              onClick={() => onSelected(batch.reference.id)}
              selected={selected === batch.reference.id}
            >
              <StatusIcon status={batch.status} draftOnly={batch.draftOnly} />
            </Item>
          ))}
      </List>
    </ExpandableItem>
  );
}

function StatusIcon({
  status,
  draftOnly,
}: {
  status: InvoicingBatchStatus;
  draftOnly?: boolean;
}) {
  switch (status) {
    case "pending":
      return (
        <Tooltip title="Pending" arrow>
          <CircularProgress size={"16"} />
        </Tooltip>
      );
    case "processing":
      return (
        <Tooltip title="Processing" arrow>
          <CircularProgress size={16} />
        </Tooltip>
      );
    case "completed":
      return (
        <Tooltip
          title={draftOnly ? "Completed (drafts only)" : "Completed"}
          arrow
        >
          {draftOnly ? (
            <CheckCircleOutlined fontSize="inherit" color="success" />
          ) : (
            <CheckCircle fontSize="inherit" color="success" />
          )}
        </Tooltip>
      );
    case "failed":
      return (
        <Tooltip title="Failed" arrow>
          <ErrorOutline fontSize="inherit" color="error" />
        </Tooltip>
      );
    default:
      return null;
  }
}

function StatusItem({
  title,
  status,
  icon,
  onSelected,
  selected,
  reports,
}: StatusItemProps) {
  const reportsByCompany = _.groupBy(reports, "companyName");

  return (
    <ExpandableItem
      title={title}
      accessory={
        <AccessoryLabel tooltip={`${reports?.length ?? 0} orders`}>
          {String(reports?.length ?? 0)}
        </AccessoryLabel>
      }
      onClick={() => onSelected(status)}
      selected={selected === status}
      icon={icon}
    >
      {Object.entries(reportsByCompany)
        .sort((a, b) => b[1].length - a[1].length)
        .map(([company, reports]) => (
          <ExpandableCompanyItem
            key={company}
            company={company}
            reports={reports}
            onSelected={(path) => onSelected(path)}
            selected={selected}
            parent={status}
          />
        ))}
    </ExpandableItem>
  );
}

interface ExpandableCompanyItemProps extends TreeItemProps {
  company: string;
  reports: CompletedPartnerInstallation[];
}

function ExpandableCompanyItem({
  company,
  reports,
  onSelected,
  selected,
  parent,
}: ExpandableCompanyItemProps) {
  return (
    <ExpandableItem
      icon={<FolderOutlined />}
      title={company}
      accessory={
        <AccessoryLabel tooltip={`${reports?.length ?? 0} orders`}>
          {String(reports?.length ?? 0)}
        </AccessoryLabel>
      }
      onClick={() => {
        onSelected(`${parent}/${company}`);
      }}
      selected={selected === `${parent}/${company}`}
      level={1}
    >
      {reports
        .sort(
          (a, b) =>
            b.createTime.toDate().getTime() - a.createTime.toDate().getTime(),
        )
        .map((report, index) => (
          <ListItem
            key={index}
            disablePadding
            divider
            sx={{ backgroundColor: "background.paper" }}
          >
            <ListItemButton
              sx={{ pl: 5 }}
              selected={
                selected === `${parent}/${company}/${report.reference.id}`
              }
              onClick={() => {
                onSelected(`${parent}/${company}/${report.reference.id}`);
              }}
            >
              <ListItemIcon>
                <DescriptionOutlined />
              </ListItemIcon>
              <ListItemText primary={report.customerName} />
              <AccessoryLabel
                tooltip={`Completed ${formatTimeRelative(
                  report.createTime.toDate(),
                )}`}
              >
                {formatTinyDistance(report.createTime)}
              </AccessoryLabel>
            </ListItemButton>
          </ListItem>
        ))}
    </ExpandableItem>
  );
}

function BatchMainPanelContent({ batch }: { batch?: InvoicingBatch }) {
  if (!batch) return null;

  return (
    <>
      <Header title={`Run at ${formatTime(batch.createTime.toDate())}`} />
      <Content>
        <BatchSummary batch={batch} />
      </Content>
    </>
  );
}

interface MainPanelContentProps {
  reports?: CompletedPartnerInstallation[];
  status?: InstallationBillingStatus;
  onJobSelected: (jobId: string) => void;
  readOnly: boolean;
}

function MainPanelContent({
  reports,
  status,
  onJobSelected,
  readOnly,
}: MainPanelContentProps) {
  const { open: openConfirmSend } = useDialog("confirm-send");

  // Function that updates the status of all the currently displayed reports.
  const setStatus = useCallback(
    async (status: InstallationBillingStatus) => {
      const batch = writeBatch(getFirestore());
      reports?.forEach((report) => {
        batch.update(report.reference, { status });
      });
      await batch.commit();
    },
    [reports],
  );

  const allLines =
    reports?.flatMap((report) => report.lines).filter(isDefined) ?? [];

  if (!reports || !status) return null;

  return (
    <>
      <Header title={`${reports.length} orders`}>
        <Stack direction="row" spacing={1}>
          {["pending", "paused"].includes(status) ? (
            <Button
              variant="text"
              color="primary"
              disabled={reports.length === 0}
              startIcon={<Check />}
              onClick={() => setStatus("queued")}
            >
              Stage
            </Button>
          ) : null}
          {["queued"].includes(status) ? (
            <Button
              variant="text"
              color="primary"
              disabled={reports.length === 0}
              startIcon={<ClearOutlined />}
              onClick={() => setStatus("pending")}
            >
              Unstage
            </Button>
          ) : null}
          {["queued", "pending"].includes(status) ? (
            <Button
              variant="text"
              color="primary"
              disabled={reports.length === 0}
              startIcon={<Pause />}
              onClick={() => setStatus("paused")}
            >
              Pause
            </Button>
          ) : null}
          {["paused"].includes(status) ? (
            <Button
              variant="text"
              color="primary"
              disabled={reports.length === 0}
              startIcon={<PlayCircleOutline />}
              onClick={() => setStatus("pending")}
            >
              Resume
            </Button>
          ) : null}
          {["queued"].includes(status) ? (
            <Button
              variant="contained"
              color="primary"
              disabled={reports.length === 0 || readOnly}
              startIcon={<Send />}
              onClick={() => openConfirmSend({ reports })}
            >
              Send
            </Button>
          ) : null}
        </Stack>
      </Header>
      <Content padding={1}>
        <Grid2 container spacing={1}>
          <Grid2 size={6}>
            <ErrorBoundary fallback={summaryFallbackRenderer}>
              <CustomerSummary lines={allLines} title={"Customer"} />
            </ErrorBoundary>
          </Grid2>
          <Grid2 size={6}>
            <ErrorBoundary fallback={summaryFallbackRenderer}>
              <InstallerSummary lines={allLines} title={"Installers"} />
            </ErrorBoundary>
          </Grid2>
          <Grid2 size={12}>
            <ReportsTable
              reports={reports}
              onRowClick={(report) => {
                onJobSelected(report.job.id);
              }}
            />
          </Grid2>
        </Grid2>
      </Content>
    </>
  );
}

const summaryFallbackRenderer: FallbackRender = ({ error, resetError }) => (
  <SummaryContainer title={"Installers"}>
    <Alert
      severity="error"
      action={
        <Button color="inherit" size="small" onClick={() => resetError()}>
          RESET
        </Button>
      }
    >
      <AlertTitle>Failed to load summary</AlertTitle>
      This might indicate an incorrectly configured article.
      <br />
      <br />
      <Typography variant="caption">{String(error)}</Typography>
    </Alert>
  </SummaryContainer>
);

function DetailsPanelContent({
  jobId,
  onClose,
}: {
  jobId: string | null;
  onClose: () => void;
}) {
  return (
    <>
      <Header title="Details">
        <IconButton onClick={onClose}>
          <Close />
        </IconButton>
      </Header>
      <Content>
        {jobId ? (
          <>
            <Internal>
              <Job slim jobId={jobId} />
            </Internal>
            <PlatformTier exact>
              <PartnersJob jobId={jobId} />
            </PlatformTier>
          </>
        ) : (
          <EmptyState />
        )}
      </Content>
    </>
  );
}
