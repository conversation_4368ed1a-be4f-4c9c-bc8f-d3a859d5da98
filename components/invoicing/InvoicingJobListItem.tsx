import { useMemo } from "react";
import { Job, JobEvents } from "../../models/job/job";
import TaskListItem, { TaskListItemProps } from "../common/tasks/TaskListItem";
import { isDefined } from "../../lib/filter-undefined";

interface InvoiceJobListItemProps
  extends Pick<TaskListItemProps, "onClick" | "selected"> {
  job: Job;
}

const billingEvents: (keyof JobEvents)[] = [
  "craftsmanBilled",
  "partnerBilled",
  "invoiceSent",
];

export default function InvoicingJobListItem({
  job,
  ...itemProps
}: InvoiceJobListItemProps) {
  const isPartiallyInvoiced = useMemo(() => {
    const events = Object.entries(job.events ?? {}).filter(([event]) =>
      billingEvents.includes(event as keyof JobEvents),
    );
    if (events.length === 0) return false;
    return !events.every(([_, value]) => value === null);
  }, [job.events]);

  return (
    <TaskListItem
      title={job.cache?.customerName ?? job.description}
      secondaryTitle={
        isPartiallyInvoiced ? "Partially invoiced" : "Not invoiced"
      }
      secondaryTitleTypographyProps={{
        color: isPartiallyInvoiced ? "warning.main" : undefined,
      }}
      timestamp={job.events?.jobDone}
      text={[
        job.cache?.companyName,
        job.partner !== "subcontractor" ? job.partner : undefined,
        ...(job.services ?? []),
      ]
        .filter(isDefined)
        .join(" • ")}
      {...itemProps}
    />
  );
}
