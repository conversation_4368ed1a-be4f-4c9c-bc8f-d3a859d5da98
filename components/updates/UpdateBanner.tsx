import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";
import { useState } from "react";
import { useInterval } from "../../lib/hooks/use-interval";

function RefreshButton() {
  return (
    <Tooltip title="Reload to get the latest version" arrow>
      <IconButton
        color="inherit"
        size="small"
        onClick={() => location.reload()}
      >
        <RefreshIcon />
      </IconButton>
    </Tooltip>
  );
}

/**
 * Banner that shows when there is an update available.
 */
export default function UpdateBanner() {
  const [version, setVersion] = useState<string>();
  const [haveUpdate, setHaveUpdate] = useState(false);

  useInterval(() => {
    // If we already know there is an update, don't check again
    if (haveUpdate) return;

    // Load the version file and compare to the current version
    fetch("/_meta/version.json", { cache: "no-store" })
      .then(async (response) => {
        const result = await response.json();
        const latestVersion = result.version;

        // If loading failed, skip and try again later
        if (!latestVersion) {
          return;
        }

        // If we don't have a version yet, this will be our baseline.
        if (!version) {
          setVersion(latestVersion);
          return;
        }

        // If the version has changed, we have an update
        if (version !== latestVersion) {
          setHaveUpdate(true);
        }
      })
      .catch(() => {
        console.warn("Failed to check for updates");
      });
  }, 30000);

  if (!haveUpdate) {
    return null;
  }

  return (
    <Alert icon={false} severity="info" action={<RefreshButton />}>
      Update available!
    </Alert>
  );
}
