import { Tooltip, IconButton, IconButtonProps, styled } from "@mui/material";

interface ToolButtonProps extends Omit<IconButtonProps, "children" | "size"> {
  title: string;
  Icon: React.ElementType;
}

/**
 * A button for a tooltip toolbar.
 */
export function TooltipToolbarButton({
  title,
  Icon,
  ...buttonProps
}: ToolButtonProps) {
  return (
    <Tooltip title={title} arrow>
      <ToolIconButton size="small" {...buttonProps}>
        <Icon fontSize="inherit" />
      </ToolIconButton>
    </Tooltip>
  );
}

const ToolIconButton = styled(IconButton)({
  fontSize: "0.9rem",
});
