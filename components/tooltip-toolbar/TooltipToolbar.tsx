import { Tooltip, TooltipProps, styled, tooltipClasses } from "@mui/material";
import { useState } from "react";

interface TooltipToolbarProps {
  placement?: "bottom-start" | "bottom-end" | "top-start" | "top-end";
  enabled?: boolean;
  offset?: number;
  children: React.ReactElement;
  tools: React.ReactNode;
}

/**
 * Displays a small toolbar visible when hovering the wrapped component.
 *
 * @example
 * <TooltipToolbar
 *  tools={
 *   <ToolsStack spacing={0}>
 *    <TooltipToolbarButton
 *      title={"Delete me"}
 *      Icon={DeleteIcon}
 *      onClick={() => {}}
 *    />
 *   </ToolsStack>
 *  }>
 * ...component to wrap...
 * </TooltipToolbar>
 *
 */
export default function TooltipToolbar({
  enabled = true,
  placement = "top-end",
  offset = 8,
  children,
  tools,
}: TooltipToolbarProps) {
  const [open, setOpen] = useState(false);

  return (
    <StyledTooltip
      open={open}
      onOpen={() => setOpen(enabled)}
      onClose={() => setOpen(false)}
      title={tools}
      placement={placement}
      slotProps={{
        popper: {
          modifiers: [
            {
              name: "offset",
              options: {
                offset: [offset, -32],
              },
            },
          ],
        },
      }}
    >
      {children}
    </StyledTooltip>
  );
}

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.background.paper,
    boxShadow: theme.shadows[1],
    padding: 0,
    borderRadius: 9,
  },
}));
