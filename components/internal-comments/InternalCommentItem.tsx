import DeleteIcon from "@mui/icons-material/Delete";
import { useAuthUser } from "../auth/AuthProvider";
import { InternalComment } from "../../models/inbox/internal-comment";
import ToolsStack from "../layout/ToolsStack";
import MessageItem from "../messaging/MessageItem";
import MessageStack from "../messaging/MessageStack";
import { Stack, Typography } from "@mui/material";
import { TooltipToolbarButton } from "../tooltip-toolbar/TooltipToolbarButton";
import { StorageFilePreview } from "../files/StorageFilePreview";
import { isSystemUser } from "../../models/user/user";

interface InternalCommentItemProps {
  comment: InternalComment;
  onDeleteClick?: () => void;
}

export default function InternalCommentItem({
  comment,
  onDeleteClick,
}: InternalCommentItemProps) {
  const authUser = useAuthUser();
  const { visibleFor, createdByType, isDeleted, files } = comment;
  const recipients = Object.entries(visibleFor ?? {})
    .filter(([key, value]) => value && key !== createdByType)
    .map(([key]) => key);

  const isFromMe =
    (createdByType ?? "done") === (authUser.partner ? "partner" : "done");
  const isInternal = !recipients?.length && isFromMe;

  const variant = isSystemUser(comment.createdBy)
    ? "system"
    : isInternal
      ? "internal"
      : isFromMe
        ? "own"
        : "external";

  if (isDeleted) {
    return (
      <MessageStack isFromMe={isFromMe} sender={comment.createdBy}>
        <Typography variant="caption" color="text.secondary" fontStyle="italic">
          Message was deleted
        </Typography>
      </MessageStack>
    );
  }

  return (
    <MessageItem
      text={comment.note}
      isFromMe={isFromMe}
      recipients={recipients}
      sender={comment.createdBy}
      createTime={comment.createTime}
      variant={variant}
      toolsEnabled={isFromMe && !isDeleted && Boolean(onDeleteClick)}
      tools={
        <ToolsStack spacing={0}>
          <TooltipToolbarButton
            title={"Delete message"}
            Icon={DeleteIcon}
            onClick={onDeleteClick}
          />
        </ToolsStack>
      }
    >
      <Stack direction="row" spacing={1}>
        {files?.map((file) => (
          <StorageFilePreview
            key={file.name}
            name={file.name}
            path={file.path}
          />
        ))}
      </Stack>
    </MessageItem>
  );
}
