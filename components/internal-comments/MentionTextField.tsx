import Mentions from "rc-mentions";
import { UserRoleContainer } from "../../models/user/user-role";
import { Field } from "react-final-form";
import { useTheme, Box } from "@mui/material";

interface MentionableTextFieldProps {
  name: string;
  label: string;
  autoFocus?: boolean;
  mentionable?: UserRoleContainer[];
}

function extractMentions(text: string): string {
  const regex = /@\[([^\]]+)\]\([^)]+\)/g;
  const replacement = "@$1";
  return text.replace(regex, replacement);
}

function replaceMentions(value: string, mentionable?: UserRoleContainer[]) {
  if (!mentionable) return value;

  const reduced = mentionable.reduce((text, { fullName, reference }) => {
    const mentionRegex = new RegExp(`@${fullName}(?![\\w\\]])`, "g"); // Match standalone "@Name"
    return text.replace(mentionRegex, `@[${fullName}](/users/${reference.id})`);
  }, value);

  return reduced;
}

const MentionTextField = ({
  autoFocus,
  name,
  mentionable,
  label,
}: MentionableTextFieldProps) => {
  const theme = useTheme();

  return (
    <Field name={name}>
      {({ input }) => (
        <Box
          sx={{
            "& textarea": {
              fontSize: "14px",
              color: theme.palette.text.primary,
              padding: "12px",
            },
          }}
        >
          <Mentions
            {...input}
            autoFocus={autoFocus}
            value={extractMentions(input.value)}
            autoSize={{
              minRows: 2,
              maxRows: 15,
            }}
            onChange={(value) => {
              input.onChange(replaceMentions(value, mentionable));
            }}
            style={{ width: "100%" }}
            placeholder={
              mentionable?.length
                ? `${label}, Use "@" to mention someone.`
                : label
            }
            options={
              mentionable?.map(({ fullName, reference }) => ({
                value: `[${fullName}](/users/${reference.id})`,
                key: `/users/${reference.id}`,
                label: fullName,
              })) || []
            }
          />
        </Box>
      )}
    </Field>
  );
};

export default MentionTextField;
