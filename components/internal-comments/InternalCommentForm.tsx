import { Paper, Stack, capitalize } from "@mui/material";
import Form from "../common/Form";
import { Checkboxes } from "mui-rff";
import { FormSpy } from "react-final-form";
import { InternalCommentParticipant } from "../../models/inbox/internal-comment";
import { UserRoleContainer } from "../../models/user/user-role";
import { LoadingButton } from "@mui/lab";
import { useState } from "react";
import MentionTextField from "./MentionTextField";

export interface InternalCommentFormValues {
  note: string;
  visibleFor: { [from in InternalCommentParticipant]?: boolean };
}

export interface InternalCommentFormProps {
  onSubmit: (values: InternalCommentFormValues) => PromiseLike<void>;
  elevated?: boolean;
  autoFocus?: boolean;
  mentionable?: UserRoleContainer[];
  possibleRecipients: InternalCommentParticipant[];
  tools?: React.ReactNode;
  children?: React.ReactNode;
}

export default function InternalCommentForm({
  autoFocus = false,
  onSubmit,
  mentionable,
  possibleRecipients,
  tools,
  children,
}: InternalCommentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  return (
    <Form
      onSubmit={async (values, form) => {
        setIsSubmitting(true);
        await onSubmit(values as InternalCommentFormValues);
        form.reset();
        setIsSubmitting(false);
      }}
      enableShortcuts
    >
      <FormSpy
        subscription={{ values: true }}
        render={({ values }) => {
          const isInternal = !Object.values(values?.visibleFor ?? {}).some(
            (value) => value === true,
          );
          return (
            <Paper
              variant="outlined"
              sx={{
                padding: 1,
                color: isInternal ? "internalMessage.contrastText" : undefined,
                backgroundColor: isInternal
                  ? "internalMessage.main"
                  : undefined,
              }}
            >
              <Stack>
                <MentionTextField
                  autoFocus={autoFocus}
                  name="note"
                  label={isInternal ? "New note" : "New message"}
                  mentionable={mentionable}
                />
                {children}
                <Stack
                  direction="row"
                  spacing={1}
                  justifyContent={tools ? "space-between" : "flex-end"}
                  alignItems={"center"}
                >
                  {tools}
                  <Stack direction="row">
                    <Stack direction="row" spacing={1}>
                      {possibleRecipients.map((recipient) => (
                        <Checkboxes
                          size="small"
                          key={recipient}
                          name={`visibleFor.${recipient}`}
                          data={{ label: capitalize(recipient), value: true }}
                        />
                      ))}
                    </Stack>
                    <LoadingButton
                      variant="contained"
                      size="small"
                      type="submit"
                      loading={isSubmitting}
                      disabled={!values.note}
                    >
                      {isInternal ? "Add note" : "Send"}
                    </LoadingButton>
                  </Stack>
                </Stack>
              </Stack>
            </Paper>
          );
        }}
      />
    </Form>
  );
}
