import RequestsTable from "./RequestsTable";
import { Grid2, Tab, Tabs, Typography } from "@mui/material";
import { useState } from "react";
import {
  getFirestore,
  collection,
  Timestamp,
  query,
  Query,
  where,
  orderBy,
  CollectionReference,
} from "@firebase/firestore";
import { INSTITUTION_REQUEST_STATUSES } from "../../config/constants";
import { subDays } from "date-fns";
import PageContainer from "../layout/PageContainer";
import PageGridContainer from "../layout/PageGridContainer";
import DialogManager from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import RequestDetailsDialog from "./dialogs/RequestDetailsDialog";

const StatusTabs = ({ status, setStatus }: { status: any; setStatus: any }) => (
  <Tabs
    value={INSTITUTION_REQUEST_STATUSES.findIndex((s) => s.days === status)}
    onChange={(_, index) => setStatus(INSTITUTION_REQUEST_STATUSES[index].days)}
  >
    {INSTITUTION_REQUEST_STATUSES.map((s) => (
      <Tab key={s.days} label={s.label} />
    ))}
  </Tabs>
);

const RequestsTableBase = ({
  initCollection,
  companyTable = false,
}: {
  initCollection?: Query;
  companyTable?: boolean;
}) => {
  const [status, setStatus] = useState(1);

  const db = getFirestore();
  let queryReference: CollectionReference | Query = collection(
    db,
    "institutionRequests",
  );

  if (initCollection) {
    queryReference = initCollection;
  }

  if (status > 0) {
    queryReference = query(
      query(
        queryReference,
        where(
          "createTime",
          ">",
          Timestamp.fromDate(subDays(new Date(), status)),
        ),
      ),
    );
  }

  queryReference = query(queryReference, orderBy("createTime", "desc"));

  return (
    <DialogManager>
      <ManagedDialog
        id="institution-request-details"
        component={RequestDetailsDialog}
      />
      <PageContainer>
        <PageGridContainer>
          <Grid2 size={12}>
            <Typography variant="h6">Institution requests</Typography>
            <StatusTabs status={status} setStatus={setStatus} />
          </Grid2>
          <Grid2 container size="grow">
            <Grid2 size="grow">
              <RequestsTable queryReference={queryReference} />
            </Grid2>
          </Grid2>
        </PageGridContainer>
      </PageContainer>
    </DialogManager>
  );
};

export default RequestsTableBase;
