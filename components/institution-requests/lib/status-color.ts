import { InstitutionRequestStatus } from "../../../models/other/institution_request";
import { Color } from "./colors";

export function getColorForStatus(status: InstitutionRequestStatus): Color {
  switch (status) {
    case "pending":
      return "default";
    case "starting":
      return "default";
    case "running":
      return "info";
    case "waitingForBankId":
      return "warning";
    case "failed":
      return "error";
    case "completed":
      return "success";
    default:
      return "default";
  }
}
