import Link from "next/link";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { <PERSON>ton, Chip, useTheme } from "@mui/material";
import { DataGrid, GridActionsColDef, GridCellParams } from "@mui/x-data-grid";
import TableLoadingOverlay from "../tables/TableLoadingOverlay";
import { FIREBASE_URL } from "../../config/constants";
import { useCollection } from "react-firebase-hooks/firestore";
import { Query } from "@firebase/firestore";
import { formatTime } from "../../lib/format-time-relative";
import {
  InstitutionRequestError,
  InstitutionRequestStatus,
} from "../../models/other/institution_request";
import { Timestamp } from "firebase/firestore";
import { getColorForStatus } from "./lib/status-color";
import { getColorForType } from "./lib/type-color";
import { useDialog } from "../dialogs/DialogManager";
import { ExtendedGridColDef } from "../common/table/columns";

const columns: ExtendedGridColDef[] = [
  {
    field: "createTime",
    type: "dateTime",
    headerName: "Created at",
    width: 160,
    editable: true,
    valueFormatter: (value: Timestamp) => formatTime(value),
  },
  {
    field: "type",
    headerName: "Type",
    width: 150,
    renderCell: (params: GridCellParams) => (
      <Chip
        color={getColorForType(params.row.type)}
        size="small"
        label={params.row.type ?? ""}
      />
    ),
  },
  {
    field: "status",
    headerName: "Status",
    width: 150,
    renderCell: (params: GridCellParams) => {
      const status: InstitutionRequestStatus = params.row.status;

      return (
        <Chip
          color={getColorForStatus(status)}
          size="small"
          label={params.row.status ?? ""}
        />
      );
    },
  },
  {
    field: "requester",
    headerName: "Requester",
    width: 150,
    renderCell: (params: GridCellParams) => {
      const { requester } = params.row;
      if (!requester) {
        return <p>(no requester)</p>;
      }

      return (
        <Link passHref href={`/users/user/?id=${requester.id}`}>
          <Button color="primary" size="small">
            Show requester
          </Button>
        </Link>
      );
    },
  },
  {
    field: "related",
    headerName: "Related",
    renderCell: (params: GridCellParams) => {
      if (params.row.type !== "sendDeductionReport") {
        return null;
      }

      const invoices = params.row.input?.invoices as string[];

      return (
        <Chip
          color={"default"}
          size="small"
          label={`${invoices.length} invoices`}
        />
      );
    },
  },
  {
    field: "errors",
    headerName: "Errors",
    renderCell: (params: GridCellParams) => {
      const errors = params.row.errors ?? [];

      if (errors.length === 0) {
        return null;
      }

      return (
        <Chip
          color={
            errors.some((e: InstitutionRequestError) => e.fatal)
              ? "error"
              : "warning"
          }
          size="small"
          label={`${errors.length} errors`}
        />
      );
    },
  },
  {
    field: "id",
    headerName: "Actions",
    type: "actions",
    width: 200,
    getActions: ({ row }) => [
      <OpenDetailsButton key={`${row.id}-open-details`} request={row} />,
      <OpenFirestoreButton key={`${row.id}-open-firestore`} id={row.id} />,
    ],
  } as GridActionsColDef,
];

function OpenDetailsButton({ request }: { request: any }) {
  const { open } = useDialog("institution-request-details");
  return <Button onClick={() => open({ request })}>Details</Button>;
}

function OpenFirestoreButton({ id }: { id: string }) {
  return (
    <a
      target="_blank"
      rel="noopener noreferrer"
      href={`${FIREBASE_URL}/firestore/data/~2FinstitutionRequests~2F${id}`}
    >
      <Button color="warning" size="small" startIcon={<WhatshotIcon />}>
        Request
      </Button>
    </a>
  );
}

const RequestsTable = ({ queryReference }: { queryReference: Query }) => {
  const [value, loading] = useCollection(queryReference);

  const theme = useTheme();

  const docs = value?.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

  return (
    <DataGrid
      style={{ backgroundColor: theme.palette.background.paper }}
      slots={{
        loadingOverlay: TableLoadingOverlay,
      }}
      disableRowSelectionOnClick
      loading={loading}
      rows={docs ?? []}
      columns={columns}
    />
  );
};

export default RequestsTable;
