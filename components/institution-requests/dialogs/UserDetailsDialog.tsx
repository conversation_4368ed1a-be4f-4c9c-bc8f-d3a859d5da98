import { Dialog, Button } from "@mui/material";
import { DialogProps } from "../../dialogs/default-dialog-props";
import UserContainer from "../../user/UserContainer";

interface UserDetailsDialogProps extends DialogProps {
  customerId?: string;
}

export default function UserDetailsDialog({
  customerId,
  isOpen,
  close,
}: UserDetailsDialogProps) {
  return (
    <Dialog
      fullWidth
      maxWidth="lg"
      open={isOpen}
      onClose={() => {
        close();
      }}
    >
      <Button
        style={{ width: 100 }}
        onClick={() => {
          close();
        }}
      >
        Close
      </Button>
      {customerId && <UserContainer userId={customerId} />}
    </Dialog>
  );
}
