import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>A<PERSON>,
  <PERSON>,
} from "@mui/material";
import { TEMP_BUCKET } from "../../../config/constants";
import { getFileUrl } from "../../../lib/get-file-url";
import { InstitutionRequest } from "../../../models/other/institution_request";
import StorageImage from "../../common/StorageImage";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { getColorForStatus } from "../lib/status-color";

interface RequestDetailsDialogProps extends DialogProps {
  request?: InstitutionRequest;
}

export default function RequestDetailsDialog({
  request,
  isOpen,
  close,
}: RequestDetailsDialogProps) {
  return (
    <Dialog
      fullWidth
      maxWidth="lg"
      open={isOpen}
      onClose={() => {
        close();
      }}
    >
      <DialogTitle>
        Request &nbsp;
        <Chip color={"default"} size="small" label={request?.type ?? ""} />{" "}
        &nbsp;
        <Chip
          color={getColorForStatus(request?.status ?? "pending")}
          size="small"
          label={request?.status ?? ""}
        />
      </DialogTitle>
      <DialogContent>
        <Divider textAlign="left">Input</Divider>
        <code>{JSON.stringify(request?.input, null, 2)}</code>
        {(request?.errors ?? []).map((error, index) => {
          return (
            <>
              <Divider textAlign="left">
                <Chip
                  color={error.fatal ? "error" : "warning"}
                  label={`Error ${index + 1}`}
                />
              </Divider>
              <p>
                {error.message}
                {error.snapshotUrl && (
                  <Button
                    color="primary"
                    size="small"
                    onClick={() =>
                      getFileUrl(error.snapshotUrl ?? "", TEMP_BUCKET).then(
                        window.open,
                      )
                    }
                  >
                    View Snapshot
                  </Button>
                )}
              </p>
              {error.screenshotUrl && (
                <StorageImage
                  bucket={TEMP_BUCKET}
                  width={500}
                  height={400}
                  path={error.screenshotUrl}
                />
              )}
            </>
          );
        })}
      </DialogContent>
      <DialogActions>
        <Button
          style={{ width: 100 }}
          onClick={() => {
            close();
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
