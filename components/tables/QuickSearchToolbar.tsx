import { useSnackbar } from "notistack";
import { useState } from "react";
import { <PERSON>Field, IconButton, Switch, Stack, Tooltip } from "@mui/material";
import { downloadCsvFile } from "../../lib/download-csv-file";
import SearchIcon from "@mui/icons-material/Search";
import ExportIcon from "@mui/icons-material/GetApp";
import ClearIcon from "@mui/icons-material/Clear";
import { LoadingButton } from "@mui/lab";
import MapIcon from "@mui/icons-material/Map";

interface QuickSearchToolbarProps {
  clearSearch: () => void;
  onChange: (e: any) => void;
  value: string;
  rowsToExport: any;
  exportFileName: string;
  onMapIconClicked?: (checked: boolean) => void;
  onFilterTestsClicked?: (checked: boolean) => void;
  mapIconActive?: boolean;
  filterTestsActive?: boolean;
}

export function QuickSearchToolbar(props: QuickSearchToolbarProps) {
  const { enqueueSnackbar } = useSnackbar();
  const [exportCsvLoading, setExportCsvLoading] = useState(false);

  return (
    <div style={{ overflow: "scroll" }}>
      <Stack
        direction="row"
        spacing={0.5}
        style={{ marginLeft: 12, marginTop: 8 }}
      >
        <TextField
          size="small"
          style={{ width: 200 }}
          value={props.value}
          onChange={props.onChange}
          placeholder="Search…"
          slotProps={{
            input: {
              startAdornment: <SearchIcon fontSize="small" />,
              endAdornment: (
                <IconButton
                  title="Clear"
                  aria-label="Clear"
                  size="small"
                  style={{ visibility: props.value ? "visible" : "hidden" }}
                  onClick={props.clearSearch}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              ),
            },
          }}
        />

        {props.onFilterTestsClicked && (
          <div style={{ display: "flex", height: 40 }}>
            <Tooltip title="Test jobs">
              <Switch
                color="secondary"
                checked={props.filterTestsActive!}
                onChange={(event) => {
                  props.onFilterTestsClicked!(event.target.checked);
                }}
              />
            </Tooltip>
          </div>
        )}

        {props.onMapIconClicked && (
          <IconButton
            color={props.mapIconActive! ? "success" : "inherit"}
            style={{ height: 40 }}
            onClick={() => {
              props.onMapIconClicked!(!props.mapIconActive!);
            }}
          >
            <MapIcon />
          </IconButton>
        )}

        <LoadingButton
          startIcon={<ExportIcon />}
          color="primary"
          loadingPosition="start"
          loading={exportCsvLoading}
          onClick={async () => {
            setExportCsvLoading(true);

            const data = await props.rowsToExport;

            await downloadCsvFile(data, `done_${props.exportFileName}`);

            setExportCsvLoading(false);
            enqueueSnackbar(`${props.exportFileName} downloaded.`, {
              variant: "success",
            });
          }}
        ></LoadingButton>
      </Stack>
    </div>
  );
}

export default QuickSearchToolbar;
