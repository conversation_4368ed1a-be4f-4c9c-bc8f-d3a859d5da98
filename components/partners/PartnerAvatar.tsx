import { partnerRef } from "../../models/other/partner";
import { Avatar, AvatarProps } from "@mui/material";
import { useDocumentDataOnce } from "react-firebase-hooks/firestore";

interface PartnerAvatarProps extends Omit<AvatarProps, "src"> {
  partnerId: string;
  size?: number;
}

export default function PartnerAvatar({
  partnerId,
  ...props
}: PartnerAvatarProps) {
  const [partner] = useDocumentDataOnce(partnerRef(partnerId));
  return <Avatar src={partner?.smallLogo} {...props} />;
}
