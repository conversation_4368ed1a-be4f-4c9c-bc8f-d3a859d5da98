import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Checkbox,
} from "@mui/material";
import {
  doc,
  collection,
  updateDoc,
  serverTimestamp,
  deleteField,
  getFirestore,
} from "firebase/firestore";
import { FC } from "react";
import { usePartner } from "../../hooks/partner/use-partner";
import Loading from "../common/Loading";
import PageContainer from "../layout/PageContainer";
import { Header } from "../layout/panels";
import { Fullscreen } from "@mui/icons-material";
import Link from "next/link";
import { useUser } from "../../hooks/user/use-user";
import { Partner } from "../../models/other/partner";
import UserAvatar from "../user/UserAvatar";
import { useDialog } from "../dialogs/DialogManager";
import { SearchData } from "../../lib/services/global-search";
import { User } from "../../models/user/user";
import NextImage from "next/image";
import removeUndefined from "../../lib/remove-undefined";

interface PartnerUserManagementProps {
  partnerId: string;
}

const PartnerUserManagement: FC<PartnerUserManagementProps> = ({
  partnerId,
}) => {
  const { open: openGlobalSearchDialog } = useDialog("global-search");
  const [partner, loading] = usePartner(partnerId);

  const db = getFirestore();

  if (loading) return <Loading />;

  if (!partner) return <p>Partner not found</p>;

  return (
    <PageContainer tight key={partner.reference.id}>
      <Stack>
        <Header title={`${partner.name} user management`}>
          <Stack spacing={2} alignItems="center" direction="row">
            <Button
              onClick={() =>
                openGlobalSearchDialog({
                  title: "Select user to add partner",
                  filteredIds: partner.users && Object.keys(partner.users),
                  defaultOptions: ["users"],
                  onItemSelected: async (selectedItem: SearchData) => {
                    const userDoc = doc(
                      collection(db, "users"),
                      selectedItem.id,
                    );

                    await updateDoc(
                      partner.reference,
                      removeUndefined({
                        [`users.${userDoc.id}`]: {
                          role: "admin",
                          fullName: selectedItem.payload.fullName,
                          reference: userDoc,
                          lastUpdatedAt: serverTimestamp(),
                        },
                      }),
                    );
                  },
                })
              }
              variant="text"
            >
              + Add
            </Button>

            <Box flex={1} />

            {partner.logo && (
              <NextImage
                alt="Partner logo"
                height={54}
                width={250}
                src={partner.logo}
                style={{ objectFit: "contain" }}
              />
            )}
          </Stack>
        </Header>

        {!partner.users && <p>No users</p>}

        {partner.users && (
          <TableContainer>
            <Table size="small" aria-label="a table">
              <TableHead>
                <TableRow>
                  <TableCell>Avatar</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Finance administrator</TableCell>
                  <TableCell align="right">Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {partner.users &&
                  Object.keys(partner.users).map((userId) => (
                    <PartnerUserRow
                      key={userId}
                      userId={userId}
                      partner={partner}
                    />
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Stack>
    </PageContainer>
  );
};

function PartnerUserRow({
  userId,
  partner,
}: {
  userId: string;
  partner: Partner;
}) {
  const [user, loading] = useUser(userId);

  if (loading || !user) return null;

  return (
    <TableRow>
      <TableCell width={10}>
        <UserAvatar size="small" userRef={user.reference} />
      </TableCell>
      <TableCell width={250}>
        {user.firstName} {user.lastName}
      </TableCell>
      <TableCell align="left" width={200}>
        <UserFinanceAdminCheckbox
          partner={partner}
          user={user}
          allowed={partner.financeAdministrators
            ?.map((e) => e.id)
            ?.includes(user.reference.id)}
        />
      </TableCell>
      <TableCell align="right">
        <Link
          passHref
          target="_blank"
          rel="noopener noreferrer"
          href={`/users/user/?id=${user.reference.id}`}
        >
          <Button startIcon={<Fullscreen />} size="small">
            Go to user
          </Button>
        </Link>
        <Button
          color="error"
          onClick={async () => {
            if (!window.confirm("Are you sure?")) return;
            await updateDoc(partner.reference, {
              [`users.${userId}`]: deleteField(),
            });
          }}
        >
          Remove
        </Button>
      </TableCell>
    </TableRow>
  );
}

function UserFinanceAdminCheckbox({
  partner,
  allowed = false,
  user,
}: {
  partner: Partner;
  user: User;
  allowed?: boolean;
}) {
  return (
    <Checkbox
      checked={allowed}
      onChange={async (event) => {
        await updateDoc(partner.reference, {
          financeAdministrators: event.target.checked
            ? [...(partner.financeAdministrators ?? []), user.reference]
            : partner.financeAdministrators?.filter(
                (partnerUser) => user.reference.id !== partnerUser.id,
              ),
        });
      }}
    />
  );
}

export default PartnerUserManagement;
