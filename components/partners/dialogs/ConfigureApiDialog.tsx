import { updateDoc } from "firebase/firestore";
import FormDialog from "../../dialogs/FormDialog";
import { DialogProps } from "../../dialogs/default-dialog-props";
import { Switches } from "mui-rff";
import removeUndefined from "../../../lib/remove-undefined";
import { partnerRef } from "../../../models/other/partner";
import Condition from "../../form-fields/Condition";
import TokenCreator from "../../integrations/TokenCreator";

interface ConfigureApiDialogProps extends DialogProps {
  apiEnabled?: boolean;
  partnerId?: string;
}

export default function ConfigureApiDialog({
  apiEnabled = false,
  partnerId,
  isOpen,
  close,
}: ConfigureApiDialogProps) {
  return (
    <FormDialog
      title={"Partner API settings"}
      initialValues={{ apiEnabled }}
      isOpen={isOpen}
      close={close}
      onSubmit={async (values) => {
        const partner = partnerRef(partnerId);
        if (!partner) throw new Error("PartnerId is required");
        await updateDoc(
          partner,
          removeUndefined({ apiEnabled: values.apiEnabled }),
        );
      }}
    >
      <Switches
        name="apiEnabled"
        data={{
          label: "API Enabled",
          value: true,
        }}
      />
      <Condition when={"apiEnabled"} is={true}>
        <TokenCreator partnerId={partnerId} />
      </Condition>
    </FormDialog>
  );
}
