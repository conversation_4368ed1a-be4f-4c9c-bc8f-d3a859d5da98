import { usePartners } from "../../hooks/partner/partnersHook";
import {
  IconButton,
  CircularProgress,
  Tooltip,
  useTheme,
  Button,
  Badge,
  Typography,
  Grid2,
} from "@mui/material";
import {
  DataGrid,
  GridActionsCellItem,
  GridActionsColDef,
  GridRenderEditCellParams,
  useGridApiContext,
} from "@mui/x-data-grid";
import {
  ImageNotSupported,
  AddPhotoAlternate,
  UploadFile,
  Settings,
  Check,
} from "@mui/icons-material";
import DialogManager, { useDialog } from "../dialogs/DialogManager";
import PageContainer from "../layout/PageContainer";
import PageGridContainer from "../layout/PageGridContainer";
import NextImage from "next/image";
import {
  getEditable,
  useDocumentUpdate,
} from "../../lib/hooks/use-document-update";
import { getDownloadURL, getStorage, ref, uploadBytes } from "firebase/storage";
import { collection, getFirestore } from "firebase/firestore";
import Router, { useRouter } from "next/router";
import PeopleIcon from "@mui/icons-material/People";
import { useState } from "react";
import { Partner, PartnerTier } from "../../models/other/partner";
import {
  dotPropColumn,
  ExtendedGridColDef,
  labeledValueColumn,
} from "../common/table/columns";
import ManagedDialog from "../dialogs/ManagedDialog";
import ConfigureApiDialog from "./dialogs/ConfigureApiDialog";
import { useBecomePartner } from "../auth/AuthProvider";

function UploadButton(props: GridRenderEditCellParams) {
  const { id, field } = props;
  const apiRef = useGridApiContext();
  const [queued, setQueued] = useState(false);
  return (
    <IconButton
      color="primary"
      aria-label="upload picture"
      component="label"
      style={{ position: "absolute" }}
    >
      <input
        hidden
        accept="image/*"
        type="file"
        onChange={(event) => {
          const newValue = event.target.files?.item(0); // The new value entered by the user
          apiRef.current.setEditCellValue({ id, field, value: newValue });
          setQueued(true);
        }}
      />
      {queued ? (
        <Tooltip title="Upload queued" arrow>
          <UploadFile />
        </Tooltip>
      ) : (
        <Tooltip title="Select logo" arrow>
          <AddPhotoAlternate />
        </Tooltip>
      )}
    </IconButton>
  );
}

function Image({
  src,
  alt,
  width,
}: {
  src: string | File;
  alt: string;
  width: number;
}) {
  if (!src) {
    return <ImageNotSupported color="disabled" />;
  }

  if (src instanceof File) {
    return <CircularProgress size={"2em"} />;
  }

  return (
    <NextImage
      alt={alt}
      width={width}
      height={40}
      src={src}
      style={{ objectFit: "contain" }}
    />
  );
}

function OpenUserManagementButton({
  partnerId,
  partner,
}: {
  partnerId: string;
  partner: Partner;
}) {
  const router = useRouter();
  return (
    <GridActionsCellItem
      icon={
        <Tooltip title="Partner user manager" arrow>
          <Badge
            color="secondary"
            badgeContent={partner.users ? Object.keys(partner.users).length : 0}
          >
            <PeopleIcon />
          </Badge>
        </Tooltip>
      }
      onClick={() => {
        router.push(
          `/partners/partnerUserManagement/?partnerId=${encodeURIComponent(
            partnerId,
          )}`,
        );
      }}
      label=""
    />
  );
}

function ApiConfigureButton({
  apiEnabled,
  partnerId,
}: {
  apiEnabled: boolean;
  partnerId: string;
}) {
  const { open } = useDialog("configure-api");
  return (
    <GridActionsCellItem
      icon={
        <Badge
          slotProps={{ badge: { style: { padding: 0 } } }}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          badgeContent={
            <Typography fontSize={"small"} lineHeight={0}>
              <Check fontSize="inherit" />
            </Typography>
          }
          invisible={!apiEnabled}
          color="success"
        >
          <Tooltip title="API settings" arrow>
            <Settings />
          </Tooltip>
        </Badge>
      }
      onClick={() => {
        open({ apiEnabled, partnerId });
      }}
      label="API settings"
    />
  );
}

function BecomePartnerButton({ partnerId }: { partnerId: string }) {
  const becomePartner = useBecomePartner();
  return (
    <Button
      variant="outlined"
      onClick={() => {
        becomePartner(partnerId);
        Router.push(`/`);
      }}
    >
      View Backoffice
    </Button>
  );
}

const columns: ExtendedGridColDef[] = [
  {
    field: "logo",
    headerName: "Logo",
    width: 120,
    align: "center",
    editable: true,
    renderCell: ({ value }) => <Image alt="Logo" width={100} src={value} />,
    renderEditCell: (props) => <UploadButton {...props} />,
  },
  {
    field: "smallLogo",
    headerName: "Small Logo",
    align: "center",
    editable: true,
    renderCell: ({ value }) => (
      <Image alt="Small Logo" width={40} src={value} />
    ),
    renderEditCell: (props) => <UploadButton {...props} />,
  },
  {
    field: "name",
    headerName: "Name",
    width: 150,
    editable: true,
  },
  labeledValueColumn({
    field: "tier",
    editable: true,
    headerName: "Tier",
    allowNull: false,
    values: [
      {
        value: PartnerTier.None,
        label: "None",
      },
      {
        value: PartnerTier.Standard,
        label: "Standard",
      },
      {
        value: PartnerTier.Platform,
        label: "Platform",
      },
    ],
  }),
  {
    field: "apiEnabled",
    headerName: "API Enabled",
    type: "boolean",
    editable: false,
    renderCell: ({ value, row }) => (
      <ApiConfigureButton apiEnabled={value} partnerId={row.reference.id} />
    ),
  },
  dotPropColumn({
    field: "messages.jobDoneMessage",
    headerName: "Job done message",
    editable: true,
    width: 300,
  }),
  {
    field: "reportUrl",
    headerName: "Looker studio report url",
    type: "string",
    // type: "url",
    width: 300,
    editable: true,
  },
  {
    field: "users",
    headerName: "Users",
    type: "actions",
    getActions: ({ row }) => [
      <OpenUserManagementButton
        key={`open-${row.id}`}
        partnerId={row.id}
        partner={row}
      />,
    ],
  } as GridActionsColDef,
  {
    field: "partnerView",
    headerName: "Partner view",
    align: "center",
    width: 160,

    renderCell: ({ row }) => <BecomePartnerButton partnerId={row.id} />,
  },
  {
    field: "warnings",
    headerName: "Warnings",
    align: "left",
    width: 300,
    renderCell: ({ row }) => {
      if (!row.users || !Object.keys(row.users).length)
        return (
          <Typography variant="body2" color="error.main">
            ⚠️ Missing users
          </Typography>
        );

      return row.financeAdministrators?.length ? null : (
        <Typography variant="body2" color="warning.main">
          ⚠️ Missing finance admin
        </Typography>
      );
    },
  },
];

const editableProps = getEditable(columns);

async function urlImage(partnerId: string, prop: string, file: string | File) {
  if (!(file instanceof File)) {
    return file;
  }
  const storageRef = getStorage();
  const imageRef = ref(
    storageRef,
    `partner-documents-v1/${partnerId}/logos/${prop}`,
  );
  await uploadBytes(imageRef, file);
  return await getDownloadURL(imageRef);
}

export default function PartnersTable() {
  const { partners, loading } = usePartners();
  const theme = useTheme();
  const partnersCollection = collection(getFirestore(), "partners");

  const [commitUpdate, updating] = useDocumentUpdate(
    partnersCollection,
    editableProps,
  );

  const docs = partners?.map((partner) => ({
    id: partner.reference.id,
    ...partner,
  }));

  return (
    <DialogManager>
      <ManagedDialog component={ConfigureApiDialog} id={"configure-api"} />
      <PageContainer>
        <PageGridContainer>
          <Grid2 container size="grow">
            <Grid2 size="grow">
              <DataGrid
                style={{ backgroundColor: theme.palette.background.paper }}
                showCellVerticalBorder
                rows={docs ?? []}
                columns={columns}
                slotProps={{
                  loadingOverlay: {
                    variant: "linear-progress",
                  },
                }}
                loading={loading || updating}
                disableRowSelectionOnClick
                editMode={"row"}
                processRowUpdate={async (row) => {
                  const [logo, smallLogo] = await Promise.all([
                    await urlImage(row.id, "logo", row.logo),
                    await urlImage(row.id, "smallLogo", row.smallLogo),
                  ]);

                  commitUpdate({ ...row, logo, smallLogo });
                  return row;
                }}
              />
            </Grid2>
          </Grid2>
        </PageGridContainer>
      </PageContainer>
    </DialogManager>
  );
}
