import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Grid2,
} from "@mui/material";
import { Field } from "react-final-form";
import { TextField } from "mui-rff";
import { FileDrop } from "react-file-drop";
import ImageIcon from "@mui/icons-material/Image";
import { ref, getStorage, uploadBytes, getDownloadURL } from "firebase/storage";

const IMAGE_TYPES = ["image/jpeg", "image/png"];

const Item = ({
  index,
  name,
  onRemove,
}: {
  index: number;
  name: string;
  onRemove: () => {};
}) => {
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<null | string>(null);

  const storage = getStorage();

  return (
    <Card
      variant="outlined"
      style={{ marginBottom: "15px", zIndex: 9999999999 }}
    >
      <CardHeader title={`Item #${index}`} />
      <CardContent>
        <Grid2 container spacing={2}>
          <Grid2 size={{ xs: 12, md: 6 }}>
            <TextField
              name={`${name}.titleEn`}
              label="Title (en)"
              variant="outlined"
              size="small"
            />
            <br />
            <br />
            <TextField
              name={`${name}.titleSv`}
              label="Title (sv)"
              variant="outlined"
              size="small"
            />

            <br />
            <br />

            <TextField
              name={`${name}.url`}
              label="Url"
              variant="outlined"
              size="small"
              fullWidth
            />
          </Grid2>

          <Grid2 size={{ xs: 12, md: 6 }}>
            <Field
              name={`${name}.backgroundImageRef`}
              label="Background image (ref)"
              variant="outlined"
            >
              {(props) => {
                if (!props.input.value)
                  return (
                    <FileDrop
                      onDrop={async (files, _) => {
                        if (!files) return;

                        const fileToSent = files[0];

                        if (fileToSent) {
                          setLoading(true);

                          if (IMAGE_TYPES.includes(fileToSent.type)) {
                            const fileRef = ref(
                              storage,
                              `content/${fileToSent.name}`,
                            );

                            await uploadBytes(fileRef, fileToSent);

                            setLoading(false);
                            props.input.onChange(`content/${fileToSent.name}`);
                          } else {
                            setError(true);
                            setLoading(false);
                          }
                        }
                      }}
                    >
                      <div
                        style={{
                          width: 200,
                          borderRadius: "8px",
                          border: "2px solid black",
                          borderStyle: "dotted",
                          marginLeft: "10px",
                          marginRight: "16px",
                          color: "black",
                          padding: "20px",
                        }}
                      >
                        <b>
                          Drop new background
                          <br />
                          image here!
                        </b>
                      </div>
                    </FileDrop>
                  );

                const pathReference = ref(storage, props.input.value);

                getDownloadURL(pathReference).then(setImageUrl);

                return (
                  <>
                    <Avatar
                      style={{ width: "200px", height: "86px" }}
                      variant="rounded"
                      src={imageUrl ?? "any"}
                    >
                      <ImageIcon />
                    </Avatar>

                    <p>{props.input.value}</p>

                    {props.input.value && (
                      <Button
                        style={{ color: "red", borderColor: "red" }}
                        variant="outlined"
                        size="small"
                        onClick={() => {
                          props.input.onChange(null);
                        }}
                      >
                        Remove BG Image
                      </Button>
                    )}
                  </>
                );
              }}
            </Field>
          </Grid2>

          {loading && <CircularProgress />}
        </Grid2>

        <br />
        {error && (
          <Alert severity="warning">
            Image name should not contain any spaces. Supported formats:
            png/jpeg
          </Alert>
        )}

        <Button
          type="button"
          variant="outlined"
          onClick={onRemove}
          style={{ color: "red", borderColor: "red" }}
        >
          Remove item
        </Button>
      </CardContent>
    </Card>
  );
};

export default Item;
