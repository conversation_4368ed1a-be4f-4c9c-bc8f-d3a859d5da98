import Link from "next/link";
import {
  CircularProgress,
  Divider,
  ListItem,
  ListItemText,
  Paper,
  Stack,
} from "@mui/material";
import { collection, getFirestore } from "@firebase/firestore";
import { useCollection } from "react-firebase-hooks/firestore";

const CategoryList = () => {
  const db = getFirestore();
  const collectionReference = collection(
    db,
    "admin/customerHome/jobCategories",
  );
  const [value, loading, error] = useCollection(collectionReference);
  if (loading) return <CircularProgress style={{ margin: "32px" }} />;
  if (error) return <pre>{JSON.stringify(error)}</pre>;

  if (!value) return null;

  const docs = value.docs;

  return (
    <Paper variant="outlined">
      <Stack margin={2} divider={<Divider flexItem />}>
        {docs.map((doc) => (
          <div key={doc.id}>
            <Link href={`cmsCategories/${doc.id}`} passHref>
              <ListItem>
                <ListItemText primary={doc.id} />
              </ListItem>
            </Link>
          </div>
        ))}
      </Stack>
    </Paper>
  );
};

export default CategoryList;
