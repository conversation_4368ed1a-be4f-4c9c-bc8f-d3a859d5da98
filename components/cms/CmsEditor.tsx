import { useDocumentData } from "react-firebase-hooks/firestore";

import { Form } from "react-final-form";
import { FieldArray } from "react-final-form-arrays";
import arrayMutators from "final-form-arrays";
import Section from "./Section";
import { Button, Stack, Typography, Paper, Alert } from "@mui/material";
import {
  collection,
  DocumentReference,
  getFirestore,
  serverTimestamp,
  setDoc,
  doc,
} from "@firebase/firestore";
import CategoryList from "./CategoryList";

const CmsEditor = ({ reference }: { reference?: DocumentReference }) => {
  const documentReference =
    reference || doc(collection(getFirestore(), "admin"), "customerHome");

  const [value, loading, error] = useDocumentData(documentReference);
  if (loading) return null;
  if (error) return <> {JSON.stringify(error)} </>;

  const sections = value?.sections || [];

  return (
    <>
      <Alert severity="info">Select category to edit</Alert>

      <CategoryList />

      <Form
        onSubmit={({ sections }: { sections: any }) => {
          setDoc(
            documentReference,
            {
              sections,
              createTime: serverTimestamp(),
            },
            { merge: true },
          );
        }}
        initialValues={{ sections }}
        mutators={{
          ...arrayMutators,
        }}
      >
        {(props: any) => {
          return (
            <form onSubmit={props.handleSubmit}>
              <Stack padding={2} spacing={1}>
                <Paper sx={{ padding: 2 }} variant="outlined">
                  <Typography variant="h6">Edit customer home</Typography>
                </Paper>

                <FieldArray name="sections">
                  {({ fields }) => (
                    <>
                      {fields.map((name: string, index: number) => (
                        <Section key={index} name={name} index={index} />
                      ))}
                      <Button
                        variant="outlined"
                        type="button"
                        onClick={() =>
                          fields.push({
                            itemSize: "small",
                            items: [],
                            subtitleEn: null,
                            subtitleSv: null,
                            titleEn: null,
                            titleSv: null,
                          })
                        }
                      >
                        New section
                      </Button>
                    </>
                  )}
                </FieldArray>

                <Button variant="contained" color="primary" type="submit">
                  Save
                </Button>
              </Stack>
            </form>
          );
        }}
      </Form>
    </>
  );
};

export default CmsEditor;
