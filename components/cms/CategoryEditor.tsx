import arrayMutators from "final-form-arrays";
import Section from "./Section";
import { <PERSON><PERSON>, Card, Stack, Typography } from "@mui/material";
import { DocumentReference, serverTimestamp, setDoc } from "firebase/firestore";
import { FieldArray } from "react-final-form-arrays";
import { Form } from "react-final-form";
import { TextField } from "mui-rff";
import { useDocumentData } from "react-firebase-hooks/firestore";

const CategoryEditor = ({ docRef }: { docRef: DocumentReference }) => {
  const [value, _, loading] = useDocumentData(docRef);
  if (loading) return null;

  if (!value) return null;

  const sections = value.sections || [];
  const bodyMarkdownEn = value.bodyMarkdownEn;
  const bodyMarkdownSv = value.bodyMarkdownSv;

  return (
    <Form
      onSubmit={({ sections, bodyMarkdownEn, bodyMarkdownSv }) => {
        setDoc(
          docRef,
          {
            bodyMarkdownEn,
            bodyMarkdownSv,
            sections,
            createTime: serverTimestamp(),
          },
          { merge: true },
        );
      }}
      initialValues={{ sections, bodyMarkdownEn, bodyMarkdownSv }}
      mutators={{
        ...arrayMutators,
      }}
    >
      {(props: any) => {
        return (
          <form style={{ padding: "20px" }} onSubmit={props.handleSubmit}>
            <Stack spacing={2}>
              <Card variant="outlined" style={{ padding: 12 }}>
                <Stack spacing={3.5}>
                  <Typography variant="h5" padding={2}>
                    Edit {docRef.id} category
                  </Typography>

                  <TextField
                    name="bodyMarkdownEn"
                    label="Body (en)"
                    variant="outlined"
                    fullWidth
                    multiline
                  />
                  <TextField
                    name="bodyMarkdownSv"
                    label="Body (sv)"
                    variant="outlined"
                    fullWidth
                    multiline
                  />
                </Stack>
              </Card>
              <FieldArray name="sections">
                {({ fields }) => (
                  <>
                    {fields.map((name, index) => (
                      <Section key={index} name={name} index={index} />
                    ))}
                    <Button
                      variant="outlined"
                      type="button"
                      onClick={() =>
                        fields.push({
                          itemSize: "small",
                          items: [],
                          subtitleEn: null,
                          subtitleSv: null,
                          titleEn: null,
                          titleSv: null,
                        })
                      }
                    >
                      New section
                    </Button>
                  </>
                )}
              </FieldArray>

              <Button variant="contained" color="primary" type="submit">
                Save
              </Button>
            </Stack>
          </form>
        );
      }}
    </Form>
  );
};

export default CategoryEditor;
