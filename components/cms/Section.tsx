import Item from "./Item";
import { FieldArray } from "react-final-form-arrays";
import { TextField } from "mui-rff";
import { Card, Button, Stack, Box } from "@mui/material";
import { Add } from "@mui/icons-material";

const Section = ({ index, name }: { index: number; name: string }) => {
  return (
    <Card variant="outlined">
      <Stack padding={2} spacing={3}>
        <h2> Section #{index}</h2>

        <TextField
          name={`${name}.titleEn`}
          label="Title (en)"
          variant="outlined"
        />

        <TextField
          name={`${name}.titleSv`}
          label="Title (sv)"
          variant="outlined"
        />
      </Stack>

      <FieldArray name={`${name}.items`}>
        {({ fields }) => (
          <>
            {fields.map((name, index) => (
              <Box style={{ padding: 8, margin: 8 }} key={index}>
                <Item
                  key={index}
                  name={name}
                  index={index}
                  onRemove={() => fields.remove(index)}
                />

                {index != 0 && (
                  <Button
                    onClick={() => {
                      fields.swap(index, index - 1);
                    }}
                  >
                    Up
                  </Button>
                )}

                {index != fields.length! - 1 && (
                  <Button
                    onClick={() => {
                      fields.swap(index, index + 1);
                    }}
                  >
                    Down
                  </Button>
                )}
              </Box>
            ))}

            <Button
              type="button"
              startIcon={<Add />}
              onClick={() =>
                fields.push({
                  color: "coral",
                  icon: "illustrationElectricity",
                  titleEn: null,
                  titleSv: null,
                  url: null,
                  backgroundImageRef: null,
                })
              }
            >
              New item
            </Button>
          </>
        )}
      </FieldArray>
    </Card>
  );
};

export default Section;
