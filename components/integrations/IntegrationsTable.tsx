import { GridActionsCellItem } from "@mui/x-data-grid";
import PlainDataGrid, { TableRow } from "../common/table/PlainDataGrid";
import {
  query,
  collection,
  getFirestore,
  where,
  DocumentReference,
  QuerySnapshot,
} from "firebase/firestore";
import {
  actionsColumn,
  dateTimeColumn,
  ExtendedGridColDef,
  stringColumn,
} from "../common/table/columns";
import { useDialog } from "../dialogs/DialogManager";
import { Tooltip } from "@mui/material";
import { Delete } from "@mui/icons-material";

const columns: ExtendedGridColDef[] = [
  dateTimeColumn({
    field: "createTime",
    headerName: "Created",
    width: 160,
  }),
  stringColumn({
    field: "name",
    headerName: "Name",
    width: 160,
  }),
  actionsColumn({
    field: "id",
    headerName: "Actions",
    renderCell: ({ row }) => [
      <DeleteKeyButton keyId={row.id} key={`delete-key-${row.id}`} />,
    ],
  }),
];

function DeleteKeyButton(props: { keyId: string }) {
  const { open } = useDialog("delete-key");
  return (
    <GridActionsCellItem
      icon={
        <Tooltip title="Delete key" arrow>
          <Delete />
        </Tooltip>
      }
      onClick={() => {
        open(props);
      }}
      label="Delete key"
    />
  );
}

function rowGenerator(value?: QuerySnapshot): TableRow[] {
  return (
    value?.docs.map((document) => {
      const data = document.data();
      return {
        id: document.id,
        paginationCursor: data.createTime,
        ...data,
      };
    }) ?? []
  );
}

interface IntegrationsTableProps {
  partner?: DocumentReference;
}

export default function IntegrationsTable({ partner }: IntegrationsTableProps) {
  if (!partner) {
    return null;
  }

  const queryRef = query(
    collection(getFirestore(), "integrations"),
    where("partner", "==", partner),
  );

  return (
    <PlainDataGrid
      queryReference={queryRef}
      columns={columns}
      rowGenerator={rowGenerator}
      sx={{ height: 400 }}
    />
  );
}
