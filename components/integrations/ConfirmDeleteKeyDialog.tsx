import { Alert } from "@mui/material";
import { collection, doc, getFirestore, deleteDoc } from "firebase/firestore";
import ConfirmDialog, { ConfirmActions } from "../dialogs/ConfirmDialog";
import { DialogProps } from "../dialogs/default-dialog-props";

interface ConfirmDeleteKeyDialogProps extends DialogProps {
  keyId?: string;
}

export default function ConfirmDeleteKeyDialog({
  isOpen,
  close,
  keyId,
}: ConfirmDeleteKeyDialogProps) {
  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={close}
      title="Delete API key?"
      description="Are you sure you want to delete this key?"
      actions={[
        {
          title: "Delete key",
          id: "delete",
          color: "error",
          onClick: async () => {
            if (!keyId) return;
            await deleteDoc(
              doc(collection(getFirestore(), "integrations"), keyId),
            );
          },
        },
        ConfirmActions.cancel,
      ]}
    >
      <Alert severity="error">
        Any integrations using this key will stop working immediately. This
        action cannot be undone.
      </Alert>
    </ConfirmDialog>
  );
}
