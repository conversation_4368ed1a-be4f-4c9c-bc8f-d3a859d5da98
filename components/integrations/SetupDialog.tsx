import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
} from "@mui/material";
import { DialogProps } from "../dialogs/default-dialog-props";
import React from "react";

export interface SetupDialogProps extends DialogProps {
  partnerId?: string;
}

interface BaseSetupDialogProps extends DialogProps {
  title: string;
  children: React.ReactNode;
  partnerId?: string;
}

export default function SetupDialog({
  isOpen,
  close,
  title,
  children,
}: BaseSetupDialogProps) {
  return (
    <Dialog
      open={isOpen}
      onClose={(_, reason) => {
        // Don't close if the user clicked outside the dialog to prevent data loss.
        if (reason === "backdropClick") return;
        close();
      }}
    >
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>{children}</DialogContent>
      <DialogActions>
        <Button onClick={close}>Close</Button>
      </DialogActions>
    </Dialog>
  );
}
