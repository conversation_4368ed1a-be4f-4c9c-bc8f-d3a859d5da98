import {
  ChipProps,
  CardContent,
  Typography,
  CardActions,
  Card,
  Chip,
  Grid2,
} from "@mui/material";

interface IntegrationCardProps {
  title: string;
  children: React.ReactNode;
  chip?: Pick<ChipProps, "label" | "color">;
  actions?: React.ReactNode;
}

export default function IntegrationCard({
  title,
  children,
  chip,
  actions,
}: IntegrationCardProps) {
  return (
    <Grid2 size={{ sm: 12, md: 4, xl: 3 }}>
      <Card>
        <CardContent>
          <Typography gutterBottom variant="h5" component="div">
            {title} {chip ? <Chip {...chip} size="small" /> : null}
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "text.secondary", minHeight: 70 }}
          >
            {children}
          </Typography>
        </CardContent>
        <CardActions>{actions}</CardActions>
      </Card>
    </Grid2>
  );
}
