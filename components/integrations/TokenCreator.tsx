import { LoadingButton } from "@mui/lab";
import { useState } from "react";
import { getApp } from "firebase/app";
import { httpsCallable, getFunctions } from "firebase/functions";
import { Alert, Typography } from "@mui/material";

interface TokenCreatorProps {
  partnerId?: string;
  keyName?: string;
}

export default function TokenCreator({
  partnerId,
  keyName = "Done API token",
}: TokenCreatorProps) {
  const [sending, setSending] = useState(false);
  const [token, setToken] = useState<string>();
  const [error, setError] = useState<unknown>();

  return (
    <>
      <LoadingButton
        size="small"
        loading={sending}
        disabled={!partnerId || Boolean(token)}
        variant="contained"
        color="primary"
        onClick={async () => {
          setError(undefined);
          setSending(true);
          try {
            const result = await httpsCallable<{}, { token: string }>(
              getFunctions(getApp(), "europe-west1"),
              "createApiToken",
            )({
              name: keyName,
              partner: partnerId,
            });
            setToken(result.data.token);
          } catch (error) {
            setError(error);
          }
          setSending(false);
        }}
      >
        Generate new API token
      </LoadingButton>
      {error && (
        <Alert severity="error">
          An error occurred:{" "}
          {[error instanceof Error ? error.message : "unknown error"]}
        </Alert>
      )}
      <TokenPayload token={token} />
    </>
  );
}

function TokenPayload({ token }: { token: string | undefined }) {
  if (!token) return null;
  return (
    <>
      <Alert severity="warning">The token will only be displayed once!</Alert>
      <Alert severity="warning" icon={false} variant="outlined">
        <Typography variant="body2" sx={{ wordWrap: "break-word" }}>
          {token}
        </Typography>
      </Alert>
    </>
  );
}
