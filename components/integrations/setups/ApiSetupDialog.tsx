import { Typography, Divider, Stack } from "@mui/material";
import SetupDialog, { SetupDialogProps } from "../SetupDialog";
import TokenCreator from "../TokenCreator";

export default function ApiSetupDialog({
  isOpen,
  close,
  partnerId,
}: SetupDialogProps) {
  return (
    <SetupDialog title={"API v2 setup"} isOpen={isOpen} close={close}>
      <Stack spacing={2}>
        <Typography>
          To setup the API v2 you need to create an API token. This token is
          used to authenticate your requests to the API.
        </Typography>
        <Typography>
          Once generated it will be displayed here only once. Make sure to copy
          it and keep it safe. You can always revoke tokens on the integrations
          page.
        </Typography>
        <Divider />
        <TokenCreator partnerId={partnerId} keyName="Done API token" />
      </Stack>
    </SetupDialog>
  );
}
