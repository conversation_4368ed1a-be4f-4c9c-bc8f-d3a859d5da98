import { useState } from "react";
import SetupDialog, { SetupDialogProps } from "../SetupDialog";
import {
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Typography,
  Button,
  Divider,
  Stack,
} from "@mui/material";
import TokenCreator from "../TokenCreator";

export default function MakeSetupDialog({
  isOpen,
  close,
  partnerId,
}: SetupDialogProps) {
  const [activeStep, setActiveStep] = useState(0);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  return (
    <SetupDialog
      title={"Make setup"}
      isOpen={isOpen}
      close={() => {
        setActiveStep(0);
        close();
      }}
    >
      <Stepper
        activeStep={activeStep}
        orientation="vertical"
        sx={{ minWidth: 400 }}
      >
        <Step key={0}>
          <StepLabel>Make account</StepLabel>
          <StepContent>
            <Stack spacing={2}>
              <Typography>
                To set up this integration you need a Make account.
              </Typography>
              <Stack direction="row" spacing={2}>
                <Button onClick={handleNext} variant="contained">
                  I have an account
                </Button>
                <Button href="https://www.make.com/" target="blank">
                  Open Make
                </Button>
              </Stack>
            </Stack>
          </StepContent>
        </Step>
        <Step key={1}>
          <StepLabel>Install Done app</StepLabel>
          <StepContent>
            <Stack spacing={2}>
              <Typography>Add the Done app to your Make account.</Typography>
              <Stack direction="row" spacing={2}>
                <Button
                  onClick={() => {
                    window.open(
                      "https://www.make.com/en/hq/app-invitation/fb8629b79c14f50bf444a03f012d2160",
                      "_blank",
                    );
                    handleNext();
                  }}
                  variant="contained"
                >
                  Install app
                </Button>
                <Button onClick={handleBack}>Go back</Button>
              </Stack>
            </Stack>
          </StepContent>
        </Step>
        <Step key={2}>
          <StepLabel>Setup connection</StepLabel>
          <StepContent>
            <Stack spacing={2}>
              <Typography>
                When you create a new scenario you will be asked for an API key
                when adding a Done step. You can generate it here now or later
                by running the setup again.
              </Typography>
              <Typography>
                Once generated the key will be displayed here only once. Make
                sure to copy it and paste it into Make. You can always revoke
                tokens on the integrations page.
              </Typography>
              <Divider />
              <TokenCreator partnerId={partnerId} keyName="Make API token" />
              <Stack direction="row" spacing={2}>
                <Button onClick={handleBack}>Go back</Button>
              </Stack>
            </Stack>
          </StepContent>
        </Step>
      </Stepper>
    </SetupDialog>
  );
}
