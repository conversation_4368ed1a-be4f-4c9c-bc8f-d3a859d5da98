{"compilerOptions": {"jsx": "preserve", "jsxImportSource": "@emotion/react", "target": "ES6", "lib": ["dom", "dom.iterable", "esnext"], "noImplicitReturns": true, "noUnusedLocals": true, "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true}, "compileOnSave": true, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}