import { useRouter } from "next/router";
import { FC } from "react";
import PageRestricted from "../../components/layout/PageRestricted";
import PartnerUserManagement from "../../components/partners/PartnerUserManagement";
import Done404Page from "../404";

const PartnerUserManagementPage: FC = () => {
  const router = useRouter();
  const partnerId = router.query.partnerId as string;

  return (
    <PageRestricted>
      {partnerId ? (
        <PartnerUserManagement partnerId={partnerId as string} />
      ) : (
        <Done404Page />
      )}
    </PageRestricted>
  );
};

export default PartnerUserManagementPage;
