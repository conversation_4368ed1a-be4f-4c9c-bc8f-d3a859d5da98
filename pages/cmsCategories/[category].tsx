import CategoryEditor from "../../components/cms/CategoryEditor";
import { collection, doc, getFirestore } from "@firebase/firestore";
import { useRouter } from "next/router";
import { FC } from "react";
import PageRestricted from "../../components/layout/PageRestricted";

const Category: FC = () => {
  const router = useRouter();
  const { category } = router.query;

  const categoryDocRef = doc(
    collection(getFirestore(), "admin"),
    `customerHome/jobCategories/${category}`,
  );

  return (
    <PageRestricted>
      <CategoryEditor docRef={categoryDocRef} />
    </PageRestricted>
  );
};

export default Category;
