import { useRouter } from "next/router";
import { FC } from "react";
import Job from "../../components/job/Job";

import Done404Page from "../404";
import { useAuthUser } from "../../components/auth/AuthProvider";
import PartnersJob from "../../components/job/PartnersJob";
import PageRestricted from "../../components/layout/PageRestricted";

const JobPage: FC = () => {
  const router = useRouter();
  const id = router.query.id;

  const auth = useAuthUser();

  if (!id) return <Done404Page />;

  if (auth.partner)
    return <PartnersJob displayGoToDetailButton={false} jobId={id as string} />;

  return (
    <PageRestricted>
      <Job displayGoToDetailButton={false} jobId={id as string} />
    </PageRestricted>
  );
};

export default JobPage;
