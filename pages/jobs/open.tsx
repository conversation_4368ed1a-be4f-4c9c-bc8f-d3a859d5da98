import { orderBy, query, where } from "firebase/firestore";
import { jobsCollection } from "../../models/job/job-provider";
import { DonePartnerReference } from "../../models/other/partner";
import { useMemo } from "react";
import { useCollection } from "react-firebase-hooks/firestore";
import _ from "lodash";
import { compile, JobFilters } from "../../components/filtering";
import { isDefined } from "../../lib/filter-undefined";
import FilterableOpenJobsPage from "../../components/job/filterable-overview/FilterableOpenJobsPage";

const coreFilter = compile({
  func: "isNotEqual",
  args: ["merchant.id", "elbilsvaruhuset"],
});

const initialGroups = [
  JobFilters.CoreGroup,
  JobFilters.PriorityGroup,
  JobFilters.TypeGroup,
  JobFilters.CustomersGroup,
  JobFilters.PrerequisitesGroup,
  JobFilters.SchedulingGroup,
];

export default function OpenOrdersPage() {
  const jobsQuery = useMemo(() => {
    return query(
      jobsCollection(),
      where("network", "==", DonePartnerReference()),
      where("status", "in", ["open", "inbox"]),
      orderBy("createTime", "desc"),
    );
  }, []);

  const [result] = useCollection(jobsQuery);

  const jobs =
    result?.docs
      ?.map((doc) => doc.data())
      // FIXME: This is a temporary hack to hide platform orders
      ?.filter(coreFilter.filter) ?? [];

  const groups = [
    ...initialGroups,
    JobFilters.MerchantsGroup(_.uniq(jobs.map((job) => job.merchant.id))),
    JobFilters.InstallersGroup(
      _.sortBy(
        _.uniqBy(
          jobs
            .map((job) =>
              job.company && job.cache?.companyName
                ? { id: job.company.id, name: job.cache.companyName }
                : undefined,
            )
            .filter(isDefined),
          "id",
        ),
        "name",
      ),
    ),
  ];

  return (
    <FilterableOpenJobsPage
      jobs={jobs}
      libraryFilterGroups={groups}
      configuration={"network"}
    />
  );
}
