import { DocumentReference, orderBy, query, where } from "firebase/firestore";
import { jobsCollection } from "../../models/job/job-provider";
import { useMemo } from "react";
import { useCollection } from "react-firebase-hooks/firestore";
import _ from "lodash";
import { JobFilters } from "../../components/filtering";
import { isDefined } from "../../lib/filter-undefined";
import FilterableOpenJobsPage from "../../components/job/filterable-overview/FilterableOpenJobsPage";
import { useAuthUser } from "../../components/auth/AuthProvider";

const initialGroups = [
  JobFilters.CoreGroup,
  JobFilters.TypeGroup,
  JobFilters.CustomersGroup,
  JobFilters.PrerequisitesGroup,
  JobFilters.SchedulingGroup,
];

export default function OpenOrdersPage() {
  const { partnerDoc } = useAuthUser();

  if (!partnerDoc) {
    return null;
  }

  return <PartnerOpenOrdersPage partnerRef={partnerDoc?.reference} />;
}

function PartnerOpenOrdersPage({
  partnerRef,
}: {
  partnerRef: DocumentReference;
}) {
  const jobsQuery = useMemo(() => {
    return query(
      jobsCollection(),
      where("merchant", "==", partnerRef),
      where("status", "in", ["open", "inbox"]),
      orderBy("createTime", "desc"),
    );
  }, []);

  const [result] = useCollection(jobsQuery);

  const jobs = result?.docs?.map((doc) => doc.data()) ?? [];

  const groups = [
    ...initialGroups,
    JobFilters.NetworksGroup(_.uniq(jobs.map((job) => job.network.id))),
    JobFilters.InstallersGroup(
      _.sortBy(
        _.uniqBy(
          jobs
            .map((job) =>
              job.company && job.cache?.companyName
                ? { id: job.company.id, name: job.cache.companyName }
                : undefined,
            )
            .filter(isDefined),
          "id",
        ),
        "name",
      ),
    ),
  ];

  return (
    <FilterableOpenJobsPage
      jobs={jobs}
      libraryFilterGroups={groups}
      configuration={"merchant"}
    />
  );
}
