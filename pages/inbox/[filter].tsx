import { useMemo, useState } from "react";
import { Issue, IssueStatus } from "../../models/inbox/issue";
import { inboxQuery } from "../../models/inbox/provider";
import IssuesStatusFilterButton from "../../components/issues/IssuesStatusFilterButton";
import { useAuthUser } from "../../components/auth/AuthProvider";
import { Assignee } from "../../components/issues/IssuesAssigneeFilterButton";
import { GetStaticPaths, GetStaticProps } from "next";
import { usePathname, useSearchParams } from "next/navigation";
import { useRouter } from "next/router";
import { useSelectedModel } from "../../lib/hooks/use-selected-model";
import InboxPage from "../../components/inbox/InboxPage";

function titleForFilter(filter?: string | string[]) {
  switch (filter) {
    case "my-inbox":
      return "My inbox";
    case "unassigned":
      return "Unassigned";
    case "all":
      return "All tasks";
  }
  return "Inbox";
}

export const getStaticPaths = (async () => {
  return {
    paths: [
      { params: { filter: "my-inbox" } },
      { params: { filter: "unassigned" } },
      { params: { filter: "all" } },
    ],
    fallback: false,
  };
}) satisfies GetStaticPaths;

export const getStaticProps = (async (context) => {
  return { props: { filter: context.params?.filter } };
}) satisfies GetStaticProps<{
  filter: string | string[] | undefined;
}>;

/**
 * Filtered Inbox Page Component
 * Displays a list of issues filtered by assignment and status.
 *
 * @param props.filter - The filter parameter from the URL ('my-inbox', 'unassigned', 'all')
 *                      Determines which issues to display based on assignment
 *
 * Features:
 * - Filters issues by status (open/closed) and assignment
 * - Supports selection of individual issues
 * - Updates URL parameters to reflect current filters
 * - Integrates with authentication to show relevant issues
 */
export default function FilteredInboxPage({
  filter = "all",
}: {
  filter: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const auth = useAuthUser();
  const [status, setStatus] = useState<IssueStatus>(
    (searchParams.get("status") as IssueStatus) ?? "open",
  );

  const assignee = useMemo<Assignee>(() => {
    switch (filter) {
      case "my-inbox":
        return auth.userDoc?.reference;
      case "unassigned":
        return null;
      case "all":
        return undefined;
    }
    return undefined;
  }, [auth.userDoc, filter]);

  const participant = auth.partner ? "partner" : "done";

  const query = useMemo(
    () =>
      inboxQuery({
        participant,
        status,
        assignee,
        participantReference: auth.partnerDoc?.reference,
      }),
    [participant, status, assignee, auth.partnerDoc?.reference],
  );

  const [selected, setSelected] = useSelectedModel<Issue>([], "issue");

  return (
    <InboxPage
      title={titleForFilter(filter)}
      query={query}
      selected={selected}
      onSelected={(issue) => {
        router.push({
          pathname,
          query: { status, issue: issue?.reference.id },
        });
        setSelected(issue);
      }}
      filterButton={
        <IssuesStatusFilterButton
          title={status}
          countQuery={query}
          onChange={(status) => {
            router.push({ pathname, query: { status } });
            setSelected(undefined);
            setStatus(status);
          }}
        />
      }
    />
  );
}
