import { useEffect, useState } from "react";
import { Issue, IssueMentionStatus } from "../../models/inbox/issue";
import { inboxMentionsQuery } from "../../models/inbox/provider";
import { useAuthUser } from "../../components/auth/AuthProvider";
import { usePathname, useSearchParams } from "next/navigation";
import { useRouter } from "next/router";
import { useSelectedModel } from "../../lib/hooks/use-selected-model";
import InboxPage from "../../components/inbox/InboxPage";
import IssuesMentionStatusFilterButton from "../../components/issues/IssuesMentionStatusFilterButton";
import { arrayRemove, updateDoc } from "firebase/firestore";

export default function MentionsInboxPage() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const auth = useAuthUser();
  const [status, setStatus] = useState<IssueMentionStatus>(
    (searchParams.get("status") as IssueMentionStatus) ?? "unread",
  );

  const participant = auth.partner ? "partner" : "done";

  const query = inboxMentionsQuery({
    participant,
    mentioned: auth.userDoc!.reference,
    status,
    participantReference: auth?.partnerDoc?.reference,
  });

  const [selected, setSelectedModel] = useSelectedModel<Issue>([], "issue");
  const [read, setRead] = useState<Issue[]>([]);

  // When an issue is selected, mark it as such and add it to the read list.
  const setSelected = (issue: Issue | undefined) => {
    // Check that the previously selected issue gets read.
    // This can happen due to automatic initial selection.
    if (selected && !read.includes(selected)) {
      setRead([...read, selected]);
    }

    // Set the new issue as selected.
    setSelectedModel(issue);

    // If the new issue is not read, mark it as read.
    if (issue && !read.includes(issue)) {
      setRead([...read, issue]);
    }
  };

  useEffect(() => {
    const flushReadMentions = async (url: string) => {
      // If we are navigating within the mentions page, do nothing.
      if (url.startsWith("/inbox/mentions")) return;

      // When we navigate away from this page, we want to mark all read mentions as read.
      await Promise.all(
        read.map(async (issue) =>
          updateDoc(issue.reference, {
            "mentions.unread": arrayRemove(auth.userDoc!.reference),
          }),
        ),
      );

      // Check that the selected issue gets set.
      // This can happen due to automatic initial selection and no new selection.
      if (selected && !read.includes(selected)) {
        updateDoc(selected.reference, {
          "mentions.unread": arrayRemove(auth.userDoc!.reference),
        });
      }

      // Finally, clear the read mentions.
      setRead([]);
    };

    // Start listening for route changes.
    router.events.on("routeChangeStart", flushReadMentions);

    // When the component is unmounted, turn off the listener.
    return () => {
      router.events.off("routeChangeStart", flushReadMentions);
    };
  }, [auth.userDoc, read, router.events, selected]);

  return (
    <InboxPage
      title={"Mentions"}
      query={query}
      selected={selected}
      onSelected={(issue) => {
        router.push({
          pathname,
          query: { status, issue: issue?.reference.id },
        });
        setSelected(issue);
      }}
      filterButton={
        <IssuesMentionStatusFilterButton
          title={status}
          countQuery={query}
          onChange={(status) => {
            router.push({ pathname, query: { status } });
            setSelected(undefined);
            setStatus(status);
          }}
        />
      }
    />
  );
}
