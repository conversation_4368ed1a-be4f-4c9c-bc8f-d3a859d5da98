import SettingsContainer from "../../components/settings/SettingsContainer";
import PageRestricted from "../../components/layout/PageRestricted";
import { Head<PERSON>, MainPanel } from "../../components/layout/panels";
import { MenuPanel } from "../../components/layout/panels";
import { Content } from "../../components/layout/panels";
import {
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
} from "@mui/material";
import { useState } from "react";
import { startCase } from "lodash";
import SuperUserManagement from "../../components/user/SuperUserManager";
import CmsEditor from "../../components/cms/CmsEditor";
import FixedPriceJobsTable from "../../components/job/table/FixedPriceJobsTable";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";

export default function SettingsPage() {
  const [selected, setSelected] = useState<string>("general");

  return (
    <PageRestricted>
      <PanelGroup autoSaveId="settings" direction="horizontal">
        <MenuPanel defaultSize={15}>
          <Header title={"Settings"} />
          <Content>
            <List disablePadding>
              <MenuListItem
                selected={selected === "general"}
                primary="General"
                onClick={() => setSelected("general")}
              />
              <MenuListItem
                selected={selected === "users"}
                primary="Users"
                onClick={() => setSelected("users")}
              />
              <MenuListItem
                selected={selected === "cms"}
                primary="CMS"
                onClick={() => setSelected("cms")}
              />
              <MenuListItem
                selected={selected === "fixedPriceJobs"}
                primary="Fixed Price Jobs"
                onClick={() => setSelected("fixedPriceJobs")}
              />
              <Divider />
            </List>
          </Content>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          <MainPanelContent selected={selected} />
        </MainPanel>
      </PanelGroup>
    </PageRestricted>
  );
}

function MenuListItem({
  primary,
  onClick,
  selected,
}: {
  primary: string;
  onClick: () => void;
  selected: boolean;
}) {
  return (
    <ListItem disablePadding sx={{ backgroundColor: "background.paper" }}>
      <ListItemButton onClick={onClick} selected={selected}>
        <ListItemText primary={primary} />
      </ListItemButton>
    </ListItem>
  );
}

function MainPanelContent({ selected }: { selected: string }) {
  if (selected === "general") {
    return (
      <>
        <Header title={startCase(selected)} />
        <Content>
          <SettingsContainer />
        </Content>
      </>
    );
  }

  if (selected === "users") {
    return <SuperUserManagement />;
  }

  if (selected === "cms") {
    return (
      <>
        <Header title={startCase(selected)} />
        <Content>
          <CmsEditor />
        </Content>
      </>
    );
  }

  if (selected === "fixedPriceJobs") {
    return (
      <>
        <Header title={startCase(selected)} />
        <Content>
          <FixedPriceJobsTable />
        </Content>
      </>
    );
  }

  return null;
}
