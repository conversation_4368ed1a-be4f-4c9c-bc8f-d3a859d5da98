import { useMemo, useState } from "react";
import { partnerJobNotBilledQuery } from "../../models/job/job-provider";
import { useJobs } from "../../hooks/job/jobsHook";
import { PlatformTier } from "../../components/auth/Tiered";
import InvoicingPageContent from "../../components/invoicing/InvoicingPage";
import PlatformInvoicingPageContent from "../../components/invoicing/PlatformInvoicingPage";
import { useAuthUser } from "../../components/auth/AuthProvider";
import PageRestricted from "../../components/layout/PageRestricted";
import { DonePartnerReference } from "../../models/other/partner";
import { DocumentReference } from "firebase/firestore";
import { titleForInvoicingFilter } from "../../components/invoicing/InvoicingFilterButton";
import InvoicingFilterButton from "../../components/invoicing/InvoicingFilterButton";

export default function InvoicingPage() {
  const authUser = useAuthUser();

  if (authUser.partnerDoc) {
    return (
      <PlatformTier>
        <PartnerInvoicing partnerRef={authUser.partnerDoc.reference} />
      </PlatformTier>
    );
  }

  return (
    <PageRestricted>
      <InternalInvoicing />
    </PageRestricted>
  );
}

function InternalInvoicing() {
  const [segmentFilter, setSegmentFilter] = useState<string>("subcontractor");

  if (segmentFilter === "partner") {
    return (
      <PlatformInvoicingPageContent
        partnerRef={DonePartnerReference()}
        tools={
          <InternalInvoicingSwitcher
            segmentFilter={segmentFilter}
            setSegmentFilter={setSegmentFilter}
          />
        }
      />
    );
  }

  return (
    <InternalInvoicingPage
      segmentFilter={segmentFilter}
      setSegmentFilter={setSegmentFilter}
    />
  );
}

function InternalInvoicingSwitcher({
  segmentFilter,
  setSegmentFilter,
}: {
  segmentFilter: string;
  setSegmentFilter: (segmentFilter: string) => void;
}) {
  return (
    <InvoicingFilterButton
      title={titleForInvoicingFilter(segmentFilter)}
      onChange={(change) => {
        setSegmentFilter?.(change);
      }}
    />
  );
}

function InternalInvoicingPage({
  segmentFilter,
  setSegmentFilter,
}: {
  segmentFilter: string;
  setSegmentFilter: (segmentFilter: string) => void;
}) {
  const query = partnerJobNotBilledQuery("done");
  const { jobs, error } = useJobs(query);

  // Need to do client side filtering because Firestore doesn't support AND + OR queries.
  const filteredJobs = useMemo(() => {
    if (!jobs) return [];

    const notPlatformJobs = jobs.filter(
      (job) => !job.tags?.includes("platform"),
    );

    if (segmentFilter === "ev-chargers") {
      return notPlatformJobs.filter((job) => job.tags?.includes("ev-chargers"));
    }
    if (segmentFilter === "subcontractor") {
      return notPlatformJobs.filter(
        (job) => !job.tags?.includes("ev-chargers"),
      );
    }

    return notPlatformJobs;
  }, [jobs, segmentFilter]);

  return (
    <PlatformTier>
      <InvoicingPageContent
        filteredJobs={filteredJobs}
        error={error}
        tools={
          <InternalInvoicingSwitcher
            segmentFilter={segmentFilter}
            setSegmentFilter={setSegmentFilter}
          />
        }
      />
    </PlatformTier>
  );
}

function PartnerInvoicing({ partnerRef }: { partnerRef: DocumentReference }) {
  return (
    <PlatformTier>
      <PlatformInvoicingPageContent partnerRef={partnerRef} />
    </PlatformTier>
  );
}
