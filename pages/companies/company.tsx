import Company from "../../components/company/details/CompanyDetails";
import { useRouter } from "next/router";
import Done404Page from "../404";
import { PlatformTier } from "../../components/auth/Tiered";

export default function CompanyPage() {
  const router = useRouter();
  const companyId = router.query.id;

  return companyId ? (
    <PlatformTier>
      <Company companyId={companyId as string} />
    </PlatformTier>
  ) : (
    <Done404Page />
  );
}
