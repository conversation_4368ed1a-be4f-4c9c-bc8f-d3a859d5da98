import { useRouter } from "next/router";
import UserContainer from "../../components/user/UserContainer";
import Done404Page from "../404";
import { PlatformTier } from "../../components/auth/Tiered";

export default function UserPage() {
  const router = useRouter();
  const userId = router.query.id;

  return userId ? (
    <PlatformTier>
      <UserContainer userId={userId as string} />
    </PlatformTier>
  ) : (
    <Done404Page />
  );
}
