import { useState } from "react";
import DialogManager from "../../components/dialogs/DialogManager";
import {
  Content,
  Header,
  MenuPanel,
  MainPanel,
  DetailsPanel,
} from "../../components/layout/panels";
import {
  Typography,
  List,
  Stack,
  Alert,
  TextField,
  Button,
  Box,
  AlertTitle,
} from "@mui/material";
import {
  query,
  collection,
  getFirestore,
  where,
  updateDoc,
} from "firebase/firestore";
import { maxLengthWithEllipsis } from "../../lib/max-length-with-ellipses";
import TaskListItem from "../../components/common/tasks/TaskListItem";
import CompanyDetails from "../../components/company/details/CompanyDetails";
import { useCompanies } from "../../hooks/company/companiesHook";
import { Company } from "../../models/company/company";
import CompanyUsers from "../../components/company/CompanyUsers";
import CompanyHomeLocation from "../../components/company/CompanyHomeLocation";
import CompanyDocuments from "../../components/company/CompanyDocuments";
import CompanyCertificates from "../../components/company/CompanyCertificates";
import Link from "next/link";
import { useCompany } from "../../hooks/company/companyHook";
import Loading from "../../components/common/Loading";
import { DocumentDropArea } from "../../components/company/Company";
import ManagedDialog from "../../components/dialogs/ManagedDialog";
import UploadDocumentDialog from "../../components/company/dialogs/UploadDocumentDialog";
import { isDefined } from "../../lib/filter-undefined";
import TextAreaEditor from "../../components/common/TextAreaEditor";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { usePanelSelection } from "../../lib/hooks/use-panel-selection";

export default function InboxPage() {
  const [selectedCompany, , detailsPanelRef, handleCompanySelect] =
    usePanelSelection<Company>();

  const { companies } = useCompanies(
    query(
      collection(getFirestore(), "companies"),
      where("checkStatus", "==", "failed"),
      where("status", "in", ["active", "new"]),
    ),
  );

  return (
    <DialogManager>
      <PanelGroup autoSaveId="company-checks" direction="horizontal">
        <MenuPanel>
          <Header title="Company checks" />
          <Content>
            <CompaniesList
              selectedCompany={selectedCompany ?? undefined}
              companies={companies ?? []}
              onCompanySelected={handleCompanySelect}
            />
          </Content>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          {selectedCompany && (
            <Header title={maxLengthWithEllipsis(40, selectedCompany.name)}>
              <Typography variant="body2">
                {selectedCompany.services?.join(", ")}{" "}
                {selectedCompany.tags?.join(", ")}
              </Typography>
            </Header>
          )}
          <Content>
            {selectedCompany && (
              <CompanyChecksPanel companyId={selectedCompany.reference.id} />
            )}
          </Content>
        </MainPanel>
        <PanelResizeHandle />
        <DetailsPanel ref={detailsPanelRef} collapsible>
          <Header title="Details" />
          <Content>
            {selectedCompany && (
              <CompanyDetails companyId={selectedCompany.reference.id} />
            )}
          </Content>
        </DetailsPanel>
      </PanelGroup>
    </DialogManager>
  );
}

function CompanyChecksPanel({ companyId }: { companyId: string }) {
  const { company, loading } = useCompany(companyId);

  const [bankgiro, setBankGiro] = useState<undefined | string>(undefined);

  if (loading || !company) return <Loading />;

  if (company.checkStatus !== "failed") {
    return <Alert> Company checks passed</Alert>;
  }

  return (
    <DialogManager>
      <DocumentDropArea company={company}>
        <ManagedDialog
          id={"upload-document"}
          component={UploadDocumentDialog}
        />

        <Stack>
          {company.checks?.bankGiro === false && (
            <Stack padding={2} spacing={1}>
              <Alert
                action={
                  <Link
                    passHref
                    target="_blank"
                    rel="noopener noreferrer"
                    href={`https://www.bankgirot.se/sok-bankgironummer?bgnr=&company=&city=&orgnr=${company.orgNo}`}
                  >
                    <Button
                      color="inherit"
                      size="small"
                      disabled={!company.orgNo?.length}
                      variant="outlined"
                    >
                      Check Bankgiro with org number
                    </Button>
                  </Link>
                }
                severity="warning"
              >
                Company has no bankgiro
              </Alert>
              <Stack spacing={1} direction="row">
                <TextField
                  value={bankgiro}
                  onChange={(event) => {
                    setBankGiro(event.target.value);
                  }}
                  size="small"
                  placeholder="Enter bankgiro"
                />
                <Button
                  onClick={() => {
                    updateDoc(company.reference, {
                      "billingSettings.paymentInfo.bankgiro": bankgiro,
                    });
                  }}
                  variant="contained"
                  size="small"
                >
                  Set bankgiro
                </Button>
              </Stack>
            </Stack>
          )}

          {company.checks?.adminUser === false && (
            <Stack padding={2} spacing={1}>
              <Alert severity="warning">Company has no admin</Alert>
              <CompanyUsers company={company} />
            </Stack>
          )}

          {company.checks?.companyDescription === false && (
            <Stack padding={2} spacing={1}>
              <Alert severity="warning">Company has no description</Alert>
              <TextAreaEditor
                docRef={company.reference}
                dataKey="profileText"
              />
            </Stack>
          )}

          {company.checks?.homeAddress === false && (
            <Stack padding={2} spacing={1}>
              <Alert severity="warning">Company has no address</Alert>
              <CompanyHomeLocation noWarning company={company} />
            </Stack>
          )}

          {(company.checks?.insuranceDocument === false ||
            company.checks?.ueAgreement === false) && (
            <Stack marginBottom={20} padding={2} spacing={1}>
              <Alert severity="warning">
                <AlertTitle>
                  Company has missing documents:{" "}
                  {[
                    !company.checks?.insuranceDocument
                      ? "insurance"
                      : undefined,
                    !company.checks?.ueAgreement ? "UE Agreement" : undefined,
                  ]
                    .filter(isDefined)
                    .join(", ")}
                </AlertTitle>
                Drop a document on the table.
              </Alert>
              <Box height={250}>
                <CompanyDocuments companyId={company.reference.id} />
              </Box>
            </Stack>
          )}

          {(company.checks?.elAlCertificate === false ||
            company.checks?.sakerVattenCertificate === false) && (
            <Stack padding={2} spacing={1}>
              <Alert severity="warning">
                Company has missing certificate check
              </Alert>
              <CompanyCertificates company={company} />
            </Stack>
          )}
        </Stack>
      </DocumentDropArea>
    </DialogManager>
  );
}

function CompaniesList({
  companies,
  selectedCompany,
  onCompanySelected,
}: {
  companies: Company[];
  selectedCompany?: Company;
  onCompanySelected: (company: Company) => void;
}) {
  return (
    <List dense>
      {companies.map((company) => (
        <TaskListItem
          secondaryTitle={
            !company.checks?.insuranceDocument ? "Missing insurance" : undefined
          }
          secondaryTitleTypographyProps={{
            color: !company.checks?.insuranceDocument
              ? "error.main"
              : undefined,
          }}
          key={company.reference.id}
          onClick={() => onCompanySelected(company)}
          selected={company.reference?.id === selectedCompany?.reference.id}
          title={company.name}
          text={company.services?.join(", ")}
        />
      ))}
    </List>
  );
}
