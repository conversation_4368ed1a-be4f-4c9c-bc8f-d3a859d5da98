import {
  <PERSON>ert,
  Button,
  ButtonProps,
  Grid2,
  <PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import { useAuthUser } from "../../components/auth/AuthProvider";
import DialogManager, {
  useDialog,
} from "../../components/dialogs/DialogManager";
import PageGridContainer from "../../components/layout/PageGridContainer";
import IntegrationCard from "../../components/integrations/IntegrationCard";
import ManagedDialog from "../../components/dialogs/ManagedDialog";
import ApiSetupDialog from "../../components/integrations/setups/ApiSetupDialog";
import MakeSetupDialog from "../../components/integrations/setups/MakeSetupDialog";
import IntegrationsTable from "../../components/integrations/IntegrationsTable";
import ConfirmDeleteKeyDialog from "../../components/integrations/ConfirmDeleteKeyDialog";

export default function PartnerIntegrationsPage() {
  const { partnerDoc } = useAuthUser();

  const integrationsEnabled = partnerDoc?.apiEnabled ?? false;

  return (
    <DialogManager>
      <ManagedDialog
        id="api-v2"
        component={ApiSetupDialog}
        partnerId={partnerDoc?.reference?.id}
      />
      <ManagedDialog
        id="make-setup"
        component={MakeSetupDialog}
        partnerId={partnerDoc?.reference?.id}
      />
      <ManagedDialog id="delete-key" component={ConfirmDeleteKeyDialog} />
      <PageGridContainer padding={5} spacing={3}>
        <Grid2>
          <Typography variant="h4">Integrations</Typography>
        </Grid2>
        <IntegrationsDisabledAlert hidden={integrationsEnabled} />
        <Grid2>
          <Grid2 container spacing={2}>
            <IntegrationCard
              title="API v2"
              actions={
                <>
                  <LinkButton
                    label="Documentation"
                    href="https://doneservices.notion.site/Done-Booking-API-v2-a5807367d21c4643a365036b63100b6d?pvs=4"
                  />
                  <DialogButton
                    label="Setup"
                    dialog="api-v2"
                    disabled={!integrationsEnabled}
                  />
                </>
              }
            >
              Our brand new API that replaces the deprecated version 1. Uses
              REST and Json to build your own integrations.
            </IntegrationCard>
            <IntegrationCard
              title="Make app"
              actions={
                <>
                  <LinkButton
                    label="Documentation"
                    href="https://doneservices.notion.site/Done-Make-application-bcb0f2765efd48e594e37d60d68f3827?pvs=4"
                  />
                  <DialogButton
                    label="Setup"
                    dialog="make-setup"
                    disabled={!integrationsEnabled}
                  />
                </>
              }
            >
              Integrated application for the{" "}
              <Link href="https://www.make.com/" target="blank">
                Make
              </Link>{" "}
              platform. With it you can easily build your own workflows and
              connect 3rd party services with the Done platform.
            </IntegrationCard>
            <IntegrationCard
              title="API v1"
              chip={{ label: "Deprecated" }}
              actions={
                <LinkButton
                  label="Documentation"
                  href="https://doneservices.notion.site/Done-Booking-API-c94b63f4dd87439aa0f26dd14cb51a65?pvs=4"
                />
              }
            >
              Our old API that have been deprecated. Use the new API v2 instead
              for new integrations.
            </IntegrationCard>
          </Grid2>
        </Grid2>
        <Grid2>
          <Typography variant="h5">Active API Keys</Typography>
        </Grid2>
        <Grid2>
          <IntegrationsTable partner={partnerDoc?.reference} />
        </Grid2>
      </PageGridContainer>
    </DialogManager>
  );
}

function IntegrationsDisabledAlert({ hidden }: { hidden: boolean }) {
  if (hidden) return null;

  return (
    <Grid2>
      <Alert severity="warning">
        Integrations are not enabled for your account.
      </Alert>
    </Grid2>
  );
}

interface BaseButtonProps
  extends Pick<
    ButtonProps,
    "color" | "style" | "variant" | "size" | "disabled"
  > {
  label: string;
}

interface DialogButtonProps extends BaseButtonProps {
  dialog: string;
}

function DialogButton({ label, dialog, ...rest }: DialogButtonProps) {
  const { open } = useDialog(dialog);
  return (
    <Button onClick={() => open()} {...rest}>
      {label}
    </Button>
  );
}

interface LinkButtonProps extends BaseButtonProps {
  href: string;
}

function LinkButton({ label, href, ...rest }: LinkButtonProps) {
  return (
    <Button href={href} target="blank" {...rest}>
      {label}
    </Button>
  );
}
