import { useState } from "react";
import JobDetails from "../../components/job/Job";
import { isEqual } from "lodash";
import {
  Content,
  Header,
  MainPanel,
  DetailsPanel,
  MenuPanel,
} from "../../components/layout/panels";
import { JobOffer } from "../../models/job/offer/job-offer";
import { List, Stack } from "@mui/material";
import { query, where, orderBy } from "firebase/firestore";
import { useJob } from "../../hooks/job/jobHook";
import SendOfferCard from "../../components/job/JobSendOfferCard";
import { maxLengthWithEllipsis } from "../../lib/max-length-with-ellipses";
import { JobTags } from "../../models/job/job";
import JobSegmentFilterButton, {
  titleForSegmentFilter,
} from "../../components/offer/JobSegmentFilterButton";
import OffersQueryFilterButton from "../../components/offer/OffersQueryFilter";
import TaskListItem from "../../components/common/tasks/TaskListItem";
import ManagedJobDialogs from "../../components/job/details/ManagedJobDialogs";
import { jobOfferCollection } from "../../models/job/offer/job-offer-provider";
import { useCollectionData } from "react-firebase-hooks/firestore";
import AutoOfferButton from "../../components/job/AutoOfferButton";
import SentOffers from "../../components/job/JobsSentOffers";
import AttachmentsContainer from "../../components/job/details/AttachmentsContainer";
import ComponentContainer from "../../components/common/ComponentContainer";
import DialogManager from "../../components/dialogs/DialogManager";
import { DonePartnerReference } from "../../models/other/partner";
import JobFormsContainer from "../../components/job/JobFormsContainer";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { usePanelSelection } from "../../lib/hooks/use-panel-selection";

function titleForQueryFilter(filter?: string | string[]) {
  switch (filter) {
    case "notSent":
      return "Not sent";
    case "notMatched":
      return "Not matched";
  }
  return "All";
}

/**
 * @deprecated To be replaced by MatchingPage
 */
export default function InboxPage() {
  const [selectedOffer, , detailsPanelRef, handleOfferSelect] =
    usePanelSelection<JobOffer>();
  const [offerTagFilter, setOfferTagFilter] = useState<string | undefined>(
    undefined,
  );

  const [offerQueryFilter, setOfferQueryFilter] = useState<string | undefined>(
    "notSent",
  );

  const [offers] = useCollectionData(
    query(
      jobOfferCollection(),
      where("jobStatus", "==", "inbox"),
      where("network", "==", DonePartnerReference()),
      orderBy("createTime", "desc"),
    ),
  );

  if (selectedOffer !== null) {
    const offer = offers?.find(
      (offer) => offer.reference.id === selectedOffer.reference.id,
    );
    if (offer && !isEqual(offer, selectedOffer)) {
      handleOfferSelect(offer);
    }
  }

  // Due to number of offer waiting jobs are low filter logic handled by client
  const filteredOffers = filterOfferedJobs(
    offers,
    offerTagFilter,
    offerQueryFilter,
  );

  return (
    <DialogManager>
      <PanelGroup autoSaveId="offers" direction="horizontal">
        <MenuPanel>
          <Header title="Offers">
            <OffersQueryFilterButton
              title={titleForQueryFilter(offerQueryFilter)}
              onChange={(change) => {
                setOfferQueryFilter(change);
              }}
            />
            <JobSegmentFilterButton
              title={titleForSegmentFilter(offerTagFilter)}
              count={filteredOffers?.length ?? 0}
              onChange={(change) => {
                setOfferTagFilter(change);
              }}
            />
          </Header>
          <Content>
            <JobOfferList
              selectedOffer={selectedOffer ?? undefined}
              offers={filteredOffers ?? []}
              onOfferSelected={handleOfferSelect}
            />
          </Content>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          <Header
            title={
              selectedOffer
                ? maxLengthWithEllipsis(40, selectedOffer.description)
                : "No offer selected"
            }
          >
            {selectedOffer && selectedOffer?.jobStatus === "inbox" && (
              <AutoOfferButton offer={selectedOffer} />
            )}
          </Header>
          <Content padding={2}>
            {selectedOffer && (
              <JobOfferPanel jobId={selectedOffer.reference.id} />
            )}
          </Content>
        </MainPanel>
        <PanelResizeHandle />
        <DetailsPanel ref={detailsPanelRef} collapsible>
          <Header title="Details" />
          <Content>
            {selectedOffer && (
              <JobDetails slim jobId={selectedOffer.reference.id} hideOffer />
            )}
          </Content>
        </DetailsPanel>
      </PanelGroup>
    </DialogManager>
  );
}

function JobOfferList({
  offers,
  selectedOffer,
  onOfferSelected,
}: {
  offers: JobOffer[];
  selectedOffer?: JobOffer;
  onOfferSelected: (offer: JobOffer) => void;
}) {
  return (
    <List dense>
      {offers.map((offer) => (
        <TaskListItem
          key={offer.reference.id}
          onClick={() => onOfferSelected(offer)}
          selected={offer.reference?.id === selectedOffer?.reference.id}
          title={offer.description}
          secondaryTitle={!offer.offeredTo?.length ? "Not sent" : "Not matched"}
          secondaryTitleTypographyProps={{
            color: !offer.offeredTo?.length ? "warning.main" : "info.main",
          }}
          text={offer.services?.join(", ")}
          timestamp={offer.createTime}
        />
      ))}
    </List>
  );
}

function JobOfferPanel({ jobId }: { jobId: string }) {
  const { job, loading } = useJob(jobId);

  if (loading || !job) return null;

  return (
    <Stack spacing={2}>
      <ManagedJobDialogs job={job} />
      <AttachmentsContainer job={job} interactive={true} />

      <JobFormsContainer job={job} />

      <ComponentContainer title="Send offer">
        {job.status === "inbox" ? (
          <SendOfferCard job={job} />
        ) : (
          <SentOffers job={job} />
        )}
      </ComponentContainer>
    </Stack>
  );
}

function filterOfferedJobs(
  offers: JobOffer[] | undefined,
  offerTagFilter?: string,
  offerQueryFilter?: string,
): JobOffer[] {
  if (!offers) return [];

  const offersTagFiltered = offerTagFilter
    ? offers?.filter((e) => e.tags?.includes(offerTagFilter as JobTags))
    : offers;

  const offersQueryFiltered = offerQueryFilter
    ? offersTagFiltered?.filter((e) =>
        offerQueryFilter === "notSent"
          ? !e.offeredTo?.length
          : e.offeredTo?.length,
      )
    : offersTagFiltered;

  return offersQueryFiltered;
}
