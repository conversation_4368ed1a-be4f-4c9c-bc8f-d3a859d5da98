import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  DetailsPanel,
  <PERSON>uPanel,
} from "../../components/layout/panels";
import { JobOffer } from "../../models/job/offer/job-offer";
import { List, Stack } from "@mui/material";
import { query, where, orderBy } from "firebase/firestore";
import { useJob } from "../../hooks/job/jobHook";
import { maxLengthWithEllipsis } from "../../lib/max-length-with-ellipses";
import TaskListItem from "../../components/common/tasks/TaskListItem";
import ManagedJobDialogs from "../../components/job/details/ManagedJobDialogs";
import { jobOfferCollection } from "../../models/job/offer/job-offer-provider";
import { useCollectionData } from "react-firebase-hooks/firestore";
import ComponentContainer from "../../components/common/ComponentContainer";
import DialogManager from "../../components/dialogs/DialogManager";
import { use<PERSON>uth<PERSON>ser } from "../../components/auth/AuthProvider";
import EmptyState from "../../components/empty-state/EmptyState";
import PartnersJob from "../../components/job/PartnersJob";
import JobMatchInstallers from "../../components/job/PartnerJobMatchInstallers";
import { DonePartnerReference, partnerRef } from "../../models/other/partner";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { usePanelSelection } from "../../lib/hooks/use-panel-selection";

export default function MatchingPage() {
  const authUser = useAuthUser();
  const [selectedOffer, , detailsPanelRef, handleOfferSelect] =
    usePanelSelection<JobOffer>();

  const [offers, _, error] = useCollectionData(
    query(
      jobOfferCollection(),
      where("jobStatus", "==", "inbox"),
      where(
        "network",
        "==",
        partnerRef(authUser.partner) ?? DonePartnerReference(),
      ),
      orderBy("createTime", "desc"),
    ),
  );

  return (
    <DialogManager>
      <PanelGroup autoSaveId="matching" direction="horizontal">
        <MenuPanel>
          <Header title="Matching" />
          <Content>
            {offers?.length ? (
              <JobOfferList
                selectedOffer={selectedOffer ?? undefined}
                offers={offers ?? []}
                onOfferSelected={handleOfferSelect}
              />
            ) : (
              <EmptyState error={error} />
            )}
          </Content>
        </MenuPanel>
        <PanelResizeHandle />
        <MainPanel>
          <Header
            title={
              selectedOffer
                ? maxLengthWithEllipsis(40, selectedOffer.description)
                : "No offer selected"
            }
          />
          <Content>
            {selectedOffer && (
              <JobOfferPanel jobId={selectedOffer.reference.id} />
            )}
          </Content>
        </MainPanel>

        <PanelResizeHandle />
        <DetailsPanel ref={detailsPanelRef} collapsible>
          <Header title="Details" />
          <Content>
            {selectedOffer && (
              <PartnersJob
                jobId={selectedOffer.reference.id}
                fixedToolbar
                hideMatching
              />
            )}
          </Content>
        </DetailsPanel>
      </PanelGroup>
    </DialogManager>
  );
}

function JobOfferList({
  offers,
  selectedOffer,
  onOfferSelected,
}: {
  offers: JobOffer[];
  selectedOffer?: JobOffer;
  onOfferSelected: (offer: JobOffer) => void;
}) {
  return (
    <List dense disablePadding>
      {offers.map((offer) => (
        <TaskListItem
          key={offer.reference.id}
          onClick={() => onOfferSelected(offer)}
          selected={offer.reference?.id === selectedOffer?.reference.id}
          title={offer.description}
          secondaryTitle={"Not matched"}
          secondaryTitleTypographyProps={{
            color: "info.main",
          }}
          text={offer.location?.subLocality ?? offer.location?.postalTown}
          timestamp={offer.createTime}
        />
      ))}
    </List>
  );
}

function JobOfferPanel({ jobId }: { jobId: string }) {
  const { job, loading } = useJob(jobId);

  if (loading || !job) return null;

  return (
    <Stack spacing={2}>
      <ManagedJobDialogs job={job} />
      <ComponentContainer title="Installers">
        <JobMatchInstallers job={job} />
      </ComponentContainer>
    </Stack>
  );
}
