import App from "../components/common/App";
import { initFirebase } from "../lib/initFirebase";
import { useEffect, useState } from "react";
import "../styles/globals.css";
import type { AppProps } from "next/app";
import { initSentry } from "../lib/initSentry";

function DoneApp({ Component, pageProps }: AppProps) {
  const [ready, setReady] = useState(false);

  useEffect(() => {
    initFirebase();
    initSentry();
    setReady(true);
  }, []);

  if (!ready) return null;

  return (
    <App>
      <Component {...pageProps} />
    </App>
  );
}
export default DoneApp;
