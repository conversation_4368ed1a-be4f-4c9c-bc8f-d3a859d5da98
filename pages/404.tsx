import { useRouter } from "next/router";

import WarningPage from "../components/common/WarningPage";

export const Done404Page: React.FC = () => {
  var hasPossibleRoute = false;

  const router = useRouter();
  const pathElements = router.asPath.replace(/\//g, " ").trim().split(" ");

  if (router.asPath.includes("jobs/")) {
    hasPossibleRoute = true;
    router.push(`/jobs/job/?id=${pathElements.pop()}`);
  }

  if (router.asPath.includes("users/")) {
    hasPossibleRoute = true;
    router.push(`/users/user/?id=${pathElements.pop()}`);
  }

  if (router.asPath.includes("company/")) {
    hasPossibleRoute = true;
    router.push(`/companies/company/?id=${pathElements.pop()}`);
  }

  if (router.asPath.includes("companies/")) {
    hasPossibleRoute = true;
    router.push(`/companies/company/?id=${pathElements.pop()}`);
  }

  return hasPossibleRoute ? null : (
    <WarningPage
      title="Page not found"
      message="The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."
    />
  );
};

export default Done404Page;
