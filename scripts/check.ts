import { getArgValue } from "./lib/get-arg";
const inquirer = require("inquirer");

interface Result {
  [key: string]: boolean;
}

/**
 * Runs the program.
 */
async function main() {
  const target = await getArgValue("target");

  const result: Result = await inquirer.prompt(
    [
      target === "dev" && {
        type: "confirm",
        name: "dev",
        message:
          "Manual deploys are discouraged! Instead, open a draft PR and test the automatically deployed version there.\nContinue with manual deploy?",
        default: false,
      },
      target === "prod" && {
        type: "confirm",
        name: "prod",
        message:
          "Manual deploys are discouraged! Instead, create a release on the GitHub repository to automatically deploy to production.\nContinue with manual deploy?",
        default: false,
      },
    ].filter(Boolean),
  );

  // Abort if any result is false.
  if (Object.values(result).includes(false)) {
    console.error("Aborting...");
    process.exit(1);
  }
}

(async () => {
  await main();
})();
