/**
 * Get the value of a command line argument if available.
 * @param flag Label of the flag to check.
 * @returns The value for the flag or undefined if flag was not set or no value was set.
 */
export function getArgValue(flag: string): string | undefined {
  const args = process.argv.slice(2);
  const index = args.indexOf(`--${flag}`) + 1;

  if (!index || index >= args.length) {
    return;
  }

  return args[index];
}
